import React, { VFC } from 'react';
import { PropGetters } from 'downshift';
import { CustomDropdownOption } from '../types';
import cx from 'classnames';

import Icon from 'components/Icon';
import Icons from 'constants/icons';
import {
  FeatureDisabledTooltip,
  FEATURES,
  DataForFeatureCheckType,
  guardFeaturePermissionArgs,
} from 'features/permissions';
import { PopoverDeprecatedAlign } from '../../Popover';
import {
  SubLabelItemStyle,
  CompactItemStyle,
  RegularItemStyle,
} from './DropdownItem.styles';

type DropdownItemProps = {
  item: CustomDropdownOption;
  idKey: string;
  highlightedIndex: number | null;
  index: number;
  getItemProps: PropGetters<CustomDropdownOption>['getItemProps'];

  isCheckedItem: (id: CustomDropdownOption['id']) => boolean;
  isDisabledItem: (id: CustomDropdownOption['id']) => boolean;
  compactItems?: boolean;
};

export const DropdownItem: VFC<DropdownItemProps> = ({
  item,
  index,
  idKey,
  getItemProps,
  highlightedIndex,

  isDisabledItem,
  isCheckedItem,
  compactItems,
}) => {
  const itemId = item[idKey as keyof CustomDropdownOption];

  const props = getItemProps({
    item,
    index,
    disabled: isDisabledItem(item.id),
  });

  const featureAvailableArgs: DataForFeatureCheckType =
    guardFeaturePermissionArgs(item)
      ? ({
          feature: item?.feature || FEATURES.UNKNOWN,
          data: item?.data || null,
        } as DataForFeatureCheckType)
      : {
          feature: FEATURES.UNKNOWN,
        };

  return (
    <FeatureDisabledTooltip
      {...featureAvailableArgs}
      direction={PopoverDeprecatedAlign.MiddleRight}
    >
      <li
        key={itemId}
        className={cx([
          'modal-select__list-item',
          { __disabled: isDisabledItem(item.id) },
        ])}
        {...props}
      >
        {compactItems ? (
          <CompactItemStyle
            className={cx([
              'modal-select__item',
              {
                __highlighted: highlightedIndex === index,
              },
              { __checked: isCheckedItem(item.id) },
              { __danger: item.danger },
            ])}
          >
            {item.icon ? (
              <Icon
                className='model-select__icon'
                name={item.icon as keyof typeof Icons}
              />
            ) : null}
            <span className='modal-select__value'>
              {item.label}
              <div>
                {item.subLabelItems?.map((text, index) => (
                  <SubLabelItemStyle key={text + index}>
                    {text}
                  </SubLabelItemStyle>
                ))}
              </div>
            </span>
          </CompactItemStyle>
        ) : (
          <RegularItemStyle
            className={cx([
              'modal-select__item',
              {
                __highlighted: highlightedIndex === index,
              },
              { __checked: isCheckedItem(item.id) },
              { __danger: item.danger },
            ])}
          >
            {item.icon ? (
              <Icon
                className='model-select__icon'
                name={item.icon as keyof typeof Icons}
              />
            ) : null}
            <span className='modal-select__value'>
              {item.label}
              <div>
                {item.subLabelItems?.map((text, index) => (
                  <SubLabelItemStyle key={text + index}>
                    {text}
                  </SubLabelItemStyle>
                ))}
              </div>
            </span>
          </RegularItemStyle>
        )}
      </li>
    </FeatureDisabledTooltip>
  );
};
