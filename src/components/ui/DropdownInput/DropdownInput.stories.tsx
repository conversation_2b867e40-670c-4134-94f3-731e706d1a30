import React, { useState } from 'react';
import { DropdownInput } from './DropdownInput';
import { css } from 'linaria';
import { styled } from 'linaria/react';

export default {
  title: 'ui/DropdownInput',
  component: DropdownInput,
  decorators: [
    (Story: React.FC) => (
      <div style={{ padding: 16 }}>
        <Story />
      </div>
    ),
  ],
};

//This dropdown is simplest example
export const Simple = () => {
  return (
    <DropdownInput
      renderTrigger={({ getToggleButtonProps, ref }) => (
        <button {...getToggleButtonProps()} ref={ref}>
          Trigger
        </button>
      )}
      renderDropdown={({ ref, style }) => (
        <div ref={ref} style={style}>
          Dropdown
        </div>
      )}
    />
  );
};

export const WithTriggerClassName = () => {
  const CustomClassName = css`
    color: red;
  `;

  return (
    <DropdownInput
      className={CustomClassName}
      renderTrigger={({ getToggleButtonProps, ref, className }) => (
        <button {...getToggleButtonProps()} ref={ref} className={className}>
          Trigger
        </button>
      )}
      renderDropdown={({ ref, style }) => (
        <div ref={ref} style={style}>
          Dropdown
        </div>
      )}
    />
  );
};

export const WithTriggerPlaceholder = () => {
  return (
    <DropdownInput
      placeholder={'Placeholder'}
      renderTrigger={({ getToggleButtonProps, ref, placeholder }) => (
        <button {...getToggleButtonProps()} ref={ref}>
          {placeholder}
        </button>
      )}
      renderDropdown={({ ref, style }) => (
        <div ref={ref} style={style}>
          Dropdown
        </div>
      )}
    />
  );
};

export const WithValueInTrigger = () => {
  return (
    <DropdownInput
      value={'value'}
      renderTrigger={({ getToggleButtonProps, ref, value }) => (
        <button {...getToggleButtonProps()} ref={ref}>
          {value}
        </button>
      )}
      renderDropdown={({ ref, style }) => (
        <div ref={ref} style={style}>
          Dropdown
        </div>
      )}
    />
  );
};

export const WithValueAndRenderValueInTrigger = () => {
  return (
    <DropdownInput
      value={'value'}
      renderValue={value => value.toUpperCase()}
      renderTrigger={({ getToggleButtonProps, ref, value, renderValue }) => (
        <button {...getToggleButtonProps()} ref={ref}>
          {renderValue?.(value ?? '')}
        </button>
      )}
      renderDropdown={({ ref, style }) => (
        <div ref={ref} style={style}>
          Dropdown
        </div>
      )}
    />
  );
};

export const WithTriggerDisabled = () => {
  return (
    <DropdownInput
      disabled
      renderTrigger={({ getToggleButtonProps, ref, disabled }) => (
        <button {...getToggleButtonProps()} ref={ref}>
          {`disabled: ${(!!disabled).toString()}`}
        </button>
      )}
      renderDropdown={({ ref, style }) => (
        <div ref={ref} style={style}>
          Dropdown
        </div>
      )}
    />
  );
};

export const WithIsOpenInTrigger = () => {
  return (
    <DropdownInput
      renderTrigger={({ getToggleButtonProps, ref, isOpen }) => (
        <button {...getToggleButtonProps()} ref={ref}>
          {`Is dropdown open: ${(!!isOpen).toString()}`}
        </button>
      )}
      renderDropdown={({ ref, style }) => (
        <div ref={ref} style={style}>
          Dropdown
        </div>
      )}
    />
  );
};

export const WithValueInDropdown = () => {
  return (
    <DropdownInput
      value={'value'}
      renderTrigger={({ getToggleButtonProps, ref }) => (
        <button {...getToggleButtonProps()} ref={ref}>
          Trigger
        </button>
      )}
      renderDropdown={({ ref, style, value }) => (
        <div ref={ref} style={style}>
          {value}
        </div>
      )}
    />
  );
};

export const WithItemsInDropdown = () => {
  const [value, setValue] = useState<number | null>(null);
  const List = styled.ul`
    margin: 0;
    padding-left: 2em;
    cursor: pointer;
  `;

  const ListItem = styled.li<{ isSelected: boolean }>`
    margin: 0;
    padding-left: 2em;
    cursor: pointer;
    font-weight: ${({ isSelected }) => (isSelected ? 'bold' : 'normal')};
  `;

  return (
    <DropdownInput
      value={value}
      onChange={selectedValue =>
        setValue(value === selectedValue ? null : selectedValue)
      }
      renderTrigger={({ getToggleButtonProps, ref, value }) => (
        <button {...getToggleButtonProps()} ref={ref}>
          {value ? `Selected value: ${value}` : 'Not selected'}
        </button>
      )}
      renderDropdown={({
        getMenuProps,
        getItemProps,
        ref,
        style,
        onChange,
      }) => (
        <List {...getMenuProps()} ref={ref} style={style}>
          {[1, 2, 3].map((indexItem, index) => {
            return (
              <ListItem
                key={indexItem}
                {...getItemProps({ item: indexItem, index, key: indexItem })}
                onClick={() => {
                  onChange?.(indexItem);
                }}
                isSelected={indexItem === value}
              >{`${indexItem} item`}</ListItem>
            );
          })}
        </List>
      )}
    />
  );
};

//TODO popoverProps
//TODO position
