import React, { ForwardRefRenderFunction, MouseEventHandler } from 'react';

type LinkButtonProps = {
  disabled?: boolean;
  onClick?: MouseEventHandler;
} & React.AnchorHTMLAttributes<HTMLAnchorElement>;

const LinkButton: ForwardRefRenderFunction<
  HTMLAnchorElement,
  LinkButtonProps
> = ({ disabled, children, onClick, ...otherProps }, ref) => (
  // eslint-disable-next-line jsx-a11y/anchor-is-valid
  <a
    ref={ref}
    href='#'
    onClick={event => {
      event.preventDefault();
      if (!disabled && onClick) {
        onClick(event);
      }
    }}
    {...otherProps}
  >
    {children}
  </a>
);

export default React.forwardRef(LinkButton);
