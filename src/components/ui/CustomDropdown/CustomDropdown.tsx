import React, { useState } from 'react';
import Downshift from 'downshift';

import {
  CustomDropdownProps,
  CustomDropdownOption,
} from 'components/ui/CustomDropdown/types';
import { CustomDropdownContent } from './parts/CustomDropdownContent';
import {
  PopoverDeprecated,
  PopoverDeprecatedAlign,
  PopoverProps,
} from '../Popover';

const PopoverDefault = ({ open, offset, ...rest }: PopoverProps) => {
  const resultOffset: [number, number] | undefined =
    typeof offset === 'object'
      ? [offset.mainAxis as number, offset.mainAxis as number]
      : typeof offset === 'number'
      ? [offset, offset]
      : undefined;

  return (
    <PopoverDeprecated
      {...rest}
      active={open}
      offset={resultOffset}
      align={PopoverDeprecatedAlign.BottomLeft}
    />
  );
};

export const CustomDropdown = <Item extends object>({
  value,
  renderValue,
  disabled,
  className,
  wrapperClassName,
  placeholder,
  options,
  idKey = 'id',
  withSearchBox,
  searchBoxProps,
  onChange,
  contentForEmptySearch,
  ignoreIDs,
  disabledIDs,
  onToggle,
  onClick,
  onVisibilityToggle,
  sizeByParent,
  PopoverComponent = PopoverDefault,
  placement,
  customStyleClassName,
  compactItems,
  ...props
}: CustomDropdownProps<Item>) => {
  const [searchValue, setSearchValue] = useState<string>('');

  const handleChangeSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(event.target.value);
  };

  const resetSearch = () => {
    setSearchValue('');
  };

  const handleToggle = (isOpen: boolean) => {
    if (typeof onToggle === 'function') onToggle(isOpen);
    resetSearch();
  };

  const isCheckedItem = (id: string) => {
    const equal = id === value;
    if (ignoreIDs) {
      return equal && !ignoreIDs.includes(id);
    }
    return equal;
  };

  const isDisabledItem = (id?: string) => {
    if (disabledIDs) {
      return id ? disabledIDs.includes(id) && id !== value : false;
    }

    return false;
  };

  const itemToString = (item: CustomDropdownOption) => {
    return (typeof item?.label === 'string' && item?.label) || item?.id || '';
  };

  const searchFilter = (item: CustomDropdownOption) => {
    if (!searchValue) {
      return true;
    }

    if (typeof item.label !== 'string') {
      return true;
    }

    return item.label.toLowerCase().includes(searchValue.toLowerCase().trim());
  };

  const commonItemList = options.filter(searchFilter);
  const visibleMainList = commonItemList.length > 0;

  return (
    <div
      {...props}
      onClick={onClick}
      placeholder={placeholder}
      className={wrapperClassName}
    >
      <Downshift
        onChange={(selection?: CustomDropdownOption) => {
          if (selection !== undefined && selection !== null) {
            onChange(
              // @ts-ignore
              selection[idKey as keyof selection],
              selection,
            );
          }
        }}
        itemToString={itemToString}
        selectedItem={
          options.find(
            option => option[idKey as keyof CustomDropdownOption] === value,
          ) || null
        }
      >
        {({
          getRootProps,
          getInputProps,
          getMenuProps,
          getToggleButtonProps,
          getItemProps,
          isOpen,
          highlightedIndex,
          setHighlightedIndex,
        }) => (
          // @ts-ignore
          <div onMouseLeave={() => setHighlightedIndex(null)}>
            <CustomDropdownContent
              sizeByParent={sizeByParent}
              getRootProps={getRootProps}
              onVisibilityToggle={onVisibilityToggle}
              isOpen={isOpen}
              baseClassName={className}
              PopoverComponent={PopoverComponent}
              placement={placement}
              disabled={disabled}
              getToggleButtonProps={getToggleButtonProps}
              onToggle={handleToggle}
              renderValue={renderValue}
              value={value}
              withSearchBox={withSearchBox}
              setHighlightedIndex={setHighlightedIndex}
              getInputProps={getInputProps}
              searchValue={searchValue}
              handleChangeSearch={handleChangeSearch}
              placeholder={placeholder}
              resetSearch={resetSearch}
              visibleMainList={visibleMainList}
              getMenuProps={getMenuProps}
              commonItemList={commonItemList}
              idKey={idKey}
              contentForEmptySearch={contentForEmptySearch}
              highlightedIndex={highlightedIndex}
              getItemProps={getItemProps}
              isCheckedItem={isCheckedItem}
              isDisabledItem={isDisabledItem}
              customStyleClassName={customStyleClassName}
              compactItems={compactItems}
            />
          </div>
        )}
      </Downshift>
    </div>
  );
};
