import React, { ReactNode } from 'react';
import { StyledGroup } from './RadioGroup.style';

interface RadioGroupProps<T> {
  isHorizontal?: boolean;
  itemRenderer?: (props: T & { name: string }) => ReactNode;
  itemsData?: T[];
  name: string;
  children?: ReactNode[];
}

const RadioGroup = <T extends object>({
  isHorizontal,
  itemRenderer,
  itemsData,
  children,
  name,
}: RadioGroupProps<T>) => {
  return (
    <StyledGroup isHorizontal={isHorizontal}>
      {itemRenderer
        ? itemsData?.map(item => itemRenderer({ ...item, name }))
        : children}
    </StyledGroup>
  );
};

export default RadioGroup;
