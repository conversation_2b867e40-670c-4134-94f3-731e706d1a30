import React, { forwardRef } from 'react';
import type { Control } from 'react-hook-form';
import type { FieldErrors } from 'react-hook-form/dist/types/errors';

import { ClientField } from 'features/multiaccount/dealer/modals/ClientForm/components/ClientField';
import {
  ClientForm,
  ClientFormType,
} from 'features/multiaccount/dealer/modals/ClientForm/types';

import { ErrorData } from 'modules/entities/types';

type ClientCompanyNameFieldProps = {
  control: Control<ClientForm>;
  formErrors: FieldErrors<ClientForm>;
  isPending: boolean;
  requestErrors: Nullable<ErrorData>;
  type: ClientFormType;
  name?: string;
};

const ClientCompanyNameField = forwardRef<
  HTMLInputElement,
  ClientCompanyNameFieldProps
>((props, ref) => {
  return (
    <ClientField
      {...props}
      ref={ref}
      name={props.name || 'company_name'}
      columnTranslationKey={`multiaccount.${props.type}-modal.column.company-name`}
      placeholderTranslationKey={`multiaccount.${props.type}-modal.placeholder.company-name`}
    />
  );
});

export default ClientCompanyNameField;
