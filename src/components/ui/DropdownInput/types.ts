import { ReactNode, RefObject } from 'react';
import { GetItemPropsOptions, ControllerStateAndHelpers } from 'downshift';
import { PopoverDeprecatedProps } from 'components/ui/Popover';
import { ButtonProps } from '../Button/Button';

type DropdownInputOnChange<T> = (
  selectedItem: T,
  stateAndHelpers?: ControllerStateAndHelpers<T>,
) => void;

export type GetToggleButtonPropsType = (options?: {
  disabled?: boolean;
}) => ButtonProps;

export interface DropdownInputProps<T, V> {
  /* className in root props gives the opportunity to pass it
    in renderTrigger without passing it in props */
  className?: string;
  value?: V;
  disabled?: boolean;
  idKey?: string;
  itemToString?: (item: TEMPORARY_ANY) => string;
  renderValue?: (value: V) => ReactNode;
  renderTrigger?: (props: {
    getToggleButtonProps: GetToggleButtonPropsType;
    isOpen: boolean;
    ref?: RefObject<HTMLButtonElement>;
    className?: string;
    disabled?: boolean;
    onChange?: DropdownInputOnChange<T>;
    placeholder?: string;
    renderValue?: (value: V) => TEMPORARY_ANY;
    value?: V;
  }) => JSX.Element;
  renderDropdown: (props: {
    getItemProps: (options: GetItemPropsOptions<{}>) => TEMPORARY_ANY;
    getMenuProps: () => TEMPORARY_ANY;
    getInputProps: (props?: TEMPORARY_ANY) => TEMPORARY_ANY;
    style: React.CSSProperties;
    highlightedIndex: number | null;
    onChange?: DropdownInputOnChange<V>;
    value?: V;
    ref?: React.LegacyRef<TEMPORARY_ANY>;
  }) => JSX.Element;
  placeholder?: string;
  popoverProps?: PartialKeys<
    PopoverDeprecatedProps,
    'renderTrigger' | 'renderPopover' | 'active'
  >;
  onChange?: DropdownInputOnChange<V>;
  align?: PopoverDeprecatedProps['align'];
}
