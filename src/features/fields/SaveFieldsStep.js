import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { FormProvider, useForm } from 'react-hook-form';

import { withLeaflet } from 'react-leaflet';
import VectorGridDefault from 'react-leaflet-vectorgrid';

import Saga from 'components/saga/Saga';

import Checkbox from 'components/ui/Checkbox/Checkbox';
import Button from 'components/ui/Button/Button';
import Icon from 'components/Icon';
import ModernSpinner from 'components/ui/ModernSpinner/ModernSpinner';
import UploadFieldsSideBar from './UploadFieldsSideBar';
import ControlledMap from 'components/maps/ControlledMap';
import ZoomControls, {
  MinZoom,
} from 'features/platform/ZoomControls/ZoomControls';
import DirectGoogleLayer from 'components/maps/DirectGoogleLayer';
import { FloatingActionsStyle } from 'components/ui/FloatingActions/styles';
import RulerLayer from 'features/platform/RulerLayer/RulerEditor/RulerEditor';

import { editOptions } from 'constants/map';

import config from 'config';
import auth from 'modules/auth';

import fields from 'modules/fields';

import saveFieldsSaga from 'sagas/local/save-fields';
import getRootScoutingUrl from 'utils/get-root-scouting-url';
import { useTranslate } from 'utils/use-translate';
import logEvent from 'sagas/global/logEvent';

const VectorGrid = withLeaflet(VectorGridDefault);

const SaveFieldsStep = ({ history, match }) => {
  const { t } = useTranslate();
  const importedFields = useSelector(state =>
    fields.selectors.getImportedFields(state),
  );

  const token = useSelector(auth.selectors.getToken);

  const [checkedIDs, setCheckedIDs] = useState([]);
  const [highlighted, setHighlighted] = useState(null);
  const [isPending, setIsPending] = useState(false);

  const dispatch = useDispatch();
  const formMethods = useForm({
    defaultValues: importedFields,
    shouldUnregister: false,
  });

  const map = useRef(null);
  const formFieldsReady = useRef(false);

  const { fileId } = match.params;
  const baseUrl = getRootScoutingUrl(history.location.pathname);

  const options = {
    url: `${config.apiHost}/en/v2/fields-users-files-features/mbtiles?&tile={x},{y},{z}&field_user_file_id=${fileId}`,
    subdomains: 'abc',
    type: 'protobuf',
    vectorTileLayerStyles: {
      default: properties => {
        return {
          color: '#FFE767',
          weight: 2,
          fill: true,
          fillOpacity: 0,
        };
      },
    },
    fetchOptions: {
      headers: {
        Authorization: `Token ${token}`,
      },
    },
    getFeatureId: feature => {
      return feature.properties.id;
    },
  };

  useEffect(() => {
    setCheckedIDs([...importedFields.map(fields => fields.id)]);
  }, [importedFields]);

  useEffect(() => {
    if (
      !formFieldsReady.current &&
      importedFields &&
      importedFields.length > 0
    ) {
      formMethods.reset(importedFields);
      formFieldsReady.current = true;
    }
  }, [importedFields, formMethods]);

  const handleCheck = useCallback(
    (event, id) => {
      if (checkedIDs.includes(id)) {
        setCheckedIDs(checkedIDs.filter(item => item !== id));
        if (map.current) {
          map.current.setFeatureStyle(id, {
            weight: 0,
          });
        }
      } else {
        if (map.current) {
          map.current.resetFeatureStyle(id);
        }
        setCheckedIDs([...checkedIDs, id]);
      }
    },
    [checkedIDs],
  );

  const handleCheckAll = checked => {
    const checkedIDs = importedFields.map(fields => fields.id);

    if (checked) {
      if (map.current) {
        checkedIDs.forEach(i => map.current.resetFeatureStyle(i));
      }
      setCheckedIDs([...checkedIDs]);
    } else {
      if (map.current) {
        checkedIDs.forEach(i =>
          map.current.setFeatureStyle(i, {
            weight: 0,
          }),
        );
      }
      setCheckedIDs([]);
    }
  };

  const handleHoverOn = id => {
    if (map.current) {
      map.current.setFeatureStyle(id, {
        color: 'white',
        fill: true,
        fillOpacity: 0,
      });
    }
    setHighlighted(id);
  };

  const handleHoverOff = id => {
    if (map.current) {
      map.current.setFeatureStyle(id, {
        color: '#FFE767',
      });
    }
    setHighlighted(null);
  };

  const onSubmit = allFields => {
    logEvent('fields_import_save_fields', { fieldsCount: checkedIDs.length });
    dispatch(fields.actions.createFields(checkedIDs, allFields));
  };

  return (
    <>
      <Saga
        saga={saveFieldsSaga}
        fileId={fileId}
        history={history}
        onSetPending={setIsPending}
      />
      <div className='main-section__body' data-layout='horizontal'>
        <div className='soil-sidebar-create'>
          <div className='soil-sidebar-stickycontrols'>
            <div className='main-uploader-header'>
              <h2 className='main-uploader-header__title'>
                {t('fields.upload.save_fields.title')}
              </h2>
              <p className={'main-uploader-header__description'}>
                {t('fields.upload.save_fields.sub_title')}
              </p>
            </div>
            <div
              className='soil-sidebar-stickycontrols__group'
              data-extraspace='true'
            >
              <Checkbox
                checked={checkedIDs.length === importedFields.length}
                onChange={event => {
                  handleCheckAll(event.target.checked);
                }}
              >
                {t('fields.list.edit.select_all')}
              </Checkbox>
            </div>
          </div>
          {!importedFields.length && (
            <div className='soil-sidebar-empty'>
              <div className='soil-sidebar-empty__ico with_loader'>
                <ModernSpinner large />
              </div>
              <h2 className='soil-sidebar-empty__title'>
                {t('fields.loading.title')}
              </h2>
            </div>
          )}
          {!!importedFields.length && (
            <FormProvider {...formMethods}>
              <UploadFieldsSideBar
                importedFields={importedFields}
                checkedIDs={checkedIDs}
                highlighted={highlighted}
                handleCheck={handleCheck}
              />
            </FormProvider>
          )}
        </div>
        <div className='map-container'>
          <ControlledMap
            id='imported-fields'
            viewportHash='default'
            onViewportHashChange={() => {}}
            mapProps={{
              className: 'map-viewer',
              boxZoom: false,
              zoomControl: false,
              minZoom: MinZoom,
              editable: true,
              editOptions,
            }}
          >
            <DirectGoogleLayer />
            <RulerLayer />
            <VectorGrid
              ref={map}
              {...options}
              onMouseOver={event => {
                handleHoverOn(event.layer.properties.id);
              }}
              onMouseOut={event => {
                handleHoverOff(event.layer.properties.id);
              }}
            />
          </ControlledMap>
          <ZoomControls id='imported-fields' showLocation showRuler />
        </div>
      </div>
      <FloatingActionsStyle>
        <Button
          className='btn btn-dark btn-lg'
          onClick={() => {
            const {
              location: { state },
            } = history;
            dispatch(fields.actions.resetImportedFields());
            history.replace({ pathname: `${baseUrl}/add/upload`, state });
          }}
        >
          {t('forms.cancel')}
        </Button>
        <Button
          className='btn btn-success btn-lg'
          type='submit'
          disabled={!checkedIDs.length || !formFieldsReady.current}
          onClick={formMethods.handleSubmit(onSubmit)}
          pending={isPending}
        >
          <span className='btn__ico'>
            <Icon className='ico-check-os' name='check' />
          </span>
          {t('forms.save')}
        </Button>
      </FloatingActionsStyle>
    </>
  );
};

export default SaveFieldsStep;
