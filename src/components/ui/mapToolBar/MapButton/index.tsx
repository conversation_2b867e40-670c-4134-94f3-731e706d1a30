import React from 'react';
import noop from 'lodash/noop';
import { cx } from 'linaria';

import Button, { ButtonProps } from 'components/ui/Button/Button';
import Icon, { IconProps } from 'components/Icon';
import { mapButtonStyle, ItemValueStyle, mapButtonIcon } from './styles';

type MapButtonProps = {
  label: string;
  iconName?: IconProps['name'];

  disabled?: boolean;
  prefix?: string;
} & ButtonProps &
  React.RefAttributes<TEMPORARY_ANY>;

const MapButton = React.forwardRef<TEMPORARY_ANY, MapButtonProps>(
  (props, ref) => {
    const {
      iconName,
      className,
      disabled,
      label,
      onClick,
      prefix,
      ...rest
    } = props;

    return (
      <Button
        className={cx(mapButtonStyle, className)}
        ref={ref}
        onClick={disabled ? noop : onClick}
        {...rest}
      >
        {iconName && <Icon name={iconName} className={mapButtonIcon} />}
        {!!prefix && <ItemValueStyle>{prefix}:</ItemValueStyle>}
        <ItemValueStyle className={prefix ? 'prefixed' : ''}>
          {label}
        </ItemValueStyle>
      </Button>
    );
  },
);

export default MapButton;
