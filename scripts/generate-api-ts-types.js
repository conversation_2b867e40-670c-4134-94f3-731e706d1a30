//Script generates TypeScript files with API types

// After running script check result index.tsx for odd imports from deleted folders
// (check ODD_FOLDERS_TO_REMOVE in this file)
/* eslint-disable */
const https = require('https');
const inquirer = require('inquirer');
const chalk = require('chalk');
const OpenAPI = require('openapi-typescript-codegen');
var fs = require('fs');

const SUCCESSFUL_STATUS_CODE_PREFIX = '2';
const CLIENT_ERROR_STATUS_CODE_PREFIX = '4';
const SERVER_ERROR_STATUS_CODE_PREFIX = '5';
const DESTINATION_FOLDER_PATH = './src/types/api/';

async function generateAPItypes() {
  const answer = await inquirer.prompt([
    {
      name: 'username',
      type: 'input',
      message: 'Enter username:',
    },
    {
      name: 'password',
      type: 'password',
      message: 'Enter password:',
    },
  ]);

  downloadApiJSONSpec({ username: answer.username, password: answer.password });
}

const downloadApiJSONSpec = ({ username, password }) => {
  const REQUEST_OPTIONS = {
    host: 'platform-api.dev.onesoil.ai',
    path: '/docs/v2?format=openapi',
    headers: {
      Authorization:
        'Basic ' + Buffer.from(`${username}:${password}`).toString('base64'),
    },
  };

  const request = https.get(REQUEST_OPTIONS, response => {
    let { statusCode, statusMessage } = response;
    let statusInfoMessage = null;
    switch (statusCode) {
      case 200:
        statusInfoMessage = `${chalk.green(
          'Authentication succeeded',
        )} ${chalk.gray(`(statusCode: ${statusCode})`)}`;
        break;
      case 401:
        statusInfoMessage = `${chalk.red('Authentication failed')} ${chalk.gray(
          `(statusCode: ${statusCode})`,
        )}`;
        break;
      case 404:
        statusInfoMessage = `${chalk.red('File not found')} ${chalk.gray(
          `(statusCode: ${statusCode})`,
        )}`;
        break;
      default:
        let statusCodePrefix = statusCode.toString()[0];
        if (statusCodePrefix === CLIENT_ERROR_STATUS_CODE_PREFIX) {
          statusInfoMessage = chalk.red(
            `Client Error: statusCode: ${statusCode}, statusMessage: ${statusMessage}`,
          );
        } else if (statusCodePrefix === SERVER_ERROR_STATUS_CODE_PREFIX) {
          statusInfoMessage = chalk.red(
            `Server Error: statusCode: ${statusCode}, statusMessage: ${statusMessage}`,
          );
        } else {
          statusInfoMessage = chalk.yellow(
            `statusCode: ${statusCode}, statusMessage: ${statusMessage}`,
          );
        }
        break;
    }
    console.log(statusInfoMessage);

    let body = '';
    response.on('data', chunk => {
      body += chunk;
    });

    response.on('end', () => {
      //Get full JSON spec file to parse
      let JsonApiResponse = null;
      try {
        if (statusCode.toString()[0] === SUCCESSFUL_STATUS_CODE_PREFIX) {
          JsonApiResponse = JSON.parse(body);
        }
      } catch (error) {
        console.error(`${chalk.red('File parsing error')}\n${error}`);
        console.log(`Received file data:\n${body}`);
      }

      if (JsonApiResponse) {
        generateAPItypesFromJSON(JsonApiResponse);
      }
    });
  });

  request.on('error', error => {
    console.error(`${chalk.red('Request error:')}\n${error}`);
  });

  request.end();
};

const generateAPItypesFromJSON = async ApiSpecJson => {
  const REQUIRED_OPEN_API_FIELDS = ['info', 'paths'];

  const absentRequiredFields = REQUIRED_OPEN_API_FIELDS.filter(
    requiredOpenAPIField => !ApiSpecJson[requiredOpenAPIField],
  );

  let isFileAppliedByUser = false;

  if (absentRequiredFields.length) {
    const answer = await inquirer.prompt([
      {
        name: 'applyIncompleteFile',
        type: 'confirm',
        message: `The JSON is missing required fields [${absentRequiredFields.join(
          ', ',
        )}], do you want to apply this JSON?`,
      },
    ]);

    isFileAppliedByUser = answer.applyIncompleteFile;
  } else {
    isFileAppliedByUser = true;
  }

  if (isFileAppliedByUser) {
    try {
      await OpenAPI.generate({
        input: ApiSpecJson,
        output: DESTINATION_FOLDER_PATH,
      });

      const ODD_FOLDERS_TO_REMOVE = ['core', 'services'].forEach(oddFolder => {
        fs.rmSync(`${DESTINATION_FOLDER_PATH}${oddFolder}`, {
          recursive: true,
          force: true,
        });
      });

      console.log(
        chalk.green(`Types was created in ${DESTINATION_FOLDER_PATH}`),
      );
    } catch (error) {
      console.error(
        `${chalk.red('Types generating OpenAPI lib error')}\n${error}`,
      );
    }
  } else {
    console.log(chalk.red('File parsing was cancelled by user'));
  }
};

generateAPItypes();
