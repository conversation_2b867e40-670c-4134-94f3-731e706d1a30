import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import { useSelector } from 'react-redux';

import { withLeaflet } from 'react-leaflet';
// @ts-ignore
import VectorGridDefault from 'react-leaflet-vectorgrid';

import auth from 'modules/auth';

import config from 'config';

type VectorGridFeatureStyle = Record<string, string | number | boolean>;
interface VectorGridInstance {
  setFeatureStyle: (id: number, style: VectorGridFeatureStyle) => void;
  resetFeatureStyle: (id: number) => void;
}

interface FileFieldVectorGridProps {
  fileId: number;
  checkedIds: number[];
  setHighlighted: (id: number | null) => void;
}

// Because the map itself has 1 and the controls have 3
const DEFAULT_Z_INDEX = 2;

const VectorGrid = withLeaflet(VectorGridDefault);

const basicStyles = {
  color: '#FFE767',
  fill: true,
  fillOpacity: 0,
};

const checkedStyles = {
  ...basicStyles,
  weight: 2,
};

const hiddenStyles = {
  ...basicStyles,
  weight: 0,
};

/**
 * I had to use a dirty hack to ensure that VectorGrid sets ref value
 * and wires up its onMouseOver and onMouseOut callbacks
 * VectorGrid does it only after a couple of re-renders
 * caused by changing specific props: zIndex and something inside gridOptions
 * That's why I had to set zIndex with useEffect and keep gridOptions as memo
 * react-leaflet-vectorgrid has been abandoned for 4 years and it's
 * really hard to make it work properly
 */
export const FileFieldVectorGrid = ({
  fileId,
  checkedIds,
  setHighlighted,
}: FileFieldVectorGridProps) => {
  const token = useSelector(auth.selectors.getToken);

  const vectorGridInstance = useRef<VectorGridInstance>(null);

  const [zIndex, setZIndex] = useState(0);

  const gridOptions = useMemo(() => {
    return {
      url: `${config.apiHost}/en/v2/fields-users-files-features/mbtiles?&tile={x},{y},{z}&field_user_file_id=${fileId}`,
      subdomains: 'abc',
      type: 'protobuf',
      vectorTileLayerStyles: {
        default: (properties: { id: number }) => {
          return hiddenStyles;
        },
      },
      fetchOptions: {
        headers: {
          Authorization: `Token ${token}`,
        },
      },
      // @ts-ignore
      getFeatureId: feature => {
        return feature.properties.id;
      },
    };
  }, [fileId, token]);

  useEffect(() => {
    setZIndex(DEFAULT_Z_INDEX);
  }, []);

  useEffect(() => {
    const vgInstance = vectorGridInstance.current;
    const savedIds = [...checkedIds];

    if (!vgInstance) {
      return;
    }

    for (const id of checkedIds) {
      // resetFeatureStyle may fail if id is not found
      try {
        vgInstance?.setFeatureStyle(id, checkedStyles);
      } catch (error) {
        // just ignore it, it's normal.
        // not all ids are in the current VectorGrid instance
      }
    }

    return () => {
      if (!vgInstance) {
        return;
      }

      for (const id of savedIds) {
        try {
          vgInstance?.resetFeatureStyle(id);
        } catch (error) {
          // just ignore it, it's normal.
        }
      }
    };
  }, [checkedIds]);

  const handleHoverOn = useCallback(
    (id: number) => {
      if (!checkedIds.includes(id)) {
        return;
      }

      if (vectorGridInstance.current) {
        vectorGridInstance.current.setFeatureStyle(id, {
          ...checkedStyles,
          color: 'white',
        });
      }

      setHighlighted(id);
    },
    [checkedIds, setHighlighted],
  );

  const handleHoverOff = useCallback(
    (id: number) => {
      if (!checkedIds.includes(id)) {
        return;
      }

      if (vectorGridInstance.current) {
        vectorGridInstance.current.setFeatureStyle(id, checkedStyles);
      }
      setHighlighted(null);
    },
    [checkedIds, setHighlighted],
  );

  return (
    <VectorGrid
      key={fileId}
      interactive={true}
      zIndex={zIndex}
      ref={vectorGridInstance}
      {...gridOptions}
      // @ts-ignore
      onMouseOver={event => {
        handleHoverOn(event.layer.properties.id);
      }}
      // @ts-ignore
      onMouseOut={event => {
        handleHoverOff(event.layer.properties.id);
      }}
    />
  );
};
