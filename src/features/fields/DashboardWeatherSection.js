import React from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import Saga from 'components/saga/Saga';
import WeatherMetrics from 'features/weather/WeatherMetrics';
import ModernSpinner from 'components/ui/ModernSpinner/ModernSpinner';

import { fetchWeather } from 'sagas/local/weather';
import { getViewportHash } from 'utils/get-root-scouting-url';
import { formatDate } from 'utils/format-date';
import { useTranslate } from 'utils/use-translate';
import { Icons } from 'features/weather/weatherIcons/WeatherIconsLarge.ts';

import fields from 'modules/fields';
import seasons from 'modules/seasons';
import weather from 'modules/weather';
import settings from 'modules/settings';

const DashboardWeatherSection = ({
  fieldId,
  field,
  currentSeason,
  weather,
  location,
  formatUnit,
}) => {
  const { t } = useTranslate();
  return (
    <div className='map-dashboard__section'>
      <div className='map-dashboard-header'>
        <h2 className='map-dashboard-header__title'>
          {t('fields.dashboard.weather')}{' '}
          {weather.status === 'resolved' && (
            <small className='c-neutral'>
              {formatDate(weather.current.time, t('weather.time_format'))}
            </small>
          )}
        </h2>
        <Link
          className='map-dashboard-header__goto'
          to={`/${getViewportHash(location.pathname)}/weather/${fieldId}`}
        >
          {t('fields.dashboard.weather.full')}
        </Link>
      </div>
      {field && weather.status === 'none' && (
        <Saga
          saga={fetchWeather}
          fieldId={fieldId}
          seasonId={currentSeason.id}
        />
      )}
      {weather.status === 'pending' && (
        <div className='map-dashboard-weather __loader'>
          <ModernSpinner large />
          <div className='map-dashboard-weather__inner'>
            {t('fields.dashboard.weather.loading')}
          </div>
        </div>
      )}
      {weather.status === 'resolved' && (
        <div className='map-dashboard-weather'>
          <div className='weather-info-current'>
            <div className='weather-info-current-metrics'>
              <div className='weather-info-current-metrics__ico'>
                <img
                  src={Icons[weather.current.icon]}
                  alt={weather.current.icon}
                />
              </div>
              <div className='weather-info-current-metrics__temp'>
                {formatUnit(weather.current.temp, 'C', 'temp')}
              </div>
            </div>
            <p className='weather-info-current-predict'>
              {t('fields.dashboard.weather.tomorrow', {
                temp: formatUnit(weather.daily[1].temp_max, 'C', 'temp'),
              })}
              ,<br />
              {t(
                weather.daily[1].precips_amount > 0
                  ? 'fields.dashboard.weather.wet'
                  : 'fields.dashboard.weather.dry',
              )}
            </p>
          </div>
          <WeatherMetrics current={weather.current} />
        </div>
      )}
    </div>
  );
};

const mapStateToProps = (state, ownProps) => ({
  field: fields.selectors.getByID(state, ownProps.fieldId),
  currentSeason: seasons.selectors.getCurrent(state),
  weather: weather.selectors.getWeatherByFieldID(state, ownProps.fieldId),
  formatUnit: settings.selectors.getUnitFormatter(state),
});

export default connect(mapStateToProps)(DashboardWeatherSection);
