import React from 'react';
import { cx } from 'linaria';
import {
  StyledSwitchRoot,
  StyledSwitchTrack,
  StyledSwitchThumb,
  styles,
} from './Switch.style';

export type SwitchSize = 'small' | 'medium' | 'large';

export type Props = {
  id: string;
  leftLabel?: string;
  labelStyle?: string;
  value: boolean;
  disabled?: boolean;
  size?: SwitchSize;
  checkedColor?: 'black' | 'green';
  onChange: Function;
};

const Switch = ({
  id,
  value,
  disabled,
  leftLabel,
  labelStyle,
  size = 'medium',
  checkedColor,
  onChange,
}: Props) => {
  const trackStyle =
    value && checkedColor === 'black'
      ? {
          backgroundColor: '#3A3A3C',
        }
      : {};

  return (
    <label htmlFor={id} className={styles.label}>
      {leftLabel && <span className={labelStyle}>{leftLabel}</span>}
      <div className={cx(styles.wrapper, styles[size])}>
        <StyledSwitchRoot className={cx(value && 'checked')} size={size}>
          <input
            id={id}
            disabled={disabled}
            className={styles.checkbox}
            type='checkbox'
            checked={value}
            onChange={e => onChange(e.target.checked)}
          />
          <StyledSwitchThumb size={size} />
        </StyledSwitchRoot>
        <StyledSwitchTrack size={size} style={trackStyle} />
      </div>
    </label>
  );
};

export default Switch;
