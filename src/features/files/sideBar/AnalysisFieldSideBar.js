import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Form } from 'react-redux-form';
import { withTranslation } from 'react-i18next';

import Icon from 'components/Icon';
import Button from 'components/ui/Button/Button';
import Saga from 'components/saga/Saga';
import AnalysisFieldItem from 'features/files/AnalysisFieldItem';
import MissingFieldRedirector from 'features/fields/MissingFieldRedirector';

import forms from 'constants/forms';
import { formatTitle, formatCropList } from 'utils/fields-formatters';
import { fetchNDVI } from 'sagas/local/field-info';
import analysisFieldSaga from 'sagas/local/analysis-field';

import fields from 'modules/fields';
import seasons from 'modules/seasons';
import settings from 'modules/settings';
import getFieldArea from 'utils/get-field-area';

class AnalysisFieldSideBar extends Component {
  render() {
    const {
      location,
      history,
      field,
      currentSeason,
      t,
      formatUnit,
      fieldFillingType,
    } = this.props;
    if (!field) {
      return null;
    }
    return (
      <Form
        className='soil-sidebar-create'
        model={fieldFillingType === 'rgb' ? forms.analysisRGB : forms.analysis} // TODO найти отображения и понять его
      >
        <Saga key={currentSeason.id} saga={fetchNDVI} fieldId={field.id} />
        <Saga saga={analysisFieldSaga} />
        <MissingFieldRedirector fieldId={field.id} />
        <div className='soil-sidebar-header'>
          <Button
            className='app-nav-back'
            onClick={() => {
              history.push({
                pathname: location.pathname
                  .replace('/analysis/fields', '/fields')
                  .replace(/\/(side-by-side|grid)/, ''),
              });
            }}
          >
            <Icon className='ico-arrow-long-left-os' name='arrow-left' />
          </Button>
          <div className='soil-sidebar-header__inner'>
            <h1 className='soil-sidebar-header__main'>
              {formatTitle(t, field)}, {getFieldArea({ field, formatUnit })}
              <br />
              <small>{formatCropList(t, field)}</small>
            </h1>
          </div>
        </div>
        <div className='analyses-sidebar'>
          <ul className='analyses-sidebar__list'>
            <AnalysisFieldItem
              fieldId={field.id}
              type={fieldFillingType === 'rgb' ? 'rgb' : 'ndvi'}
              index={1}
            />
            <AnalysisFieldItem
              fieldId={field.id}
              type={fieldFillingType === 'rgb' ? 'rgb' : 'ndvi'}
              index={2}
            />
          </ul>
        </div>
      </Form>
    );
  }
}

const mapStateToProps = (state, ownProps) => ({
  fieldFillingType: fields.selectors.getFieldFillingType(state),
  field: fields.selectors.getByID(state, ownProps.match.params.fieldId),
  currentSeason: seasons.selectors.getCurrent(state),
  formatUnit: settings.selectors.getUnitFormatter(state),
});

export default connect(mapStateToProps)(
  withTranslation()(AnalysisFieldSideBar),
);
