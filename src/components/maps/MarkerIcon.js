import React, { Fragment } from 'react';
import { divIcon, marker } from 'leaflet';
import { MapLayer, with<PERSON><PERSON><PERSON>t } from 'react-leaflet';
import { render } from 'react-dom';

class MarkerIcon extends MapLayer {
  createLeafletElement(newProps) {
    const { map, layerContainer, position, ...props } = newProps;
    this.icon = divIcon(props);
    const el = marker(position, { ...props, icon: this.icon });
    this.contextValue = { ...props.leaflet, popupContainer: el };
    return el;
  }

  updateLeafletElement(fromProps, toProps) {
    if (toProps.position !== fromProps.position) {
      this.leafletElement.setLatLng(toProps.position);
    }
    if (toProps.zIndexOffset !== fromProps.zIndexOffset) {
      this.leafletElement.setZIndexOffset(toProps.zIndexOffset);
    }
    if (toProps.opacity !== fromProps.opacity) {
      this.leafletElement.setOpacity(toProps.opacity);
    }
    if (toProps.draggable !== fromProps.draggable) {
      if (toProps.draggable) {
        this.leafletElement.dragging.enable();
      } else {
        this.leafletElement.dragging.disable();
      }
    }
    const { _icon } = this.leafletElement;
    if (_icon && toProps.className !== fromProps.className) {
      _icon.classList.remove(...fromProps.className.split(' '));
      _icon.classList.add(...toProps.className.split(' '));
    }
  }

  renderComponent = () => {
    const container = this.leafletElement._icon;
    if (!container) {
      return;
    }
    const { marker } = this.props;
    render(<Fragment>{marker}</Fragment>, container);
  };

  componentDidMount(...args) {
    super.componentDidMount(...args);
    this.renderComponent();
  }

  componentDidUpdate(...args) {
    super.componentDidUpdate(...args);
    this.renderComponent();
  }
}

export default withLeaflet(MarkerIcon);
