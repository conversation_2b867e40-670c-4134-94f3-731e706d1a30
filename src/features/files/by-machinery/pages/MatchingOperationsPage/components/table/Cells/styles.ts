import { styled } from 'linaria/react';

type CellStyleProps = {
  hovered?: boolean;
};

export const CellStyle = styled.div<CellStyleProps>`
  display: flex;
  justify-content: start;
  align-items: center;
  min-height: 42px;
  padding: ${({ hovered }) => (hovered ? '0' : '0 8px')};

  .form-select__value {
    font-size: 14px;
  }
`;

export const AreaStyle = styled.span<CellStyleProps>`
  text-decoration: underline;
  text-decoration-style: dotted;
  cursor: pointer;
`;

export const AreaPercentageStyle = styled.span<CellStyleProps>`
  color: #a5b2bc;
`;

export const NotValueStyle = styled.span<CellStyleProps>`
  color: #a5b2bc;
`;
