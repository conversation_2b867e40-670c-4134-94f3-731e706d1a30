import React, { type FC, useState, useMemo } from 'react';
import moment from 'moment';

import Icon from 'components/Icon';

import { useTranslate } from 'utils/use-translate';

import { styles } from './MonthPicker.styles';

type MonthPickerProps = {
  maxDate?: string; // YYYY-MM format
  minDate?: string; // YYYY-MM format
  onChange: (monthYear: string) => void;
  onClose: () => void;
  value: string; // YYYY-MM format
};

type MonthOptionType = {
  disabled: boolean;
  label: string; // Jan, Feb, etc.
  value: number; // 0-11 (moment month index)
};

const MONTHS: Omit<MonthOptionType, 'disabled'>[] = [
  { value: 0, label: 'months.short.january' },
  { value: 1, label: 'months.short.february' },
  { value: 2, label: 'months.short.march' },
  { value: 3, label: 'months.short.april' },
  { value: 4, label: 'months.short.may' },
  { value: 5, label: 'months.short.june' },
  { value: 6, label: 'months.short.july' },
  { value: 7, label: 'months.short.august' },
  { value: 8, label: 'months.short.september' },
  { value: 9, label: 'months.short.october' },
  { value: 10, label: 'months.short.november' },
  { value: 11, label: 'months.short.december' },
];

const MonthPicker: FC<MonthPickerProps> = ({
  maxDate,
  minDate,
  onChange,
  onClose,
  value,
}) => {
  const { t } = useTranslate();

  // NOTE: Parse current value to get selected month and year
  const selectedMoment = moment(value, 'YYYY-MM');
  const selectedYear = selectedMoment.year();
  const selectedMonth = selectedMoment.month();

  // NOTE: State for the year being viewed (can be different from selected year)
  const [viewingYear, setViewingYear] = useState(selectedYear);

  // NOTE: Calculate min/max years from date constraints
  const minYear = minDate ? moment(minDate, 'YYYY-MM').year() : 1900;
  const maxYear = maxDate ? moment(maxDate, 'YYYY-MM').year() : 2100;

  // NOTE: Generate month options with disabled state
  const monthOptions = useMemo((): MonthOptionType[] => {
    return MONTHS.map(month => {
      const monthMoment = moment().year(viewingYear).month(month.value);
      const monthString = monthMoment.format('YYYY-MM');

      let disabled = false;

      if (minDate && monthString < minDate) {
        disabled = true;
      }

      if (maxDate && monthString > maxDate) {
        disabled = true;
      }

      return {
        ...month,
        disabled,
      };
    });
  }, [viewingYear, minDate, maxDate]);

  const handlePreviousYear = (): void => {
    if (viewingYear > minYear) {
      setViewingYear(prev => prev - 1);
    }
  };

  const handleNextYear = (): void => {
    if (viewingYear < maxYear) {
      setViewingYear(prev => prev + 1);
    }
  };

  const handleMonthSelect = (monthValue: number): void => {
    const monthMoment = moment().year(viewingYear).month(monthValue);
    const monthString = monthMoment.format('YYYY-MM');
    onChange(monthString);
    onClose();
  };

  const isCurrentMonth = (monthValue: number): boolean => {
    return viewingYear === selectedYear && monthValue === selectedMonth;
  };

  return (
    <div className={styles.monthPickerContainer}>
      {/* NOTE: Year Navigation Header */}
      <div className={styles.yearHeader}>
        <button
          type='button'
          className={styles.yearNavButton}
          onClick={handlePreviousYear}
          disabled={viewingYear <= minYear}
        >
          <Icon className='ico-chevron-right-os' name='chevron-left' />
        </button>
        <div className={styles.yearLabelWrap}>
          <span className={styles.yearLabel}>{viewingYear}</span>
        </div>
        <button
          type='button'
          className={styles.yearNavButton}
          onClick={handleNextYear}
          disabled={viewingYear >= maxYear}
        >
          <Icon className='ico-chevron-right-os' name='chevron-right' />
        </button>
      </div>

      {/* NOTE: Month Grid */}
      <div className={styles.monthGrid}>
        {monthOptions.map(month => (
          <button
            key={month.value}
            type='button'
            className={`
              ${styles.monthButton}
              ${isCurrentMonth(month.value) ? styles.monthButtonSelected : ''}
              ${month.disabled ? styles.monthButtonDisabled : ''}
            `}
            onClick={() => handleMonthSelect(month.value)}
            disabled={month.disabled}
          >
            {t(month.label)}
          </button>
        ))}
      </div>
    </div>
  );
};

export default MonthPicker;
