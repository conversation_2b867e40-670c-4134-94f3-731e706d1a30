import React from 'react';
import { connect, useDispatch } from 'react-redux';
import { Form, Control, getField, actions } from 'react-redux-form';
import cx from 'classnames';

import Button from 'components/ui/Button/Button';
import LinkButton from 'components/LinkButton';
import BodyClassName from 'components/BodyClassName';
import Saga from 'components/saga/Saga';
import SubmitButton from 'components/forms/SubmitButton';
import Logo from 'components/Logo';

import authLoginSaga from 'sagas/local/auth-login';
import { required } from 'utils/validators';
import { parseQuery } from 'utils/query';
import { useTranslate } from 'utils/use-translate';

import forms from 'constants/forms';
import auth from 'modules/auth';

const ResetPasswordPage = ({
  submitFailed,
  history,
  location,
  onAuthorize,
}) => {
  const { t } = useTranslate();
  const dispatch = useDispatch();

  dispatch(actions.change('forms.forgot.login', location.state.login));

  return (
    <section className={cx('onesoil-hello', { 'shake-error': submitFailed })}>
      <BodyClassName className='onesoil-hello-page' />
      <Saga
        saga={authLoginSaga}
        {...parseQuery(location)}
        history={history}
        noReset
      />

      <div className='onesoil-hello__header'>
        <Logo />
      </div>

      <p className='onesoil-hello__description'>{t('auth.reset.hint')}</p>

      <Form
        className='onesoil-hello-form'
        model={forms.forgot}
        onSubmit={form => onAuthorize('login', form, true)}
      >
        <div className='form-group'>
          <div className='form-label'>{t(`auth.password.label.reset`)}</div>
          <Control.input type='hidden' model='.login' name='login' />
          <Control.password
            tabIndex={1}
            model='.password'
            className='form-input size-full'
            validators={{ required }}
            placeholder={t('auth.password.placeholder')}
          />
        </div>

        <div className='form-group'>
          <SubmitButton
            native
            model={forms.forgot}
            render={({ ...otherProps }) => (
              <Button
                className='btn btn-success btn-lg size-full'
                type='submit'
                {...otherProps}
              >
                {t(`auth.confirm.login`)}
              </Button>
            )}
          />
        </div>

        <div className='onesoil-hello-backto'>
          <LinkButton
            onClick={() => {
              dispatch(actions.change('forms.forgot.password', ''));
              dispatch(actions.setInitial('forms.forgot'));
              history.goBack();
            }}
          >
            {t('auth.back')}
          </LinkButton>
        </div>
      </Form>
    </section>
  );
};

const mapStateToProps = state => ({
  submitFailed: getField(state, forms.forgot).submitFailed,
});

const mapDispatchToProps = {
  onAuthorize: auth.actions.authorize,
};

export default connect(mapStateToProps, mapDispatchToProps)(ResetPasswordPage);
