import RootState from 'types/rootState';

import { StepValidationStatus } from '../types';
import { getMatchingOperations } from '../../../redux/selectors';
import { ApiDataFieldOperations } from '../../../types';

export const matchOperationCheck = (state: RootState) => {
  const operations: ApiDataFieldOperations[] = getMatchingOperations(state);

  return operations.some(operation => operation.is_enabled)
    ? StepValidationStatus.valid
    : StepValidationStatus.needToMatch;
};
