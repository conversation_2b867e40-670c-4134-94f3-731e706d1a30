import React, { PropsWithChildren } from 'react';
import {
  Bread<PERSON><PERSON>bsItem,
  BreadCrumbsContainer,
  BreadCrumbsList,
} from './Breadcrumbs.style';

interface ItemProps extends PropsWithChildren<{}> {
  withoutLastItem?: boolean;
}

const Item = ({ children, withoutLastItem }: ItemProps) => (
  <BreadCrumbsItem withoutLastItem={withoutLastItem}>
    {children}
  </BreadCrumbsItem>
);

const Breadcrumbs = ({ children }: PropsWithChildren<{}>) => (
  <BreadCrumbsContainer>
    <BreadCrumbsList>{children}</BreadCrumbsList>
  </BreadCrumbsContainer>
);

Breadcrumbs.Item = Item;

export default Breadcrumbs;
