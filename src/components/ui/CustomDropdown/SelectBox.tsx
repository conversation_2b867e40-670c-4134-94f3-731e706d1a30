import React from 'react';
import { css } from 'linaria';

import { CustomDropdown } from './CustomDropdown';
import { CustomDropdownOption, CustomDropdownProps } from './types';
import Icon from '../../Icon';
import { useTranslate } from 'utils/use-translate';
import { Popover } from '../Popover';

export const clearIcon = css`
  width: 8px;
  margin-left: 8px;
`;

export const contentForEmptySearchStyle = css`
  padding: 8px 15px;
  color: #222;
  font-size: 15px;
  line-height: 1.25rem;
`;

export type SelectBoxProps = {
  options: CustomDropdownOption[];
  unClearable?: boolean;
  value: Nullable<string>;
  wrapperClassName?: string;
  placeholder?: string;
  searchPlaceholder?: string;
  onVisibilityToggle?: CustomDropdownProps<CustomDropdownOption>['onVisibilityToggle'];
  disabled?: CustomDropdownProps<CustomDropdownOption>['disabled'];
  noFoundedText?: string;
  onChange: (value: string | null) => void;
  withSearchBox?: boolean;
  sizeByParent?: boolean;
  rightText?: string | null;
};

export const SelectBox = ({
  options,
  value,
  unClearable = false,
  wrapperClassName,
  withSearchBox,
  placeholder,
  noFoundedText,
  onVisibilityToggle,
  searchPlaceholder,
  onChange,
  disabled,
  sizeByParent = true,
  rightText,
}: SelectBoxProps) => {
  const { t } = useTranslate();

  return (
    <CustomDropdown
      sizeByParent={sizeByParent}
      options={options}
      value={value}
      disabled={disabled}
      contentForEmptySearch={
        noFoundedText && (
          <div className={contentForEmptySearchStyle}>{t(noFoundedText)}</div>
        )
      }
      PopoverComponent={Popover}
      className='form-select'
      wrapperClassName={wrapperClassName}
      onChange={onChange}
      onVisibilityToggle={onVisibilityToggle}
      renderValue={props => {
        return (
          <>
            <span className='form-select__value'>
              {options.find(option => option.id === props?.value)?.label ||
                t(placeholder || '')}
            </span>
            {rightText && <span>{rightText}</span>}
            {props?.value && !unClearable && (
              <Icon
                name='close'
                className={clearIcon}
                onClick={() => onChange(null)}
              />
            )}
          </>
        );
      }}
      withSearchBox={withSearchBox}
      searchBoxProps={{ placeholder: t(searchPlaceholder || '') as string }}
    />
  );
};
