import React, { VFC } from 'react';

import { ApiDataFieldOperations } from 'features/files/by-machinery/types';
import { useTranslate } from 'utils/use-translate';

import { CellStyle } from './styles';

export type CellCropProps = {
  operations: ApiDataFieldOperations[];
  hovered: boolean;
};

export const CellCrop: VFC<CellCropProps> = ({ operations }) => {
  const { t } = useTranslate();
  const crops = operations.map(operation =>
    t(`import.matching_operations.crop.${operation.crop}`),
  );

  return <CellStyle hovered={false}>{crops.join(',')}</CellStyle>;
};
