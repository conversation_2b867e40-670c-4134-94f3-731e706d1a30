import { styled } from 'linaria/react';

export const ProgressBarStyles = styled.div`
  width: 35px;
  height: 2px;

  background: rgba(0, 0, 0, 0.15);
  border-radius: 4px;
`;

type ProgressBarFillStylesProps = {
  progress: number;
};
export const ProgressBarFillStyles = styled.div<ProgressBarFillStylesProps>`
  width: ${({ progress }) => progress * 100}%;
  height: 100%;
  background: #27ae60;
  border-radius: 4px;
`;
