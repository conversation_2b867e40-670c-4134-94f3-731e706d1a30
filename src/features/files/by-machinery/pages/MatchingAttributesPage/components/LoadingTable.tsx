import React from 'react';

import {
  ColumnHeadCellStyle,
  ColumnTitleStyle,
  Delimiter,
  MatchingTableStyle,
  Skeleton,
} from './table/styles';
import { TableCell } from './table/TableCell';

export const LoadingTable = () => {
  const columns = [1, 2];
  const rows = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];

  return (
    <MatchingTableStyle columns={columns.length - 1}>
      {columns.map(column => (
        <ColumnHeadCellStyle key={column}>
          <ColumnTitleStyle>
            <Skeleton />
          </ColumnTitleStyle>
        </ColumnHeadCellStyle>
      ))}
      <Delimiter top='8' columns={columns.length - 1} />
      {rows.map((row, index) => (
        <React.Fragment key={row}>
          {columns.map(column => (
            <TableCell key={column}>
              <Skeleton />
            </TableCell>
          ))}
          {index % 3 === 2 && index !== 11 && (
            <Delimiter columns={columns.length - 1} />
          )}
        </React.Fragment>
      ))}
    </MatchingTableStyle>
  );
};
