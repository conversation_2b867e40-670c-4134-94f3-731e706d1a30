import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { useSelector, useDispatch, useStore } from 'react-redux';
import {
  AutoSizer,
  Table,
  Column,
  defaultTableRowRenderer,
} from 'react-virtualized';
import cx from 'classnames';
import moment from 'moment';
import omit from 'lodash/omit';
import orderBy from 'lodash/orderBy';
import flatMap from 'lodash/flatMap';

import Icon from 'components/Icon';
import Button from 'components/ui/Button/Button';
import Saga from 'components/saga/Saga';
import Checkbox from 'components/ui/Checkbox/Checkbox';
import Breadcrumbs from 'components/ui/Breadcrumbs/Breadcrumbs';
import EnterExitTransition from 'components/EnterExitTransition';
import ToastContainer from 'features/platform/ToastContainer/ToastContainer';
import ImportFilesButton from './ImportFilesButton';
import CreateFolderPopup from './CreateFolderPopup';

import auth from 'modules/auth';
import modems from 'modules/modems';

import { statusLabels, statusIcons } from 'constants/modems';
import { formatTitle, getFilename } from 'utils/modems';
import { convertBytes } from 'utils/units';
import { downloadFromUrl } from 'utils/files';
import getRootScoutingUrl from 'utils/get-root-scouting-url';
import { formatDate } from 'utils/format-date';
import { useTranslate } from 'utils/use-translate';
import logEvent from 'sagas/global/logEvent';
import { modemsSaga } from 'sagas/local/modems';

const UploadsCount = () => {
  const uploads = useSelector(state => modems.selectors.getUploads(state));
  const uploadsDone = uploads.filter(u => u.progress === 1);
  if (uploads.length === 0) {
    return null;
  }
  return (
    <div className='modems-uploads-count'>{`${uploadsDone.length}/${uploads.length}`}</div>
  );
};

const ModemViewPane = ({ match, history }) => {
  const { modemId } = match.params;
  const directory = history.location.pathname.replace(match.url, '');
  const splitted = directory.split('/');

  const [isEntered, setIsEntered] = useState(false);
  const [showPopup, setShowPopup] = useState(false);
  const [checkedIds, setCheckedIds] = useState({});
  const [sortBy, setSortBy] = useState('filename');
  const [sortDirection, setSortDirection] = useState('ASC');
  const dispatch = useDispatch();
  const userID = useSelector(auth.selectors.getUserID);
  const { t } = useTranslate();
  const modem = useSelector(state => modems.selectors.getByID(state, modemId));
  const files = useSelector(state =>
    modems.selectors.getFilesInDirectory(state, modemId, directory),
  );
  const iteratee = file => {
    if (sortBy === 'filename') {
      return file.filename.toLowerCase();
    }
    return file[sortBy];
  };
  const sortedFiles = orderBy(files, [iteratee], [sortDirection.toLowerCase()]);
  const store = useStore();

  useEffect(() => {
    if (!modem) {
      return;
    }
    dispatch(modems.actions.fetchFiles(modemId));
  }, [dispatch, modem, modemId]);

  useEffect(() => {
    setCheckedIds({});
  }, [history.location]);

  if (!modem) {
    return null;
  }

  const modemUser = modem.modems_users.find(user => user.user_id === userID);
  const sort = ({ sortBy, sortDirection }) => {
    logEvent('modems_files_sort', { sortBy, sortDirection });
    setSortBy(sortBy);
    setSortDirection(sortDirection);
  };
  const removeFiles = () => {
    logEvent('modems_files_remove', { count: Object.keys(checkedIds).length });
    for (const filename of Object.keys(checkedIds)) {
      const file = files.find(file => file.filename === filename);
      const id = file.uuid;
      if (id) {
        dispatch(modems.actions.removeFiles([id], modemId));
      } else {
        const folder = `${directory}/${filename}`;
        const allFilesInFolder = modems.selectors.getAllFilesInFolder(
          store.getState(),
          modemId,
          folder,
        );
        const fileIds = allFilesInFolder.map(fileInFolder => fileInFolder.uuid);
        dispatch(modems.actions.removeFiles(fileIds, modemId));
      }
    }
    setCheckedIds({});
  };
  const downloadFiles = () => {
    const count = Object.keys(checkedIds).length;
    logEvent('modems_files_download', { count });
    const selectedFileIds = flatMap(Object.keys(checkedIds), filename => {
      const file = files.find(file => file.filename === filename);
      if (file.isFolder) {
        const folder = `${directory}/${filename}`;
        const allFilesInFolder = modems.selectors.getAllFilesInFolder(
          store.getState(),
          modemId,
          folder,
        );
        return allFilesInFolder.map(file => file.uuid);
      } else {
        return [file.uuid];
      }
    });

    // needs to download archive with folder if was selected one folder with single file
    const checkedFile = files.find(
      file => file.filename === Object.keys(checkedIds)[0],
    );
    if (selectedFileIds.length === 1 && !checkedFile.isFolder) {
      // selector needed because file can be not in the current folder
      // in this component we have files and folders in current directory
      const file = modems.selectors.getFileById(
        store.getState(),
        modemId,
        selectedFileIds[0],
      );
      const shortFilename = getFilename(file.filename);
      downloadFromUrl(file.url, shortFilename);
    } else {
      dispatch(modems.actions.downloadArchiveTask(modemId, selectedFileIds));
    }

    setCheckedIds({});
  };
  const refreshFiles = () => {
    logEvent('modems_files_refresh');
    dispatch(modems.actions.fetchFiles(modemId));
  };
  const showFolderPopup = () => {
    logEvent('modems_files_folder_show_popup');
    setShowPopup(true);
  };
  const getIsDisabled = file => {
    return (
      file.status === 'deleting_from_modem' ||
      file.status === 'uploading_from_modem'
    );
  };
  const renderSubtitle = () => {
    if (splitted.length === 1) {
      return (
        <div className='modems-section-subtitle'>
          <span>{t('modems.index')}</span>
          <span className='modems-section-subtitle__index'>
            {modem.serial_number.toUpperCase()}
          </span>
        </div>
      );
    }
    return (
      <div className='breadcrumbs-container'>
        <Breadcrumbs>
          {splitted.map((folder, index, array) => {
            if (folder === '') {
              folder = formatTitle(t, modemUser, modem);
            }
            const prevDirectory = array
              .slice(0, index - array.length + 1)
              .join('/');
            const path = `${getRootScoutingUrl(
              history.location.pathname,
            )}/${modemId}${prevDirectory}`;
            return (
              <Breadcrumbs.Item withoutLastItem key={prevDirectory}>
                <Link to={path}>{folder}</Link>
              </Breadcrumbs.Item>
            );
          })}
        </Breadcrumbs>
      </div>
    );
  };
  const renderTitle = () => {
    if (splitted.length === 1) {
      return <h1>{formatTitle(t, modemUser, modem)}</h1>;
    }
    return <h1>{splitted[splitted.length - 1]}</h1>;
  };
  const renderButtons = () => {
    if (Object.keys(checkedIds).length > 0) {
      return (
        <div className='modems-section-header__buttons'>
          <Button
            className='btn btn-default'
            onClick={removeFiles}
            data-style='error'
          >
            <span className='btn__ico'>
              <Icon className='ico-trash-n-os' name='trashcan' />
            </span>
            {t('modems.files.remove')}
          </Button>
          <Button className='btn btn-default' onClick={downloadFiles}>
            <span className='btn__ico'>
              <Icon className='ico-download-os' name='download' />
            </span>
            {t('modems.files.download')}
          </Button>
        </div>
      );
    }
    return (
      <div className='modems-section-header__buttons'>
        <Button className='btn btn-default' onClick={refreshFiles}>
          <span className='btn__ico'>
            <Icon className='ico-refresh-os' name='refresh-btn' />
          </span>
          {t('modems.files.refresh')}
        </Button>
        <Button className='btn btn-default' onClick={showFolderPopup}>
          <span className='btn__ico'>
            <Icon name='folder-btn' />
          </span>
          {t('modems.folder.create')}
        </Button>
        <ImportFilesButton modemId={modemId} directory={directory} />
      </div>
    );
  };
  const renderChecked = () => {
    if (Object.keys(checkedIds).length === 0) {
      return <div className='modems-selected-stats' />;
    }
    const stats = Object.keys(checkedIds).reduce(
      (result, curr) => {
        const file = files.find(file => file.filename === curr);
        if (!file) {
          return result;
        }
        if (file.isFolder) {
          return {
            ...result,
            foldersCount: result.foldersCount + 1,
          };
        } else {
          return {
            ...result,
            filesCount: result.filesCount + 1,
          };
        }
      },
      { foldersCount: 0, filesCount: 0 },
    );
    let statsString = '';
    if (stats.filesCount > 0) {
      statsString = t('modems.selected.count.files', {
        count: stats.filesCount,
      });
      if (stats.foldersCount > 0) {
        statsString += `, ${t('modems.selected.count.folders', {
          count: stats.foldersCount,
        })}`;
      }
    } else {
      statsString = t('modems.selected.count.folders', {
        count: stats.foldersCount,
      });
    }
    return (
      <div className='modems-selected-stats'>
        <b>{t('modems.selected')}</b>
        <span>{statsString}</span>
      </div>
    );
  };
  const onRowClick = ({ event, index, rowData }) => {
    if (
      event.target.className &&
      event.target.className.includes &&
      event.target.className.includes('form-checkbox')
    ) {
      return;
    }
    if (rowData.isFolder) {
      setCheckedIds({});
      logEvent('modems_files_folder_open');
      history.replace(`${history.location.pathname}/${rowData.filename}`);
    }
  };
  const rowGetter = ({ index }) => sortedFiles[index];
  const rowRenderer = props => {
    const disabled = getIsDisabled(props.rowData);
    const className = cx(props.className, {
      'modems-files-table-row-folder': props.rowData.isFolder,
      'modems-files-table-row-file': !props.rowData.isFolder,
      __disabled: disabled && !props.rowData.isFolder,
      __highlighted: props.rowData.isHighlighted,
    });
    return defaultTableRowRenderer({ ...props, className });
  };
  const headerCheckRenderer = ({ columnData, rowData }) => {
    const checked =
      Object.keys(columnData).length > 0 &&
      Object.keys(columnData).length ===
        files.filter(file => !getIsDisabled(file)).length;
    const disabled =
      files.length === files.filter(file => getIsDisabled(file)).length;
    return (
      <Checkbox
        checked={checked}
        disabled={disabled}
        isSmall
        onChange={event => {
          if (event.target.checked) {
            const all = files.reduce((result, curr) => {
              const disabled = getIsDisabled(curr);
              if (!disabled) {
                result[curr.filename] = true;
              }
              return result;
            }, {});
            setCheckedIds(all);
            // todo:
          } else {
            setCheckedIds({});
          }
        }}
      />
    );
  };
  const checkRenderer = ({ columnData, rowData }) => {
    const disabled = getIsDisabled(rowData);
    return (
      <Checkbox
        disabled={disabled}
        isSmall
        checked={!!columnData[rowData.filename]}
        onChange={event => {
          setCheckedIds(prevState => {
            if (!prevState[rowData.filename]) {
              return { ...prevState, [rowData.filename]: true };
            }
            return omit(prevState, rowData.filename);
          });
        }}
      />
    );
  };
  const nameRenderer = ({ cellData, rowData }) => {
    if (rowData.isFolder) {
      return (
        <div className='modems-filename'>
          <Icon name='folder' className='db' />
          <span>{String(cellData)}</span>
        </div>
      );
    }
    return (
      <div className='modems-filename'>
        <Icon name='file' className='db' />
        <span>{String(cellData)}</span>
      </div>
    );
  };
  const dateRenderer = ({ cellData }) =>
    formatDate(moment(cellData), t('moment.full_date_time.with_year'));
  const sizeRenderer = ({ cellData }) => convertBytes(cellData, t);
  const statusRenderer = ({ cellData, rowData }) => {
    if (typeof statusLabels[cellData] === 'undefined') {
      return '';
    }
    return (
      <div className='modems-status'>
        <span>{t(statusLabels[cellData])}</span>
        <Icon name={statusIcons[cellData]} />
      </div>
    );
  };

  return (
    <div className='map-content __normal'>
      <Saga saga={modemsSaga} />
      <ToastContainer id='modems'>
        <UploadsCount />
      </ToastContainer>
      <EnterExitTransition
        variant='popup'
        onEntered={() => setIsEntered(true)}
        onExit={() => setIsEntered(false)}
      >
        {showPopup && (
          <CreateFolderPopup
            autoFocus={isEntered}
            history={history}
            files={files}
            onConfirm={({ folder }) => {
              setShowPopup(false);
              logEvent('modems_files_folder_create', { folder });
              dispatch(modems.actions.createFolder(folder, directory, modemId));
            }}
            onCancel={() => {
              logEvent('modems_files_folder_cancel');
              setShowPopup(false);
            }}
          />
        )}
      </EnterExitTransition>
      <div className='modems-section'>
        <div className='modems-section-header'>
          <div className='modems-section-title'>
            {renderSubtitle()}
            {renderTitle()}
          </div>
          <div className='modems-section-info'>
            {renderButtons()}
            {renderChecked()}
          </div>
        </div>

        <div className='soil-sidebar-body modems-section-body'>
          <AutoSizer>
            {({ height, width }) => (
              <Table
                className='modems-files-table'
                headerHeight={42}
                height={height}
                rowCount={files.length}
                rowGetter={rowGetter}
                rowHeight={42}
                rowRenderer={rowRenderer}
                width={width}
                onRowClick={onRowClick}
                sort={sort}
                sortBy={sortBy}
                sortDirection={sortDirection}
              >
                <Column
                  dataKey='check'
                  width={34}
                  cellRenderer={checkRenderer}
                  headerRenderer={headerCheckRenderer}
                  flexShrink={0}
                  columnData={checkedIds}
                  disableSort
                  headerStyle={{ margin: 0 }}
                  style={{ margin: 0 }}
                />
                <Column
                  dataKey='filename'
                  width={144}
                  label={t('modems.table.filename')}
                  cellRenderer={nameRenderer}
                  flexGrow={1}
                />
                <Column
                  dataKey='updated_at'
                  width={140}
                  label={t('modems.table.updated_at')}
                  cellRenderer={dateRenderer}
                />
                <Column
                  dataKey='created_at'
                  width={140}
                  label={t('modems.table.created_at')}
                  cellRenderer={dateRenderer}
                />
                <Column
                  dataKey='size'
                  width={70}
                  label={t('modems.table.size')}
                  cellRenderer={sizeRenderer}
                  headerStyle={{ justifyContent: 'flex-end' }}
                  style={{ textAlign: 'right' }}
                />
                <Column
                  dataKey='status'
                  width={160}
                  label={t('modems.table.status')}
                  cellRenderer={statusRenderer}
                  headerStyle={{ justifyContent: 'flex-end' }}
                  style={{ color: '#a5b2bc', textAlign: 'right' }}
                />
              </Table>
            )}
          </AutoSizer>
        </div>
      </div>
    </div>
  );
};

export default ModemViewPane;
