import { css } from 'linaria';
import { styled } from 'linaria/react';

export const mapButtonStyle = css`
  padding: 0 20px;
  border-radius: 20px;
  border-width: 0;
  background-color: rgba(17, 17, 17, 0);
  transition: color 0.1s, background-color 0.1s;
  color: #ebebeb;
  min-height: 40px;
  display: flex;
  align-items: center;
  transform: translate(0, 0);
`;

export const ItemValueStyle = styled.span`
  &.prefixed {
    font-weight: 500;
    margin-left: 5px;
  }
`;

export const mapButtonIcon = css`
  margin-bottom: 2px;
  margin-left: -5px;
  margin-right: 5px;
`;
