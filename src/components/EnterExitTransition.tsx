import React, { FC } from 'react';
import { TransitionGroup, CSSTransition } from 'react-transition-group';
import { TransitionChildren } from 'react-transition-group/Transition';

export const Variants = {
  popup: {
    enterActive: '__opened',
    enterDone: '__opened',
    enter: '__initial',
    exitActive: '__initial',
  },
  legacy: {
    enterActive: '__initial',
    enterDone: '__normal',
    enter: '__initial',
    exitActive: '__initial',
  },
  default: {
    enterActive: '__state-normal',
    enterDone: '__state-normal',
    enter: '__state-initial',
    exitActive: '__state-initial',
  },
  withExit: {
    enter: '__state-initial',
    enterActive: '__state-normal',
    enterDone: '__state-normal',
    exit: '__state-ending',
    exitActive: '__state-ending',
    exitDone: '__state-initial',
  },
};

type EnterExitTransitionProps = {
  timeout?: number;
  variant?: keyof typeof Variants;
  children: TransitionChildren;
};

const EnterExitTransition: FC<EnterExitTransitionProps> = ({
  timeout = 300,
  variant = 'default',
  children,
  ...otherProps
}) => (
  <TransitionGroup component={null} appear>
    {children && (
      <CSSTransition
        classNames={Variants[variant]}
        timeout={timeout}
        {...otherProps}
      >
        {children}
      </CSSTransition>
    )}
  </TransitionGroup>
);

export default EnterExitTransition;
