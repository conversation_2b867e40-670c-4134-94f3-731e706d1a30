.form-input {
    max-height: var(--input-max-height);
    border-radius: 8px;
    padding: var(--input-padding);
    vertical-align: top;
    margin: 0;
    font-size: var(--input-font-size);
    line-height: 1.25rem;

    background: var(--input-background-color);
    color: var(--input-text-color);
    border-width: var(--input-border-width);
    border-color: var(--input-border-color);
    border-style: solid;

    resize: vertical;
    outline: none;
    transition: border-color 0.1s, border-width 0.1s;
}
.form-input:hover {
    border-color: var(--input-border-color-hovered);
    background: var(--input-background-color-hovered);
    color: var(--input-text-color-hovered);
}
.form-input:focus,
.form-input:active {
    border-color: var(--input-border-color-active);
    background: var(--input-background-color-active);
    color: var(--input-text-color-active);
}
.form-input.__error {
    border-color: var(--input-border-color-error);
    background: var(--input-background-color-error);
    color: var(--input-text-color-error);
}
.form-input::-webkit-input-placeholder {
    color: var(--input-placeholder-color);
}
.form-input::-moz-placeholder {
    color: var(--input-placeholder-color);
}
.form-input:-ms-input-placeholder {
    color: var(--input-placeholder-color);
}
.form-input:-moz-placeholder {
    color: var(--input-placeholder-color);
}
.form-input[disabled] {
    border-color: var(--input-border-color-disabled);
    background: var(--input-background-color-disabled);
    color: var(--input-text-color-disabled);
}
.form-input[disabled]::-webkit-input-placeholder {
    color: var(--input-placeholder-color-disabled);
}
.form-input[disabled]::-moz-placeholder {
    color: var(--input-placeholder-color-disabled);
}
.form-input[disabled]:-ms-input-placeholder {
    color: var(--input-placeholder-color-disabled);
}
.form-input[disabled]:-moz-placeholder {
    color: var(--input-placeholder-color-disabled);
}
.form-input.form-size-medium {
    font-size: 14px;
    line-height: 20px;
    min-height: 34px;
    padding-top: 5px;
    padding-bottom: 5px;
    border-radius: 6px;
}
.form-textarea-no-resize {
    resize: none;
}
