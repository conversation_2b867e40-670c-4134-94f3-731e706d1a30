.tabs {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}

.list {
  display: flex;
  gap: 28px;
  list-style-type: none;
  padding: 0;
  margin: 0;
  position: relative;
}

.list > .item {
  display: flex;
  position: relative;
}

.button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  border: none;
  border-bottom-width: 2px;
  border-bottom-style: solid;
  border-bottom-color: transparent;
  padding: 7px 0 4px;
  margin: 0;
  background: transparent;
  cursor: pointer;
  transition: border-color 0.3s;
  color: var(--color-black-light);
  font-size: 16px;
  line-height: 22px;
  font-weight: 500;
}

.button__active {
  border-color: var(--color-black);
  color: var(--color-black);
}

.button__disabled {
  color: var(--color-grey-70)
}

.panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;
}
