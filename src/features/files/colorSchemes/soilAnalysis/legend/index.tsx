import React, { VFC } from 'react';

import { FileDataSourceType } from '../../types';
import { getSoilElementParameter } from '../utils';

import {
  LegendPalette,
  LegendBox,
  LegendRow,
  LegendText,
  SoilAnalysisLegend,
  LegendBlock,
} from './styles';
import OverlapFileLegend from './OverlapFileLegend';
import UndercoverFileLegend from './UndercoverFileLegend';

const SoilAnalysisFileLegend: VFC<FileDataSourceType> = dataSource => {
  const parameter = getSoilElementParameter(dataSource.column.name);

  return (
    <LegendRow>
      <LegendBox>
        <SoilAnalysisLegend>
          {parameter.breakPoints.map((breakPoint, index) => (
            <LegendBlock key={index}>
              <LegendText>
                {index === 0 && '<'}
                {index === parameter.breakPoints.length - 1 && '>'}
                {Array.isArray(breakPoint) ? breakPoint[0] : breakPoint}
              </LegendText>
              <LegendPalette color={parameter.colorsPattern[index]} />
            </LegendBlock>
          ))}
        </SoilAnalysisLegend>

        <OverlapFileLegend {...dataSource} />
        <UndercoverFileLegend {...dataSource} />
      </LegendBox>
    </LegendRow>
  );
};

export default SoilAnalysisFileLegend;
