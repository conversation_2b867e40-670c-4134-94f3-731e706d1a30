import React from 'react';
import { styled } from 'linaria/react';

import ModernSpinner, {
  SpinnerTheme,
} from 'components/ui/ModernSpinner/ModernSpinner';
import { CommonButtonProps } from './types';

export const ButtonStyled = styled.button`
  color: #222;
  font: 1em 'Graphik', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
    'Segoe UI Symbol';
  cursor: pointer;
`;
/**
 * please don't use it directly. Use SmartButton which allow you use link too, not only onClick
 */
export type ButtonProps = CommonButtonProps &
  React.RefAttributes<HTMLButtonElement> &
  React.DetailedHTMLProps<
    React.ButtonHTMLAttributes<HTMLButtonElement>,
    HTMLButtonElement
  >;

const Button: React.ForwardRefExoticComponent<ButtonProps> = React.forwardRef(
  (
    {
      borderRadius = 'default',
      buttonColor = 'default',
      children,
      dataTestId,
      disabled,
      pending,
      onMouseEnter,
      onMouseLeave,
      ...otherProps
    },
    ref,
  ) => {
    const [isHovered, setIsHovered] = React.useState(false);

    const buttonColorPalette = {
      black: '#1C1C1E',
      blackHover: '#333335',
    };

    const getBackgroundColor = () => {
      if (buttonColor === 'black') {
        if (isHovered && !disabled && !pending) {
          return buttonColorPalette.blackHover;
        }
        return buttonColorPalette.black;
      }
      return undefined;
    };

    const buttonStyle = {
      ...(buttonColor === 'black' && {
        backgroundColor: getBackgroundColor(),
      }),
      ...(borderRadius === 'rounded' && {
        borderRadius: '9999px',
      }),
    };

    const handleMouseEnter = (e: React.MouseEvent<HTMLButtonElement>) => {
      setIsHovered(true);
      onMouseEnter?.(e);
    };

    const handleMouseLeave = (e: React.MouseEvent<HTMLButtonElement>) => {
      setIsHovered(false);
      onMouseLeave?.(e);
    };

    return (
      <button
        ref={ref}
        // @ts-ignore
        type='button'
        data-testid={dataTestId}
        disabled={disabled || pending}
        style={buttonStyle}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        {...otherProps}
      >
        {pending ? <ModernSpinner theme={SpinnerTheme.White} /> : children}
      </button>
    );
  },
);

export default Button;
