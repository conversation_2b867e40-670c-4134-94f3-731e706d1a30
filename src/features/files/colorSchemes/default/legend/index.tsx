import React, { VFC } from 'react';
import { StringMap } from 'i18next';

import { useTranslate } from 'utils/use-translate';
import { colors } from 'constants/style';

import { FileDataSourceType } from 'features/files/colorSchemes/types';
import OverlapFileLegend from 'features/files/colorSchemes/default/legend/OverlapFileLegend';
import UndercoverFileLegend from 'features/files/colorSchemes/default/legend/UndercoverFileLegend';
import LegendBar from 'features/files/colorSchemes/default/legend/LegendBar';

const DefaultFileLegend: VFC<FileDataSourceType> = dataSource => {
  const { t } = useTranslate();

  return (
    <div className='map-legend'>
      <div className='map-legend__item'>
        <LegendBar
          from={
            t('files.legend.low', {
              column: dataSource.column.name,
              value: dataSource.column.min,
            } as StringMap) as string
          }
          to={
            t('files.legend.high', {
              column: dataSource.column.name,
              value: dataSource.column.max,
            } as StringMap) as string
          }
          colors={colors.colorPaletteGradient}
        />
      </div>
      <OverlapFileLegend {...dataSource} />
      <UndercoverFileLegend {...dataSource} />
    </div>
  );
};

export default DefaultFileLegend;
