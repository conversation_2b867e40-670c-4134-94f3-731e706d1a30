import React, { VFC } from 'react';
import {
  StyledPage,
  styles,
} from 'components/WhatsNew/WhatsNewContent/WhatsNewContent.style';
import Button from 'components/ui/Button/Button';

import WhatsNewGuideImg from 'assets/images/whatsnew/whatsnew_guide.png';
import WhatsNewGuideImg2x from 'assets/images/whatsnew/<EMAIL>';
import { WhatsNewProps } from '../types';
import { FeaturesKeyEnum } from '../consts';

const Guide: VFC<WhatsNewProps> = ({ style, onClick, isLast, t }) => (
  <StyledPage style={{ ...style }}>
    <div className={styles.semiPage}>
      <div className={styles.pageContent}>
        <div className={styles.textContent}>
          <h2 className={styles.title}>{t('whatsnew.guide.title')}</h2>
          <p className={styles.text}>{t('whatsnew.guide.text')}</p>
        </div>
        <Button className='btn btn-success fit-content' onClick={onClick}>
          {t(`whatsnew.${isLast ? 'end' : 'next'}.action_text`)}
        </Button>
      </div>
    </div>
    <div className={styles.semiPage}>
      <picture className={styles.pagePicture}>
        <source srcSet={`${WhatsNewGuideImg} 1x, ${WhatsNewGuideImg2x} 2x`} />
        <img src={WhatsNewGuideImg} alt={t('whatsnew.guide.title') as string} />
      </picture>
    </div>
  </StyledPage>
);

const WhatsNewGuide = {
  key: FeaturesKeyEnum.mapColors,
  component: Guide,
  filter: {
    locale: 'ru',
  },
};

export default WhatsNewGuide;
