import { call, put, select, takeEvery } from 'redux-saga/effects';

// @ts-ignore
import fields from 'modules/fields';

// @ts-ignore
import { apiCall } from 'utils/effects';

import {
  BulkUploadStep,
  FieldAttribute,
} from 'features/fields/BulkUpload/types';

import bulkUploadModule from 'features/fields/BulkUpload/redux';
import { getCropsKeys } from 'features/fields/BulkUpload/saga/utils';

export interface MatchCropsSagaHost {
  props: {
    fileId: number;
    onSetData: (data: Record<string, string[]>) => void;
  };
}

export function* matchCropsSaga(host: MatchCropsSagaHost) {
  const { fileId, onSetData } = host.props;

  const { properties_map } = yield call(
    apiCall,
    fields.actions.fetchDataFromFile(fileId),
  );

  if (properties_map) {
    const cropsKeys = getCropsKeys(properties_map);

    if (cropsKeys.length) {
      const data: Record<string, string[]> = yield call(
        apiCall,
        fields.actions.fetchCropsFromFile(fileId, cropsKeys),
      );

      onSetData(data);
    }
  }

  yield takeEvery(
    fields.actions.setMatchedProperties.toString(),
    function* (action: {
      payload: { properties: Record<string, FieldAttribute>; proceed: boolean };
    }) {
      yield call(
        apiCall,
        fields.actions.updateFieldsFile(fileId, {
          ...action.payload.properties,
          ...properties_map,
        }),
      );

      const fileIds: number[] = yield select(
        bulkUploadModule.selectors.getUploadedFilesIds,
      );

      yield call(
        apiCall,
        bulkUploadModule.actions.fetchFieldsFromFiles(fileIds),
      );

      yield put(bulkUploadModule.actions.setCurrentStep(BulkUploadStep.SAVING));
    },
  );
}
