import { css } from 'linaria';
import { styled } from 'linaria/react';

export const styles = {
  cropRotationSidebar: css`
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    width: 300px;
    box-sizing: border-box;
    background: #fff;
    box-shadow: -2px 0 20px 0 rgba(76, 82, 87, 0.1),
      0 0 0 0 rgba(189, 189, 189, 0.3);
    display: flex;
    flex-direction: column;
    transition: transform 0.3s, opacity 0.3s;
    opacity: 0;
    transform: translateX(100%);
    z-index: 2;
    &.__normal {
      opacity: 1;
      transform: translateX(0);
      transition: transform 0.3s, opacity 0.3s;
    }
    &.__initial {
      opacity: 0;
      transform: translateX(100%);
      transition: transform 0.3s, opacity 0.3s;
    }
  `,
  cropRotationSidebarInner: css`
    transform: translateX(100%);
    height: 100%;
    &.__normal {
      opacity: 1;
      transform: translateX(0);
      transition: transform 0.3s, opacity 0.3s;
    }
    &.__initial {
      position: absolute;
      opacity: 0;
      transform: translateX(100%);
      transition: transform 0.3s, opacity 0.3s;
    }
  `,
  sidebarHeader: css`
    height: 50px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-bottom: 1px solid #e4e7ea;
    padding-left: 40px;
    padding-right: 40px;
    position: relative;
    flex: 0 0 auto;
  `,

  sidebarTitle: css`
    font-weight: 500;
    font-size: 14px;
    color: #222222;
    text-align: center;
    line-height: 19px;
    margin: 0;
  `,

  sidebarSubtitle: css`
    font-size: 12px;
    color: #a5b2bc;
    text-align: center;
    line-height: 16px;
  `,

  sidebarActionIcon: css`
    width: 16px;
    height: 16px;
    color: #222222;
  `,

  sidebarFieldPreview: css`
    position: relative;
    z-index: 2;
    display: flex;
    overflow: hidden;
    flex: 1 1 auto;
    background-color: #d8d8d8;
    height: 144px;
    &:before {
      position: absolute;
      z-index: 15;
      right: 0;
      bottom: 0;
      content: attr(data-label);
      border-radius: 8px 0 0;
      background-color: rgba(0, 0, 0, 0.5);
      font-size: 16px;
      line-height: 20px;
      font-weight: 500;
      color: #fff;
      padding: 6px 10px;
    }
  `,

  sidebarBody: css`
    display: flex;
    flex-direction: column;
    height: calc(100% - 50px);
    overflow-y: auto;
  `,

  sidebarBottom: css`
    border-top: 1px solid #e4e7ea;
  `,

  sidebarBottomLink: css`
    font-size: 12px;
    line-height: 16px;
    font-weight: 500;
    display: block;
    padding: 22px 0;
    text-align: center;
    &:hover {
      background-color: #f5f7f9;
    }
  `,

  sidebarBottomText: css`
    color: #007aff;
  `,

  sidebarFloatingAction: css`
    position: absolute;
    z-index: 4;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 0 10px 10px;
    background: linear-gradient(0deg, #fff 0%, rgba(255, 255, 255, 0) 100%);
  `,
};

export const StyledSidebarAction = styled.button`
  position: absolute;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 50%;
  transform: translateY(-50%);
  padding: 0;
  border: none;
  background: transparent;
  transform-origin: center center;
  transition: transform 0.2s;
  &:hover {
    transform: translateY(-50%) scale(1.2);
  }
`;

export const StyledLeftAction = styled(StyledSidebarAction)`
  left: 16px;
`;

export const StyledRightAction = styled(StyledSidebarAction)`
  right: 16px;
`;
