import React, { useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { useTranslate } from 'utils/use-translate';
import { useGoogleLogin } from '@react-oauth/google';
import { actions as formActions } from 'react-redux-form';
import { useHistory } from 'react-router-dom';

import { parseQuery } from 'utils/query';

import forms from 'constants/forms';

import Button from 'components/ui/Button/Button';
import { OnSocialLoginCallback } from 'features/auth/SocialLoginButtons/types';

interface Props {
  location: TEMPORARY_ANY;
  type: string;
  onSocialLogin: OnSocialLoginCallback;
}

export const GoogleLoginButton = ({ location, type, onSocialLogin }: Props) => {
  const { t } = useTranslate();
  const history = useHistory();
  const dispatch = useDispatch();

  const login = useGoogleLogin({
    flow: 'auth-code',
    onSuccess: ({ code }) => {
      const query = parseQuery(location);
      if (code) {
        onSocialLogin('google', { code }, undefined, query.d);
      } else {
        alert(t('auth.social.error'));
      }
    },
  });

  const onClickHandle = useCallback(() => {
    if (type === 'login') {
      login();
    } else {
      dispatch(formActions.change(`${forms.login}.accept_terms`, null));
      history.push({
        pathname: '/auth/confirm',
        state: {
          provider: 'google',
        },
      });
    }
  }, [type, login, history, dispatch]);

  return (
    <Button
      className='btn size-full'
      data-social='google'
      onClick={onClickHandle}
    >
      <span className='onesoil-hello-socials__socico' />
      {t(`auth.provider.google`)}
    </Button>
  );
};
