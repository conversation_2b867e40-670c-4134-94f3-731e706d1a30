import React from 'react';

import { PopoverDeprecated } from 'components/ui/Popover';

const ForeignObject = ({ x, y, children, ...otherProps }) => (
  <PopoverDeprecated
    active
    align='centered'
    renderTrigger={props => (
      <circle cx={x} cy={y} r={5} style={{ fill: 'transparent' }} {...props} />
    )}
    renderPopover={props =>
      children && React.cloneElement(React.Children.only(children), props)
    }
    {...otherProps}
  />
);

export default ForeignObject;
