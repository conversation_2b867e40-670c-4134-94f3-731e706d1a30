import React, { VFC } from 'react';
import { cx } from 'linaria';

export enum SpinnerTheme {
  Black = 'black',
  Green = 'green',
  White = 'white',
}

interface ModernSpinnerProps {
  huge?: boolean;
  large?: boolean;
  theme?: SpinnerTheme;
}

const ModernSpinner: VFC<ModernSpinnerProps> = ({
  theme = SpinnerTheme.Black,
  large,
  huge,
}) => {
  return (
    <span
      className={cx(
        'g-loader',
        theme,
        large ? 'large' : null,
        huge ? 'huge' : null,
      )}
    >
      <span className='g-loader__inner' />
    </span>
  );
};

export default ModernSpinner;
