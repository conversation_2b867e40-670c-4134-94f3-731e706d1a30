import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react';
import '@testing-library/jest-dom';

import { SortDirections } from 'constants/sortDirections';

import Sorter, { SorterButtonDataTestId } from './Sorter';

test('can accept dataTestId', async () => {
  render(
    <Sorter label={''} value={SortDirections.ASCENDING} onChange={() => {}} />,
  );

  expect(screen.queryByTestId(SorterButtonDataTestId)).toBeInTheDocument();
});

test('can render label', async () => {
  const LABEL = 'label';
  render(
    <Sorter
      label={LABEL}
      value={SortDirections.ASCENDING}
      onChange={() => {}}
    />,
  );

  expect(screen.queryByTestId(SorterButtonDataTestId)).toHaveTextContent(LABEL);
});

test('can render without label', async () => {
  render(
    <Sorter label={''} value={SortDirections.ASCENDING} onChange={() => {}} />,
  );

  expect(screen.queryByTestId(SorterButtonDataTestId)).toHaveTextContent('-');
});

test('run onChange after click', async () => {
  const mockOnChange = jest.fn();
  render(
    <Sorter
      label={''}
      value={SortDirections.ASCENDING}
      onChange={mockOnChange}
    />,
  );

  fireEvent.click(screen.getByTestId(SorterButtonDataTestId));

  expect(mockOnChange).toHaveBeenCalledTimes(1);
});

test('run onChange with right argument', async () => {
  const mockOnChange = jest.fn();
  render(
    <Sorter
      label={''}
      value={SortDirections.ASCENDING}
      onChange={mockOnChange}
    />,
  );

  fireEvent.click(screen.getByTestId(SorterButtonDataTestId));

  expect(mockOnChange).toHaveBeenCalledWith(SortDirections.DESCENDING);
});
