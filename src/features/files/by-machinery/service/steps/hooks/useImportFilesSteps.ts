import { useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import {
  baseRoutes,
  soilSamplingRoutes,
  electroconductivityRoutes,
} from '../constants';
import { StepRoute, StepStatus, StepValidationStatus } from '../types';
import { DataTypesKey } from 'features/files/by-machinery/types';

import { parseQuery } from 'utils/query';

const getRoutes = (dataType: string) => {
  switch (dataType) {
    case DataTypesKey.machinery:
      return baseRoutes;
    case DataTypesKey.soilSampling:
      return soilSamplingRoutes as typeof baseRoutes;
    case DataTypesKey.electroconductivity:
      return electroconductivityRoutes as typeof baseRoutes;
    default:
      return baseRoutes;
  }
};

export const useImportFilesSteps = (): StepRoute[] => {
  const history = useHistory();
  const query = parseQuery(history.location);
  const dataType = query['data-type'] as string;
  const routes = getRoutes(dataType);

  const allRoutes = useSelector(state =>
    routes.map(route => {
      let isValid = StepValidationStatus.valid;

      if (route.validation) {
        for (let i = 0; i < route.validation.length; i++) {
          const temp = route.validation[i]!(state);
          if (temp !== StepValidationStatus.valid) {
            isValid = temp;

            break;
          }
        }
      }

      return {
        ...route,
        status: route.check ? route.check(state) : StepStatus.available,
        valid: isValid,
      };
    }),
  );

  return allRoutes.filter(item => item.status !== StepStatus.hiding);
};
