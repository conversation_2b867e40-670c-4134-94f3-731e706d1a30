import { styled } from 'linaria/react';

export const NawProgressStyle = styled.nav`
  flex: 0 0 auto;
  margin: 0;
  padding: 0;
  color: #a5b2bc;
  font-weight: 500;

  & a {
    color: var(--color-primary);
  }

  & a:hover {
    color: var(--color-secondary);
  }
`;

export const NawProgressListStyle = styled.ul`
  list-style-type: none;
  margin: 0;
  padding: 0;
  display: flex;
`;

export const NawProgressListItemStyle = styled.li`
  margin-left: 30px;

  &:before {
    position: absolute;
    content: '';
    width: 20px;
    height: 1px;
    background-color: #b7c1c9;
    margin-left: -25px;
    margin-top: 9px;
  }

  &:first-child:before {
    display: none;
  }

  &[data-status='active'] {
    color: #222;
  }
`;
