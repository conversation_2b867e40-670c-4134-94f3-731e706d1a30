import React, { Fragment } from 'react';
import { Route } from 'react-router-dom';

import Saga from 'components/saga/Saga';
import AddFieldsLayer from './AddFieldsLayer';
import OwnFieldsLayer from './OwnFieldsLayer';
import EditFieldsLayer from './EditFieldsLayer';
import DrawFieldsLayer from './DrawFieldsLayer';
import TooltipsLayer from './TooltipsLayer';
import mapClickSaga from 'sagas/local/map-click';

const FieldsLayer = ({ match, history }) => (
  <Fragment>
    <Saga saga={mapClickSaga} history={history} match={match} />
    <Route path={`${match.url}/add/select`} component={AddFieldsLayer} />
    <Route path={`${match.url}/add/create`} component={DrawFieldsLayer} />
    <Route path={`${match.url}/:fieldId?`} component={TooltipsLayer} />
    <Route path={`${match.url}/:fieldId?`} component={OwnFieldsLayer} />
    <Route path={`${match.url}/edit/list`} component={EditFieldsLayer} />
  </Fragment>
);

export default FieldsLayer;
