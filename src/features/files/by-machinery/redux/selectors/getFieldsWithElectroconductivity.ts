import { createSelector } from 'reselect';
import { selectors } from './index';
import { fieldsSelectors } from 'modules/fields/selectors';
import { ApiDataFieldElectroconductivity } from '../../types';

export const getFieldsWithElectroconductivity = createSelector(
  selectors.getMatchingElectroconductivity,
  fieldsSelectors.getAllOwn,
  (electroconductivityData: ApiDataFieldElectroconductivity[], fields) => {
    const fieldsWithDataIds = electroconductivityData.map(
      record => record.field_user_season.id,
    );

    return fields.filter(field =>
      fieldsWithDataIds.includes(field.fieldUserSeasonId),
    );
  },
);
