import React, { Component } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import { withTranslation } from 'react-i18next';

import ui from 'modules/ui';

import SelectedFieldNDVIFiles from 'features/files/SelectedFieldNDVIFiles/SelectedFieldNDVIFiles';

import { SelectedField } from 'features/platform/SelectedField/SelectedField';

class SelectedFieldFiles extends Component {
  render() {
    const {
      fieldId,
      index,
      hideVegetationTooltip,
      changeTooltipIndex,
      mapIndex,
      tooltipIndex,
      updateTooltipValue,
    } = this.props;

    return (
      <SelectedField
        fieldId={fieldId}
        index={index ?? null}
        withoutSaga
        SelectedFieldNDVILayer={
          <SelectedFieldNDVIFiles
            fieldId={fieldId}
            hideVegetationTooltip={hideVegetationTooltip}
            index={index}
            changeTooltipIndex={changeTooltipIndex}
            mapIndex={mapIndex}
            tooltipIndex={tooltipIndex}
            updateTooltipValue={updateTooltipValue}
          />
        }
      />
    );
  }
}

const mapDispatchToProps = {
  onSetTooltipPosition: ui.actions.setTooltipCoordinates,
  onToggleTooltipVisibility: ui.actions.toggleTooltipVisibility,
};

export default withRouter(
  connect(null, mapDispatchToProps)(withTranslation()(SelectedFieldFiles)),
);
