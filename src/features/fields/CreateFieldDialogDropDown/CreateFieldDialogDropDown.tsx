import React, { ReactNode, useCallback } from 'react';
import { useSelector } from 'react-redux';

import { useTranslate } from 'utils/use-translate';
import logEvent from 'sagas/global/logEvent';
// @ts-ignore
import fields from 'modules/fields';
// @ts-ignore
import auth from 'modules/auth';
// @ts-ignore
import seasons from 'modules/seasons';
// @ts-ignore
import { CustomDropdown } from 'components/ui/CustomDropdown/CustomDropdown';
import { CustomDropdownOption } from 'components/ui/CustomDropdown/types';

interface CreateFieldDialogDropDownProps {
  className?: string;
  renderValue: (props?: {
    value?: TEMPORARY_ANY;
    isOpen?: boolean;
    onToggle?: () => void;
  }) => ReactNode;
  onChange: (
    option: string | null | undefined,
    selection?: CustomDropdownOption,
  ) => void;
}

const CreateFieldDialogDropDown = ({
  className,
  renderValue,
  onChange: onChangeProps,
}: CreateFieldDialogDropDownProps) => {
  const userID = useSelector(auth.selectors.getUserID);
  const currentSeason: TEMPORARY_ANY = useSelector(
    seasons.selectors.getCurrent,
  );
  const fieldIDs: { id: string }[] = useSelector(
    fields.selectors.getAllSortedIDs,
  );
  const isFieldListEmpty = fieldIDs?.length === 0;

  const { t } = useTranslate();

  const onChange = (
    option: string | null | undefined,
    selection: CustomDropdownOption,
  ) => {
    let method = null;
    switch (option) {
      case 'select':
        method = 'select_on_map';
        break;
      case 'create':
        method = 'draw_fields';
        break;
      case 'upload':
        method = 'upload_file';
        break;
    }

    logEvent('select_clip_plus_field', {
      method,
      clean_seasons: isFieldListEmpty,
      user_id: userID,
      season_id: currentSeason?.id ?? null,
    });

    onChangeProps(option, selection);
  };

  const onEvent = useCallback(
    isOpen => {
      if (!isOpen) {
        logEvent('field_create_dialog', {
          user_id: userID,
          season_id: currentSeason?.id ?? null,
        });
      }
    },
    [userID, currentSeason],
  );

  return (
    <CustomDropdown
      className={className}
      options={['select', 'create', 'upload'].map(id => ({
        label: t(`fields.welcome.${id}.action`) as string,
        id,
      }))}
      onToggle={onEvent}
      renderValue={renderValue}
      onChange={onChange}
    />
  );
};

export default CreateFieldDialogDropDown;
