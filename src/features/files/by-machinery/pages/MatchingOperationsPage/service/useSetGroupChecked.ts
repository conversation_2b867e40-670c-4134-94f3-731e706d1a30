import { MutableRefObject, useCallback } from 'react';
import { useDispatch } from 'react-redux';

import entities from 'modules/entities';
import { EntityOperation } from 'modules/entities/types';

import { DataStoreType } from 'features/files/by-machinery/pages/MatchingOperationsPage/service/useDataStore';
import { ApiDataFieldOperations } from '../../../types/server';

export type OnSetPropertyType = (
  operation: ApiDataFieldOperations,
  is_enabled: boolean,
) => void;

export const useSetGroupChecked = (
  dataStore: MutableRefObject<DataStoreType>,
): OnSetPropertyType => {
  const dispatch = useDispatch();

  return useCallback(
    (operation, is_enabled) => {
      const parentId = operation.uuid;

      const parentOperation = dataStore.current.operations.filter(
        v => v.uuid === parentId,
      );
      const childrenOperation = dataStore.current.operations.filter(
        v => v.parent_operation_uuid === parentId,
      );

      const requests: EntityOperation[] = parentOperation
        .concat(childrenOperation)
        .map(item => ({
          type: 'patch',
          entityType: 'fieldsDataOperation',
          path: {
            id: item.uuid,
          },
          query: {
            include: ['data_source.layer.properties'],
          },
          body: {
            is_enabled: is_enabled,
          },
        }));

      dispatch(entities.actions.request('setRequestId', requests));
    },
    [dataStore, dispatch],
  );
};
