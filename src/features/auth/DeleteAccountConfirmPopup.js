import React from 'react';
import { useSelector } from 'react-redux';

import Button from 'components/ui/Button/Button';
import RenderAboveEverything from 'components/RenderAboveEverything';

import auth from 'modules/auth';

import { useTranslate } from 'utils/use-translate';

const DeleteAccountPopup = ({ onClose }) => {
  const { t } = useTranslate();
  const email = useSelector(auth.selectors.getUserEmail);

  return (
    <RenderAboveEverything>
      <div className='modal modal-editseasons'>
        <div className='modal-outer'>
          <div className='modal-editseasons-content'>
            <div className='modal-editseasons-content__header'>
              {t('delete_account.confirm.title')}
            </div>
            <div className='modal-editseasons-content__body'>
              <div className='form-group'>
                {t('delete_account.confirm.email_description', {
                  email,
                })}
              </div>
            </div>
            <div className='modal-editseasons-content__actions'>
              <Button
                className='btn btn-success btn-lg'
                onClick={() => {
                  onClose();
                }}
              >
                {t('delete_account.confirm.button.success')}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </RenderAboveEverything>
  );
};

export default DeleteAccountPopup;
