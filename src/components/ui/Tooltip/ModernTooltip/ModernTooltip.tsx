import React, { ReactNode, useState, VFC, useRef } from 'react';

import {
  PopoverDeprecated,
  PopoverDeprecatedAlign,
  PopoverDeprecatedProps,
} from 'components/ui/Popover';
import {
  SimpleTooltip,
  SimpleTooltipProps,
} from 'components/ui/Tooltip/SimpleTooltip/SimpleTooltip';

export enum ModernTooltipDirection {
  FromTop = 'from-top',
  FromBottom = 'from-bottom',
  FromLeft = 'from-left',
  FromRight = 'from-right',
}

type ModernTooltipProps = {
  active?: boolean;
  animate?: ModernTooltipDirection;
  offset?: [number, number];
  renderTooltip: () => ReactNode;
  closeDelay?: number;
} & Omit<PopoverDeprecatedProps, 'renderPopover'> &
  Omit<SimpleTooltipProps, 'style'>;

export const ModernTooltip: VFC<ModernTooltipProps> = ({
  active,
  offset = [10, 0],
  align = PopoverDeprecatedAlign.MiddleRight,
  animate,
  renderTrigger,
  renderTooltip,
  closeDelay,

  className,
  maxWidth,
  onMouseEnter,
  onMouseLeave,
  onClick,
  arrow,
  theme,
  small,

  ...otherProps
}) => {
  const timer = useRef<NodeJS.Timeout | null>(null);
  const [hovered, setHovered] = useState<boolean>(false);

  const onTriggerMouseEnter = () => {
    if (timer.current) {
      clearTimeout(timer.current);
    }
    setHovered(true);
  };
  const onTriggerMouseLeave = () => {
    if (timer.current) {
      clearTimeout(timer.current);
    }
    if (closeDelay) {
      timer.current = setTimeout(setHovered, closeDelay, false);
    } else {
      setHovered(false);
    }
  };

  return (
    <PopoverDeprecated
      active={
        (active !== undefined && active !== null ? active : true) && hovered
      }
      offset={offset}
      align={align}
      {...otherProps}
      renderTrigger={props =>
        renderTrigger({
          // @ts-ignore
          onMouseEnter: onTriggerMouseEnter,
          onMouseLeave: onTriggerMouseLeave,
          ...props,
        })
      }
      renderPopover={({ style, ...props }) => (
        <SimpleTooltip
          className={className ?? ''}
          maxWidth={maxWidth}
          onMouseEnter={event => {
            onTriggerMouseEnter();
            onMouseEnter?.(event);
          }}
          onMouseLeave={event => {
            onTriggerMouseLeave();
            onMouseLeave?.(event);
          }}
          onClick={onClick}
          direction={animate}
          arrow={arrow}
          style={style}
          small={small}
          theme={theme}
          {...props}
        >
          {renderTooltip()}
        </SimpleTooltip>
      )}
    />
  );
};
