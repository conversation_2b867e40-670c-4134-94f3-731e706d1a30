import React, { VFC } from 'react';

import { colors } from 'constants/style';

import { useUndercoverPercentage } from 'features/files/colorSchemes/hooks/useUndercoverPercentage';
import { FileDataSourceType } from 'features/files/colorSchemes/types';
import {
  LegendPalette,
  LegendText,
  LegendBlock,
  SoilAnalysisCoverageLegend,
} from './styles';

const UndercoverFileLegend: VFC<FileDataSourceType> = dataSource => {
  const undercover = useUndercoverPercentage(dataSource);

  if (undercover === null) {
    return null;
  }

  return (
    <SoilAnalysisCoverageLegend>
      <LegendBlock>
        <LegendText>{undercover}</LegendText>
        <LegendPalette color={colors.undercoverColor} />
      </LegendBlock>
    </SoilAnalysisCoverageLegend>
  );
};

export default UndercoverFileLegend;
