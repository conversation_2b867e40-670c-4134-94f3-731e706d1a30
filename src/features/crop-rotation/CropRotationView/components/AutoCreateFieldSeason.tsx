import { useEffect, VFC } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import fieldSeason from 'modules/fieldSeason';

import {
  sendFirstFieldsEvents,
  // @ts-ignore
} from 'utils/fields-analytics';

type AutoCreateFieldSeasonProps = {
  seasonId: number;
  fieldId: number;
  fieldSeasonId?: number;
};

const AutoCreateFieldSeason: VFC<AutoCreateFieldSeasonProps> = ({
  seasonId,
  fieldId,
  fieldSeasonId,
}) => {
  const dispatch = useDispatch();

  const [prevFieldSeason, nextFieldSeason] = useSelector(stage => {
    return fieldSeason.selectors.getPrevAndNextFieldSeason(stage, {
      seasonId,
      fieldId,
    });
  });
  const seasonForGeom = prevFieldSeason || nextFieldSeason;

  useEffect(() => {
    if (!seasonForGeom?.id) {
      return;
    }
    dispatch(fieldSeason.actions.fetchGeom(seasonForGeom?.id));
  }, [seasonForGeom?.id, dispatch]);

  useEffect(() => {
    if (!fieldSeasonId && seasonForGeom?.geom) {
      dispatch(
        fieldSeason.actions.createFieldSeason({
          fieldId,
          seasonId,
          seasonForGeom,
        }),
      );

      sendFirstFieldsEvents([fieldId]);
    }
  }, [fieldSeasonId, seasonForGeom, fieldId, seasonId, dispatch]);

  return null;
};

export default AutoCreateFieldSeason;
