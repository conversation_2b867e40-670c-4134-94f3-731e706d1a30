import { css } from 'linaria';
import { styled } from 'linaria/react';
import chroma from 'chroma-js';
import { ExpandedKeys } from './types';
import { SortDirections } from 'constants/sortDirections';

export const styles = {
  cropRotationContainer: css`
    background: white;
    border-radius: 8px 0 0 8px;
    z-index: 4;
    display: flex;
    flex-direction: column;
    flex: 1;
  `,
  cropRotationHeader: css`
    position: relative;
  `,
  cropRotationStickyHeaderTop: css``,
  cropRotationStickyHeaderBottom: css``,
  cropRotationStickyHeaderRow: css`
    display: flex;
  `,
  cropRotationHeaderContent: css`
    display: flex;
    justify-content: space-between;
    padding: 20px 20px 16px;
    background: #ffffff;
    position: relative;
    z-index: 3;
  `,

  cropRotationHeaderRight: css`
    display: flex;

    button {
      margin-left: 10px;
    }
  `,

  cropRotationTitle: css`
    font-family: Graphik, sans-serif;
    font-weight: bold;
    font-size: 36px;
    line-height: 45px;
    margin: 0;
    padding: 0 20px;
  `,

  cropRotationTitleSticky: css`
    display: block;
    font-size: 24px;
    line-height: 32px;
    padding: 0 20px;
    font-weight: bold;
  `,

  cropRotationClose: css`
    position: absolute;
    right: 20px;
    top: 20px;
    width: 42px;
    height: 42px;
    border: none;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: #e4e7ea;
    transition: background-color 0.3s;
    &:hover {
      background-color: #d3d7db;
    }
  `,

  cropRotationContent: css`
    display: flex;
    flex-direction: column;
    flex-grow: 1;
  `,

  cropRotationBody: css`
    width: 100%;
    flex-grow: 1;
    .gridTopRight,
    .gridBottomLeft {
      -ms-overflow-style: none;
      scrollbar-width: none;
      &::-webkit-scrollbar {
        -webkit-appearance: none;
        width: 0;
        height: 0;
      }
    }
    .gridTopRight {
      border-bottom: 1px solid #e4e7ea;
    }
  `,

  gridRow: css`
    display: flex;
  `,

  cropRotationGrid: css`
    width: 100%;
    overflow: hidden !important;
  `,

  headerCell: css`
    display: block;
    padding: 10px;
  `,

  headerCellSticky: css`
    display: block;
    padding: 6px 10px;
  `,

  headerCellTop: css`
    cursor: pointer;
    color: #222222;
    transition: color 0.3s;
    &:hover {
      color: #5e5e5e;
      .season-header-edit-icon {
        opacity: 1;
        visibility: visible;
      }
    }
  `,

  headerCellTitle: css`
    display: flex;
    align-items: center;
  `,

  collapseTrigger: css`
    padding-left: 20px;
    padding-right: 20px;
    position: absolute;
    bottom: 0;
  `,

  seasonNameContainer: css`
    display: inline-flex;
    flex-shrink: 1;
    max-width: calc(100% - 23px);
  `,

  seasonName: css`
    font-family: Graphik, sans-serif;
    font-weight: 700;
    font-size: 24px;
    line-height: 32px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  `,

  seasonNameSticky: css`
    font-size: 20px;
    line-height: 27px;
  `,

  editIcon: css`
    width: 16px;
    height: 16px;
    margin-left: 7px;
    flex-shrink: 0;
    flex-grow: 0;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
    background-image: url("data:image/svg+xml,%3Csvg width='13' height='13' viewBox='0 0 13 13' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M0 10.0502V12.6945H2.64432L10.4433 4.89551L7.79897 2.2512L0 10.0502ZM12.4882 2.85057C12.7632 2.57557 12.7632 2.13132 12.4882 1.85631L10.8382 0.206257C10.5632 -0.0687523 10.1189 -0.0687523 9.84391 0.206257L8.55349 1.49668L11.1978 4.141L12.4882 2.85057Z' fill='%23A5B2BC'/%3E%3C/svg%3E%0A");
    background-position: center center;
    background-repeat: no-repeat;
  `,

  headerCellSubtitle: css`
    font-size: 12px;
    line-height: 16px;
    margin-bottom: 15px;
    color: #5e5e5e;
  `,

  headerCellSeasonData: css`
    font-size: 12px;
    line-height: 16px;
    font-weight: 400;
    color: #5e5e5e;
  `,

  headerCellStats: css`
    margin-top: 5px;
    line-height: 1;
  `,

  headerCellStatsText: css``,

  colHeaderInfoProgress: css`
    position: relative;
    height: 16px;
    width: 100%;
    background-color: #e4e7ea;
    border-radius: 2px;
    margin-bottom: 5px;
  `,

  stackBarHolder: css`
    margin-top: 15px;
  `,

  addLink: css`
    width: 100%;
    height: 100%;
    display: flex;
    cursor: pointer;
  `,

  cellLink: css`
    position: relative;
    width: 100%;
    display: flex;
    flex-direction: column;
    transition: outline-color 0.3s;
    outline: 2px solid transparent;
    outline-offset: -2px;
    cursor: pointer;
    /* &:hover,
    &.__active {
      outline-color: #007aff;
    } */
  `,

  cellText: css`
    display: block;
    max-width: 190px;
    font-size: 14px;
    line-height: 18px;
    color: #222222;

    &.__inactive {
      color: #a5b2bc;
      transition: color 0.3s;
    }
  `,

  oneLineBlock: css`
    max-width: 100%;
  `,

  oneLine: css`
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  `,

  cellVarietyText: css`
    margin-top: -2px;
    font-size: 12px;
    line-height: 16px;
    color: #222222;
  `,

  cellTextRow: css`
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 85%;
  `,

  cellDateText: css`
    font-size: 10px;
    line-height: 16px;
    color: #5e5e5e;
  `,

  cellDatesIcon: css`
    margin: 0 5px;
  `,

  cropGroups: css`
    flex: 0.5;
    position: relative;
    display: flex;
    flex-direction: column;
    margin-top: 2px;
    & > div {
      margin-top: 0;
    }
  `,

  moreCrops: css`
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    padding-left: 10px;
    padding-right: 10px;
    align-items: flex-start;
    justify-content: center;
  `,
  attentionIconContainer: css`
    position: absolute;
    width: 16px;
    height: 16px;
    right: 5px;
    bottom: 5px;
    cursor: pointer;
  `,
  attentionIconContainerInline: css`
    top: 0;
    bottom: 0;
    height: auto;
    display: inline-flex;
    align-items: center;
  `,
  attentionIcon: css`
    color: #ff3b30;
    width: 16px;
    height: 16px;
  `,
  attentionIconActive: css`
    color: #ff3b30;
  `,
  bottomLeft: css`
    box-shadow: 5px 0 4px -3px rgba(0, 0, 0, 0.07);
    -ms-overflow-style: none;
    scrollbar-width: none;
    z-index: 1;

    &::-webkit-scrollbar {
      display: none;
    }
  `,
  bottomRight: css`
    overflow-x: hidden !important;
    -ms-overflow-style: none;
    scrollbar-width: none;

    &::-webkit-scrollbar {
      display: none;
    }
  `,
  cropsTable: css``,
  cropName: css`
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  `,
  fieldMeta: css`
    position: relative;
    font-weight: 500;
    font-size: 14px;
    line-height: 14px;
    display: flex;
    align-items: flex-start;
    max-height: 100%;
    span {
      margin-left: 10px;
      margin-top: 5px;
      word-break: break-word;
    }

    ::after {
      content: '';
      position: absolute;
      width: 100%;
      height: 30px;
      bottom: -10px;
      left: 48px;
      background: linear-gradient(
        360deg,
        #ffffff 0%,
        rgba(255, 255, 255, 0) 100%
      );
    }
  `,
  fieldPreview: css`
    width: 48px;
    height: 48px;
    flex-shrink: 0;
    overflow: hidden;
    border-radius: 4px;
    background: #f5f7f9;
    display: flex;
    justify-content: center;
    align-items: center;

    img {
      max-width: 100%;
      height: auto;
    }
  `,
  fieldArea: css`
    flex-shrink: 0;
    margin-left: 4px;
  `,
  emptyContainer: css`
    width: 100%;
    position: relative;
    border: 1px solid #dbd5df;
    border-radius: 4px;
    background-color: #fff;
    transition: background-color 0.3s, color 0.3s, border-color 0.3s;
    color: #a5b2bc;
    /* outline: 2px solid transparent; */
    /* outline-offset: -2px; */

    &:hover,
    &.__active {
      /* outline-color: #007aff; */
      background-color: #ffffff;
      border-color: #b7c1c9;
      color: #5e5e5e;
      .add-crop {
        width: auto;
      }
    }
  `,
  add: css`
    -webkit-mask-image: -webkit-radial-gradient(white, black);
    height: 24px;
    padding-left: 24px;
    width: 0;
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    overflow: hidden;
    transition: width 0.3s;
    white-space: nowrap;
    &:before {
      content: '+';
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      position: absolute;
      left: 0;
      top: 0;
    }
  `,
  addCropSectionTitle: css`
    padding-left: 15px;
    font-size: 10px;
    line-height: 16px;
    color: #a5b2bc;
    text-transform: uppercase;
    letter-spacing: 1px;

    .modal-select__list + & {
      margin-top: 20px;
    }
  `,
  addField: css`
    display: flex;
    align-items: center;
    border: none;
    padding: 0;
    margin: 0;
    background: none;
    transition: color 0.3s;
    width: 100%;
    &:hover {
      color: #27ae60;
    }
  `,
  addFieldIcon: css`
    font-size: 24px;
  `,
  addFieldText: css`
    margin-left: 10px;
    font-size: 14px;
    font-weight: 500;
  `,

  cropForm: css`
    width: 360px;
    padding-top: 15px;
  `,

  cropFormBtnArea: css`
    padding: 0 20px;
    margin-top: 15px;
  `,
};

type StyledCropRotationStickyHeaderProps = {
  isSticky: boolean;
};
export const StyledCropRotationStickyHeader = styled.div<StyledCropRotationStickyHeaderProps>`
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  z-index: 2;
  transition: transform 0.3s;
  transform: ${props =>
    props.isSticky ? 'translateY(100%)' : 'translateY(0)'};
`;

type StyledCellProps = {
  columnIndex: number;
} & Omit<
  React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement>,
  'translate'
>;
export const StyledCell = styled.div<StyledCellProps>`
  /* border-bottom: 1px solid #f5f7f9;
  border-right: 1px solid #f5f7f9; */
  padding: 5px 10px;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  position: relative;
  overflow: hidden;
  border-left: 1px solid
    ${props => (props.columnIndex <= 1 ? 'transparent' : '#E4E7EA')};
`;

export const StyledTopLeftCell = styled.div`
  height: 100%;
`;

type StyledFieldsColumnCellProps = {
  rowIndex: number;
  activeRowIndex: number;
};
export const StyledFieldsColumnCell = styled(
  StyledCell,
)<StyledFieldsColumnCellProps>`
  align-items: flex-start;
  justify-content: space-between;
  padding: 10px 10px 10px 20px;
  font-size: 14px;
  line-height: 14px;
  font-weight: 400;
  border-bottom: 1px solid #e4e7ea;
  border-left: none;
  background-color: ${props =>
    props.activeRowIndex === props.rowIndex ? '#F5F7F9' : 'initial'};
  &:after {
    display: none;
  }
  &:last-child {
    border-bottom: none;
  }
`;

type StyledFieldsHeaderCellProps = {
  columnIndex: number;
};
export const StyledFieldsHeaderCell = styled(
  StyledCell,
)<StyledFieldsHeaderCellProps>`
  align-items: flex-end;
  justify-content: space-between;
  padding-left: ${props => (props.columnIndex === 0 ? '20px' : '10px')};
  border-left: 1px solid
    ${props => (props.columnIndex <= 1 ? 'transparent' : '#E4E7EA')};
  border-bottom: 1px solid #e4e7ea;
`;

export const StyledAddCrop = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  cursor: pointer;
  border-radius: 4px;
`;

type StyledEmptyContainerProps = {
  active: boolean;
};

export const StyledEmptyContainer = styled.div<StyledEmptyContainerProps>`
  width: 100%;
  position: relative;
  border: 1px solid #dbd5df;
  border-radius: 4px;
  background-color: #fff;
  transition: background-color 0.3s, color 0.3s, border-color 0.3s;
  color: #a5b2bc;
  /* outline: 2px solid transparent; */
  /* outline-offset: -2px; */
  box-shadow: ${props => (props.active ? '0 2px 4px 0 rgba(0,0,0,0.2)' : '')};
  &:hover,
  &.__active {
    /* outline-color: #007aff; */
    background-color: #ffffff;
    border-color: #b7c1c9;
    color: #5e5e5e;
    .add-crop {
      width: auto;
    }
  }
`;

type StyledCollapseTriggerProps = {
  state: ExpandedKeys;
};

export const StyledCollapseTrigger = styled.button<StyledCollapseTriggerProps>`
  font-weight: 500;
  font-size: 14px;
  line-height: 18px;
  display: flex;
  align-items: center;
  color: #222222;
  transition: color 0.3s;
  border: none;
  background: none;
  &:hover {
    color: var(--color-primary);
  }
  .collapse-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: #e4e7ea;
    margin-right: 5px;
    transition: transform 0.3s;
    transform: ${props =>
      props.state === 'expanded' ? 'rotate(0.5turn)' : 'rotate(0)'};
  }
`;

type StyledSortButtonProps = {
  sort?: SortDirections;
};

export const StyledSortButton = styled.button<StyledSortButtonProps>`
  border: none;
  padding: 0 12px 0 0;
  color: #5e5e5e;
  font-size: 14px;
  line-height: 18px;
  position: relative;
  background: transparent;
  font-weight: ${props =>
    props.sort === SortDirections.ASCENDING ||
    props.sort === SortDirections.DESCENDING
      ? 500
      : 400};
  transition: color 0.3s;
  &:hover {
    color: #222222;
  }

  &:before,
  &:after {
    content: '';
    width: 4px;
    height: 4px;
    border-left: 1px solid #a5b2bc;
    border-bottom: 1px solid #a5b2bc;
    position: absolute;
    right: 0;
  }

  &:before {
    top: 5px;
    transform: rotate(135deg);
    border-color: ${props =>
      props.sort === SortDirections.ASCENDING ? '#5E5E5E' : '#A5B2BC'};
  }

  &:after {
    bottom: 6px;
    transform: rotate(-45deg);
    border-color: ${props =>
      props.sort === SortDirections.DESCENDING ? '#5E5E5E' : '#A5B2BC'};
  }
`;

export type StyledHeaderCellProps = {
  columnIndex: number;
} & StyledCellProps;

export const StyledHeaderCell = styled(StyledCell)<StyledHeaderCellProps>`
  display: block;
  overflow: hidden;
  border-left: 1px solid
    ${props => (props.columnIndex === 1 ? 'transparent' : '#E4E7EA')};
  padding: 0;
  a {
    display: block;
    padding: 10px;
  }
  /* &:hover {
    background-color: #f5f7f9;
  } */
`;

type StyledCropGroupWrapProps = {
  flex: string | number;
};
export const StyledCropGroupWrap = styled.div<StyledCropGroupWrapProps>`
  display: flex;
  flex-direction: column;
  width: 100%;
  flex: ${props => props.flex};
  margin-top: 2px;

  &:first-child {
    margin-top: 0;
  }
`;

type StyledCropGroupProps = {
  flex: string | number;
  active?: boolean;
  isEmpty?: boolean;
  color?: string;
};

export const StyledCropGroup = styled.div<StyledCropGroupProps>`
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 5px 10px 3px;
  align-items: flex-start;
  justify-content: space-between;
  flex: ${props => props.flex};
  border-radius: 4px;
  border-left: ${props => `6px solid ${props.color}`};
  transition: background-color 0.3s;
  background-color: ${props =>
    props.color ? chroma(props.color).alpha(0.2).hex() : 'initial'};
  margin-top: 2px;
  overflow: hidden;
  box-shadow: ${props => (props.active ? '0 2px 4px 0 rgba(0,0,0,0.2)' : '')};

  &:first-child {
    margin-top: 0;
  }
  &:hover {
    background-color: ${props =>
      !props.isEmpty
        ? props.color
          ? chroma(props.color).alpha(0.4).hex()
          : 'initial'
        : '#E4E7EA'};
    border-left-color: ${props =>
      props.isEmpty ? '#D5DBDF' : `${props.color}`};
    .__inactive {
      color: ${props => (props.isEmpty ? 'transparent' : 'inherit')};
    }
    .add-crop {
      opacity: 1;
    }
  }

  .add-crop {
    color: #5e5e5e;
    opacity: 0;
    width: auto;
    transition: opacity 0.3s;
  }
`;

type StyledCropsTableCellProps = {
  rowIndex: number;
  columnIndex: number;
};

export const StyledCropsTableCell = styled.div<StyledCropsTableCellProps>`
  height: 100%;
  display: flex;
  align-items: ${props => (props.rowIndex === 0 ? 'flex-end' : 'flex-start')};
  justify-content: ${props =>
    props.columnIndex === 0 ? 'flex-start' : 'flex-end'};
  font-family: Graphik, sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  padding: 8px 10px 8px 20px;
  border-left: 1px solid
    ${props =>
      props.columnIndex === 0 || props.columnIndex === 1
        ? 'transparent'
        : '#E4E7EA'};
  background-color: ${props =>
    props.rowIndex % 2 === 0 ? 'white' : '#F5F7F9'};
`;

export const StyledCropsHeaderCell = styled(StyledCropsTableCell)`
  font-weight: 700;
  padding-top: 26px;
`;

// @ts-ignore
export const StyledCropColorBar = styled.div<{ color: string }>`
  background-color: ${props => props.color};
  width: 8px;
  height: 15px;
  margin-right: 6px;
`;

export const StyledAutoRotateCell = styled.div<{ columnIndex: number }>`
  padding: 15px 10px;
  display: flex;
  border-left: 1px solid
    ${props => (props.columnIndex === 1 ? 'transparent' : '#E4E7EA')};
  .btn {
    width: 100%;
    font-weight: 500;
  }
`;

type StyledSkeletonCellProps = {
  width: number;
  height: number;
  marginTop: number;
  marginBottom: number;
  marginLeft: number;
  marginRight: number;
};

export const StyledSkeletonCell = styled.div<StyledSkeletonCellProps>`
  width: ${props => `${props.width}px`};
  height: ${props => `${props.height}px`};
  margin-top: ${props => `${props.marginTop}px`};
  margin-bottom: ${props => `${props.marginBottom}px`};
  margin-left: ${props => `${props.marginLeft}px`};
  margin-right: ${props => `${props.marginRight}px`};
  background-color: #e4e7ea;
  border-radius: 3px;
  mask-image: linear-gradient(
    to right,
    transparent 0%,
    black 25%,
    black 75%,
    transparent 100%
  );
  mask-size: 200% 100%;
  mask-repeat: repeat;
  mask-position: 50% top;
  animation: skeleton-effect-wave 1s infinite;
  @keyframes skeleton-effect-wave {
    0% {
      mask-position: 50% top;
    }
    100% {
      mask-position: -150% top;
    }
  }
`;

type StyledKebabButtonProps = {
  emptyField?: boolean;
};

export const StyledKebabButton = styled.button<StyledKebabButtonProps>`
  position: absolute;
  width: 20px;
  height: 20px;
  border: none;
  right: 13px;
  top: 8px;
  border-radius: 2px;
  background-color: ${props =>
    props.emptyField ? '#f5f7f9' : 'rgba(255, 255, 255, 0.5)'};
  transition: background-color 0.3s, opacity 0.3s, visibility 0.3s;
  opacity: 0;
  visibility: hidden;
  background-image: url("data:image/svg+xml,%3Csvg width='14' height='4' viewBox='0 0 14 4' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M2 3.75a1.75 1.75 0 100-3.5 1.75 1.75 0 000 3.5zM8.75 2a1.75 1.75 0 11-3.5 0 1.75 1.75 0 013.5 0zm5 0a1.75 1.75 0 11-3.5 0 1.75 1.75 0 013.5 0z' fill='%23222'/%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: 14px;
  &:hover {
    background-color: ${props =>
      props.emptyField ? '#e4e7ea' : 'rgba(255, 255, 255, 1)'};
  }
`;

type StyledFieldsCropsCellProps = {
  isActive: boolean;
};

export const StyledFieldsCropsCell = styled(
  StyledCell,
)<StyledFieldsCropsCellProps>`
  background-color: ${props => (props.isActive ? '#F5F7F9' : 'initial')};
  &:hover {
    ${StyledKebabButton} {
      opacity: 1;
      visibility: visible;
    }
  }
`;
