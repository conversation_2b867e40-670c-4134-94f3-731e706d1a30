import React, {
  useState,
  useMemo,
  useCallback,
  useEffect,
  useContext,
} from 'react';
import EventEmitter from 'events';

const dummyEventEmitter = new EventEmitter();
export const ChartSyncContext = React.createContext(dummyEventEmitter);

export const useCursorSyncer = () => {
  const eventEmitter = useContext(ChartSyncContext);

  return useCallback(
    hoveredPoints => {
      eventEmitter.emit('update-cursor', hoveredPoints);
    },
    [eventEmitter],
  );
};

export const useZoomSyncer = () => {
  const eventEmitter = useContext(ChartSyncContext);
  const [zoomDomain, setZoomDomain] = useState();

  useEffect(() => {
    eventEmitter.on('update-zoom', setZoomDomain);
    return () => eventEmitter.off('update-zoom', setZoomDomain);
  }, [eventEmitter]);

  const updateZoom = useCallback(
    zoomDomain => {
      eventEmitter.emit('update-zoom', zoomDomain);
    },
    [eventEmitter],
  );

  return [zoomDomain, updateZoom];
};

export const useHoveredPoints = () => {
  const [hoveredPoints, setHoveredPoints] = useState([]);
  const eventEmitter = useContext(ChartSyncContext);

  useEffect(() => {
    eventEmitter.on('update-cursor', setHoveredPoints);
    return () => eventEmitter.off('update-cursor', setHoveredPoints);
  }, [eventEmitter]);

  return hoveredPoints;
};

const ChartSyncProvider = ({ children }) => {
  const eventEmitter = useMemo(() => new EventEmitter(), []);

  return (
    <ChartSyncContext.Provider value={eventEmitter}>
      {children}
    </ChartSyncContext.Provider>
  );
};

export default ChartSyncProvider;
