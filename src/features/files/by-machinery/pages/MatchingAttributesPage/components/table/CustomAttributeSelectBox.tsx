import React, { VFC } from 'react';
import { useSelector } from 'react-redux';

import entities from 'modules/entities';

import { CustomDropdownOption } from 'components/ui/CustomDropdown/types';
import { SelectBox } from 'components/ui/CustomDropdown/SelectBox';

import {
  ApiDataFieldLayer,
  MetaAttributesApi,
  TasksByMachineryMeta,
} from 'features/files/by-machinery/types';

import {
  getSetPropertyRequestName,
  OnSetPropertyType,
} from 'features/files/by-machinery/pages/MatchingAttributesPage/service/useSetAttribute';

import { useSingletonSelector } from 'utils/optimization/useSingletonSelector';
import { deepEqualDecorator } from 'utils/optimization/deepEqualDecorator';
import { useDelayedState } from 'utils/optimization/useDelayedState';

import { attributeSelectBoxWrapper, Skeleton } from './styles';

type AttributeSelectorProps = {
  attribute: { id: string; name: string; units: [] };
  layer: ApiDataFieldLayer;
  index: number;
  onSetAttributeValue: OnSetPropertyType;
  onSetSelectValue: (index: number, value: string | null) => void;
  meta: TasksByMachineryMeta;
};

export const optionsCalculation = deepEqualDecorator(
  ([properties]: [
    ApiDataFieldLayer['properties'],
    MetaAttributesApi['allowed_types'],
  ]) =>
    Object.values(properties)
      .map<CustomDropdownOption>(property => ({
        id: property.name,
        label: property.name,
        subLabelItems: (property.sample_values || []).filter(
          s => typeof s === 'string',
        ) as string[],
      }))
      .sort((a, b) => a.id?.localeCompare(b.id)),
);

export const CustomAttributeSelectBox: VFC<AttributeSelectorProps> = ({
  attribute,
  layer,
  onSetAttributeValue,
  index,
  onSetSelectValue,
  meta,
}) => {
  const properties = layer.properties.filter(
    property => property.is_custom_target_name,
  );

  const currentProperty = properties.find(
    property => property.target_name === attribute.name,
  );

  const value = currentProperty?.name;
  const metaProperty =
    meta?.groups.length > 0
      ? meta.groups[0]!.properties.find(v => v.name === 'custom')
      : null;

  const options: CustomDropdownOption[] = useSingletonSelector(
    optionsCalculation,
    [layer.properties, metaProperty?.allowed_types || []],
  );

  const status = useSelector(state =>
    entities.selectors.getEntityStatus(
      state,
      'fieldsDataLayerProperty',
      currentProperty?.uuid,
      'target_name',
    ),
  );

  const setRequestId = getSetPropertyRequestName(attribute.name, layer.uuid);
  const setRequest = useSelector(state =>
    entities.selectors.getRequestStatus(state, setRequestId),
  );
  const isLoading = useDelayedState(
    setRequest.status === 'pending' || status === 'pending',
  );
  if (isLoading) {
    return <Skeleton />;
  }

  return (
    <>
      <div style={{ width: '100%' }}>
        <SelectBox
          options={options}
          value={value}
          wrapperClassName={attributeSelectBoxWrapper}
          onChange={value => {
            onSetAttributeValue(value, attribute.name, layer.uuid);
            onSetSelectValue(index, value);
          }}
          noFoundedText='import.matching.attributes.select.empty'
          withSearchBox
          placeholder='import.matching.attributes.select'
          searchPlaceholder='import.matching.attributes.select_search'
        />
      </div>
    </>
  );
};

export const CustomAttributeSelectBoxMemo = React.memo(
  CustomAttributeSelectBox,
);
