import React, { useEffect, VFC } from 'react';
import cx from 'classnames';
import { Actions, DownshiftState, PropGetters } from 'downshift';
import { styled } from 'linaria/react';

import { Popover, PopoverProps } from 'components/ui/Popover';
import { DropdownSearchBox } from './DropdownSearchBox';
import { DropdownItem } from './DropdownItem';
import { CustomDropdownOption } from '../types';
import { Placement } from '@floating-ui/react';

const CompactInnerStyle = styled.div`
  &.modal-select__inner {
    padding: 5px 0;
  }
`;

const CompactButtonStyle = styled.div`
  &.form-select {
    font-size: 12px;
    font-weight: 500;
    line-height: 130%;
    min-height: stretch;
    padding: 5px 4px;
  }
`;

export type CustomDropdownContentProps<T> = {
  value?: string | null;
  onVisibilityToggle?: (isOpen: boolean) => void;
  baseClassName?: string;
  disabled?: boolean;
  withSearchBox?: boolean;
  onToggle?: (isOpen: boolean) => void;
  renderValue: (props?: {
    value?: string | null;
    isOpen: boolean;
  }) => React.ReactNode;
  contentForEmptySearch?: React.ReactNode;
  customStyleClassName?: string;

  searchValue: string;
  handleChangeSearch: (event: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
  PopoverComponent: typeof Popover;
  placement?: Placement;
  resetSearch: () => void;
  visibleMainList: boolean;
  commonItemList: CustomDropdownOption[];
  idKey?: string;
  isCheckedItem: (id: string) => boolean;
  isDisabledItem: (id: string) => boolean;
  compactItems?: boolean;

  getInputProps: PropGetters<T>['getInputProps'];
  getRootProps: PropGetters<T>['getRootProps'];
  getMenuProps: PropGetters<T>['getMenuProps'];
  getToggleButtonProps: PropGetters<T>['getToggleButtonProps'];
  getItemProps: PropGetters<T>['getItemProps'];
  isOpen: DownshiftState<T>['isOpen'];
  highlightedIndex: DownshiftState<T>['highlightedIndex'];
  setHighlightedIndex: Actions<T>['setHighlightedIndex'];
  sizeByParent?: PopoverProps['sizeByParent'];
};

export const CustomDropdownContent: VFC<
  CustomDropdownContentProps<TEMPORARY_ANY>
> = ({
  setHighlightedIndex,
  onVisibilityToggle,
  isOpen,
  baseClassName,
  disabled,
  getToggleButtonProps,
  onToggle,
  renderValue,
  value,
  withSearchBox,
  searchValue,
  handleChangeSearch,
  placeholder,
  resetSearch,
  getInputProps,
  visibleMainList,
  getMenuProps,
  commonItemList,
  idKey = 'id',
  contentForEmptySearch = '',
  highlightedIndex,
  PopoverComponent,
  placement,
  getItemProps,
  isCheckedItem,
  isDisabledItem,
  sizeByParent,
  customStyleClassName,
  compactItems,
}) => {
  useEffect(() => {
    onVisibilityToggle?.(isOpen);

    return () => onVisibilityToggle?.(false);
  }, [isOpen, onVisibilityToggle]);

  /* Downshift state support null as highlightedIndex
  But doesn't support setting it to null */
  return (
    //@ts-ignore
    <div onMouseLeave={() => setHighlightedIndex(null)}>
      <PopoverComponent
        open={isOpen}
        sizeByParent={sizeByParent}
        placement={placement}
        renderTrigger={props => {
          const ButtonComponent = compactItems ? CompactButtonStyle : 'div';
          return (
            <ButtonComponent
              {...props}
              className={cx([
                baseClassName,
                {
                  'form-select--active': isOpen,
                  'form-select--disabled': disabled,
                },
              ])}
              tabIndex={1}
              {...getToggleButtonProps({
                onClick: () => {
                  onToggle?.(isOpen);
                },
              })}
            >
              {renderValue({
                value,
                isOpen,
              })}
            </ButtonComponent>
          );
        }}
        renderPopover={props => (
          <div
            {...props}
            className={cx('modal-select', customStyleClassName)}
            data-searchbox={`${withSearchBox}`}
          >
            {withSearchBox && (
              <DropdownSearchBox
                getInputProps={getInputProps}
                searchValue={searchValue}
                handleChangeSearch={handleChangeSearch}
                placeholder={placeholder}
                resetSearch={resetSearch}
              />
            )}
            {commonItemList.length === 0 && contentForEmptySearch}
            {visibleMainList &&
              (compactItems ? (
                <CompactInnerStyle
                  className='modal-select__inner'
                  {...getMenuProps()}
                >
                  <ul className='modal-select__list'>
                    {commonItemList.map((item, index) => {
                      return (
                        <DropdownItem
                          key={item.id}
                          item={item}
                          index={index}
                          idKey={idKey}
                          highlightedIndex={highlightedIndex}
                          getItemProps={getItemProps}
                          isCheckedItem={isCheckedItem}
                          isDisabledItem={isDisabledItem}
                          compactItems={compactItems}
                        />
                      );
                    })}
                  </ul>
                </CompactInnerStyle>
              ) : (
                <div className='modal-select__inner' {...getMenuProps()}>
                  <ul className='modal-select__list'>
                    {commonItemList.map((item, index) => {
                      return (
                        <DropdownItem
                          key={item.id}
                          item={item}
                          index={index}
                          idKey={idKey}
                          highlightedIndex={highlightedIndex}
                          getItemProps={getItemProps}
                          isCheckedItem={isCheckedItem}
                          isDisabledItem={isDisabledItem}
                          compactItems={compactItems}
                        />
                      );
                    })}
                  </ul>
                </div>
              ))}
          </div>
        )}
      />
    </div>
  );
};
