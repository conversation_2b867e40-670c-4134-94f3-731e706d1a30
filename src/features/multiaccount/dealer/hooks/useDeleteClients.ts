import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import entities from 'modules/entities';
import { DealerClient } from 'types/multiaccount';
import multiaccountModule from 'modules/multiaccount';

export const REQUEST_DELETE_CLIENTS_ID = 'dealer-delete-send';

export const useDeleteClients = (uuids: string[], onFinish: () => void) => {
  const dispatch = useDispatch();
  const clients: DealerClient[] = useSelector(
    multiaccountModule.selectors.getAllDealerRecords,
  );

  const onSubmit = () => {
    dispatch(
      entities.actions.request(
        REQUEST_DELETE_CLIENTS_ID,
        uuids.map(uuid => ({
          type: 'delete',
          entityType:
            clients.find(item => item.uuid === uuid)?.status === 'active'
              ? 'dealerWorkspace'
              : 'dealerInvitation',
          path: {
            id: uuid,
          },
        })),
      ),
    );
  };

  const request = useSelector(state => {
    return entities.selectors.getRequestStatus(
      state,
      REQUEST_DELETE_CLIENTS_ID,
    );
  });

  useEffect(() => {
    if (request.status === 'resolved') {
      onFinish();

      // refetch workspace and membership to delete w and m connected to deleted client
      dispatch(
        entities.actions.request('fetch-memberships', [
          {
            type: 'fetchMany',
            entityType: 'workspace',
            query: { 'include[]': 'workspace_account' },
          },
          { type: 'fetchMany', entityType: 'membership' },
        ]),
      );

      dispatch(entities.actions.clearRequest(REQUEST_DELETE_CLIENTS_ID));
    }
  }, [request.status, onFinish, dispatch]);

  return {
    onSubmit,
    request,
  };
};
