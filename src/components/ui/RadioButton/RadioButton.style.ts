import { styled } from 'linaria/react';

export const StyledValue = styled.span`
  font-variant-ligatures: normal;
  padding-left: 6px;
  flex-shrink: 1;
  flex-grow: 1;
  flex-basis: auto;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

export const StyledLabel = styled.label<{ checked: boolean }>`
  cursor: pointer;
  display: inline-flex;
  vertical-align: top;
  font-size: 1rem;
  line-height: 1.25rem;
  min-height: 1.5rem;
  padding: 4px 0;
  align-items: center;
  width: 100%;
  overflow: ${props => (props.checked ? 'hidden' : 'visible')};
`;

export const StyledInputEl = styled.span`
  position: relative;
  width: 20px;
  height: 20px;
  border-radius: 5px;
  border: 2px solid rgba(165, 178, 188, 0.3);
  flex: 0 0 auto;
  background-color: rgba(39, 174, 96, 0);
  transition: opacity 0.15s, border-width 0.15s, border-color 0.15s,
    background-color 0.15s;
  &:before {
    content: '';
    position: absolute;
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 13 10' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cdefs%3E%3Cpath d='M49.59,636.08l-3.17,-3.17l-1.42,1.41l4.59,4.59l8,-8l-1.41,-1.41z' id='a'/%3E%3C/defs%3E%3Cg transform='translate(-45 -629)'%3E%3Cuse xlink:href='%23a' fill='%23fff'/%3E%3C/g%3E%3C/svg%3E");
    background-size: contain;
    background-position: 50%;
    width: 13px;
    height: 10px;
    opacity: 0;
    left: 0;
    margin: 5px 0 0 4px;
    transform: translate(-2px, -7px);
    transition: transform 0.15s, opacity 0.15s;
  }
`;

export const StyledInput = styled.input`
  position: absolute;
  left: -9999px;
  opacity: 0;
  &[type='radio'] {
    + ${StyledInputEl} {
      border-radius: 20px;
      &:after {
        position: absolute;
        content: '';
        left: 50%;
        top: 50%;
        margin-left: -4px;
        margin-top: -4px;
        transform-origin: center;
        background-color: #fff;
        width: 8px;
        height: 8px;
        opacity: 0;
        border-radius: 50%;
        transform: scale(2.5);
        transition: transform 0.25s, opacity 0.25s;
      }
    }
    &:checked {
      + ${StyledInputEl} {
        background-color: var(--color-primary);
        border-width: 0;
        &:after {
          opacity: 1;
          transform: scale(1);
        }
      }
      ~ ${StyledValue} {
        flex: 1 1 auto;
      }
      &:disabled {
        + ${StyledInputEl} {
          &:after {
            background-color: #c5cdd4;
          }
        }
      }
    }

    &:disabled {
      + ${StyledInputEl} {
        background-color: rgba(165, 178, 188, 0.2) !important;
        border-width: 0;
      }
    }
  }
`;
