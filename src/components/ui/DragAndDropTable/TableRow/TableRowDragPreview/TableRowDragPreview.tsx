import React from 'react';
import { cx } from 'linaria';
import {
  tableCell,
  tableCellDragPreview,
  DragPreviewIconContainer,
  DragPreviewChildrenContainer,
} from 'components/ui/DragAndDropTable/TableRow/TableRow.style';
import Icon from 'components/Icon';

export const TableRowDragPreview = ({
  children,
}: React.PropsWithChildren<{}>) => {
  return (
    <div className={cx(tableCell, tableCellDragPreview)}>
      <div className={DragPreviewIconContainer}>
        <Icon name='drag' />
      </div>
      <div className={DragPreviewChildrenContainer}>{children}</div>
    </div>
  );
};
