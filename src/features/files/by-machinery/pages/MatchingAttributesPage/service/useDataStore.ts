import { useRef } from 'react';

import {
  TasksByMachineryMeta,
  ApiDataFieldLayer,
} from 'features/files/by-machinery/types';
import { LayersLinksType } from 'features/files/by-machinery/pages/MatchingAttributesPage/utils/createLayersLinks';

export type DataStoreType = {
  meta: Nullable<TasksByMachineryMeta>;
  dataLayers: ApiDataFieldLayer[];
  layersLinks: LayersLinksType;
};

export const useDataStore = (data: DataStoreType) => {
  const dataStore = useRef<DataStoreType>(data);

  dataStore.current = data;

  return dataStore;
};
