import { css } from 'linaria';

const tableBackground = 'white';
const tableBorder = '1px solid #e4e7ea';
const groupColor = '#27ae60';
const hoveredBackgroundColor = '#f5f7f9';
const borderRadius = '8px';
const dragDropBackgroundColor = '#d4efdf';

export const SideControlWidth = 28;
export const SideControlWidthPx = '28px';

//Cells
export const tableCell = css`
  background-color: ${tableBackground};
  line-height: 18px;
  padding: 2px 4px;
  white-space: nowrap;
`;

export const tableCellHovered = css`
  background-color: ${hoveredBackgroundColor};

  /* Line with hovered color to cover border of previous td */
  &::before {
    position: absolute;
    z-index: 2;
    content: '';
    width: calc(100% - 28px);
    height: 1px;
    top: -1px;
    left: 28px;
    background-color: ${hoveredBackgroundColor};
  }
`;

export const tableCellDragDropHovered = css`
  background-color: ${dragDropBackgroundColor};

  /* Line with hovered color to cover border of previous td */
  &::before {
    position: absolute;
    z-index: 2;
    content: '';
    width: calc(100% - 28px);
    height: 1px;
    top: 0;
    left: 28px;
  }
`;

export const tableCellFixed = css`
  position: sticky;
  z-index: 1;
`;

export const tableCellFixedLast = css`
  border-right: ${tableBorder};
`;

export const tableCellDragPreview = css`
  padding: 0;
  border-radius: ${borderRadius};
  background-color: ${hoveredBackgroundColor};
  display: inline-flex;
  align-items: center;
  filter: drop-shadow(0px 6px 12px rgba(0, 0, 0, 0.2));
`;

export const DragPreviewIconContainer = css`
  min-width: 28px;
  height: 39px;
  display: flex;
  justify-content: center;
  align-items: center;
`;

export const DragPreviewChildrenContainer = css`
  padding: 10px 12px;
`;

//Side controls
export const SideControlElement = css``;

export const SideControl = css`
  padding: 0;
  height: 39px;
  border-bottom: none;
  display: inline-block;

  & .${SideControlElement} {
    position: relative;
    top: -1px;
    height: calc(100% + 1px);
    min-width: 28px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &.${tableCellDragDropHovered} {
    background-color: ${tableBackground};

    &::after {
      top: 0;
      left: 0;
      position: absolute;
      content: '';
      height: 100%;
      width: 100%;
      background-color: ${dragDropBackgroundColor};
    }
  }
`;

export const SideControlHidden = css`
  & .${SideControlElement} {
    visibility: hidden;
  }
`;

export const SideControlHovered = css`
  & .${SideControlElement} {
    background-color: ${hoveredBackgroundColor};
  }
`;

export const tableRowLeftSideControl = css`
  position: sticky;
  z-index: 1;
  left: 0;
  width: 100%;

  & .${SideControlElement} {
    border-top-left-radius: ${borderRadius};
    border-bottom-left-radius: ${borderRadius};
  }

  &.${tableCellDragDropHovered} {
    &::after {
      border-top-left-radius: ${borderRadius};
      border-bottom-left-radius: ${borderRadius};
    }
  }
`;

export const tableRowRightSideControl = css`
  & .${SideControlElement} {
    border-top-right-radius: ${borderRadius};
    border-bottom-right-radius: ${borderRadius};
  }

  &.${tableCellDragDropHovered} {
    &::after {
      border-top-right-radius: ${borderRadius};
      border-bottom-right-radius: ${borderRadius};
    }
  }
`;

//Row
export const tableRow = css`
  position: relative;
  background-color: ${tableBackground};
  border-radius: ${borderRadius};

  &:not(:last-child) td {
    border-bottom: ${tableBorder};

    &.${tableCellHovered} {
      border-bottom-color: ${hoveredBackgroundColor};
    }

    &.${SideControl} {
      border-bottom: none;
    }
  }
`;

export const hoveredRow = css`
  background-color: ${hoveredBackgroundColor};
`;

export const draggingRow = css`
  & td {
    opacity: 0;
  }
`;

export const tableCellGroupStart = css`
  border-left: 1px solid ${groupColor};
`;

export const moveFromGroupLine = css`
  content: '';
  position: absolute;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: ${groupColor};
  z-index: 3;
  opacity: 1;

  &::before,
  &::after {
    content: '';
    position: absolute;
    bottom: -2px;
    border-radius: 50%;
    width: 5px;
    height: 5px;
    background-color: ${groupColor};
  }

  &::before {
    left: 0;
  }

  &::after {
    right: 0;
  }
`;

export const moveFromGroupLineTop = css`
  top: 0;
`;

export const moveFromGroupLineBottom = css`
  bottom: 0;
`;

//Header
export const tableHeaderCell = css`
  font-weight: 500;
  border-bottom: ${tableBorder};
  padding: 0 12px;
`;

export const tableHeaderCellContent = css`
  display: flex;
`;

export const tableHeaderCellSortIcon = css`
  transition: 0.5s;
`;

export const tableHeaderCellSortIconDesc = css`
  transform: rotateZ(180deg);
`;

export const tableCellFixedHeader = css`
  /* this block hide group label under fixed header column */
  &:before {
    position: absolute;
    content: '';
    bottom: 100%;
    /* it's moved in left to cover leftSideControl, which is absent in header */
    left: -28px;
    /* column width plus control width plus border */
    width: calc(100% + 28px + 1px);
    height: 20px;
    background: ${tableBackground};
  }
`;

export const tableCellGroupStartHeader = css`
  position: relative;
`;

export const columnGroupHeaderLabel = css`
  color: ${groupColor};
  font-size: 14px;
  line-height: 18px;
  white-space: nowrap;
  padding-right: 16px;
`;

export const columnGroupHeaderCell = css``;

export const tableCellActive = css`
  cursor: pointer;
`;
