import React, { VFC } from 'react';
import { Trans } from 'react-i18next';
import {
  StyledPage,
  styles,
} from 'components/WhatsNew/WhatsNewContent/WhatsNewContent.style';
import Button from 'components/ui/Button/Button';

import WhatsNewImgEn from 'assets/images/whatsnew/whatsnew_croprotation_v2_en.png';
import WhatsNewImgEn2x from 'assets/images/whatsnew/<EMAIL>';
import WhatsNewImgDe from 'assets/images/whatsnew/whatsnew_croprotation_v2_de.png';
import WhatsNewImgDe2x from 'assets/images/whatsnew/<EMAIL>';
import WhatsNewImgFr from 'assets/images/whatsnew/whatsnew_croprotation_v2_fr.png';
import WhatsNewImgFr2x from 'assets/images/whatsnew/<EMAIL>';
import WhatsNewImgHu from 'assets/images/whatsnew/whatsnew_croprotation_v2_hu.png';
import WhatsNewImgHu2x from 'assets/images/whatsnew/<EMAIL>';
import WhatsNewImgIt from 'assets/images/whatsnew/whatsnew_croprotation_v2_it.png';
import WhatsNewImgIt2x from 'assets/images/whatsnew/<EMAIL>';
import WhatsNewImgPt from 'assets/images/whatsnew/whatsnew_croprotation_v2_pt.png';
import WhatsNewImgPt2x from 'assets/images/whatsnew/<EMAIL>';
import WhatsNewImgRu from 'assets/images/whatsnew/whatsnew_croprotation_v2_ru.png';
import WhatsNewImgRu2x from 'assets/images/whatsnew/<EMAIL>';
import WhatsNewImgEs from 'assets/images/whatsnew/whatsnew_croprotation_v2_es.png';
import WhatsNewImgEs2x from 'assets/images/whatsnew/<EMAIL>';
import WhatsNewImgUk from 'assets/images/whatsnew/whatsnew_croprotation_v2_uk.png';
import WhatsNewImgUk2x from 'assets/images/whatsnew/<EMAIL>';
import WhatsNewImgPl from 'assets/images/whatsnew/whatsnew_croprotation_v2_pl.png';
import WhatsNewImgPl2x from 'assets/images/whatsnew/<EMAIL>';
import WhatsNewImgTr from 'assets/images/whatsnew/whatsnew_croprotation_v2_tr.png';
import WhatsNewImgTr2x from 'assets/images/whatsnew/<EMAIL>';
import { ImageLanguageMap, WhatsNewProps } from '../types';
import { FeaturesKeyEnum } from '../consts';

const images: ImageLanguageMap = {
  en: [WhatsNewImgEn, WhatsNewImgEn2x],
  de: [WhatsNewImgDe, WhatsNewImgDe2x],
  fr: [WhatsNewImgFr, WhatsNewImgFr2x],
  hu: [WhatsNewImgHu, WhatsNewImgHu2x],
  it: [WhatsNewImgIt, WhatsNewImgIt2x],
  pt: [WhatsNewImgPt, WhatsNewImgPt2x],
  ru: [WhatsNewImgRu, WhatsNewImgRu2x],
  es: [WhatsNewImgEs, WhatsNewImgEs2x],
  uk: [WhatsNewImgUk, WhatsNewImgUk2x],
  pl: [WhatsNewImgPl, WhatsNewImgPl2x],
  tr: [WhatsNewImgTr, WhatsNewImgTr2x],
  default: [WhatsNewImgEn, WhatsNewImgEn2x],
};

const CropRotation: VFC<WhatsNewProps> = ({
  style,
  onClick,
  t,
  language,
  isLast,
}) => {
  const Img = images[language] ? images[language]?.[0] : images.default[0];
  const Img2x = images[language] ? images[language]?.[1] : images.default[1];

  return (
    <StyledPage style={{ ...style }}>
      <div className={styles.semiPage}>
        <div className={styles.pageContent}>
          <div className={styles.textContent}>
            <h2 className={styles.title}>
              {t('whatsnew.croprotation.v2.title')}
            </h2>
            <p className={styles.text}>
              <Trans>{t('whatsnew.croprotation.v2.text')}</Trans>
            </p>
          </div>
          <Button className='btn btn-success fit-content' onClick={onClick}>
            {t(`whatsnew.${isLast ? 'end' : 'next'}.action_text`)}
          </Button>
        </div>
      </div>
      <div className={styles.semiPage}>
        <picture
          className={styles.pagePicture}
          style={{
            marginTop: 28,
            borderBottomRightRadius: 8,
            overflow: 'hidden',
          }}
        >
          <source srcSet={`${Img} 1x, ${Img2x} 2x`} />
          <img src={Img} alt={t('whatsnew.croprotation.v2.title') as string} />
        </picture>
      </div>
    </StyledPage>
  );
};

const WhatsNewCropRotationV2 = {
  key: FeaturesKeyEnum.cropRotationV2,
  component: CropRotation,
  filter: {},
};

export default WhatsNewCropRotationV2;
