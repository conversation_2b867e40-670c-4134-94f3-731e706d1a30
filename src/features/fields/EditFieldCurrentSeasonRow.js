import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Control, actions as formActions } from 'react-redux-form';

import Icon from 'components/Icon';
import Button from 'components/ui/Button/Button';
import DateInput from 'components/ui/DateInput/DateInput';
import { CustomDropdown } from 'components/ui/CustomDropdown/CustomDropdown';
import VarietyInputRow from './VarietyInputRow';

import fields from 'modules/fields';
import settings from 'modules/settings';

import { formatDate } from 'utils/format-date';
import { useTranslate } from 'utils/use-translate';

import forms from 'constants/forms';
import { formatYieldUnit } from 'utils/units';

import logEvent from 'sagas/global/logEvent';
import moment from 'moment';

const EditFieldCurrentSeasonRow = ({
  crops,
  season,
  setShowPopup,
  fieldUserSeasonId,
}) => {
  const { t } = useTranslate();
  const units = useSelector(settings.selectors.getUnits);
  const yieldUnitsByCrop = useSelector(settings.selectors.getYieldUnitsByCrop);
  const dispatch = useDispatch();

  const sortedCropTypes = useSelector(fields.selectors.getSortedCropTypes);
  const cropTypes = ['propose', ...sortedCropTypes, 'remove'];

  return crops.map(crop => (
    <tr key={crop.index}>
      <td>
        <Control.select
          component={CustomDropdown}
          model={`.crops[${crop.index}].crop`}
          changeAction={(model, value) => {
            if (value === 'remove') {
              return crops.length > 1
                ? formActions.remove(`${forms.field}.crops`, crop.index)
                : formActions.change(`${forms.field}.crops[${crop.index}]`, {
                    season_id: crop.season_id,
                  });
            }
            if (value === 'propose') {
              setShowPopup(true);
              return formActions.change(`${forms.field}.crops[${crop.index}]`, {
                season_id: crop.season_id,
              });
            }
            return formActions.change(`${forms.field}.crops[${crop.index}]`, {
              ...crop,
              crop: value,
              variety: null,
              yield_value_units: yieldUnitsByCrop[value] || null,
            });
          }}
          controlProps={{
            className: 'form-select',
            withSearchBox: cropTypes.length >= 5,
            searchBoxProps: {
              placeholder: t('suggest.crops_type.search.placeholder'),
            },
            placeholder: t('fields.select'),
            options: cropTypes.map(type => ({
              label: t(`crop.${type}`),
              id: type,
            })),
            ignoreIDs: ['propose', 'remove'],
            renderValue: ({ value }) => {
              let label = 'fields.select';
              if (value && value !== 'propose' && value !== 'remove') {
                label = `crop.${value}`;
              }
              return (
                <div className='form-select__value u-select__value'>
                  {t(label)}
                </div>
              );
            },
          }}
        />
      </td>
      <td>
        <Control.text
          className='form-input size-full'
          component={VarietyInputRow}
          type='text'
          model={`.crops[${crop.index}].variety`}
          controlProps={{
            className: 'form-select',
            crop: crop.crop || null,
            placeholder: t('fields.edit.crops.unknown'),
            disabled: !crop.crop,
          }}
        />
      </td>
      <td>
        <Control.select
          model={`.crops[${crop.index}].sowing_date`}
          component={DateInput}
          controlProps={{
            disabled: !crop.crop,
          }}
          calendarProps={{
            openToDate: moment(),
            maxDate: crop.harvest_date,
          }}
          placeholder={t('fields.edit.crops.select_date')}
          renderValue={value =>
            formatDate(value, t('seasons.edit.date_format'))
          }
        />
      </td>
      <td>
        <Control.select
          model={`.crops[${crop.index}].harvest_date`}
          component={DateInput}
          controlProps={{
            disabled: !crop.crop,
          }}
          calendarProps={{
            openToDate: crop.sowing_date || moment(),
            minDate: crop.sowing_date,
          }}
          placeholder={t('fields.edit.crops.select_date')}
          renderValue={value =>
            formatDate(value, t('seasons.edit.date_format'))
          }
        />
      </td>
      <td>
        <div className='unit-append'>
          <Control.text
            className='form-input size-full'
            controlProps={{
              disabled: !crop.crop,
            }}
            placeholder='0'
            type='number'
            step='any'
            min={0}
            model={`.crops[${crop.index}].yield_value`}
          />
          <Control.select
            wrapperClassName='unit-append__wrapper'
            className='unit-append__select'
            component={CustomDropdown}
            model={`.crops[${crop.index}].yield_value_units`}
            onChange={unit => {
              logEvent('change_yield_unit', {
                crop: crop.crop,
                unit,
                field_user_season_id: `${fieldUserSeasonId}`,
              });
            }}
            controlProps={{
              disabled: !crop.crop,
              options: units.yield.map(({ unit }) => ({
                label: formatYieldUnit(
                  unit,
                  units,
                  yieldUnitsByCrop[crop.crop],
                  t,
                ),
                id: unit,
              })),
              renderValue: ({ value }) => (
                <>
                  <div className='form-select__value u-select__value'>
                    {formatYieldUnit(
                      value,
                      units,
                      yieldUnitsByCrop[crop.crop],
                      t,
                    )}
                  </div>
                  <Icon
                    className='ico-chevron-down-small-os'
                    name='arrow-down-small'
                  />
                </>
              ),
            }}
          />
        </div>
      </td>
      <td>
        <div className='table-seasons-actions'>
          <div className='table-seasons-actions__item'>
            <Button
              className='btn btn-default btn-icon btn-add'
              onClick={() => {
                dispatch(
                  formActions.push(`${forms.field}.crops`, {
                    season_id: season.id,
                  }),
                );
              }}
            >
              <span className='btn__ico'>
                <Icon className='ico-plus-os' name='plus-btn' />
              </span>
            </Button>
          </div>
          {crops.length > 1 && (
            <div className='table-seasons-actions__item'>
              <Button
                className='btn btn-default btn-icon btn-remove'
                onClick={() => {
                  dispatch(
                    formActions.remove(`${forms.field}.crops`, crop.index),
                  );
                }}
              >
                <span className='btn__ico'>
                  <Icon className='ico-trash-n-os' name='trashcan' />
                </span>
              </Button>
            </div>
          )}
        </div>
      </td>
    </tr>
  ));
};

export default EditFieldCurrentSeasonRow;
