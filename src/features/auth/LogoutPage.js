import { Component } from 'react';
import { connect } from 'react-redux';

import auth from 'modules/auth';

import { getYieldHost } from 'utils/yield';

class LogoutPage extends Component {
  componentWillMount() {
    const { onLogout } = this.props;
    onLogout();
  }
  componentDidMount() {
    const { location } = this.props;
    setTimeout(() => {
      const query = new URLSearchParams(location.search);
      query.set('app', `${getYieldHost()}/authorize`);
      query.set('embedded', '');
      window.location = `/auth/login?${query.toString()}`;
    }, 500);
  }
  render() {
    return null;
  }
}

const mapDispatchToProps = {
  onLogout: auth.actions.logout,
};

export default connect(null, mapDispatchToProps)(LogoutPage);
