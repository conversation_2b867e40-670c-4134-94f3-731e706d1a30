export const InitialState = {
  login: {
    login: '',
    password: '',
    accept_terms: false,
  },
  forgot: {
    login: '',
    password: '',
  },
  note: {
    point: null,
    text: '',
    type: null,
    field_id: null,
    images: [],
  },
  field: {
    title: '',
    crops: [],
  },
  fieldGeometry: {
    geometry: null,
  },
  addFields: {
    fieldIDs: [],
    columns: [],
  },
  file: {
    type: 'select',
    properties: [],
    operation_date: null,
  },
  addFiles: {
    files: [],
  },
  settings: {
    unitSystem: 'metric',
  },
  season: {
    title: '',
    copy_fields: true,
    start_date: null,
    end_date: null,
  },
  stationCoords: null,
  propose: {
    crop: '',
    notify: true,
    email: '',
  },
  annotation: {
    type: 'note',
    date: null,
    info: {
      type: 'text',
      text: '',
    },
  },
  analysis: {
    ndvi1: 0,
    ndvi2: 1,
  },
  analysisRGB: {
    rgb1: 0,
    rgb2: 1,
  },
};

type FormsKey = keyof typeof InitialState;

type FormsMap = {
  [K in FormsKey]: `forms.${K}`;
};

const keys = Object.keys(InitialState) as FormsKey[];
const forms: FormsMap = keys.reduce<FormsMap>((result, key) => {
  // @ts-ignore
  result[key] = `forms.${key}`;
  return result;
}, {} as FormsMap);

export default forms;
