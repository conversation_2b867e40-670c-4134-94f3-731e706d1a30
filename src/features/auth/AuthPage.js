import React from 'react';
import { Switch, Route } from 'react-router-dom';

import ToastContainer from 'features/platform/ToastContainer/ToastContainer';
import { INVITATION_TYPES } from 'features/multiaccount/constants';
import AuthorizePage from './AuthorizePage';
import ForgotPasswordPage from './ForgotPasswordPage';
import ResetPasswordPage from './ResetPasswordPage';
import ConfirmSignupPage from './ConfirmSignupPage';
import PartnerSignupPage from './PartnerSignupPage';
import ImpersonatePage from './ImpersonatePage';
import InvitationSignupPage from './InvitationSignupPage';
import AuthFooter from './AuthFooter';

import config from 'config';

const Partners = ['cargill', 'sodrugestvo', 'welcome'];
const PartnersRe = Partners.join('|');

const AuthPage = ({ match, location }) => {
  const authRoutes = !config.whiteLabel?.hideSignup ? 'login|signup' : 'login';
  return (
    <main className='page-container'>
      <ToastContainer id='global' />
      <Switch>
        <Route
          path={`${match.url}/:type(login|signup)/:invitationType(${INVITATION_TYPES.DEALER}|${INVITATION_TYPES.CONSULTANT}|${INVITATION_TYPES.WORKSPACE})`}
          component={InvitationSignupPage}
        />
        <Route
          path={`${match.url}/:type(${authRoutes})/:partner(${PartnersRe})`}
          component={PartnerSignupPage}
        />
        <Route
          path={`${match.url}/:type(${authRoutes})`}
          component={AuthorizePage}
        />
        <Route path={`${match.url}/impersonate`} component={ImpersonatePage} />
        <Route path={`${match.url}/forgot`} component={ForgotPasswordPage} />
        <Route path={`${match.url}/reset`} component={ResetPasswordPage} />
        <Route path={`${match.url}/confirm`} component={ConfirmSignupPage} />
      </Switch>
      <AuthFooter
        hideLang={location.pathname.endsWith('welcome')}
        hideCopy={
          !location.pathname.endsWith('welcome') &&
          Partners.some(partner => location.pathname.endsWith(partner))
        }
      />
    </main>
  );
};

export default AuthPage;
