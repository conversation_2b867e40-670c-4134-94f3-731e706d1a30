import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';

import Saga from 'components/saga/Saga';

// @ts-ignore
import CustomDropdown from 'features/scouting/CustomDropdown';
import Button from 'components/ui/Button/Button';
import Icon from 'components/Icon';
import ModernSpinner from 'components/ui/ModernSpinner/ModernSpinner';
import EnterExitTransition from 'components/EnterExitTransition';

// @ts-ignore
import MissedCropPopup from 'features/fields/MissedCropPopup';

import api from 'modules/api';
// @ts-ignore
import fields from 'modules/fields';
// @ts-ignore
import seasons from 'modules/seasons';

import { matchColumnsSaga } from 'features/fields/BulkUpload/saga';
import logEvent from 'sagas/global/logEvent';
import { formatTitle as formatSeasonTitle } from 'utils/seasons-formatters';
import { TranslateFunction, useTranslate } from 'utils/use-translate';

import bulkUploadModule from 'features/fields/BulkUpload/redux';

import {
  BulkUploadStep,
  FieldAttribute,
} from 'features/fields/BulkUpload/types';
import { RootState, SeasonType } from 'types/rootState';
import { FloatingActionsStyle } from 'components/ui/FloatingActions/styles';

const FIXED_ATTR = [
  'crop',
  'sowing_date',
  'harvest_date',
  'yield_value',
  'variety',
];

const createAttrs = (
  t: TranslateFunction,
  seasons: SeasonType[] = [],
): FieldAttribute[] => {
  return seasons.flatMap(item => [
    ...FIXED_ATTR.map(attr => ({
      id: `${attr}_${item.id}`,
      label:
        t(`fields.upload.match_crops.${attr}.label`) +
        ` (${formatSeasonTitle(t, item)})`,
      season_id: item.id,
      type: attr,
    })),
  ]);
};

export const MatchColumnsStep = () => {
  const { t } = useTranslate();
  const saveRequest = useSelector((state: RootState) =>
    api.selectors.getRequestStatus(state, 'update-fields-file'),
  );
  const allSeasons = useSelector(state => seasons.selectors.getAll(state));
  const fieldsFetchingPending = useSelector(
    bulkUploadModule.selectors.getFieldsFetchingPending,
  );

  const fileIds = useSelector(
    bulkUploadModule.selectors.getValidFileIdsWithProps,
  );

  // The interface shoult work with only one fileId
  const fileId = fileIds[0];

  const cropsAttr = createAttrs(t, allSeasons);

  const attributes = [
    { id: 'default', label: t('fields.upload.match_crops.no_data') },
    { id: 'title', label: t('fields.upload.match_crops.field_title') },
    ...cropsAttr,
  ];
  const dispatch = useDispatch();

  const [matchFields, setMatchFields] = useState<
    Record<string, FieldAttribute>
  >({});
  const [data, setData] = useState<Record<string, string[]>>({});
  const [showPopup, setShowPopup] = useState(false);

  const handleSubmit = () => {
    logEvent('fields_import_match_properties', matchFields);
    dispatch(fields.actions.setMatchedProperties(fileId, matchFields));
  };

  // Nothing to show if there are no uploaded files
  if (!fileId) {
    return null;
  }

  return (
    <>
      <Saga
        saga={matchColumnsSaga}
        fileId={fileId}
        onSetData={setData}
        onShowPopup={setShowPopup}
      />
      <EnterExitTransition variant='popup'>
        {showPopup && (
          <MissedCropPopup
            seasonIds={showPopup}
            allSeasons={allSeasons}
            onConfirm={() => {
              logEvent('fields_import_missed_crops', { answer: 'confirm' });
              setShowPopup(false);
              const proceed = true;
              dispatch(
                fields.actions.setMatchedProperties(
                  fileId,
                  matchFields,
                  proceed,
                ),
              );
            }}
            onCancel={() => {
              logEvent('fields_import_missed_crops', { answer: 'cancel' });
              setShowPopup(false);
            }}
          />
        )}
      </EnterExitTransition>
      <div className='main-section__body'>
        <form onSubmit={() => {}}>
          <div className='main-uploader' data-type='columns'>
            <div className='main-uploader-header'>
              <h2 className='main-uploader-header__title'>
                {t('fields.upload.match_columns.title')}
              </h2>
              <p className={'main-uploader-header__description'}>
                {t('fields.upload.match_columns.sub_title')}
              </p>
            </div>
            <div className='main-uploader-naming'>
              <div className='main-uploader-naming__header'>
                <div className='main-uploader-naming__col'>
                  {t('fields.upload.match_columns.from_file')}
                </div>
                <div className='main-uploader-naming__col'>
                  {t('fields.upload.match_columns.to_platform')}
                </div>
              </div>
              {!Object.keys(data).length && (
                <div className='main-uploader-spinner'>
                  <ModernSpinner large />
                </div>
              )}
              {Object.keys(data).map(key => (
                <div
                  key={key}
                  className='main-uploader-naming__row'
                  data-column={matchFields[`${key}`] ? 'selected' : 'passed'}
                >
                  <div className='main-uploader-naming__arr'>
                    <Icon
                      className='ico-arrow-circle-right-os'
                      name='arrow-circle-right'
                    />
                  </div>
                  <div className='main-uploader-naming__col'>
                    <h3 className='main-uploader-naming__title'>{[key]}</h3>
                    <ul className='main-uploader-naming-chars'>
                      {data[key]!.map(item => (
                        <li
                          key={item}
                          className='main-uploader-naming-chars__list-item'
                        >
                          {item}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div className='main-uploader-naming__col'>
                    <CustomDropdown
                      placeholder={t('files.actions.placeholder')}
                      className='form-select form-select-sm'
                      value={matchFields[key]}
                      renderValue={({ value }: { value: FieldAttribute }) => (
                        <div className='form-select__value'>
                          {value?.label
                            ? value.label
                            : t('fields.upload.match_crops.no_data')}
                        </div>
                      )}
                      options={attributes.map(option => ({
                        label: option.label,
                        id: option.id,
                      }))}
                      disabledIDs={Object.values(matchFields).map(
                        item => item.id,
                      )}
                      withSearchBox={attributes.length >= 5}
                      searchBoxProps={{
                        placeholder: t(
                          'fields.upload.match_crops.search_placeholder',
                        ),
                      }}
                      onClick={(event: { stopPropagation: () => void }) =>
                        event.stopPropagation()
                      }
                      onChange={(action: string) => {
                        dispatch(
                          bulkUploadModule.actions.setReachedStep(
                            BulkUploadStep.COLUMN_MATCHING,
                          ),
                        );
                        let state = { ...matchFields };
                        if (action === 'default') {
                          delete state[key];
                        } else {
                          state = {
                            ...state,
                            [key]: attributes.find(item => item.id === action)!,
                          };
                        }
                        setMatchFields(state);
                      }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </form>
      </div>
      {Boolean(Object.keys(data).length) && (
        <FloatingActionsStyle>
          <Button
            className='btn btn-dark btn-lg'
            onClick={() => {
              dispatch(bulkUploadModule.actions.resetBulkUploadState());
            }}
          >
            {t('forms.cancel')}
          </Button>
          <Button
            className='btn btn-success btn-lg'
            type='submit'
            pending={
              saveRequest.status === 'pending' ||
              fieldsFetchingPending ||
              !Object.keys(data).length
            }
            onClick={() => handleSubmit()}
          >
            {t('forms.next')}
          </Button>
        </FloatingActionsStyle>
      )}
    </>
  );
};
