import React from 'react';
import { connect } from 'react-redux';
import { Redirect } from 'react-router-dom';
import centroid from '@turf/centroid';

import Saga from 'components/saga/Saga';

import zoomToFieldSaga from 'sagas/local/zoom-to-field';

import auth from 'modules/auth';
import fields from 'modules/fields';

const DefaultZoom = 16;

const ZoomToField = ({ authorized, field, match, history }) => {
  if (!authorized) {
    return (
      <Redirect
        to={{
          pathname: '/auth/login',
          search: `?next=${history.location.pathname}&internal=true`,
        }}
      />
    );
  }
  if (!field) {
    return (
      <Saga saga={zoomToFieldSaga} fieldId={match.params.fieldId} persist />
    );
  }
  const fieldCenter = centroid(field.geometry);
  const coords = fieldCenter.geometry.coordinates
    .slice()
    .reverse()
    .map(v => v.toFixed(4))
    .join(',');
  return (
    <Redirect
      to={`/@${coords},${DefaultZoom}z/fields/${field.id}${
        history.location.search
      }`}
    />
  );
};

const mapStateToProps = (state, ownProps) => ({
  authorized: auth.selectors.isAuthorized(state),
  field: fields.selectors.getByID(state, `o${ownProps.match.params.fieldId}`),
});

export default connect(mapStateToProps)(ZoomToField);
