import { <PERSON>a, <PERSON> } from '@storybook/react';
import React, { Fragment, useMemo, useState } from 'react';

import ts, {
  EmitHint,
  isIdentifier,
  isLiteralTypeNode,
  isPropertySignature,
  isStringLiteral,
  isTypeAliasDeclaration,
  isTypeLiteralNode,
  isUnionTypeNode,
  PropertySignature,
  TypeAliasDeclaration,
} from 'typescript';

import { EventsContainer, EventsDt } from './Events.style';

import raw from 'raw.macro';

export default {
  title: 'common/Events',
  parameters: {
    controls: {
      disable: true,
    },
  },
} as Meta;

const eventsJson = raw('../../types/events.ts');

const parseEvents = () => {
  const sourceNode = ts.createSourceFile(
    'x.ts',
    eventsJson,
    ts.ScriptTarget.Latest,
  );

  const printer = ts.createPrinter({ newLine: ts.NewLineKind.LineFeed });

  const eventTypeNode = sourceNode.statements.find(
    statement =>
      isTypeAliasDeclaration(statement) && statement.name.text === 'Event',
  ) as TypeAliasDeclaration;

  if (!eventTypeNode) {
    throw new Error('Cannot find declaration of Event type alias');
  }

  const eventTypeType = eventTypeNode.type;
  if (!isUnionTypeNode(eventTypeType)) {
    throw new Error('Unsupported kind of type declaration');
  }

  return eventTypeType.types.map(node => {
    if (isTypeLiteralNode(node)) {
      const nameNode = node.members.find(
        member =>
          isPropertySignature(member) &&
          isIdentifier(member.name) &&
          member.name.text === 'name',
      ) as PropertySignature;

      const paramsNode = node.members.find(
        member =>
          isPropertySignature(member) &&
          isIdentifier(member.name) &&
          member.name.text === 'params',
      ) as PropertySignature;

      if (
        !nameNode ||
        !nameNode.type ||
        !isLiteralTypeNode(nameNode.type) ||
        !isStringLiteral(nameNode.type.literal)
      ) {
        return null;
      }

      if (
        !paramsNode ||
        !paramsNode.type ||
        !isTypeLiteralNode(paramsNode.type)
      ) {
        return null;
      }

      const params = paramsNode.type.members.map(paramNode => {
        if (!isPropertySignature(paramNode) || !isIdentifier(paramNode.name)) {
          return null;
        }
        return {
          name: paramNode.name.text,
          type: paramNode.type
            ? printer.printNode(
                EmitHint.Unspecified,
                paramNode.type,
                sourceNode,
              )
            : 'unknown',
        };
      });

      return {
        name: nameNode.type.literal.text,
        params,
      };
    }

    return null;
  });
};

export const Events: Story = () => {
  const [query, setQuery] = useState('');

  const events = useMemo(() => parseEvents(), []);

  const filteredEvents = useMemo(
    () => events.filter(event => event?.name.toLowerCase().includes(query)),
    [query, events],
  );

  return (
    <EventsContainer>
      <input
        type='text'
        placeholder='Search'
        defaultValue=''
        onChange={event => {
          setQuery(event.target.value);
        }}
      />

      <dl>
        {filteredEvents.map((event, index) => {
          if (!event) {
            return (
              <EventsDt key={index}>error parsing event #{index + 1}</EventsDt>
            );
          }
          return (
            <Fragment key={event.name}>
              <EventsDt>{event.name}</EventsDt>
              {event.params.map((param, paramIndex) => {
                if (!param) {
                  return (
                    <dd key={paramIndex}>error parsing param #{index + 1}</dd>
                  );
                }
                return (
                  <dd key={param.name}>
                    {param.name}: {param.type}
                  </dd>
                );
              })}
            </Fragment>
          );
        })}
      </dl>
    </EventsContainer>
  );
};
