import { styled } from 'linaria/react';

export const TableWrapper = styled.div`
  padding-top: 1rem;
  padding-bottom: 5px;
`;

export const AdvertisingSection = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
`;

export const AdvertisingBlock = styled.div`
  background: #f5f7f9;
  border-radius: 8px;
  display: flex;
  flex-direction: row;
  gap: 32px;
  padding: 24px;
  margin-top: 16px;
`;

export const AdvertisingTitle = styled.div`
  font-weight: 700;
  font-size: 20px;
  line-height: 24px;
  color: #1c1c1e;
`;

export const AdvertisingDescription = styled.div`
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  color: #222222;
  margin-top: 12px;
`;

export const AdvertisingDescriptionBlock = styled.div`
  display: flex;
  flex-direction: column;
  margin-right: 8px;
`;

export const AdvertisingIvon = styled.div`
  display: flex;
  flex-direction: column;
  margin-right: 8px;
`;

export const CreateFieldSelect = styled.div`
  margin-top: 16px;
  display: none;
  flex-direction: column;
  width: fit-content;
`;
