import React, { ReactNode } from 'react';
import cx from 'classnames';

import LinkButton from './LinkButton';
import { DropdownInput } from 'components/ui/DropdownInput/DropdownInput';
import { DropdownInputProps } from './ui/DropdownInput/types';
import { PopoverDeprecatedAlign } from 'components/ui/Popover';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type SelectInputProps<T extends Record<string, any>, V> = {
  modalClassName?: string;
  options: T[];
  idKey?: string;
  titleKey?: string;
  renderValue: (props: { kind: string; value: Nullable<T> }) => ReactNode;
  position?: PopoverDeprecatedAlign;
  disabled?: boolean;
} & Omit<DropdownInputProps<T, V>, 'renderDropdown' | 'renderValue'>;

type StingNumber = string | number;

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const SelectInput = <T extends Record<string, any>, V extends StingNumber>({
  modalClassName,
  options,
  idKey = 'id',
  titleKey,
  renderValue,
  position,
  disabled = false,
  ...otherProps
}: SelectInputProps<T, V>) => (
  <DropdownInput
    align={position}
    disabled={disabled}
    idKey={idKey}
    renderValue={value =>
      renderValue({
        kind: 'selection',
        value: options.find(option => option[idKey] === value),
      })
    }
    renderDropdown={({
      value,
      onChange,
      getMenuProps,
      getItemProps,
      highlightedIndex,
      ...otherProps
    }) => (
      <div className={cx('modal-select', modalClassName)} {...otherProps}>
        <div className='modal-select__inner' {...getMenuProps()}>
          <ul className='modal-select__list'>
            {options.map((option, idx) => (
              <li
                key={option[idKey]}
                className='modal-select__list-item'
                {...getItemProps({ item: option, index: idx })}
              >
                <LinkButton
                  className={cx('modal-select__item', {
                    __checked: value === option[idKey],
                  })}
                >
                  <span className='modal-select__value'>
                    {titleKey
                      ? option[titleKey]
                      : renderValue({
                          kind: 'option',
                          value: option,
                        })}
                  </span>
                </LinkButton>
              </li>
            ))}
          </ul>
        </div>
      </div>
    )}
    {...otherProps}
  />
);

export default SelectInput;
