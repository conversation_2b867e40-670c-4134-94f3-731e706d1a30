import React, { VFC } from 'react';
import { useSelector } from 'react-redux';

import { ImportTaskTitle, PageStyle } from '../styles';
import { useTranslate } from 'utils/use-translate';
import getFieldArea from 'utils/get-field-area';
import { CreateFieldDialog } from './components/CreateFieldDialog';

import {
  FieldBlockHeaderStyle,
  FieldBlockStyle,
  FieldDescriptionStyle,
  FieldSubtitleStyle,
  FieldTitleStyle,
  preview,
} from './styles';
import { fieldsSelectors } from 'modules/fields/selectors';
import settings from 'modules/settings';

import { MatchingOperationTable } from './components/table/MatchingOperationTable';
import { useDataStore } from './service/useDataStore';
import { useSetParent } from './service/useSetParent';
import { useSetType } from './service/useSetType';
import { useSetChecked } from './service/useSetChecked';
import { useSetGroupChecked } from './service/useSetGroupChecked';
import { useSetDate } from './service/useSetDate';
import { DescriptionBlock } from './components/DescriptionBlock';
import { AdvertisingSection } from './components/styles';
import {
  useFieldDataOperationsLoader,
  useMetaLoader,
  useActiveDataLayerLoader,
} from 'features/files/by-machinery/dataLoaders';
import { formatMeta } from 'features/files/by-machinery/pages/MatchingOperationsPage/utils/formatMeta';
import { mergeStatuses } from 'modules/entities/utils/mergeStatuses';
import { StatusType } from 'modules/entities/types';
import { FieldImage } from '../../../../fields/FieldImage';
import { getFieldsWithOperation } from '../../redux/selectors/getFieldsWithOperation';
import { NoFieldDescriptionBlock } from './components/NoFieldDescriptionBlock';
import { attributeStepCheck } from '../../service/steps/checks/attributeStepCheck';
import { StepStatus } from '../../service/steps';
import { selectors } from '../../redux/selectors';
import { parseQuery } from 'utils/query';
import { DataTypesKey } from '../../types';
import { useLocation } from 'react-router-dom';
import { getFieldsWithElectroconductivity } from '../../redux/selectors/getFieldsWithElectroconductivity';

export const MatchingOperationsPage: VFC = () => {
  const { t } = useTranslate();
  const location = useLocation();
  const formatUnit = useSelector(settings.selectors.getUnitFormatter);
  const query = parseQuery(location);
  const dataType = (query['data-type'] || DataTypesKey.machinery) as string;

  const [dataLayers, requestData] = useActiveDataLayerLoader();
  const [operations, requestOperations] = useFieldDataOperationsLoader();
  const [meta, requestMeta] = useMetaLoader();
  const fields = useSelector(fieldsSelectors.getAllOwn);
  const fieldsWithOperations = useSelector(getFieldsWithOperation);

  const fieldsWithElectroconductivity = useSelector(
    getFieldsWithElectroconductivity,
  );

  const fieldsWithData =
    dataType === DataTypesKey.electroconductivity
      ? fieldsWithElectroconductivity
      : fieldsWithOperations;

  const dataStore = useDataStore({ operations, fields });
  const setParent = useSetParent(dataStore);
  const setType = useSetType(dataStore);
  const setChecked = useSetChecked(dataStore);
  const setGroupChecked = useSetGroupChecked(dataStore);
  const setDate = useSetDate();
  const isAttributeStepAvailable = useSelector(attributeStepCheck);
  const activeTasks = useSelector(selectors.getActiveTasks) as [];

  const fieldRequestStatus = useSelector(state =>
    fieldsSelectors.getOwnStatus(state),
  );

  const status = mergeStatuses(requestData, requestOperations, requestMeta);

  if (
    fieldRequestStatus !== 'resolved' ||
    (status !== StatusType.updating && status !== StatusType.resolved) ||
    !meta ||
    activeTasks.length === 0
  ) {
    return null;
  }

  return (
    <form onSubmit={() => {}}>
      <PageStyle style={{ paddingBottom: 120 }}>
        <ImportTaskTitle>
          {t('import.matching_operations.title') as string}
        </ImportTaskTitle>
        <AdvertisingSection>
          {fieldsWithData.length === 0 && <NoFieldDescriptionBlock />}
          {isAttributeStepAvailable !== StepStatus.hiding &&
            fieldsWithData.length > 0 && <DescriptionBlock />}
          {fieldsWithData.length > 0 && (
            <CreateFieldDialog
              operations={operations}
              dataLayers={dataLayers}
            />
          )}
        </AdvertisingSection>

        {fieldsWithData.map((field, index) => {
          const title =
            field.title ||
            // @ts-ignore
            t('fields.default_title', {
              index: index + 1,
            });

          return (
            <FieldBlockStyle key={field.id}>
              <FieldBlockHeaderStyle>
                <FieldImage className={preview} field={field} />
                <FieldDescriptionStyle>
                  <FieldTitleStyle>{title}</FieldTitleStyle>
                  <FieldSubtitleStyle>
                    {getFieldArea({ field: field, formatUnit })}
                  </FieldSubtitleStyle>
                </FieldDescriptionStyle>
              </FieldBlockHeaderStyle>
              <MatchingOperationTable
                setParent={setParent}
                setDate={setDate}
                setChecked={setChecked}
                setGroupChecked={setGroupChecked}
                setType={setType}
                operations={operations.filter(
                  record => record.field_uuid === field.fieldUserSeasonId,
                )}
                field={field}
                meta={formatMeta(meta)}
              />
            </FieldBlockStyle>
          );
        })}
      </PageStyle>
    </form>
  );
};
