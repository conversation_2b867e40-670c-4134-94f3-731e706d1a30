.ico-arrow-left-os {
  width: 11px;
  height: 10px;
}
.ico-arrow-long-left-os {
  width: 14px;
  height: 12px;
}
.ico-arrow-right-os {
  width: 11px;
  height: 10px;
}
.ico-calendar-os {
  width: 15px;
  height: 12px;
}
.ico-check-os {
  width: 14px;
  height: 10px;
}
.ico-chevron-down-os {
  width: 16px;
  height: 9px;
}
.ico-chevron-down-small-os {
  width: 8px;
  height: 5px;
}
.ico-chevron-down-big-os {
  width: 14px;
  height: 8px;
}
.ico-clear-os {
  width: 16px;
  height: 16px;
}
.ico-close-os {
  width: 12px;
  height: 12px;
}
.ico-graph-os {
  width: 16px;
  height: 16px;
}
.ico-infomsg-os {
  width: 16px;
  height: 16px;
}
.ico-loader-os {
  width: 16px;
  height: 16px;
}
.ico-location-os {
  width: 15px;
  height: 15px;
}
.ico-minus-os {
  width: 14px;
  height: 2px;
}
.ico-pin-os {
  width: 15px;
  height: 18px;
}
.ico-plus-os {
  width: 12px;
  height: 12px;
}
.ico-plus-small-os {
  width: 8px;
  height: 8px;
}
.ico-attach-os {
  width: 12px;
  height: 12px;
}
.ico-chevron-left-os {
  width: 8px;
  height: 12px;
}
.ico-chevron-right-os {
  width: 8px;
  height: 12px;
}
.ico-download-os {
  width: 16px;
  height: 16px;
}
.ico-question-os {
  width: 14px;
  height: 14px;
}
.ico-exclamation-os {
  width: 14px;
  height: 14px;
}
.ico-exclamation-mask-os {
  width: 17px;
  height: 16px;
}
.ico-pencil-os {
  width: 10px;
  height: 11px;
}
.ico-trash-os {
  width: 7px;
  height: 10px;
}
.ico-trash-n-os {
  width: 10px;
  height: 13px;
}
.ico-trash-light-os {
  width: 14px;
  height: 16px;
}
.ico-search-os {
  width: 15px;
  height: 15px;
}
.ico-refresh-os {
  width: 15px;
  height: 14px;
}
.ico-arrow-down-os {
  width: 8px;
  height: 9px;
}
.ico-arrow-up-os {
  width: 8px;
  height: 9px;
}
.ico-copy-os {
  width: 12px;
  height: 12px;
}
.ico-visible-os {
  width: 16px;
  height: 12px;
}
.ico-lock-os {
  width: 12px;
  height: 15px;
}
.ico-revert-os {
  width: 21px;
  height: 16px;
}
.ico-select-os {
  width: 16px;
  height: 20px;
}
.ico-cut-os {
  width: 18px;
  height: 16px;
}
.ico-ruler-os {
  width: 21px;
  height: 21px;
}
.ico-filters-os {
  width: 12px;
  height: 12px;
}
.ico-eye-closed-os {
  width: 16px;
  height: 12px;
}
.ico-fullscreen-os {
  width: 14px;
  height: 14px;
}
.ico-arrow-circle-right-os {
  width: 14px;
  height: 14px;
}
.ico-level-signal {
  display: inline-flex;
  vertical-align: top;
  width: 11px;
  height: 12px;
  justify-content: space-between;
  align-items: flex-end;
  font-size: 1px;
  line-height: 1;
}
.ico-level-signal__item {
  width: 3px;
  border-radius: 2px;
  background-color: #d5dbdf;
}
.ico-level-signal__item.__selected {
  background-color: #222;
}
.ico-level-signal__item[data-level="low"] {
  height: 4px;
}
.ico-level-signal__item[data-level="medium"] {
  height: 8px;
}
.ico-level-signal__item[data-level="high"] {
  height: 12px;
}
.ico-level-battery {
  position: relative;
  display: inline-block;
  vertical-align: top;
  font-size: 1px;
  line-height: 1;
  width: 14px;
  height: 10px;
  border-radius: 3px;
  border: 2px solid currentColor;
  padding: 1px;
  margin-right: 2px;
}
.ico-level-battery__item {
  display: block;
  width: 0;
  height: 4px;
  background-color: currentColor;
  border-radius: 1px;
}
.ico-level-battery:after {
  position: absolute;
  right: -4px;
  top: 50%;
  margin-top: -2px;
  content: '';
  width: 1px;
  height: 4px;
  border-top-right-radius: 1px;
  border-bottom-right-radius: 1px;
  background-color: currentColor;
}
.ico-level-battery[data-error-level="low"] {
  color: #ff3b30;
}
.ico-level-battery[data-error-level="medium"] {
  color: #ecb225;
}

html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
}
* {
  box-sizing: border-box;
}
*:after,
*:before {
  box-sizing: inherit;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
html {
  font-size: 16px;
}
body {
  color: #222;
  font: 14px/1.45 'Graphik', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
    'Segoe UI Symbol';
  background-color: #e5e3df;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
td,
th {
  font: 1em/1.45 'Graphik', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
    'Segoe UI Symbol';
}
input,
textarea,
option,
select,
button {
  color: #222;
  font: 1em 'Graphik', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
    'Segoe UI Symbol';
}
strong,
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 500;
}
[role='button'],
button {
  cursor: pointer;
}
a {
  color: #007aff;
  text-decoration: none;
}
a:hover {
  color: #014e86;
}
.hs-title {
  font-size: 18px;
  font-weight: 700;
  line-height: 1.17;
  margin-top: 0;
  margin-bottom: 5px;
}
.page-container {
  position: relative;
  height: 100%;
}
.page-panels {
  display: flex;
}
.page-panels .map-container {
  position: relative;
  z-index: 3;
  flex: 2 1 auto;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  will-change: auto;
}
.global-notification-true .soil-sidebar {
  margin-top: 50px;
}
.global-notification-true .map-controls {
  margin-top: 50px;
}
.global-notification-true .top-toolbar,
.global-notification-true .map-search {
  -webkit-transform: translate(0, 50px);
  transform: translate(0, 50px);
}
.global-notification-true .main-section {
  margin-top: 50px;
}
.main-container-notification {
  position: absolute;
  left: 0;
  right: 0;
  z-index: 93;
  height: 50px;
  color: #fff;
  background-color: #222;
  -webkit-transform: translate(0, -50px);
  transform: translate(0, -50px);
  text-align: center;
  display: flex;
  border-top-left-radius: 8px;
}
.main-container-notification__content {
  margin: auto;
  width: 100%;
  padding: 5px 20px;
  font-weight: 500;
  font-size: 14px;
}
.main-container-notification[data-type='success'] {
  background-color: var(--color-primary);
}
.main-container-notification[data-type='warning'] {
  background-color: #eb924e;
}
.main-container-notification[data-type='error'] {
  background-color: #eb4e4e;
}
.main-container-notification[data-position='bottom'] {
  bottom: 0;
  -webkit-transform: translate(0, 50px);
  transform: translate(0, 50px);
  border-top-left-radius: 0;
  border-bottom-left-radius: 8px;
}
.main-container-notification[data-radius='none'] {
  border-radius: 0;
}
.main-container-notification.__state-normal {
  -webkit-transform: translate(0, 0);
  transform: translate(0, 0);
  transition: -webkit-transform 0.15s linear;
  transition: transform 0.15s linear;
  transition: transform 0.15s linear, -webkit-transform 0.15s linear;
}
.main-container-notification-actions {
  display: flex;
  align-items: center;
  justify-content: center;
}
.main-container-notification-actions__ico {
  margin-right: 5px;
  flex: 0 0 auto;
}
.main-container-notification-actions__action {
  margin-left: 8px;
  flex: 0 0 auto;
}
.app-nav-back {
  width: 20px;
  height: 20px;
  border-width: 0;
  font-size: 0;
  line-height: 2px;
  vertical-align: top;
  color: #222;
  padding: 4px;
  background: none;
}
.app-nav-back .ico-arrow-left-os {
  width: 14px;
  height: 12px;
}
.app-nav-edit {
  width: 20px;
  border-width: 0;
  font-size: 0;
  line-height: 2px;
  vertical-align: top;
  color: #222;
  padding: 3px 0 4px;
  background: none;
}
.app-nav-edit svg {
  vertical-align: top;
  width: 13px;
  height: 13px;
}
.app-side-filter-select__item {
  cursor: pointer;
  font-size: 15px;
  line-height: 1.34;
  font-weight: 500;
  white-space: nowrap;
}
.app-side-filter-select__item:after {
  display: inline-block;
  content: '';
  background-image: url("data:image/svg+xml,%3Csvg id='SVGDoc' width='8' height='6' xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:avocode='https://avocode.com/' viewBox='0 0 8 6'%3E%3Cdefs%3E%3Cpath d='M476.00871,26.47568l-3.02759,-2.97568l-0.98112,0.9948l4.00871,4.0052l3.82458,-4.0052l-1.02186,-0.9948z' id='Path-0'/%3E%3C/defs%3E%3Cdesc%3EGenerated with Avocode.%3C/desc%3E%3Cg transform='matrix%281,0,0,1,-472,-23%29'%3E%3Cg clip-path='url%28%23clip-BD5FD9A2-7769-4AAA-B3ED-01F4362C9393%29'%3E%3Ctitle%3EShape%3C/title%3E%3Cuse xlink:href='%23Path-0' fill='%23222222' fill-opacity='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  width: 8px;
  height: 6px;
  vertical-align: top;
  margin-top: 8px;
  margin-left: 5px;
  transition: -webkit-transform 0.2s;
  transition: transform 0.2s;
  transition: transform 0.2s, -webkit-transform 0.2s;
}
.offer-internal {
  position: relative;
  border-radius: 6px;
  background-color: #ffea72;
  min-height: 80px;
  display: flex;
  align-items: center;
}
.offer-internal__pic {
  flex: 0 0 auto;
  border-bottom-left-radius: 6px;
  border-top-left-radius: 6px;
}
.offer-internal__close {
  position: absolute;
  border: none;
  padding: 0;
  position: absolute;
  right: 10px;
  top: 10px;
  background: none;
  color: rgba(34, 34, 34, 0.3);
  font-size: 1px;
  line-height: 1;
}
.offer-internal__close svg {
  width: 9px;
  height: 9px;
}
.offer-internal__content {
  font-size: 12px;
  line-height: 1.25;
  padding: 10px 10px 10px 0;
}
.offer-internal__content p {
  margin-top: 0;
  margin-bottom: 4px;
}
.offer-internal__link {
  display: inline-block;
  border-radius: 4px;
  background-color: #fff;
  color: #222;
  font-size: 10px;
  font-weight: bold;
  line-height: 12px;
  letter-spacing: 0.5px;
  padding: 6px 10px;
}
.g-loader {
  display: inline-block;
}
.g-loader__inner {
  display: inline-block;
  -webkit-animation: spin 1s infinite linear;
  animation: spin 1s infinite linear;
  -webkit-transform-origin: 50%;
  transform-origin: 50%;
  width: 16px;
  height: 16px;
  vertical-align: top;
  background: url('../images/ic_loader_24.png') no-repeat 50% / contain;
}
.g-loader.green .g-loader__inner {
  background-image: url('../images/ic_loader_24_green.png');
}
.g-loader.white .g-loader__inner {
  background-image: url('../images/ic_loader_24_white.png');
}
.g-loader.large .g-loader__inner {
  width: 24px;
  height: 24px;
}
.page-app-welcome {
  background-color: #fff;
}
.page-app-closed {
  background-color: #fff;
}
.app-closed {
  position: relative;
  min-height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}
.app-closed__inner {
  position: relative;
  z-index: 4;
  text-align: center;
  padding: 0 20px 30px;
  font-size: 18px;
  line-height: 23px;
}
.app-closed__plantain {
  font-size: 67px;
  line-height: 1;
  margin-bottom: 14px;
}
.app-closed__title {
  font-size: 40px;
  font-weight: bold;
  line-height: 44px;
  margin-bottom: 14px;
}
.app-closed-nav {
  position: relative;
  z-index: 3;
  margin: 0;
}
.app-closed-nav__list {
  flex: 0 0 auto;
  margin: 0;
  padding: 0 0 17px;
  list-style-type: none;
  display: flex;
  justify-content: center;
}
.app-closed-nav__list-item {
  margin: 0 15px;
}
.app-closed-visual {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  max-height: 480px;
  height: 100%;
  width: 100%;
}
.app-closed-visual__inner {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 625px;
  overflow: hidden;
}
.app-closed-visual__field {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: hidden;
  pointer-events: none;
}
.app-closed-visual__field:after {
  position: absolute;
  z-index: 1;
  content: '';
  width: 494px;
  height: 617px;
  top: -8px;
  left: -8px;
  background-image: repeating-linear-gradient(
    -45deg,
    transparent,
    transparent 16.6px,
    #27ae60 16.6px,
    #27ae60 25.6px
  );
  background-position: 0 0;
}
.app-closed-visual__field:before {
  position: absolute;
  z-index: 2;
  content: '';
  width: 900px;
  height: 478px;
  top: 620px;
  left: -150px;
  background-color: #fff;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
}
.app-closed-visual-car {
  position: absolute;
  z-index: 3;
  left: 42px;
  top: 139px;
}
.app-closed-visual-car__el {
  position: absolute;
  z-index: 3;
  width: 232px;
  height: 200px;
  top: 0;
  left: 0;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  transition: -webkit-transform 0.5s;
  transition: transform 0.5s;
  transition: transform 0.5s, -webkit-transform 0.5s;
}
.app-closed-visual-car__el.__anim {
  -webkit-transform: translate(-440px, 440px) rotate(-45deg);
  transform: translate(-440px, 440px) rotate(-45deg);
}
.app-closed-visual-car__el.__grow .app-closed-visual-car__bg {
  opacity: 0;
}
.app-closed-visual-car__el:before {
  position: absolute;
  z-index: 3;
  content: '';
  width: 232px;
  height: 200px;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 232 200' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg transform='translate(-79 -146)' fill='%23222' fill-rule='nonzero'%3E%3Cg transform='rotate(45 293.6457 14.8213)'%3E%3Cg transform='translate(240 140)'%3E%3Cpath d='M176.495942,55.196143 L208.130013,23.316135 L230.725778,46.0875693 L199.091707,77.9675773 L212.649166,91.6304378 L153.900177,159.944741 L95.1511878,100.739012 L162.938483,41.5332824 L176.495942,55.196143 Z M162.034652,58.8395725 L113.2278,100.739012 L153.900177,141.727593 L195.476384,92.5412952 L162.034652,58.8395725 Z M183.274671,62.0275733 L192.312977,71.136147 L217.168319,46.0875693 L208.130013,36.9789956 L183.274671,62.0275733 Z M87.0167125,174.518459 L153.900177,241.921904 L144.861871,251.030478 L4.76812788,109.847585 L13.8064339,100.739012 L80.6898983,168.142457 L110.516308,138.084164 L102.381833,129.886447 L108.708647,123.510446 L131.304412,146.28188 L124.977598,152.657882 L116.843122,144.460165 L87.0167125,174.518459 Z M131.304412,36.9789956 L140.342718,46.0875693 L95.1511878,91.6304378 L86.1128819,82.5218641 L131.304412,36.9789956 Z M208.130013,114.401872 L217.168319,123.510446 L171.976789,169.053314 L162.938483,159.944741 L208.130013,114.401872 Z M203.61086,0.544700781 L212.649166,9.65327448 L185.534248,36.9789956 L176.495942,27.8704219 L203.61086,0.544700781 Z M244.283237,41.5332824 L253.321543,50.6418562 L226.206625,77.9675773 L217.168319,68.8590036 L244.283237,41.5332824 Z M135.823565,251.030478 L131.304412,255.584764 L0.248974879,123.510446 L4.76812788,118.956159 L135.823565,251.030478 Z'/%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
    no-repeat 50% / contain;
}
.app-closed-visual-car__el:after {
  position: absolute;
  content: '';
  width: 50px;
  height: 19px;
  top: 0;
  left: -10px;
  background-color: #fff;
}
.app-closed-visual-car__bg {
  position: absolute;
  z-index: 2;
  top: 17px;
  left: -10px;
  width: 1300px;
  height: 200px;
  background-color: #fff;
  transition: opacity 1s;
  pointer-events: none;
}
@-webkit-keyframes spin {
  100% {
    -webkit-transform: rotate(-360deg);
    transform: rotate(-360deg);
  }
}
@keyframes spin {
  100% {
    -webkit-transform: rotate(-360deg);
    transform: rotate(-360deg);
  }
}
.form-group {
  margin-bottom: 15px;
}
.form-group-subgroup {
  margin-bottom: 5px;
}
.form-title {
  position: relative;
  font-weight: 500;
  padding-bottom: 6px;
  margin-bottom: 15px;
}
.form-title:after {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  content: '';
  height: 1px;
  border-bottom: 0.5px solid #d6dce1;
}
.form-content {
  line-height: 1.3;
}
.form-content p {
  margin-top: 0;
  margin-bottom: 10px;
}
.form-hr {
  margin: 15px 0;
  height: 1px;
  border: none;
  border-bottom: 0.5px solid rgba(165, 178, 188, 0.45);
}
.form-hr.__alt {
  margin-bottom: 10px;
}
.form-hr.__mg {
  margin-left: -20px;
  margin-right: -20px;
  margin-top: 20px;
}
.form-hr.__mg-short {
  margin-left: -20px;
  margin-right: -20px;
}
.form-hr.__big {
  position: relative;
  background-color: rgba(233, 236, 239, 0.5);
  height: 10px;
  margin-top: 20px;
  margin-left: -20px;
  margin-right: -20px;
  border-bottom: 0;
}
.form-hr.__big:before {
  position: absolute;
  content: '';
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  border-top: 0.5px solid #d6dce1;
}
.form-label {
  display: block;
  margin-bottom: 1px;
  font-size: 14px;
  font-weight: 500;
}
.form-label__quest {
  display: inline-block;
  vertical-align: top;
  margin-top: 2px;
  margin-left: 2px;
  margin-bottom: -2px;
  color: rgba(165, 178, 188, 0.3);
  cursor: pointer;
}
.form-label__quest:hover {
  color: rgba(165, 178, 188, 0.5);
}
.form-label__side {
  font-weight: normal;
  float: right;
}

.form-checkbox {
  cursor: pointer;
  display: inline-flex;
  max-width: 100%;
  vertical-align: top;
  font-size: 1rem;
  line-height: 1.25rem;
}
.form-checkbox__chk {
  position: absolute;
  left: -9999px;
  top: 0;
  opacity: 0;
}
.form-checkbox__chk[type='checkbox']:checked + .form-checkbox__el {
  background-color: var(--color-primary);
  border-width: 0;
}
.form-checkbox__chk[type='checkbox']:checked + .form-checkbox__el:before {
  opacity: 1;
  -webkit-transform: translate(0, 0);
  transform: translate(0, 0);
}
.form-checkbox__chk[type='checkbox']:disabled + .form-checkbox__el {
  background-color: rgba(165, 178, 188, 0.2) !important;
  border-width: 0;
}
.form-checkbox__chk[type='checkbox']:disabled + .form-checkbox__el:before {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 13 10' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cdefs%3E%3Cpath d='M49.59,636.08l-3.17,-3.17l-1.42,1.41l4.59,4.59l8,-8l-1.41,-1.41z' id='a'/%3E%3C/defs%3E%3Cg transform='translate(-45 -629)'%3E%3Cuse xlink:href='%23a' fill='%23C5CDD4'/%3E%3C/g%3E%3C/svg%3E");
}

.form-checkbox.partly .form-checkbox__chk[type='checkbox'] + .form-checkbox__el:before {
  background: #FFFFFF;
  width: 12px;
  height: 2px;
  top: 4px;
}

.form-checkbox__chk[type='checkbox']:checked:disabled
  + .form-checkbox__el:before {
  opacity: 1;
  -webkit-transform: translate(0, 0);
  transform: translate(0, 0);
}
.form-checkbox__chk[type='radio'] + .form-checkbox__el {
  border-radius: 20px;
}
.form-checkbox__chk[type='radio'] + .form-checkbox__el:after {
  position: absolute;
  content: '';
  left: 50%;
  top: 50%;
  margin-left: -4px;
  margin-top: -4px;
  -webkit-transform-origin: center;
  transform-origin: center;
  background-color: #fff;
  width: 8px;
  height: 8px;
  opacity: 0;
  border-radius: 50%;
  -webkit-transform: scale(2.5);
  transform: scale(2.5);
  transition: opacity 0.25s, -webkit-transform 0.25s;
  transition: transform 0.25s, opacity 0.25s;
  transition: transform 0.25s, opacity 0.25s, -webkit-transform 0.25s;
}
.form-checkbox__chk[type='radio']:disabled + .form-checkbox__el {
  background-color: rgba(165, 178, 188, 0.2) !important;
  border-width: 0;
}
.form-checkbox__chk[type='radio']:checked:disabled + .form-checkbox__el:after {
  background-color: #c5cdd4;
}
.form-checkbox__chk[type='radio']:checked + .form-checkbox__el {
  background-color:var(--color-primary);
  border-width: 0;
}
.form-checkbox__chk[type='radio']:checked + .form-checkbox__el:after {
  opacity: 1;
  -webkit-transform: scale(1);
  transform: scale(1);
}
.form-checkbox__el {
  position: relative;
  width: 20px;
  height: 20px;
  border-radius: 5px;
  border: 2px solid rgba(165, 178, 188, 0.3);
  flex: 0 0 auto;
  background-color: rgba(39, 174, 96, 0);
  transition: opacity 0.15s, border-width 0.15s, border-color 0.15s,
    background-color 0.15s;
  margin-top: -1.5px;
}
.form-checkbox__el:before {
  position: absolute;
  content: '';
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 13 10' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cdefs%3E%3Cpath d='M49.59,636.08l-3.17,-3.17l-1.42,1.41l4.59,4.59l8,-8l-1.41,-1.41z' id='a'/%3E%3C/defs%3E%3Cg transform='translate(-45 -629)'%3E%3Cuse xlink:href='%23a' fill='%23fff'/%3E%3C/g%3E%3C/svg%3E");
  background-size: contain;
  background-position: 50%;
  width: 13px;
  height: 10px;
  opacity: 0;
  left: 0;
  margin: 5px 0 0 4px;
  -webkit-transform: translate(-2px, -7px);
  transform: translate(-2px, -7px);
  transition: opacity 0.15s, -webkit-transform 0.15s;
  transition: transform 0.15s, opacity 0.15s;
  transition: transform 0.15s, opacity 0.15s, -webkit-transform 0.15s;
}
.form-checkbox__value {
  padding-left: 6px;
  flex: 1 1 auto;
}
.form-checkbox__value.__oneline {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.form-checkbox__emoji {
  display: inline-flex;
  vertical-align: top;
  align-items: center;
  justify-content: center;
  height: 24px;
  margin: -2px 0;
}
.form-checkbox__emoji img {
  vertical-align: top;
}
.form-checkbox:hover .form-checkbox__el {
  border-color: rgba(165, 178, 188, 0.7);
}
.form-checkbox:hover .form-checkbox__chk:checked + .form-checkbox__el {
  background-color: var(--color-secondary);
}
.form-checkbox[data-legend='color-1']
  .form-checkbox__chk:checked
  + .form-checkbox__el {
  background-color: #f4d359;
}
.form-checkbox[data-legend='color-1']:hover
  .form-checkbox__chk:checked
  + .form-checkbox__el {
  background-color: #d3b442;
}
.form-checkbox[data-legend='color-2']
  .form-checkbox__chk:checked
  + .form-checkbox__el {
  background-color: #72bbf5;
}
.form-checkbox[data-legend='color-2']:hover
  .form-checkbox__chk:checked
  + .form-checkbox__el {
  background-color: #5ca4de;
}
.form-checkbox[data-legend='color-3']
  .form-checkbox__chk:checked
  + .form-checkbox__el {
  background-color: #a955b8;
}
.form-checkbox[data-legend='color-3']:hover
  .form-checkbox__chk:checked
  + .form-checkbox__el {
  background-color: #9440a3;
}
.form-checkbox[data-legend='color-4']
  .form-checkbox__chk:checked
  + .form-checkbox__el {
  background-color: #27ae60;
}
.form-checkbox[data-legend='color-4']:hover
  .form-checkbox__chk:checked
  + .form-checkbox__el {
  background-color: #239553;
}
.form-checkbox[data-color='inherit'] .form-checkbox__el {
  border-color: currentColor;
  opacity: 0.5;
}
.form-checkbox[data-color='inherit']:hover .form-checkbox__el {
  border-color: currentColor;
  opacity: 1;
}
.form-checkbox[data-color='inherit']
  .form-checkbox__chk:checked
  + .form-checkbox__el {
  background-color: currentColor;
  opacity: 1;
}
.form-checkbox[data-color='inherit']:hover
  .form-checkbox__chk:checked
  + .form-checkbox__el {
  background-color: currentColor;
  opacity: 1;
}
.form-tooglebox,
.form-togglebox {
  background-color: #e9ecef;
  border-radius: 8px;
  padding: 4px;
  color: #222;
  font-size: 14px;
}
.form-tooglebox__inner,
.form-togglebox__inner {
  position: relative;
  display: flex;
}
.form-tooglebox__bg,
.form-togglebox__bg {
  position: absolute;
  z-index: 1;
  left: 0;
  top: 0;
  bottom: 0;
  width: 50%;
  border-radius: 6px;
  background-color: #fff;
  transition: -webkit-transform 0.15s;
  transition: transform 0.15s;
  transition: transform 0.15s, -webkit-transform 0.15s;
}
.form-tooglebox__item,
.form-togglebox__item {
  cursor: pointer;
  position: relative;
  z-index: 2;
  flex: 50%;
  display: flex;
  height: 34px;
  align-items: center;
  text-align: center;
  padding: 0 5px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.form-tooglebox__value,
.form-togglebox__value {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1rem;
  max-height: 32px;
}
.form-tooglebox__input,
.form-togglebox__input {
  position: absolute;
  left: -9999px;
  opacity: 0;
}
.form-tooglebox__input:checked + .form-togglebox__item,
.form-togglebox__input:checked + .form-togglebox__item,
.form-tooglebox__input:checked + .form-tooglebox__item,
.form-togglebox__input:checked + .form-tooglebox__item {
  font-weight: 500;
}
.form-tooglebox__input[data-input='last']:checked ~ .form-togglebox__bg,
.form-togglebox__input[data-input='last']:checked ~ .form-togglebox__bg,
.form-tooglebox__input[data-input='last']:checked ~ .form-tooglebox__bg,
.form-togglebox__input[data-input='last']:checked ~ .form-tooglebox__bg {
  -webkit-transform: translate(100%, 0);
  transform: translate(100%, 0);
}
.form-tooglebox__bg,
.form-togglebox__bg {
  position: absolute;
}
.form-tooglebox[data-theme='dark'],
.form-togglebox[data-theme='dark'] {
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.25);
  background-color: #222;
  color: #fff;
}
.form-tooglebox[data-theme='dark'] .form-tooglebox__bg,
.form-togglebox[data-theme='dark'] .form-tooglebox__bg {
  background-color: #383838;
}
.form-tooglebox[data-boxes='true']
  .form-tooglebox__input:checked
  + .form-togglebox__item,
.form-togglebox[data-boxes='true']
  .form-tooglebox__input:checked
  + .form-togglebox__item,
.form-tooglebox[data-boxes='true']
  .form-togglebox__input:checked
  + .form-togglebox__item,
.form-togglebox[data-boxes='true']
  .form-togglebox__input:checked
  + .form-togglebox__item,
.form-tooglebox[data-boxes='true']
  .form-tooglebox__input:checked
  + .form-tooglebox__item,
.form-togglebox[data-boxes='true']
  .form-tooglebox__input:checked
  + .form-tooglebox__item,
.form-tooglebox[data-boxes='true']
  .form-togglebox__input:checked
  + .form-tooglebox__item,
.form-togglebox[data-boxes='true']
  .form-togglebox__input:checked
  + .form-tooglebox__item {
  font-weight: 500;
  background-color: #fff;
  border-radius: 6px;
}
.form-total {
  display: flex;
  font-size: 16px;
  font-weight: 500;
}
.form-total__label {
  flex: 1 1 auto;
  min-width: 0;
}
.form-total__value {
  flex: 0 0 auto;
}
.list-checkboxes {
  list-style-type: none;
  padding: 0;
  margin: 0;
}
.list-checkboxes__list-item {
  margin: 0 0 12px;
}
.form-label + .list-checkboxes {
  margin-top: 12px;
}
.form-split-group {
  display: flex;
  align-items: flex-end;
}
.form-split-group .form-select,
.form-split-group .form-input {
  border-radius: 0;
}
.form-split-group__col {
  flex: 0 0 auto;
}
.form-split-group__col.__fluid {
  flex: 1 1 auto;
  min-width: 0;
}
.form-split-group__col:first-child .form-select,
.form-split-group__col:first-child .form-input {
  border-bottom-left-radius: 8px;
  border-top-left-radius: 8px;
}
.form-split-group__col:last-child .form-select,
.form-split-group__col:last-child .form-input {
  border-bottom-right-radius: 8px;
  border-top-right-radius: 8px;
}
.form-input-clear {
  position: absolute;
  z-index: 3;
  right: 11px;
  top: 50%;
  width: 20px;
  height: 20px;
  background: rgba(165, 178, 188, 0.25);
  border: none;
  padding: 0;
  margin: -10px 0 0;
  border-radius: 20px;
  font-size: 0;
  line-height: 0;
  display: none;
}
.form-input-clear:hover {
  background-color: rgba(165, 178, 188, 0.5);
}
.form-input-clear:before {
  position: absolute;
  content: '';
  left: 50%;
  top: 50%;
  margin-left: -1px;
  margin-top: -6px;
  width: 2px;
  height: 12px;
  background-color: #fff;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
}
.form-input-clear:after {
  position: absolute;
  content: '';
  left: 50%;
  top: 50%;
  margin-left: -1px;
  margin-top: -6px;
  width: 2px;
  height: 12px;
  background-color: #fff;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}
.form-input-clear[data-size='small'] {
  width: 16px;
  height: 16px;
  margin-top: -8px;
}
.form-input-clear[data-size='small']:after {
  -webkit-transform: rotate(0);
  transform: rotate(0);
  width: 8px;
  height: 8px;
  margin: -4px 0 0 -4px;
  background: url("data:image/svg+xml,%3Csvg width='8' height='8' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cpath d='M95.76408,13.236 L95.76408,13.236 C95.4517077,12.9236277 94.9452523,12.9236277 94.63288,13.236 L92.00008,15.8688 L89.36728,13.236 C89.0549077,12.9236277 88.5484523,12.9236277 88.23608,13.236 L88.23608,13.236 C87.9237077,13.5483723 87.9237077,14.0548277 88.23608,14.3672 L90.86888,17 L88.23608,19.6328 C87.9237077,19.9451723 87.9237077,20.4516277 88.23608,20.764 L88.23608,20.764 C88.5484523,21.0763723 89.0549077,21.0763723 89.36728,20.764 L92.00008,18.1312 L94.63288,20.764 C94.9452523,21.0763723 95.4517077,21.0763723 95.76408,20.764 L95.76408,20.764 C96.0764523,20.4516277 96.0764523,19.9451723 95.76408,19.6328 L93.13128,17 L95.76408,14.3672 C96.0764523,14.0548277 96.0764523,13.5483723 95.76408,13.236 Z' transform='translate(-88 -13)' fill='%23ffffff' fill-rule='evenodd'/%3E%3C/svg%3E");
}
.form-input-clear[data-size='small']:before {
  display: none;
}
.form-input-clear[data-border='true']:before {
  position: absolute;
  content: '';
  display: block;
  left: -11px;
  margin-top: -14px;
  pointer-events: none;
  width: 1px;
  height: 28px;
  -webkit-transform: rotate(0deg);
  transform: rotate(0deg);
  background-color: rgba(214, 220, 225, 0.5);
}
.form-input-clear[data-theme='black'] {
  background-color: #222;
}
.form-input-clear[data-theme='black']:hover {
  background-color: #000;
}
.form-inline-fields {
  display: flex;
  align-items: center;
}
.form-inline-fields__label {
  flex: 0 0 auto;
  font-size: 1rem;
  line-height: 1.25rem;
  padding-right: 10px;
}
.form-inline-fields__input {
  flex: 1 1 auto;
}
.form-photos {
  display: flex;
  flex-wrap: wrap;
  margin-left: -5px;
}
.form-photos__list-item {
  flex: 1.5;
  min-width: 33%;
  margin-bottom: 5px;
  height: 83px;
  display: flex;
  padding-left: 5px;
}
.form-photos__list-item:only-child {
  height: 146px;
}
.form-photos__pic {
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 6px;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: 50%;
}
.form-photos__item {
  position: relative;
  background-color: rgba(214, 220, 225, 0.2);
  border-radius: 6px;
  width: 100%;
  cursor: pointer;
}
.form-photos__item:before {
  position: absolute;
  z-index: 2;
  content: '';
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 6px;
  opacity: 0;
}
.form-photos__item:hover:before {
  opacity: 1;
}
.form-photos__item:hover .form-photos__remove {
  opacity: 1;
}
.form-photos__item[data-status='loading']:before {
  opacity: 1;
}
.form-photos__item[data-status='loading'] .form-photos__remove {
  opacity: 1;
}
.form-photos-progress {
  position: absolute;
  z-index: 3;
  width: 40px;
  height: 40px;
  top: 50%;
  left: 50%;
  margin-top: -20px;
  margin-left: -20px;
  vertical-align: top;
  -webkit-transform: rotate(-90deg);
  transform: rotate(-90deg);
}
.form-photos-progress__value {
  fill: none;
  stroke: #fff;
  stroke-linecap: round;
  -webkit-transform-origin: center;
  transform-origin: center;
}
.form-photos__remove {
  position: absolute;
  z-index: 4;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: none;
  color: #fff;
  font-size: 0;
  background: none;
  opacity: 0;
  width: 100%;
}
.form-photos__remove:before {
  position: absolute;
  width: 20px;
  height: 20px;
  top: 50%;
  left: 50%;
  margin-left: -10px;
  margin-top: -10px;
  content: '';
  background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10 20C4.477 20 0 15.523 0 10S4.477 0 10 0s10 4.477 10 10-4.477 10-10 10m5.412-13.998l-1.414-1.414L10 8.586 6.002 4.588 4.588 6.002 8.586 10l-3.998 3.998 1.414 1.414L10 11.414l3.998 3.998 1.414-1.414L11.414 10z' fill='%23fff'/%3E%3C/svg%3E");
}
.form-input-extended {
  position: relative;
}
.form-input-extended[data-prepend='ico'] .form-input {
  padding-left: 35px;
}
.form-input-extended__ico {
  position: absolute;
  top: 50%;
  left: 2px;
  margin-top: -19px;
  width: 35px;
  height: 38px;
  line-height: 38px;
  text-align: center;
  display: flex;
}
.form-input-extended__ico img {
  vertical-align: top;
  margin: auto;
}
.form-input-extended.__filled .form-input-clear {
  display: block;
}
.form-input-extended.__filled .form-input {
  padding-right: 40px;
}
.form-group-grid {
  display: flex;
}
.form-group-grid__col {
  flex: 0 0 auto;
}
.form-group-grid__col.__fluid {
  flex: 1 1 auto;
  min-width: 0;
}
.form-group-grid__col.__calendarlabel {
  width: 22px;
  align-self: center;
}
.form-group-grid__col.__dash {
  padding: 0 4px;
  align-self: center;
}
.form-group-grid.__sps-default {
  margin-left: -10px;
}
.form-group-grid.__sps-default .form-group-grid__col {
  padding-left: 10px;
}
.form-group-grid.__sps-medium {
  margin-left: -15px;
}
.form-group-grid.__sps-medium .form-group-grid__col {
  padding-left: 15px;
}
.form-append {
  position: relative;
}
.form-append:after {
  position: absolute;
  pointer-events: none;
  top: 2px;
  bottom: 2px;
  right: 2px;
  content: attr(data-appendix);
  color: #a5b2bc;
  white-space: nowrap;
  padding: 9px 13px 9px 3px;
  background-color: rgba(255, 255, 255, 0.9);
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
  font-size: 1rem;
  line-height: 1.25rem;
}
.form-append--disabled:after {
  color: rgba(165, 178, 188, 0.5);
}
.form-group-items-header {
  display: flex;
  padding-bottom: 6px;
}
.form-group-items-header__col {
  flex: 0 0 auto;
  font-size: 12px;
  font-weight: 500;
}
.form-group-items-header__col.__fluid {
  flex: 1 1 auto;
  min-width: 0;
}
.form-group-items-row {
  padding-bottom: 5px;
  display: flex;
}
.form-group-items-row__col {
  flex: 0 0 auto;
  border-left: 0.5px solid #c1cbd4;
}
.form-group-items-row__col.__fluid {
  flex: 1 1 auto;
  min-width: 0;
}
.form-group-items-row__col:first-child {
  border-left-width: 0;
}
.form-group-items-row__col:first-child .form-select,
.form-group-items-row__col:first-child .form-input {
  border-bottom-left-radius: 6px;
  border-top-left-radius: 6px;
}
.form-group-items-row__col:last-child .form-select,
.form-group-items-row__col:last-child .form-input {
  border-bottom-right-radius: 6px;
  border-top-right-radius: 6px;
}
.form-group-items .form-select,
.form-group-items .form-input {
  border-radius: 0;
}

.pagination {
  display: inline-flex;
  min-height: 42px;
}
.pagination__item {
  background-color: #e9ecef;
  box-sizing: content-box;
  min-width: 42px;
  text-align: center;
  flex: 0 0 auto;
  color: #222;
  display: flex;
  align-items: center;
  justify-content: center;
}
.pagination__item:first-child {
  border-bottom-left-radius: 6px;
  border-top-left-radius: 6px;
}
.pagination__item:last-child {
  border-bottom-right-radius: 6px;
  border-top-right-radius: 6px;
}
.pagination__item + .pagination__item {
  border-left: 1px solid #d2d4d9;
}
.pagination__item + .pagination__item.__disabled {
  border-left-color: rgba(210, 212, 217, 0.75);
}
.pagination__item:hover {
  background-color: #d5dbdf;
}
.pagination__item.__disabled {
  color: #c5cdd4;
  pointer-events: none;
  background-color: #f5f7f9;
}
.pagination__item.__disabled:hover {
  background-color: #f5f7f9;
}
.pagination-arrow-back {
  margin: 0 auto;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  border-right: 4px solid currentColor;
  width: 0;
  height: 0;
  display: inline-block;
  vertical-align: top;
}
.pagination-arrow-forward {
  margin: 0 auto;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  border-left: 4px solid currentColor;
  width: 0;
  height: 0;
  display: inline-block;
  vertical-align: top;
}
.pagination-small {
  min-height: 34px;
}
.pagination-small .pagination-arrow-back,
.pagination-small .pagination-arrow-forward {
  min-width: 34px;
}
.tags-list {
  display: flex;
  flex-wrap: wrap;
  padding: 0;
  margin: 0 0 -5px -5px;
  list-style-type: none;
}
.tags-list__list-item {
  margin-left: 5px;
  margin-bottom: 5px;
  flex: 0 0 auto;
}
.tags-list__item {
  display: flex;
  border-radius: 17px;
  border: 1px solid rgba(165, 178, 188, 0.5);
  line-height: 1.25rem;
  padding: 7px 8px 5px 14px;
  align-items: center;
  background: none;
}
.tags-list__item[data-action='add'] {
  padding-left: 30px;
  padding-right: 16px;
  font-weight: 500;
  position: relative;
}
.tags-list__item[data-action='add']:hover {
  color: #27ae60;
}
.tags-list__item[data-action='add']:after {
  position: absolute;
  content: '';
  left: 17px;
  width: 8px;
  height: 2px;
  top: 50%;
  margin-top: -1px;
  background-color: currentColor;
}
.tags-list__item[data-action='add']:before {
  position: absolute;
  content: '';
  left: 20px;
  top: 50%;
  margin-top: -4px;
  height: 8px;
  width: 2px;
  background-color: currentColor;
}
.tags-list__item[disabled],
.tags-list__item.__disabled {
  color: rgba(165, 178, 188, 0.7);
  pointer-events: none;
  background-color: #f5f7f9;
  border-color: transparent;
}
.tags-list__value {
  flex: 0 0 auto;
  max-width: 300px;
  overflow: hidden;
  white-space: nowrap;
  position: relative;
  text-overflow: ellipsis;
}
.tags-list .form-input-clear {
  position: relative;
  top: auto;
  margin: -2px 0 0 5px;
  width: 16px;
  height: 16px;
  flex: 0 0 auto;
  display: block;
  right: auto;
  background-color: rgba(165, 178, 188, 0.35);
}
.tags-list .form-input-clear:after {
  display: none;
}
.tags-list .form-input-clear:before {
  -webkit-transform: rotate(0);
  transform: rotate(0);
  width: 8px;
  height: 8px;
  margin: -4px 0 0 -4px;
  background: url("data:image/svg+xml,%3Csvg width='8' height='8' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cpath d='M95.76408,13.236 L95.76408,13.236 C95.4517077,12.9236277 94.9452523,12.9236277 94.63288,13.236 L92.00008,15.8688 L89.36728,13.236 C89.0549077,12.9236277 88.5484523,12.9236277 88.23608,13.236 L88.23608,13.236 C87.9237077,13.5483723 87.9237077,14.0548277 88.23608,14.3672 L90.86888,17 L88.23608,19.6328 C87.9237077,19.9451723 87.9237077,20.4516277 88.23608,20.764 L88.23608,20.764 C88.5484523,21.0763723 89.0549077,21.0763723 89.36728,20.764 L92.00008,18.1312 L94.63288,20.764 C94.9452523,21.0763723 95.4517077,21.0763723 95.76408,20.764 L95.76408,20.764 C96.0764523,20.4516277 96.0764523,19.9451723 95.76408,19.6328 L93.13128,17 L95.76408,14.3672 C96.0764523,14.0548277 96.0764523,13.5483723 95.76408,13.236 Z' transform='translate(-88 -13)' fill='%23ffffff' fill-rule='evenodd'/%3E%3C/svg%3E");
}
.tags-list .form-input-clear:hover {
  background-color: rgba(165, 178, 188, 0.55);
}
.form-upload {
  position: relative;
  overflow: hidden;
  color: #007aff;
}
.form-upload:hover {
  color: #014e86;
}
.form-upload__input {
  font-size: 80px;
  cursor: pointer;
  position: absolute;
  right: 0;
  top: 0;
  opacity: 0;
}
.form-upload__label {
  font-size: 12px;
  font-weight: 500;
}
.form-upload__label svg {
  margin-top: 2px;
  vertical-align: top;
}
.u-select {
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.u-select__virtualselect {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: none;
  background: none;
  opacity: 0;
  font-size: 14px;
}
.u-select__virtualselect option {
  font-size: 14px;
}
.nitro-table {
  border-bottom: 0.5px solid rgba(165, 178, 188, 0.45);
  margin-left: -20px;
  margin-right: -20px;
}
.nitro-table table {
  width: 100%;
}
.nitro-table th {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.3;
  padding: 0 4px 15px 16px;
  text-align: left;
}
.nitro-table td {
  padding: 14px 4px 14px 16px;
  vertical-align: top;
  border-top: 0.5px solid #d6dce0;
  text-align: left;
}
.nitro-table tr td:last-child,
.nitro-table tr th:last-child {
  padding-right: 20px;
}
.nitro-table .bgc-entity {
  padding: 0;
}
.fields-table {
  margin-left: -20px;
  margin-bottom: -10px;
}
.fields-table__quest {
  color: rgba(165, 178, 188, 0.3);
  display: inline-block;
  vertical-align: top;
  margin-top: 0;
  margin-bottom: -3px;
  cursor: pointer;
}
.fields-table table {
  width: 100%;
}
.fields-table table[data-layout='fixed'] {
  table-layout: fixed;
}
.fields-table table[data-valign='top'] td {
  padding-top: 8px;
  vertical-align: top;
}
.fields-table td {
  padding: 0 0 10px 20px;
  height: 52px;
  font-size: 16px;
  line-height: 1.25rem;
  vertical-align: middle;
}
.fields-table th {
  padding: 0 0 2px 20px;
  text-align: left;
  line-height: 1.063rem;
  font-weight: 500;
}
.fields-table tfoot td {
  vertical-align: top;
  height: auto;
}
.fields-table tfoot small {
  display: inline-block;
  font-size: 14px;
  line-height: 0.9375rem;
}
.fields-table-area {
  display: flex;
  align-items: center;
}
.fields-table-area__int {
  flex: 0 0 auto;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 6px;
}
.fields-table-area__value {
  flex: 1 1 auto;
}
.form-checkboxes-with-options {
  display: inline-flex;
}
.form-checkboxes-with-options__checkbox {
  flex: 1 1 auto;
  min-width: 0;
}
.form-checkboxes-with-options__checkbox .form-checkbox {
  max-width: 100%;
}
.form-checkboxes-with-options .form-checkbox__value {
  white-space: nowrap;
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
}
.form-checkboxes-with-options__option {
  flex: 0 0 auto;
  padding-left: 10px;
  font-size: 12px;
  padding-top: 3px;
}
.form-enter-checkboxes {
  list-style-type: none;
  padding: 0;
  margin: 0 0 40px -15px;
  display: flex;
}
.form-enter-checkboxes__list-item {
  flex: 1;
  margin: 0 0 0 15px;
}
.form-enter-checkboxes__chk {
  position: absolute;
  left: -9999px;
}
.form-enter-checkboxes__item {
  cursor: pointer;
}
.form-enter-checkboxes__action {
  display: block;
  box-shadow: 0 0 6px rgba(236, 236, 239, 0.5);
  border-radius: 12px;
  border: 1px solid rgba(214, 220, 225, 0.5);
  background-color: #fff;
  padding: 19px 15px 17px;
  text-align: center;
}
.form-enter-checkboxes__action:hover {
  background-color: #fafafb;
  box-shadow: none;
}
.form-enter-checkboxes__symb {
  display: flex;
  margin: 0 auto 8px;
  width: 50px;
  height: 50px;
  background-color: rgba(165, 178, 188, 0.15);
  border-radius: 50%;
  align-items: center;
  justify-content: center;
}
.form-enter-checkboxes__symb svg {
  vertical-align: top;
  margin: auto;
}
.form-enter-checkboxes__label {
  font-size: 16px;
  font-weight: 500;
}
.form-colorpicker {
  list-style-type: none;
  padding: 0;
  margin: 0 0 0 -13px;
  flex-wrap: wrap;
  display: flex;
}
.form-colorpicker__list-item {
  margin-left: 13px;
}
.form-colorpicker__radio {
  position: absolute;
  left: -9999px;
}
.form-colorpicker__radio:checked + .form-colorpicker__color:before {
  opacity: 1;
  -webkit-transform: scale(1);
  transform: scale(1);
}
.form-colorpicker__item {
  cursor: pointer;
}
.form-colorpicker__color {
  display: flex;
  width: 26px;
  height: 26px;
  border-radius: 50%;
  align-items: center;
  justify-content: center;
}
.form-colorpicker__color:before {
  opacity: 0;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #fff;
  content: '';
  -webkit-transform: scale(2.5);
  transform: scale(2.5);
  transition: opacity 0.25s, -webkit-transform 0.25s;
  transition: transform 0.25s, opacity 0.25s;
  transition: transform 0.25s, opacity 0.25s, -webkit-transform 0.25s;
}
.modal {
  position: fixed;
  z-index: 999;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  display: flex;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.1s ease-out, visibility 0.1s ease-out;
}
.modal.__opened {
  display: flex;
  opacity: 1;
  visibility: visible;
}
.modal.__opened .modal-outer {
  -webkit-transform: scale(1);
  transform: scale(1);
}
.modal-outer {
  position: relative;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.25);
  border-radius: 8px;
  background-color: #fff;
  -webkit-transform: scale(0.8);
  transform: scale(0.8);
  transition: -webkit-transform 0.1s ease-out;
  transition: transform 0.1s ease-out;
  transition: transform 0.1s ease-out, -webkit-transform 0.1s ease-out;
}
.modal-inline {
  -webkit-transform: scale(1);
  transform: scale(1);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e4e7ea;
}
.modal-inner {
  position: relative;
  z-index: 1;
  overflow-y: auto;
  max-height: 100vh;
  -webkit-overflow-scrolling: touch;
}
.modal-heading {
  text-align: center;
  margin-bottom: 17px;
}
.modal-heading__maintitle {
  margin-bottom: 0;
  margin-top: 0;
  font-size: 18px;
  line-height: 1.23;
  font-weight: 500;
}
.modal-heading__desc {
  color: rgba(34, 34, 34, 0.46);
  font-size: 12px;
  margin-top: 0;
  margin-bottom: 0;
}
.modal-close {
  position: absolute;
  z-index: 2;
  top: 11px;
  right: 12px;
  width: 34px;
  height: 34px;
  font-size: 0;
  background: #fff;
  border-width: 0;
  color: #222222;
  cursor: pointer;
}
.modal-close:before {
  position: absolute;
  content: '';
  left: 50%;
  margin-left: -6px;
  top: 50%;
  margin-top: -1px;
  width: 12px;
  height: 2px;
  border-radius: 1px;
  background-color: currentColor;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}
.modal-close:after {
  position: absolute;
  content: '';
  left: 50%;
  margin-left: -6px;
  top: 50%;
  margin-top: -1px;
  width: 12px;
  height: 2px;
  border-radius: 1px;
  background-color: currentColor;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
}
.modal-close:hover {
  color: #999;
}
.modal-content {
  display: flex;
  flex-direction: column;
}
.modal-content__header {
  position: relative;
  flex: 0 0 auto;
  font-size: 24px;
  font-weight: 700;
  line-height: 1.25;
  padding: 20px 25px 14px;
}
.modal-content__body {
  position: relative;
  flex: 1 1 auto;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0 25px 0;
}
.modal-content__body .form-hr.__mg,
.modal-content__body .form-hr.__big {
  margin-left: -25px;
  margin-right: -25px;
}
.modal-content__body .edit-action-delete {
  font-size: 16px;
}
.modal-content__actions {
  position: relative;
  padding: 10px 10px 10px 0;
  flex: 0 0 auto;
  display: flex;
}
.modal-content__errors {
  color: #a52c2c;
  margin: 10px 25px;
  display: flex;
}
.modal-content__actions:after {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  content: '';
  height: 1px;
  border-top: 0.5px solid #d6dce1;
}
.modal-content__actions .btn {
  flex: 1.5;
  margin-left: 10px;
}
.modal .main-container-notification {
  top: 0;
}
.modal-history .modal-outer {
  width: 100%;
  max-width: 1000px;
}
.modal-history .modal-inner {
  padding: 30px 48px 13px;
}
.modal-editsettings .modal-outer {
  width: 100%;
  max-width: 340px;
}
.modal-editsettings-content {
  display: flex;
  flex-direction: column;
}
.modal-editsettings-content__header {
  position: relative;
  flex: 0 0 auto;
  font-size: 19px;
  font-weight: 500;
  line-height: 21px;
  letter-spacing: -0.4px;
  padding: 20px 20px 20px;
}
.modal-editsettings-content__header:after {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  content: '';
  height: 1px;
  border-bottom: 0.5px solid #d6dce1;
}
.modal-editsettings-content__body {
  position: relative;
  flex: 1 1 auto;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 15px 20px 0;
}
.modal-editsettings-content__actions {
  position: relative;
  padding: 10px 10px 10px 0;
  flex: 0 0 auto;
  display: flex;
}
.modal-editsettings-content__actions:after {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  content: '';
  height: 1px;
  border-top: 0.5px solid #d6dce1;
}
.modal-editsettings-content__actions .btn {
  flex: 1.5;
  margin-left: 10px;
}
.modal-editseasons .modal-outer {
  width: 100%;
  max-width: 444px;
}
.modal-editseasons-content {
  display: flex;
  flex-direction: column;
}
.modal-editseasons-content__header {
  position: relative;
  flex: 0 0 auto;
  font-size: 24px;
  font-weight: 700;
  line-height: 1.25;
  padding: 20px 25px 14px;
}
.modal-editseasons-content__body {
  position: relative;
  flex: 1 1 auto;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0 25px 0;
}
.modal-editseasons-content__body .form-hr.__mg,
.modal-editseasons-content__body .form-hr.__big {
  margin-left: -25px;
  margin-right: -25px;
}
.modal-editseasons-content__body .edit-action-delete {
  font-size: 16px;
}
.modal-editseasons-content__actions {
  position: relative;
  padding: 10px 10px 10px 0;
  flex: 0 0 auto;
  display: flex;
}
.modal-editseasons-content__actions:after {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  content: '';
  height: 1px;
  border-top: 0.5px solid #d6dce1;
}
.modal-editseasons-content__actions .btn {
  flex: 1.5;
  margin-left: 10px;
}
.modal-setfields .modal-outer {
  width: 100%;
  max-width: 444px;
}
.modal-setfields-content {
  display: flex;
  flex-direction: column;
}
.modal-setfields-content__header {
  position: relative;
  flex: 0 0 auto;
  font-size: 24px;
  font-weight: 700;
  line-height: 1.25;
  padding: 20px 25px 14px;
}
.modal-setfields-content__body {
  position: relative;
  flex: 1 1 auto;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0 25px 5px;
}
.modal-setfields-content__body .form-hr.__mg,
.modal-setfields-content__body .form-hr.__big {
  margin-left: -25px;
  margin-right: -25px;
}
.modal-setfields-content__actions {
  position: relative;
  padding: 10px 10px 10px 0;
  flex: 0 0 auto;
  display: flex;
}
.modal-setfields-content__actions:after {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  content: '';
  height: 1px;
  border-top: 0.5px solid #d6dce1;
}
.modal-setfields-content__actions .btn {
  flex: 1.5;
  margin-left: 10px;
}
.modal-defcreate .modal-outer {
  width: 100%;
  max-width: 444px;
}
.modal-rename.modal-outer {
  width: 100%;
  max-width: 320px;
}
.modal-rename .modal-content__body {
  padding: 15px;
}
.modal-settime.modal-outer {
  width: 100%;
  max-width: 320px;
}
.modal-settime .modal-content__body {
  padding: 15px;
}
.map-content {
  width: 100%;
  top: 0;
  right: 0;
  height: 100%;
  position: absolute;
  z-index: 101;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  will-change: auto;
}
.map-content--bg-gray {
  background-color: #fafafb;
}
.map-content__close {
  position: absolute;
  z-index: 4;
  right: 0;
  top: 0;
  padding: 20px;
  background: none;
  border: none;
}
.map-content__close:hover {
  color: #27ae60;
}
.map-content__close[data-theme='round'] {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  background-color: #e4e7ea;
  padding: 1px 0 0;
  right: 20px;
  top: 20px;
}
.map-content__close[data-theme='round'] svg {
  vertical-align: top;
}
.map-content__close[data-theme='round']:hover {
  color: #222;
  background-color: #d5dbdf;
}
.map-content.__initial {
  -webkit-transform: translate(100%, 0);
  transform: translate(100%, 0);
}
.map-content.__normal {
  -webkit-transform: translate(0, 0);
  transform: translate(0, 0);
  transition: -webkit-transform 0.2s;
  transition: transform 0.2s;
  transition: transform 0.2s, -webkit-transform 0.2s;
}
.map-content .app-nav-back {
  position: absolute;
  left: 16px;
  top: 16px;
}
.map-content-body {
  flex: 1 1 auto;
  position: relative;
  z-index: 2;
  overflow-y: auto;
  min-height: 0;
  will-change: scroll-position;
}
.map-content-header {
  position: relative;
  z-index: 5;
  flex: 0 0 auto;
  padding: 0 15px 0;
  min-height: 50px;
}
.map-content-header:after {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  content: '';
  height: 1px;
  border-bottom: 0.5px solid #d6dce1;
}
.map-content-header__inner {
  display: flex;
  align-items: center;
  min-height: 50px;
}
.map-content-header__main {
  text-align: center;
  font-size: 15px;
  line-height: 1.2;
  font-weight: 500;
  margin-top: 0;
  margin-bottom: 0;
  flex: 1 1 auto;
}
.map-content-header__main small {
  display: inline-block;
  color: #a5b2bc;
  font-size: 12px;
  font-weight: normal;
  letter-spacing: normal;
}
.popup-edit-bound {
  position: fixed;
  z-index: 992;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  flex-direction: column;
}
.popup-edit-bound .main-container-notification {
  position: relative;
  -webkit-transform: translate(0, 0);
  transform: translate(0, 0);
  transition: margin-top 0.15s;
  border-radius: 0;
  margin-top: -50px;
}
.popup-edit-bound .main-container-notification[data-size='long-tooltip'] {
  height: 66px;
  margin-top: -66px;
}
.popup-edit-bound .main-container-notification.__state-normal {
  -webkit-transform: translate(0, 0);
  transform: translate(0, 0);
  margin-top: 0;
}
.popup-edit-bound.__opened {
  display: flex;
}
.popup-edit-bound-tools {
  position: absolute;
  z-index: 3;
  right: 10px;
  top: 10px;
  display: flex;
}
.popup-edit-bound-tools-sub {
  position: absolute;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.25);
  border-radius: 8px;
  background-color: #222;
  left: 7px;
  top: 52px;
  list-style-type: none;
  padding: 0;
  margin: 0;
}
.popup-edit-bound-tools-sub .ico-trash-n-os {
  width: 14px;
  height: 16px;
}
.popup-edit-bound-tools-sub:first-child .popup-edit-bound-tools__item {
  border-top-right-radius: 8px;
  border-top-left-radius: 8px;
}
.popup-edit-bound-tools-sub:last-child .popup-edit-bound-tools__item {
  border-bottom-right-radius: 8px;
  border-bottom-left-radius: 8px;
}
.popup-edit-bound-tools-section {
  list-style-type: none;
  padding: 0;
  margin: 0;
  display: flex;
  margin-left: 10px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.25);
  border-radius: 8px;
  background-color: #222;
}
.popup-edit-bound-tools-section__list-item {
  position: relative;
}
.popup-edit-bound-tools-section__list-item:first-child
  .popup-edit-bound-tools__item {
  border-bottom-left-radius: 8px;
  border-top-left-radius: 8px;
}
.popup-edit-bound-tools-section__list-item
  + .popup-edit-bound-tools-section__list-item:before {
  content: '';
  position: absolute;
  z-index: 3;
  width: 2px;
  height: 22px;
  border-radius: 1px;
  background-color: rgba(255, 255, 255, 0.1);
  margin-left: -1px;
  margin-top: 10px;
}
.popup-edit-bound-tools__ico {
  display: inline-flex;
  vertical-align: top;
  width: 24px;
  height: 24px;
  margin: -3px 5px -1px 0;
  align-items: center;
  justify-content: center;
}
.popup-edit-bound-tools__ico svg {
  margin: auto;
}
.popup-edit-bound-tools__item {
  color: #fff;
  margin: 0;
  padding: 11px 16px 11px 12px;
  background: none;
  border: none;
  vertical-align: top;
  line-height: 1.25rem;
}
.popup-edit-bound-tools__item:hover,
.popup-edit-bound-tools__item.__selected {
  background-color: rgba(216, 216, 216, 0.1);
}
.popup-edit-bound__map {
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.popup-edit-bound__content {
  position: relative;
  z-index: 1;
  flex: 1 1 auto;
  min-height: 0;
}
.popup-edit-bound-inner-wrapper {
  display: flex;
}
.popup-edit-bound-inner {
  position: relative;
  flex: 1;
  top: 6px
}
.field-geometry-toolbar-wrapper {
  position: relative;
}
.field-geometry-toolbar-wrapper .popup-edit-bound-tools {
  position: relative;
}

.popup-edit-bound-container__content {
  top: 6px;
}

.modal-select {
  position: absolute;
  z-index: 941;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(214, 220, 225, 0.5);
  background-color: #fff;
  border-radius: 8px;
}
.modal-select.__state-initial {
  -webkit-transform: translate(0, 20px);
  transform: translate(0, 20px);
  opacity: 0;
}
.modal-select.__state-normal {
  -webkit-transform: translate(0, 0);
  transform: translate(0, 0);
  opacity: 1;
  transition: opacity 0.2s, -webkit-transform 0.2s;
  transition: opacity 0.2s, transform 0.2s;
  transition: opacity 0.2s, transform 0.2s, -webkit-transform 0.2s;
}
.modal-select > :first-child {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}
.modal-select > :last-child {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}
.modal-select__inner {
  position: relative;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  overflow-x: hidden;
  max-height: 280px;
  padding: 10px 0;
}
.modal-select__title {
  font-size: 15px;
  line-height: 1.25rem;
  color: #a5b2bc;
  padding: 0 15px;
  margin-bottom: 8px;
}
.modal-select__list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}
.modal-select__list + .modal-select__title {
  border-top: 1px solid rgba(214, 220, 225, 0.5);
  margin-top: 9px;
  padding-top: 15px;
}
.modal-select__list-item {
  position: relative;
}
.modal-select__list-item[aria-selected='true'] .modal-select__item {
  color: #222;
  background-color: #f5f7f9;
}
.modal-select__ico {
  flex: 0 0 auto;
  width: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  font-size: 0;
  margin: -8px 0;
}
.modal-select__ico img {
  vertical-align: top;
}
.model-select__icon {
  margin-right: 10px;
}
.modal-select__value {
  flex: 1 1 auto;
  text-overflow: ellipsis;
  width: 90%;
  overflow: hidden;
  white-space: nowrap;
}
.modal-select__abbr {
  color: #a5b2bc;
  padding-left: 10px;
}
.modal-select__item {
  display: flex;
  color: #222;
  font-size: 15px;
  line-height: 1.25rem;
  padding: 8px 15px;
  align-items: center;
  cursor: pointer;
}
.modal-select__item:hover,
.modal-select__item.__checked {
  color: #222;
  background-color: #f5f7f9;
}
.modal-select__item.__checked {
  font-weight: 500;
}
.modal-select__heading {
  font-size: 15px;
  line-height: 1.25rem;
  padding: 11px 15px 0;
  font-weight: 500;
}
.modal-select__loader {
  border-top: 1px solid rgba(214, 220, 225, 0.5);
  padding: 15px;
  text-align: center;
  font-size: 15px;
  line-height: 1.25rem;
}
.modal-select-searchbox {
  position: relative;
  z-index: 3;
  padding: 10px;
  border-bottom: 1px solid rgba(214, 220, 225, 0.5);
}
.modal-select-searchbox__icon {
  position: absolute;
  top: 13px;
  left: 15px;
  color: #a5b2bc;
}
.modal-select-searchbox .form-input {
  padding-left: 42px;
}
.modal-select-searchbox:after {
  position: absolute;
  pointer-events: none;
  content: '';
  height: 10px;
  bottom: -11px;
  left: 0;
  right: 0;
}
.modal-select[data-searchbox='true'] .modal-select__inner {
  padding-top: 0;
  max-height: 250px;
  min-height: 128px;
}
.modal-select[data-theme='black'] {
  background-color: #222;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.25);
  border-width: 0;
}
.modal-select[data-theme='black'] .modal-select__item {
  color: #fff;
}
.modal-select[data-theme='black'] .modal-select__item:hover,
.modal-select[data-theme='black'] .modal-select__item.__checked {
  color: #fff;
  background-color: #101010;
}
.modal-calendar {
  position: absolute;
  z-index: 942;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  background-color: #fff;
  border-radius: 8px;
  width: 268px;
  border: 1px solid rgba(214, 220, 225, 0.5);
}
.modal-calendar.__state-initial {
  -webkit-transform: translate(0, 20px);
  transform: translate(0, 20px);
  opacity: 0;
}
.modal-calendar.__state-normal {
  -webkit-transform: translate(0, 0);
  transform: translate(0, 0);
  opacity: 1;
  transition: opacity 0.2s, -webkit-transform 0.2s;
  transition: opacity 0.2s, transform 0.2s;
  transition: opacity 0.2s, transform 0.2s, -webkit-transform 0.2s;
}
.modal-calendar-action {
  border-top: 1px solid rgba(214, 220, 225, 0.5);
  padding: 0;
}
.modal-calendar-action__item {
  display: flex;
  align-items: center;
  min-height: 50px;
  cursor: pointer;
  border-bottom-left-radius: 7px;
  border-bottom-right-radius: 7px;
}
.modal-calendar-action__item:hover .modal-calendar-action__label {
  color: #27ae60;
}
.modal-calendar-action__item.disabled,
.modal-calendar-action__item[disabled] {
  pointer-events: none;
  color: rgba(165, 178, 188, 0.7) !important;
  background-color: #f5f7f9 !important;
}
.modal-calendar-action__item.disabled .modal-calendar-action__label,
.modal-calendar-action__item[disabled] .modal-calendar-action__label,
.modal-calendar-action__item.disabled .modal-calendar-action__note,
.modal-calendar-action__item[disabled] .modal-calendar-action__note {
  color: currentColor;
}
.modal-calendar-action__inner {
  width: 100%;
  text-align: center;
  padding: 0 20px;
  line-height: 1.125rem;
}
.modal-calendar-action__label {
  font-weight: 500;
  font-size: 15px;
  color: #222;
}
.modal-calendar-action__note {
  color: #a5b2bc;
  font-size: 12px;
}
.modal-calendar-action__ico {
  display: inline-flex;
  min-height: 20px;
  align-items: center;
  line-height: 1;
  font-size: 1px;
  margin: -1.5px 5px -1px 0;
  vertical-align: top;
}
.modal-calendar-action__ico svg {
  margin: auto;
}
.modal-calendar-dates {
  padding: 16px 12px 8px;
}
.modal-calendar-dates__table {
  width: 100%;
  table-layout: fixed;
}
.modal-calendar-dates__table td {
  padding: 1px 0 0;
  text-align: center;
}
.modal-calendar-dates__titleday {
  padding: 0 0 3px;
  color: #a5b2bc;
  font-size: 10px;
  font-weight: 400;
  letter-spacing: 0.7px;
  line-height: 18px;
  text-align: center;
  text-transform: uppercase;
}
.modal-calendar-dates__item {
  width: 34px;
  border-radius: 20px;
  margin: 0 auto;
  line-height: 20px;
  padding-top: 4.5px;
  padding-bottom: 3.5px;
  border: 3px solid #fff;
}
.modal-calendar-dates__othermonth {
  color: #a5b2bc;
}
.modal-calendar-dates__day.__disabled .modal-calendar-dates__item {
  color: rgba(165, 178, 188, 0.5);
  cursor: default;
}
.modal-calendar-dates__day.__disabled .modal-calendar-dates__item:hover {
  background-color: transparent;
}
.modal-calendar-dates__currentmonth .modal-calendar-dates__item {
  cursor: pointer;
}
.modal-calendar-dates__currentmonth .modal-calendar-dates__item:hover {
  background-color: #e9ecef;
}
.modal-calendar-dates__currentday .modal-calendar-dates__item {
  color: #fff;
  background-color: var(--color-primary) !important;
  font-weight: 500;
}
.modal-calendar > :last-child {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}
.modal-calendar-area {
  position: relative;
  overflow: hidden;
}
.modal-calendar-items {
  display: flex;
}
.modal-calendar-items.__anim-allow {
  transition: -webkit-transform 0.3s;
  transition: transform 0.3s;
  transition: transform 0.3s, -webkit-transform 0.3s;
}
.modal-calendar-items__item {
  flex: 0 0 auto;
  width: 100%;
}
.modal-calendar-arrow {
  position: absolute;
  z-index: 3;
  top: 0;
  right: 0;
  border-top-right-radius: 8px;
  background-image: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, #fff 40%);
  display: flex;
  padding: 1px 11px 0 25px;
  min-height: 49px;
  align-items: center;
}
.modal-calendar-arrow__item {
  border: none;
  padding: 0;
  margin: 0;
  background: none;
  width: 26px;
  height: 26px;
  text-align: center;
  margin-left: 2px;
  display: flex;
}
.modal-calendar-arrow__item:hover {
  color: var(--color-primary);
}
.modal-calendar-arrow__item svg {
  vertical-align: top;
  margin: auto;
}
.modal-calendar-arrow__item[disabled],
.modal-calendar-arrow__item[disabled]:hover {
  color: #c5cdd4;
  cursor: default;
}
.modal-calendar-header {
  position: relative;
  min-height: 50px;
  display: flex;
  align-items: center;
  padding: 3.5px 20px 0;
  font-size: 15px;
  font-weight: 500;
  line-height: 1.25rem;
  border-bottom: 1px solid rgba(214, 220, 225, 0.5);
}
.modal-calendar-header__item {
  border: none;
  padding: 0;
  background: none;
  font-weight: 500;
  margin-right: 12px;
}
.modal-calendar-header__item:after {
  display: inline-block;
  content: '';
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid currentColor;
  vertical-align: top;
  margin: 5px 0 0 4px;
}
.modal-calendar-header__item:hover {
  color: var(--color-primary);
}
.modal-nav {
  position: absolute;
  z-index: 941;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(214, 220, 225, 0.5);
  background-color: #fff;
  border-radius: 8px;
}
.modal-nav.__state-initial {
  -webkit-transform: translate(0, 20px);
  transform: translate(0, 20px);
  opacity: 0;
}
.modal-nav.__state-normal {
  -webkit-transform: translate(0, 0);
  transform: translate(0, 0);
  opacity: 1;
  transition: opacity 0.2s, -webkit-transform 0.2s;
  transition: opacity 0.2s, transform 0.2s;
  transition: opacity 0.2s, transform 0.2s, -webkit-transform 0.2s;
}
.modal-nav__list {
  position: relative;
  z-index: 1;
  list-style-type: none;
  padding: 10px 0 0;
  margin: 0;
  max-height: 300px;
  overflow-x: hidden;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}
.modal-nav__list-item {
  position: relative;
  display: flex;
  min-height: 53px;
  font-size: 15px;
  overflow: hidden;
}
.modal-nav__list-item:hover,
.modal-nav__list-item.__checked {
  background-color: #f5f7f9;
}
.modal-nav__list-item:hover .modal-nav__item,
.modal-nav__list-item.__checked .modal-nav__item {
  color: #222;
}
.modal-nav__list-item:hover .modal-nav__aside {
  visibility: visible;
}
.modal-nav__list-item.__hover-disabled {
  background-color: transparent;
}
.modal-nav__list-item.__checked span.modal-nav__value {
  font-weight: 500;
}
.modal-nav__list-item:last-child .modal-nav__item {
  border-bottom-right-radius: 7px;
  border-bottom-left-radius: 7px;
}
.modal-nav__list-item:first-child:before {
  display: none;
}
.modal-nav__list-item:first-child .modal-nav__item {
  border-top-right-radius: 7px;
  border-top-left-radius: 7px;
}
.modal-nav__aside {
  visibility: hidden;
  padding: 8px 15px 8px 5px;
  line-height: 1;
  align-self: center;
}
.modal-nav__item {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  justify-content: center;
  margin: 0;
  background: none;
  color: #222;
  padding: 7px 15px;
}
.modal-nav__item.__danger {
  color: #FF3B30;
}
.modal-nav__list-item:hover .modal-nav__item.__danger {
  color: #EB4D3D;
}
.modal-nav__item.__has-arrow {
  position: relative;
  padding-right: 36px;
}
.modal-nav__item.__has-arrow:before {
  position: absolute;
  right: 15px;
  top: 50%;
  margin-top: -4px;
  border-left: 4px solid #222;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  content: '';
}
.modal-nav__label {
  color: #a5b2bc;
  font-size: 12px;
  line-height: 1;
  padding: 1px 0;
}
.modal-nav__value {
  line-height: 1;
  padding: 1px 0;
}
.modal-nav-action {
  position: relative;
  z-index: 4;
  border-top: 1px solid rgba(214, 220, 225, 0.5);
  padding: 0;
  border-bottom-right-radius: 8px;
  border-bottom-left-radius: 8px;
}
.modal-nav-action__item {
  display: flex;
  align-items: center;
  min-height: 50px;
  cursor: pointer;
  border-bottom-left-radius: 7px;
  border-bottom-right-radius: 7px;
}
.modal-nav-action__item:hover .modal-nav-action__label {
  color: var(--color-primary);
}
.modal-nav-action__item.disabled,
.modal-nav-action__item[disabled] {
  pointer-events: none;
  color: rgba(165, 178, 188, 0.7) !important;
  background-color: #f5f7f9 !important;
}
.modal-nav-action__item.disabled .modal-nav-action__label,
.modal-nav-action__item[disabled] .modal-nav-action__label,
.modal-nav-action__item.disabled .modal-nav-action__note,
.modal-nav-action__item[disabled] .modal-nav-action__note {
  color: currentColor;
}
.modal-nav-action__inner {
  width: 100%;
  text-align: center;
  padding: 0 20px;
  line-height: 1.125rem;
}
.modal-nav-action__label {
  font-weight: 500;
  font-size: 15px;
  color: #222;
}
.modal-nav-action__note {
  color: #a5b2bc;
  font-size: 12px;
}
.modal-nav-action__ico {
  display: inline-flex;
  min-height: 20px;
  align-items: center;
  line-height: 1;
  font-size: 1px;
  margin: -2.5px 5px -1px 0;
  vertical-align: top;
}
.modal-nav-action__ico svg {
  margin: auto;
}
.modal-nav__inner > :first-child {
  border-top-right-radius: 7px;
  border-top-left-radius: 7px;
}
.modal-nav__inner > :last-child {
  border-bottom-right-radius: 7px;
  border-bottom-left-radius: 7px;
}
.modal-nav__inner > :last-child.modal-nav__list {
  padding-bottom: 10px;
}
.modal-nav-arrow {
  position: absolute;
  z-index: 3;
  overflow: hidden;
}
.modal-nav-arrow__inner {
  position: absolute;
  display: block;
  left: 50%;
  top: 50%;
  margin: -5px 0 0 -5px;
  width: 10px;
  height: 10px;
  border: 1px solid rgba(214, 220, 225, 0.5);
  background-color: #fff;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  -webkit-transform-origin: center;
  transform-origin: center;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}
.modal-nav[data-arrow='left'] .modal-nav-arrow {
  left: -16px;
  width: 16px;
  height: 26px;
}
.modal-nav[data-arrow="left-bottom"] .modal-nav-arrow {
  left: -16px;
  bottom: 15px;
  width: 16px;
  height: 26px;
}
.modal-nav[data-arrow="left-bottom"] .modal-nav-arrow__inner {
  margin-left: 3px;
}

.modal-nav[data-arrow="left"] .modal-nav-arrow__inner {
  margin-left: 3px;
}
.modal-nav[data-arrow='right'] .modal-nav-arrow {
  right: -16px;
  width: 16px;
  height: 26px;
}
.modal-nav[data-arrow='right'] .modal-nav-arrow__inner {
  margin-left: -13px;
}
.modal-nav[data-arrow='bottom'] .modal-nav-arrow {
  bottom: -16px;
  height: 16px;
  width: 26px;
}
.modal-nav[data-arrow='bottom'] .modal-nav-arrow__inner {
  margin-top: -13px;
}
.modal-nav[data-arrow='top'] .modal-nav-arrow {
  top: -16px;
  height: 16px;
  width: 26px;
}
.modal-nav[data-arrow='top'] .modal-nav-arrow__inner {
  margin-top: 3px;
}
.modal-nav-userbar {
  min-width: 190px;
}
.modal-nav-userbar-card {
  text-align: center;
}
.modal-nav-userbar .modal-nav__list {
  padding: 0 !important;
}
.modal-nav-userbar .modal-nav__list-item {
  border-top: 1px solid rgba(214, 220, 225, 0.5);
}
.modal-nav-userbar .modal-nav__list-item:first-child {
  border-top-right-radius: 8px;
  border-top-left-radius: 8px;
  border-top-width: 0;
}
.modal-nav-userbar .modal-nav__list-item:last-child {
  border-bottom-right-radius: 8px;
  border-bottom-left-radius: 8px;
}
.chart-tooltip {
  position: absolute;
  z-index: 923;
  border-radius: 6px;
  padding: 0 15px;
  background-color: rgba(34, 34, 34, 0.9);
  color: #fff;
  font-size: 14px;
  line-height: 1.25rem;
}
.chart-tooltip-header {
  display: flex;
  color: #afafaf;
  font-size: 12px;
  line-height: 13px;
  padding-bottom: 3px;
}
.chart-tooltip-header__name {
  flex: 1 1 auto;
  min-width: 0;
}
.chart-tooltip-header__aside {
  padding-left: 19px;
}
.chart-tooltip-value {
  display: flex;
  align-items: center;
}
.chart-tooltip-value__inner {
  flex: 1 1 auto;
  min-width: 0;
  word-break: break-word;
}
.chart-tooltip-value__aside {
  flex: 0 0 auto;
  padding-left: 19px;
  white-space: nowrap;
}
.chart-tooltip-value__dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  flex: 0 0 auto;
  margin-right: 5px;
}
.chart-tooltip__section {
  padding: 10px 0 9px;
}
.chart-tooltip__section + .chart-tooltip__section {
  border-top: 1px solid #5b5b5b;
}
.fast-tooltip {
  position: absolute;
  z-index: 9512;
  background-color: rgba(34, 34, 34, 0.8);
  border-radius: 6px;
  max-width: 300px;
  font-size: 12px;
}
.fast-tooltip.__state-initial {
  opacity: 0;
}
.fast-tooltip.__state-initial[data-direction='from-top'] {
  -webkit-transform: translate(0, -10px);
  transform: translate(0, -10px);
}
.fast-tooltip.__state-initial[data-direction='from-bottom'] {
  -webkit-transform: translate(0, 10px);
  transform: translate(0, 10px);
}
.fast-tooltip.__state-initial[data-direction='from-left'] {
  -webkit-transform: translate(-10px, 0);
  transform: translate(-10px, 0);
}
.fast-tooltip.__state-initial[data-direction='from-right'] {
  -webkit-transform: translate(10px, 0);
  transform: translate(10px, 0);
}
.fast-tooltip.__state-normal {
  -webkit-transform: translate(0, 0);
  transform: translate(0, 0);
  transition: opacity 0.15s, -webkit-transform 0.15s;
  transition: opacity 0.15s, transform 0.15s;
  transition: opacity 0.15s, transform 0.15s, -webkit-transform 0.15s;
}
.fast-tooltip__inner {
  padding: 5px 10px 4px;
  color: #fff;
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
}
.fast-tooltip.__arrow-top:before {
  position: absolute;
  border-bottom: 3px solid rgba(34, 34, 34, 0.85);
  border-left: 3px solid transparent;
  border-right: 3px solid transparent;
  content: '';
  top: -3px;
  left: 50%;
  margin-left: -3px;
}
.fast-tooltip.__arrow-bottom:before {
  position: absolute;
  border-bottom: 3px solid rgba(34, 34, 34, 0.85);
  border-left: 3px solid transparent;
  border-right: 3px solid transparent;
  content: '';
  bottom: -3px;
  left: 50%;
  margin-left: -3px;
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
}
.fast-tooltip__clear {
  color: rgba(255, 255, 255, 0.5);
  background: none;
  border: none;
  padding: 0;
  margin: 3px -2px 0 3px;
  font-size: 1px;
  line-height: 1;
  vertical-align: top;
}
.fast-tooltip__clear .ico-clear-os {
  width: 10px;
  height: 10px;
}
.fast-tooltip__clear:hover {
  color: #fff;
}

.simple-tooltip {
  position: absolute;
  z-index: 999;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  min-width: 145px;
  padding: 10px 15px 15px;
  font-size: 13px;
  line-height: 1.32;
  border: 1px solid rgba(214, 220, 225, 0.5);
}
.simple-tooltip.__top {
  z-index: 10000;
}


.simple-tooltip.small {
  border-radius: 6px;
  min-width: 0;
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.45;
}

.simple-tooltip.__initial,
.simple-tooltip.__state-initial {
  opacity: 0;
}

.simple-tooltip.__state-initial[data-direction='from-top'] {
  -webkit-transform: translate(0, -10px);
  transform: translate(0, -10px);
}
.simple-tooltip.__state-initial[data-direction='from-bottom'] {
  -webkit-transform: translate(0, 10px);
  transform: translate(0, 10px);
}
.simple-tooltip.__state-initial[data-direction='from-left'] {
  -webkit-transform: translate(-10px, 0);
  transform: translate(-10px, 0);
}
.simple-tooltip.__state-initial[data-direction='from-right'] {
  -webkit-transform: translate(10px, 0);
  transform: translate(10px, 0);
}


.simple-tooltip.__initial.__arrow-left,
.simple-tooltip.__state-initial.__arrow-left {
  -webkit-transform: translate(20px, 0);
  transform: translate(20px, 0);
}
.simple-tooltip.__initial.__arrow-right,
.simple-tooltip.__state-initial.__arrow-right {
  -webkit-transform: translate(-20px, 0);
          transform: translate(-20px, 0);
}
.simple-tooltip.__initial.__arrow-bottom,
.simple-tooltip.__state-initial.__arrow-bottom {
  -webkit-transform: translate(0, 20px);
  transform: translate(0, 20px);
}
.simple-tooltip.__initial.__arrow-top,
.simple-tooltip.__state-initial.__arrow-top {
  -webkit-transform: translate(0, -20px);
  transform: translate(0, -20px);
}
.simple-tooltip.__normal,
.simple-tooltip.__state-normal {
  -webkit-transform: translate(0, 0);
  transform: translate(0, 0);
  transition: opacity 0.15s, -webkit-transform 0.15s;
  transition: opacity 0.15s, transform 0.15s;
  transition: opacity 0.15s, transform 0.15s, -webkit-transform 0.15s;
}
.simple-tooltip-arrow {
  position: absolute;
  z-index: 2;
  overflow: hidden;
}
.simple-tooltip-arrow__inner {
  position: absolute;
  width: 11px;
  height: 11px;
}
.simple-tooltip-arrow__inner:before {
  position: absolute;
  content: '';
  background-color: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(214, 220, 225, 0.5);
  width: 11px;
  height: 11px;
  border-top-right-radius: 2px;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.1);
}

.simple-tooltip[data-theme='dark'] .simple-tooltip-arrow__inner:before {
  background-color: #222;
}

.simple-tooltip__emoji {
  margin-bottom: 9px;
}
.simple-tooltip__emoji img {
  vertical-align: top;
}
.simple-tooltip__content p {
  margin-top: 0;
  margin-bottom: 0;
}

.simple-tooltip__content a {
  cursor: pointer;
}

.simple-tooltip__forms {
  margin-top: 10px;
  line-height: 1.45;
}
.simple-tooltip-actions {
  display: flex;
  margin: 10px 0 0 -5px;
}
.simple-tooltip-actions__item {
  flex: 1.5;
  margin-left: 5px;
}
.simple-tooltip[data-theme='dark'] .simple-tooltip-arrow__inner:before {
  background-color: #222;
  border: none;
}
.simple-tooltip[data-theme='onboarding'] .simple-tooltip-arrow__inner:before {
  background-color: #007aff;
  border: none;
}
.simple-tooltip.__arrow-bottom .simple-tooltip-arrow {
  bottom: -14px;
  left: 50%;
  margin-left: -12px;
  width: 26px;
  height: 14px;
}
.simple-tooltip[data-theme='dark'].small.__arrow-bottom .simple-tooltip-arrow {
  bottom: -10px;
}
.simple-tooltip.__arrow-bottom .simple-tooltip-arrow__inner {
  top: 50%;
  left: 50%;
  -webkit-transform: rotate(-90deg);
  transform: rotate(-90deg);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
}
.simple-tooltip.__arrow-top .simple-tooltip-arrow {
  top: -14px;
  left: 50%;
  margin-left: -12px;
  width: 26px;
  height: 14px;
}
.simple-tooltip[data-theme='dark'].small.__arrow-top .simple-tooltip-arrow {
  top: -10px;
}
.simple-tooltip.__arrow-top .simple-tooltip-arrow__inner {
  top: 50%;
  left: 50%;
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
}
.simple-tooltip.__arrow-left .simple-tooltip-arrow {
  left: -14px;
  top: 50%;
  margin-top: -13px;
  height: 26px;
  width: 14px;
}
.simple-tooltip.__arrow-left .simple-tooltip-arrow__inner {
  top: 50%;
  margin-top: -3px;
  right: -11px;
  -webkit-transform: rotate(-90deg);
  transform: rotate(-90deg);
  -webkit-transform-origin: center;
  transform-origin: center;
}
.simple-tooltip.small.__arrow-left .simple-tooltip-arrow__inner {
  right: -13px;
}
.simple-tooltip.__arrow-right .simple-tooltip-arrow {
  right: -14px;
  top: 50%;
  margin-top: -13px;
  height: 26px;
  width: 14px;
}
.simple-tooltip.__arrow-right .simple-tooltip-arrow__inner {
  top: 50%;
  margin-top: -7px;
  left: -11px;
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
  -webkit-transform-origin: center;
          transform-origin: center;
}
.simple-tooltip.small.__arrow-right .simple-tooltip-arrow__inner {
  left: -13px;
}
.simple-tooltip.small .simple-tooltip__content{
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
}
.simple-tooltip__content p {
  margin-top: 0;
  margin-bottom: 0;
}
.simple-tooltip[data-theme='dark'] {
  color: #fff;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.25);
  background-color: #222;
  border: 0;
}
.simple-tooltip[data-theme='dark'] h2 {
  font-size: 16px;
  line-height: 22px;
  margin-top: 0;
  margin-bottom: 5px;
}
.simple-tooltip[data-theme='onboarding'] {
  color: #fff;
  box-shadow: 0 5px 10px rgba(18, 51, 82, 0.37);
  border-radius: 7px;
  background-color: #007aff;
  padding: 24px 20px 20px 20px;
  text-align: center;
  border: 0;
}
.simple-tooltip[data-theme='onboarding'] .simple-tooltip__content {
  font-weight: 500;
  text-align: center;
}
.simple-tooltip[data-theme='onboarding'].__arrow-top .simple-tooltip-arrow {
  left: 50%;
  top: -8px;
  width: 0;
  height: 0;
  border-bottom: 8px solid #007aff;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
}
.simple-tooltip[data-theme='onboarding'].__arrow-left .simple-tooltip-arrow__inner::before {
  background-color: #007aff;
  border: 1px solid #007aff;
}
.simple-tooltip[data-theme='onboarding'].__arrow-top
  .simple-tooltip-arrow__inner {
  display: none;
}
.btn {
  display: inline-block;
  cursor: pointer;
  padding: 11px 15px 11px;
  border-width: 0;
  white-space: nowrap;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background: none;
  transition: color 0.1s linear, background-color 0.1s linear,
    box-shadow 0.1s linear;
  min-height: 42px;
  line-height: 1.45;
  border-radius: 8px;
  font-weight: 500;
  text-align: center;
}
.btn-primary {
  background-color: #e9ecef;
  color: #222;
  font-weight: 500;
}
.btn-primary:hover {
  background-color: #d5dbe0;
  color: #222;
}
.btn-primary.disabled,
.btn-primary[disabled] {
  cursor: default;
  background-color: #f5f7f9 !important;
  color: rgba(165, 178, 188, 0.7) !important;
}
.btn-primary[data-shadow='true'] {
  box-shadow: 0 4px 8px rgba(143, 149, 154, 0.4);
}
.btn-primary[data-shadow='true'].disabled,
.btn-primary[data-shadow='true'][disabled] {
  box-shadow: 0 4px 8px rgba(162, 169, 176, 0.4) !important;
}
.btn-default {
  background-color: transparent;
  color: #222;
  box-shadow: inset 0 0 0 1px #d2d8dc;
}
.btn-default:hover {
  background-color: transparent;
  color: var(--color-primary);
}
.btn-default.disabled,
.btn-default[disabled] {
  cursor: default;
  background-color: #f5f7f9 !important;
  color: rgba(165, 178, 188, 0.7) !important;
  box-shadow: none !important;
}
.btn-default[data-style='error'] {
  color: #ff3b30;
}
.btn-default[data-style='error']:hover {
  color: #a52c2c;
}
.btn-default[data-shadow='true'] {
  box-shadow: 0 4px 8px rgba(143, 149, 154, 0.4);
}
.btn-default[data-shadow='true'].disabled,
.btn-default[data-shadow='true'][disabled] {
  box-shadow: 0 4px 8px rgba(162, 169, 176, 0.4) !important;
}
.btn-success {
  background-color: var(--color-primary);
  color: #fff;
}
.btn-success.btn-xs {
  box-shadow: 0 4px 8px rgba(23, 73, 44, 0.4);
}
.btn-success:hover {
  background-color: var(--color-secondary);
  color: #fff;
}
.btn-success.disabled,
.btn-success[disabled] {
  cursor: default;
  background-color: #f5f7f9 !important;
  color: rgba(165, 178, 188, 0.7) !important;
}
.btn-success[data-shadow='true'] {
  box-shadow: 0 4px 8px rgba(23, 73, 44, 0.4);
}
.btn-success[data-shadow='true'].disabled,
.btn-success[data-shadow='true'][disabled] {
  box-shadow: 0 4px 8px rgba(162, 169, 176, 0.4) !important;
}
.btn-danger {
  background-color: #ff3b30;
  color: #fff;
}
.btn-danger.btn-xs {
  box-shadow: 0 4px 8px rgba(23, 73, 44, 0.4);
}
.btn-danger:hover {
  background-color: #a52c2c;
  color: #fff;
}
.btn-danger.disabled,
.btn-danger[disabled] {
  cursor: default;
  background-color: #f5f7f9 !important;
  color: rgba(165, 178, 188, 0.7) !important;
}
.btn-danger[data-shadow='true'] {
  box-shadow: 0 4px 8px rgba(143, 149, 154, 0.4);
}
.btn-danger[data-shadow='true'].disabled,
.btn-danger[data-shadow='true'][disabled] {
  box-shadow: 0 4px 8px rgba(162, 169, 176, 0.4) !important;
}
.btn-dark {
  background-color: #4d4d4d;
  color: #fff;
}
.btn-dark.btn-xs {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}
.btn-dark:hover {
  background-color: #5e5e5e;
  color: #fff;
}
.btn-dark.disabled,
.btn-dark[disabled] {
  cursor: default;
  background-color: #f5f7f9 !important;
  color: rgba(165, 178, 188, 0.7) !important;
}
.btn-dark[data-shadow='true'] {
  box-shadow: 0 4px 8px rgba(143, 149, 154, 0.4);
}
.btn-dark[data-shadow='true'].disabled,
.btn-dark[data-shadow='true'][disabled] {
  box-shadow: 0 4px 8px rgba(162, 169, 176, 0.4) !important;
}
.btn-lg {
  padding: 15.5px 20px 14.5px;
  min-height: 50px;
  border-radius: 10px;
  font-size: 1rem;
  line-height: 1.25rem;
}
.btn-lg .btn__ico {
  margin-top: -1px;
  margin-right: 5px;
}

.btn-lg .btn__ico.btn__ico-right {
  margin-top: -1px;
  margin-left: 5px;
  margin-right: 0px;
}

.btn-lg .btn__arrow {
  margin-top: 8.5px;
}
.btn-sm {
  padding: 7.5px 10px 6.5px;
  min-height: 34px;
  border-radius: 6px;
  font-size: 14px;
  line-height: 1.25rem;
}
.btn-sm .btn__ico {
  margin-top: -1px;
  margin-right: 5px;
}
.btn-sm .btn__arrow {
  margin-top: 8px;
}
.btn-sm[data-radius='rounded'] {
  padding-left: 17px;
  padding-right: 17px;
}
.btn-icon {
  min-width: 56px;
}
.btn-icon .btn__ico {
  margin-right: 0;
}
.btn__ico {
  display: inline-flex;
  vertical-align: top;
  margin-right: 4px;
  font-size: 1px;
  min-height: 1.25rem;
  align-items: center;
  margin-top: -1px;
}
.btn__ico[data-size='large'] .ico-pencil-os {
  width: 13px;
  height: 13px;
}
.btn__ico[data-size='large'] .ico-attach-os {
  width: 16px;
  height: 15px;
}
.btn .ico-trash-n-os {
  margin-top: -1px;
}
.btn__arrow {
  display: inline-block;
  vertical-align: top;
  border-left: 3.5px solid transparent;
  border-right: 3.5px solid transparent;
  border-top: 4px solid currentColor;
  margin-top: 7.5px;
  -webkit-transform-origin: center;
  transform-origin: center;
}
.btn-warr {
  position: relative;
}
.btn--active .btn__arrow {
  -webkit-transform: rotate(-180deg);
  transform: rotate(-180deg);
}
.btn[data-radius='rounded'] {
  border-radius: 30px;
}
.btn-link {
  color: #007aff;
  padding: 0;
  margin: 0;
  border: none;
  background: none;
}
.btn-link:hover {
  color: #014e86;
}
.btn-open {
  border-radius: 50%;
  min-width: 42px;
  padding-left: 0;
  padding-right: 0;
  text-align: center;
}
.btn-open .btn__ico {
  margin-right: 0;
  margin-top: 0;
}
.btn-open .ico-chevron-down-small-os {
  width: 14px;
  height: 8px;
}
.btn-open--active .btn__ico {
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
}
.online-cells {
  display: flex;
}
.online-cells__item {
  width: 22px;
  height: 2px;
  background-color: rgba(175, 175, 175, 0.5);
}
.online-cells__item + .online-cells__item {
  margin-left: 2px;
}
.online-cells__item.__filled {
  background-color: #26ae60;
}
.progress-bar {
  position: relative;
  width: 24px;
  height: 24px;
}
.progress-bar__ring {
  top: -1.5px;
  left: 0;
  position: absolute;
  -webkit-transform: rotate(90deg) translate3d(0, 0, 0) scaleX(-1);
  transform: rotate(90deg) translate3d(0, 0, 0) scaleX(-1);
  width: 24px;
  height: 24px;
  margin: 0;
  border: none;
}
.progress-bar__circle {
  stroke-dasharray: 76 76;
  stroke-dashoffset: 13;
  stroke-miterlimit: round;
}
.progress-bar-numbers {
  position: absolute;
  top: 2px;
  left: 0;
  width: 100%;
  height: 20px;
  overflow: hidden;
}
.progress-bar-numbers__count {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  text-align: center;
  line-height: 20px;
  font-size: 14px;
  font-weight: 500;
}
.progress-bar-numbers__count.__state-initial {
  -webkit-transform: translate(0, -20px);
  transform: translate(0, -20px);
  opacity: 0;
}
.progress-bar-numbers__count.__state-normal {
  -webkit-transform: translate(0, 0);
  transform: translate(0, 0);
  opacity: 1;
}
.progress-bar-numbers__count.__state-ending {
  -webkit-transform: translate(0, 20px);
  transform: translate(0, 20px);
  opacity: 0;
}
.progress-bar[data-sec='5'] .progress-bar__circle {
  transition: stroke-dashoffset 5s linear 0s;
}
.progress-bar.__state-normal .progress-bar__circle {
  stroke-dashoffset: 76;
}
.notifications {
  position: absolute;
  z-index: 9991;
  left: 50%;
  -webkit-transform: translate(-50%, 0);
  transform: translate(-50%, 0);
  height: 40px;
  border-radius: 8px;
  background-color: rgba(34, 34, 34, 0.8);
  padding: 0 15px;
  display: flex;
  font-size: 14px;
  line-height: 1.45;
  align-items: center;
}
.notifications__inner {
  width: 100%;
  color: #fff;
}
.notifications.__initial {
  -webkit-transform: translate(-50%, -44px);
  transform: translate(-50%, -44px);
}
.notifications.__normal {
  -webkit-transform: translate(-50%, 4px);
  transform: translate(-50%, 4px);
}
.notifications.__exit {
  -webkit-transform: translate(-50%, 24px);
  transform: translate(-50%, 24px);
  opacity: 0;
}
.notification-inline {
  border-radius: 8px;
  display: flex;
  align-items: center;
  padding: 0 15px;
}
.notification-inline[data-type='warning'] {
  background-color: #ffefb4;
}
.notification-inline[data-type='article'] {
  position: relative;
  background-color: #f6f8f9;
  padding-right: 34px;
  color: #222;
}
.notification-inline[data-type='article']:hover {
  color: #222;
  background-color: #e8eaed;
}
.notification-inline[data-type='article']:hover:before {
  width: 17px;
  height: 17px;
  right: 8px;
  top: 8px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' viewBox='0 0 17 17'%3E%3Cpath d='M15.99705,1l-7.59705,7.59705' fill='none' stroke='%23a5b2bc' stroke-miterlimit='50' stroke-width='2'/%3E%3Cpath d='M12,1v0h4v0v4v0' fill='none' stroke='%23a5b2bc' stroke-miterlimit='50' stroke-width='2'/%3E%3Cg%3E%3Cpath d='M14,10.00043v0v2.99957c0,1.65685 -1.34315,3 -3,3h-7c-1.65685,0 -3,-1.34315 -3,-3v-7c0,-1.65685 1.34315,-3 3,-3h3v0' fill='none' stroke='%23a5b2bc' stroke-miterlimit='50' stroke-width='2'/%3E%3C/g%3E%3Cg%3E%3Cpath d='M14,10.00043v0v2.99957c0,1.65685 -1.34315,3 -3,3h-7c-1.65685,0 -3,-1.34315 -3,-3v-7c0,-1.65685 1.34315,-3 3,-3h3v0' fill='none' stroke='%23a5b2bc' stroke-miterlimit='50' stroke-width='2'/%3E%3Cpath d='M12,1v0h4v0v4v0' fill='none' stroke='%23a5b2bc' stroke-miterlimit='50' stroke-width='2'/%3E%3Cg%3E%3Cpath d='M15.99705,1l-7.59705,7.59705' fill='none' stroke='%23a5b2bc' stroke-miterlimit='50' stroke-width='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}
.notification-inline[data-type='article']:before {
  position: absolute;
  content: '';
  right: 10px;
  top: 10px;
  width: 15px;
  height: 15px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' viewBox='0 0 15 15'%3E%3Cpath d='M13.99705,1l-7.59705,7.59705' fill='none' stroke='%23a5b2bc' stroke-miterlimit='50' stroke-width='2'/%3E%3Cpath d='M10,1v0h4v0v4v0' fill='none' stroke='%23a5b2bc' stroke-miterlimit='50' stroke-width='2'/%3E%3Cg%3E%3Cpath d='M14,8.00043v0v2.99957c0,1.65685 -1.34315,3 -3,3h-7c-1.65685,0 -3,-1.34315 -3,-3v-7c0,-1.65685 1.34315,-3 3,-3h3v0' fill='none' stroke='%23a5b2bc' stroke-miterlimit='50' stroke-width='2'/%3E%3C/g%3E%3Cg%3E%3Cpath d='M14,8.00043v0v2.99957c0,1.65685 -1.34315,3 -3,3h-7c-1.65685,0 -3,-1.34315 -3,-3v-7c0,-1.65685 1.34315,-3 3,-3h3v0' fill='none' stroke='%23a5b2bc' stroke-miterlimit='50' stroke-width='2'/%3E%3Cpath d='M10,1v0h4v0v4v0' fill='none' stroke='%23a5b2bc' stroke-miterlimit='50' stroke-width='2'/%3E%3Cg%3E%3Cpath d='M13.99705,1l-7.59705,7.59705' fill='none' stroke='%23a5b2bc' stroke-miterlimit='50' stroke-width='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}
.notification-inline[data-float='right'] {
  float: right;
}
.notification-inline__content {
  flex: 1 1 auto;
  padding: 14px 0 14px;
  font-weight: 500;
  line-height: 1.22;
}
.notification-inline__ico {
  flex: 0 0 auto;
  padding-right: 15px;
  font-size: 34px;
  line-height: 1;
}
.notification-inline__ico img {
  vertical-align: top;
  width: auto;
  height: auto;
  max-width: 40px;
  max-height: 40px;
}
.notification-inline__side {
  flex: 0 0 auto;
  padding: 15px 0 15px 45px;
}
.notification-inline--mb {
  margin-bottom: 25px;
}
.table {
  width: 100%;
}
.table[data-layout='fixed'] {
  table-layout: fixed;
}
.table .table-sort {
  position: relative;
  display: inline-block;
  vertical-align: top;
  margin-left: 3px;
  width: 6px;
  height: 7px;
  margin-top: 7px;
  visibility: hidden;
}
.table .table-sort:before {
  position: absolute;
  content: '';
  top: 0;
  left: 0;
  border-bottom: 3px solid currentColor;
  border-left: 3px solid transparent;
  border-right: 3px solid transparent;
}
.table .table-sort:after {
  position: absolute;
  content: '';
  bottom: 0;
  left: 0;
  border-top: 3px solid currentColor;
  border-left: 3px solid transparent;
  border-right: 3px solid transparent;
}
.table .table-sort[data-direction='up'] {
  visibility: visible;
}
.table .table-sort[data-direction='up']:after {
  border-top-color: #a5b2bc;
}
.table .table-sort[data-direction='down'] {
  visibility: visible;
}
.table .table-sort[data-direction='down']:before {
  border-bottom-color: #a5b2bc;
}
.table td,
.table .td {
  padding: 10px 12px 10px 0;
  border-bottom: 0.5px solid #d6dce0;
}
.table th,
.table .th {
  font-weight: 500;
  cursor: pointer;
  padding: 0 12px 8px 0;
  vertical-align: bottom;
  text-align: left;
  border-bottom: 0.5px solid #d6dce0;
}
.table th.text-right {
  text-align: right;
}
.table th.text-center {
  text-align: center;
}
.table th.sorting {
  cursor: pointer;
}
.table th:hover .table-sort {
  visibility: visible;
}
.table th:hover .table-sort:after {
  border-top-color: #bc0200;
}
.table th:hover .table-sort[data-direction='up']:before {
  border-bottom-color: #bc0200;
}
.table th:hover .table-sort[data-direction='up']:after {
  border-top-color: #a5b2bc;
}
.table th:hover .table-sort[data-direction='down']:after {
  border-bottom-color: #bc0200;
}
.table tr th:last-child,
.table tr td:last-child,
.table .tr .th:last-child,
.table .tr .td:last-child {
  padding-right: 0;
}
.table .hid-v {
  visibility: hidden;
}
.table tr:hover .hid-v {
  visibility: visible;
}
.logo {
  position: relative;
  min-height: 32px;
  display: block;
}
.logo:before {
  display: block;
  content: '';
  width: 92px;
  height: 32px;
  background: url('../images/logo.svg') no-repeat 50% / contain;
  transition: opacity 0.2s, -webkit-transform 0.2s;
  transition: opacity 0.2s, transform 0.2s;
  transition: opacity 0.2s, transform 0.2s, -webkit-transform 0.2s;
}
.logo:after {
  position: absolute;
  top: 4px;
  left: 0;
  display: block;
  content: '';
  width: 32px;
  height: 32px;
  background: url('../images/logo-short.svg') no-repeat 50% / contain;
  transition: opacity 0.2s, -webkit-transform 0.2s;
  transition: opacity 0.2s, transform 0.2s;
  transition: opacity 0.2s, transform 0.2s, -webkit-transform 0.2s;
  opacity: 0;
}
.side-mobileapp__item {
  padding: 0;
  min-height: 50px;
  padding-left: 19px;
  display: flex;
  width: 100%;
  align-items: center;
  color: #222;
  line-height: 1rem;
}
.side-mobileapp__item:hover {
  color: #222;
  background-color: #dddfe4;
}
.side-navigation {
  position: relative;
  background-color: #eeeff2;
  width: 220px;
  margin-right: -20px;
  transition: width 0.2s;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  flex: 0 0 auto;
}
.side-navigation-profile {
  position: relative;
}
.side-navigation-profile__ico {
  position: absolute;
  top: 50%;
  left: 17px;
  margin-top: -11px;
  width: 20px;
  height: 20px;
  font-size: 1px;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
.side-navigation-profile__ico svg {
  vertical-align: top;
}
.side-navigation-profile__item {
  display: flex;
  min-height: 51px;
  align-items: center;
  width: 100%;
  padding: 0 0 0 46px;
  border: none;
  margin: 0;
  background: none;
  text-align: left;
  border-top: 1px solid rgba(214, 220, 225, 0.8);
}
.side-navigation-profile__item:after {
  position: absolute;
  content: '';
  position: absolute;
  top: 50%;
  margin-top: -4px;
  left: 180px;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  border-left: 5px solid currentColor;
  transition: opacity 0.2s, transition 0.2s;
}
.side-navigation-profile__item:hover {
  color: #222;
  background-color: #dddfe4;
  border-top-color: #d6dce1;
}
.side-navigation-profile__value {
  position: relative;
  overflow: hidden;
  width: 134px;
  transition: opacity 0.2s;
  flex: 0 0 auto;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.navigation__ico {
  position: absolute;
  top: 50%;
  margin-left: -28px;
  margin-top: -11px;
  width: 20px;
  height: 20px;
  font-size: 1px;
  line-height: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navigation__notification-ico {
  position: absolute;
  bottom: -8px;
  right: -8px;
}

.navigation__notification-circular-progressbar {
  position: absolute;
  bottom: -4px;
  right: -4px;
  background-color: white;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.navigation__ico svg {
  vertical-align: top;
}
.navigation__ico .navigation__ico__beta {
  position: absolute;
  right: -4px;
  top:  -2px;
}
.navigation__share {
  width: 15px;
  height: 15px;
  font-size: 1px;
  line-height: 2px;
  display: flex;
}
.navigation__share svg {
  vertical-align: top;
  fill: #a5b2bc;
}
.navigation__value {
  transition: opacity 0.2s;
  width: 144px;
}

.soil-sidebar {
  position: relative;
  z-index: 2;
  background-color: #fff;
  flex: 0 0 auto;
  width: 301px;
  border-right: 1px solid rgba(214, 220, 225, 0.5);
  overflow: hidden;
  transition: margin-top 0.15s linear;
  will-change: transform;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}
.soil-sidebar-filterapplied {
  position: relative;
  z-index: 3;
  height: 0;
  font-size: 0;
  line-height: 0;
}
.soil-sidebar-filterapplied__inner {
  position: absolute;
  left: 10px;
  top: 10px;
  right: 10px;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.36);
  border-radius: 8px;
  background-color: rgba(34, 34, 34, 0.85);
  font-size: 14px;
  line-height: 20px;
  color: #fff;
  padding: 8px 38px 8px 12px;
}
.soil-sidebar-filterapplied__clear {
  position: absolute;
  color: #fff;
  width: 16px;
  height: 16px;
  background: none;
  border: none;
  font-size: 0;
  line-height: 0;
  top: 10px;
  right: 10px;
}
.soil-sidebar-filterapplied__clear:before {
  position: absolute;
  content: '';
  background-color: currentColor;
  width: 1px;
  height: 10px;
  left: 50%;
  top: 50%;
  margin-top: -5px;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}
.soil-sidebar-filterapplied__clear:after {
  position: absolute;
  content: '';
  background-color: currentColor;
  width: 1px;
  height: 10px;
  left: 50%;
  top: 50%;
  margin-top: -5px;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
}
.soil-sidebar-filterapplied + .soil-sidebar-body {
  padding-top: 47px;
}
.soil-sidebar-stickycontrols {
  position: relative;
}
.soil-sidebar-stickycontrols__group {
  padding: 10px 20px;
  border-bottom: 1px solid rgba(214, 220, 225, 0.5);
}
.soil-sidebar-stickycontrols__group[data-extraspace='true'] {
  padding-bottom: 17px;
}
.soil-sidebar-stickycontrols .form-checkbox__value {
  padding-left: 10px;
}
.soil-sidebar-stickycontrols .main-uploader-header {
  text-align: left;
  padding: 30px 20px 12px;
}
.soil-sidebar-stickycontrols .main-uploader-header__title {
  font-size: 24px;
  line-height: 1.34;
}
.soil-sidebar-stickycontrols .main-uploader-header__description {
  margin-top: 0;
}
.soil-sidebar-selall {
  position: relative;
  padding: 11px 20px 10px;
}
.soil-sidebar-selall:after {
  position: absolute;
  content: '';
  bottom: 0;
  left: 0;
  right: 0;
  border-bottom: 0.5px solid rgba(165, 178, 188, 0.45);
  height: 1px;
}
.soil-sidebar-selall .form-checkbox__value {
  padding-left: 10px;
  font-size: 14px;
}
.soil-sidebar-header {
  position: relative;
  flex: 0 0 auto;
  padding: 0 15px 0;
  min-height: 50px;
}
.soil-sidebar-header:after {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  content: '';
  height: 1px;
  border-bottom: 1px solid rgba(214, 220, 225, 0.5);
}
.soil-sidebar-header__inner {
  display: flex;
  align-items: center;
  min-height: 50px;
}
.soil-sidebar-header .form-tooglebox {
  margin: 0 -5px 11px;
}
.soil-sidebar-header__main {
  text-align: center;
  font-size: 15px;
  line-height: 1.2;
  font-weight: 500;
  margin-top: 0;
  margin-bottom: 0;
  flex: 1 1 auto;
  padding-top: 1px;
}
.soil-sidebar-header__main small {
  display: inline-block;
  color: #a5b2bc;
  font-size: 12px;
  font-weight: normal;
  letter-spacing: normal;
  white-space: nowrap;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.soil-sidebar-footer {
  flex: 0 0 auto;
}
.soil-sidebar .app-nav-edit {
  position: absolute;
  right: 16px;
  top: 15px;
}
.soil-sidebar .app-nav-back {
  position: absolute;
  left: 16px;
  top: 15px;
}
.soil-sidebar .app-side-filter-select {
  position: absolute;
  right: 20px;
  top: 15px;
}
.soil-sidebar-toolbar {
  position: absolute;
  right: 0;
  top: 15px;
  display: flex;
  height: 20px;
  align-items: center;
}
.soil-sidebar-toolbar__action {
  border: none;
  padding: 0;
  margin: 0;
  background: none;
}
.soil-sidebar-toolbar__action.__active {
  color: #27ae60;
}
.soil-sidebar-toolbar svg {
  vertical-align: top;
}
.soil-sidebar-toolbar .ico-pencil-os {
  width: 13px;
  height: 13px;
}
.soil-sidebar-toolbar__item {
  margin-right: 20px;
  font-size: 1px;
}
.soil-sidebar.__addnew .soil-sidebar-create {
  -webkit-transform: translate(-100%, 0);
  transform: translate(-100%, 0);
}
.soil-sidebar.__addnew .soil-sidebar-viewer {
  -webkit-transform: translate(-100%, 0);
  transform: translate(-100%, 0);
  opacity: 0;
}
.soil-sidebar-addnew {
  position: absolute;
  z-index: 4;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 0 10px 10px;
  background: linear-gradient(0deg, #fff 0%, rgba(255, 255, 255, 0) 100%);
}
.soil-sidebar-addnew__back {
  font-size: 12px;
  line-height: 1.25;
  font-weight: 500;
  color: #007aff;
  border-width: 0;
  padding: 0;
  margin: 0;
  text-align: center;
  width: 100%;
  background: none;
  margin-top: 10px;
}
.soil-sidebar-addnew__back:hover {
  color: #014e86;
}
.soil-sidebar-addnew__list {
  display: flex;
  padding: 0;
  margin: 0 0 0 -10px;
  list-style-type: none;
}
.soil-sidebar-addnew__list-item {
  flex: 1.5;
  margin-left: 10px;
}
.soil-sidebar-addnew__list-item .btn {
  width: 100%;
}
.soil-sidebar-addnew__list-item[data-size='short'] {
  flex: 1.2;
}
.soil-sidebar-viewer {
  position: relative;
  z-index: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: opacity 0.2s ease-out, -webkit-transform 0.2s ease-out;
  transition: transform 0.2s ease-out, opacity 0.2s ease-out;
  transition: transform 0.2s ease-out, opacity 0.2s ease-out,
    -webkit-transform 0.2s ease-out;
}
.soil-sidebar-search {
  position: relative;
  z-index: 3;
  margin-bottom: 10px;
  margin-top: -8px;
}
.soil-sidebar-search__btn {
  position: absolute;
  width: 13px;
  height: 13px;
  padding: 0;
  border-width: 0;
  color: #afafaf;
  background: none;
  margin: 6px 0 0 10px;
}
.soil-sidebar-search__btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 10px;
  height: 10px;
  background-color: currentColor;
  border-radius: 50%;
}
.soil-sidebar-search__btn:after {
  position: absolute;
  content: '';
  left: 6px;
  top: 7px;
  width: 1px;
  height: 7px;
  background-color: currentColor;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  -webkit-transform-origin: left top;
  transform-origin: left top;
}
.soil-sidebar-search__input {
  width: 100%;
  background-color: rgba(240, 240, 240, 0.91);
  border-width: 0;
  padding: 5px 6px 6px 27px;
  border-radius: 8px;
  transition: background-color 0.15s;
  margin: 0;
  vertical-align: top;
}
.soil-sidebar-search__input:focus {
  background-color: #e9e9e9;
  outline: none;
}
.soil-sidebar-body {
  flex: 1 1 auto;
  min-height: 0;
  position: relative;
  z-index: 2;
}
/* we need scroll class if AutoSizer List is not used */
.soil-sidebar-body.__scroll {
  overflow-y: auto;
}
.soil-sidebar-body:after {
  content: '';
  display: block;
  height: 67px;
}
.soil-sidebar-body.__empty-state {
  display: flex;
}
.soil-sidebar-empty {
  margin: auto 0;
  padding: 10px 26px 77px;
  text-align: center;
  color: #a5b2bc;
  width: 100%;
}
.soil-sidebar-empty__ico {
  margin-bottom: 15px;
  font-size: 1px;
  line-height: 1;
}
.soil-sidebar-empty__ico.with_loader {
  vertical-align: top;
}
.soil-sidebar-empty__title {
  color: #222;
  margin-top: 0;
  margin-bottom: 5px;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.34;
}
.soil-sidebar-empty p {
  margin-top: 5px;
  margin-bottom: 10px;
}
.soil-old-notes-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}
.soil-old-notes-list:after {
  content: '';
  display: block;
  height: 67px;
}
.soil-old-notes-list__list-item {
  position: relative;
  border-top: 0.5px solid rgba(165, 178, 188, 0.45);
}
.soil-old-notes-list__list-item:first-child {
  border-top-width: 0;
}
.soil-old-notes-list__item {
  cursor: pointer;
  padding: 15px 20px 14px;
  transition: background-color 0.1s;
}
.soil-old-notes-list__item:hover {
  background-color: #f6f8f9;
}
.soil-old-notes-list__item:hover .soil-old-notes-list-actions {
  visibility: visible;
}
.soil-old-notes-list__item.__selected {
  border-left: 2px solid var(--color-primary);
  background-color: #f6f8f9;
  padding-left: 18px;
}
.soil-old-notes-list__item.__selected .soil-old-notes-list-actions {
  visibility: visible;
}
.soil-old-notes-list[data-mode='edit'] .soil-old-notes-list__item {
  padding-left: 50px;
}
.soil-old-notes-list__dot {
  margin: 0 .3em 0 0;
}
.soil-old-notes-list__date {
  margin-right: .3em;
}
.soil-old-notes-list__secondary {
  white-space: nowrap;
}
.soil-old-notes-list-header {
  font-size: 10px;
  font-weight: 500;
  line-height: 1.6;
  text-transform: uppercase;
  letter-spacing: 0.6px;
}
.soil-old-notes-list-body {
  font-size: 14px;
  line-height: 1.45;
}
.soil-old-notes-list-body p {
  margin-top: 0;
  margin-bottom: 10px;
}
.soil-old-notes-list-footer {
  margin-top: 5px;
  display: flex;
  font-size: 12px;
  /* justify-content: space-between; */
  color: #a5b2bc;
}
.soil-old-notes-list-footer a {
  color: currentColor;
}
.soil-old-notes-list-actions {
  position: absolute;
  right: 15px;
  margin-top: -4px;
  cursor: pointer;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border-width: 0;
  background-color: rgba(240, 240, 240, 0);
  padding: 10px 0;
  color: #a5b2bc;
  line-height: 4px;
  transition: background-color 0.15s;
  visibility: hidden;
}
.soil-old-notes-list-actions__item {
  position: relative;
  display: block;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: currentColor;
  margin: 0 auto;
}
.soil-old-notes-list-actions__item:before {
  position: absolute;
  content: '';
  left: -6px;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: currentColor;
}
.soil-old-notes-list-actions__item:after {
  position: absolute;
  content: '';
  right: -6px;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: currentColor;
}
.soil-old-notes-list-actions:hover {
  color: #808f9b;
}
.soil-old-notes-list-actions-wrapper + .soil-old-notes-list-header {
  margin-right: 27px;
}
.soil-old-notes-list.__single {
  position: relative;
  padding: 15px 15px 14px;
}
.soil-old-notes-list.__single .soil-old-notes-list-actions {
  visibility: visible;
}
.soil-old-notes-list.__single .soil-sidebar-gallery {
  margin-left: -15px;
  margin-right: -15px;
  min-width: 290px;
}
.soil-old-notes-list.__single .soil-sidebar-gallery__area {
  border-left-width: 10px;
  border-right-width: 10px;
}
.soil-old-notes-list.__single:after {
  display: none;
}
.soil-notes-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}
.soil-notes-list:after {
  content: '';
  display: block;
  height: 67px;
}
.soil-notes-list__list-item {
  position: relative;
  border-top: 1px solid #e4e7ea;
}
.soil-notes-list__list-item:first-child {
  border-top-width: 0;
}
.soil-notes-list-meta {
  list-style-type: none;
  padding: 0;
  margin: 0 0 5px;
  display: flex;
  font-size: 12px;
  line-height: 1.43;
  align-items: center;
}
.soil-notes-list-meta__item {
  margin-right: 5px;
  flex: 0 0 auto;
}
.soil-notes-list-meta__item--color {
  margin-left: auto;
  margin-right: 0;
  margin-bottom: 1px;
  font-size: 1px;
  line-height: 1;
}
.soil-notes-list-meta__item--fields {
  flex: 1 1 auto;
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #a5b2bc;
}
.soil-notes-list-meta__dot {
  display: inline-block;
  vertical-align: top;
  width: 10px;
  height: 10px;
  border-radius: 50%;
}
.soil-notes-list-header {
  position: relative;
  font-weight: 500;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.soil-notes-list-body {
  position: relative;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.soil-notes-list-types {
  list-style-type: none;
  padding: 0;
  margin: 0;
}
.soil-notes-list-types__list-item {
  margin-top: 10px;
  display: flex;
  align-items: flex-start;
}
.soil-notes-list-types__list-item:last-child {
  margin-bottom: 0;
}
.soil-notes-list-types__pic {
  flex: 0 0 auto;
  width: 34px;
  height: 34px;
  text-align: center;
  line-height: 34px;
  background-position: 50%;
  background-size: contain;
  background-repeat: no-repeat;
  margin: 0 7px 0 0;
  background-color: rgba(214, 220, 225, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.soil-notes-list-types__pic img {
  vertical-align: top;
}
.soil-notes-list-types__content {
  flex: 1 1 auto;
}
.soil-notes-list-types__meta {
  display: block;
  font-size: 12px;
  line-height: 1.43;
  color: #a5b2bc;
  margin-bottom: -1px;
}
.soil-notes-list__item {
  cursor: pointer;
  padding: 15px 20px 14px;
  transition: background-color 0.1s;
}
.soil-notes-list__item:hover {
  background-color: #f6f8f9;
}
.soil-notes-list__item:hover .soil-notes-list-actions {
  visibility: visible;
}
.soil-notes-list__item:hover .soil-notes-list-meta__item--color {
  visibility: hidden;
}
.soil-notes-list__item.__selected {
  border-left: 2px solid var(--color-primary);
  background-color: #f6f8f9;
  padding-left: 18px;
}
.soil-notes-list__item.__selected .soil-notes-list-actions {
  visibility: visible;
}
.soil-notes-list__item.__selected .soil-notes-list-meta__item--color {
  visibility: hidden;
}
.soil-notes-list__item.__selected .soil-notes-list-header {
  white-space: normal;
  overflow: visible;
}
.soil-notes-list__item.__selected .soil-notes-list-body {
  white-space: normal;
  overflow: visible;
}
.soil-notes-list__dot {
  margin: 0 0.3em;
}
.soil-notes-list.__single {
  padding: 15px 20px 14px;
}
.soil-notes-list.__single .soil-notes-list-actions {
  visibility: visible;
}
.soil-notes-list.__single .soil-sidebar-gallery {
  min-width: 290px;
  margin-top: 10px;
  margin-bottom: 0;
}
.soil-notes-list.__single .soil-notes-list-header {
  white-space: normal;
  overflow: visible;
}
.soil-notes-list.__single .soil-notes-list-body {
  white-space: normal;
  overflow: visible;
}
.soil-notes-list.__single:after {
  display: none;
}
.soil-notes-list-footer {
  margin-top: 10px;
  display: flex;
  font-size: 12px;
  justify-content: space-between;
  color: #a5b2bc;
}
.soil-notes-list-actions {
  position: absolute;
  right: 15px;
  margin-top: -4px;
  cursor: pointer;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border-width: 0;
  background-color: rgba(240, 240, 240, 0);
  padding: 10px 0;
  color: #a5b2bc;
  line-height: 4px;
  transition: background-color 0.15s;
  visibility: hidden;
}
.soil-notes-list-actions__item {
  position: relative;
  display: block;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: currentColor;
  margin: 0 auto;
}
.soil-notes-list-actions__item:before {
  position: absolute;
  content: '';
  left: -6px;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: currentColor;
}
.soil-notes-list-actions__item:after {
  position: absolute;
  content: '';
  right: -6px;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: currentColor;
}
.soil-notes-list-actions:hover {
  color: #808f9b;
}
.soil-notes-list-actions + .soil-notes-list-header {
  margin-right: 27px;
}
.side-gallery {
  position: relative;
  overflow: hidden;
  border-radius: 6px;
  height: 146px;
  margin-top: 10px;
}
.side-gallery__list {
  margin-left: -2px;
  display: flex;
  height: 148px;
}
.side-gallery__item {
  position: relative;
  flex: 1.5;
  padding-left: 2px;
  display: flex;
  padding-bottom: 2px;
}
.side-gallery__pic {
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.1);
}
.side-gallery[data-photos='3'] .side-gallery__list {
  display: block;
}
.side-gallery[data-photos='3'] .side-gallery__item {
  width: 50%;
  height: 74px;
}
.side-gallery[data-photos='3'] .side-gallery__item:first-child {
  height: 148px;
  float: left;
}
.side-gallery[data-photos='4'] .side-gallery__list {
  flex-wrap: wrap;
}
.side-gallery[data-photos='4'] .side-gallery__item {
  height: 74px;
  flex: 0 0 auto;
  width: 50%;
}
.side-gallery-count {
  position: absolute;
  top: 0;
  left: 2px;
  right: 0;
  bottom: 2px;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}
.side-gallery-count__item {
  font-size: 20px;
  font-weight: 700;
  letter-spacing: 0.19px;
}
.soil-sidebar-gallery {
  position: relative;
  overflow-x: auto;
  margin: 5px -20px 10px;
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style: -ms-autohiding-scrollbar;
}
.soil-sidebar-gallery__area {
  display: table;
  min-width: 100%;
  border-right: 15px solid transparent;
  border-left: 15px solid transparent;
}
.soil-sidebar-gallery__item {
  display: table-cell;
  padding: 0 5px;
}
.soil-sidebar-gallery__pic {
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  border-radius: 4px;
  min-width: 125px;
  background-color: rgba(0, 0, 0, 0.1);
}
.soil-sidebar-gallery__pic:before {
  content: '';
  display: block;
  height: 0;
  padding-bottom: 64%;
}
.soil-sidebar-gallery__remove {
  position: absolute;
  z-index: 3;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.65);
  border-radius: 4px;
  border-width: 0;
  color: #fff;
  display: none;
}
.soil-sidebar-gallery__remove:before {
  position: absolute;
  content: '';
  width: 2px;
  height: 16px;
  content: '';
  background-color: currentColor;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  top: 50%;
  left: 50%;
  margin-left: -1px;
  margin-top: -8px;
}
.soil-sidebar-gallery__remove:after {
  position: absolute;
  content: '';
  width: 2px;
  height: 16px;
  content: '';
  background-color: currentColor;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  top: 50%;
  left: 50%;
  margin-left: -1px;
  margin-top: -8px;
}
.soil-sidebar-gallery__action {
  position: relative;
}
.soil-sidebar-gallery-seeall {
  position: absolute;
  z-index: 3;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(34, 34, 34, 0.75);
  border-radius: 4px;
  border-width: 0;
  color: #fff;
  display: flex;
  align-items: center;
  font-size: 12px;
  font-weight: 500;
}
.soil-sidebar-gallery-seeall__inner {
  width: 100%;
  text-align: center;
}
.soil-sidebar-gallery-seeall:hover {
  color: #fff;
}
.soil-sidebar-gallery__file {
  position: relative;
}
.soil-sidebar-gallery__file:hover .soil-sidebar-gallery__remove {
  display: block;
}
.soil-sidebar-bulkactions {
  border-bottom: 1px solid rgba(214, 220, 225, 0.5);
  padding: 10px 20px 15px;
}
.soil-fields-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}
.soil-fields-list__list-item {
  position: relative;
  border-top: 1px solid rgba(214, 220, 225, 0.5);
}
.soil-fields-list__list-item:first-child {
  border-top-width: 0;
}
.soil-fields-list__list-item:hover {
  background-color: #f6f8f9;
}
.soil-fields-list__list-item:hover .soil-fields-list-actions {
  visibility: visible;
}
.soil-fields-list__list-item.__updated {
  background-color: #f6f8f9;
}
.soil-fields-list__list-item[data-status='noselected'] .form-input {
  color: #000;
  opacity: 0.2;
  pointer-events: none;
}
.soil-fields-list__item {
  padding: 10px 20px;
  display: flex;
  align-items: center;
  min-height: 69px;
  color: #222;
}
.soil-fields-list__item:hover {
  color: #222;
}
.soil-fields-list__pic-wrapper {
  position: relative;
}

.soil-fields-list__item.__indicator {
  background: linear-gradient(0deg, rgba(243, 210, 99, 0.12), rgba(243, 210, 99, 0.12)), #FFFFFF;
}

.soil-fields-list__item.__selected {
  border-left: 2px solid var(--color-primary);
  background-color: #f6f8f9;
  padding-left: 18px;
}



.soil-fields-list__item.__selected + .soil-fields-list-actions {
  visibility: visible;
}
.soil-fields-list[data-mode='edit'] .soil-fields-list__item,
.soil-fields-list[data-mode='edit'] .soil-notes-list__item,
.soil-fields-list[data-mode='edit'] .soil-old-notes-list__item {
  padding-left: 50px;
}
.soil-fields-list[data-actions='disabled'] .soil-fields-list__header {
  margin-right: 0;
}
.soil-fields-list[data-size='compact'] .soil-fields-list__item {
  min-height: 60px;
}
.soil-fields-list[data-size='compact'] .soil-fields-list__pic {
  width: 40px;
  height: 40px;
}
.soil-fields-list-toolbar {
  display: flex;
  align-items: center;
  font-size: 1px;
}
.soil-fields-list-toolbar__item {
  margin-left: 7px;
}
.soil-fields-list-toolbar svg {
  vertical-align: top;
}
.soil-fields-list-toolbar .ico-exclamation-os {
  margin: -1px 0;
}
.soil-fields-list__edit-chk {
  position: absolute;
  top: 50%;
  left: 20px;
  margin-top: -10px;
}
.soil-fields-list__back-to {
  padding: 15px 20px;
  text-align: center;
}
.soil-fields-list__back-to small {
  font-size: 12px;
  font-weight: 500;
}
.soil-fields-list-actions {
  position: absolute;
  right: 17px;
  cursor: pointer;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border-width: 0;
  background-color: rgba(240, 240, 240, 0);
  padding: 10px 0;
  color: #a5b2bc;
  line-height: 4px;
  transition: background-color 0.15s;
  visibility: hidden;
  top: 12px;
}
.soil-fields-list-actions__item {
  position: relative;
  display: block;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: currentColor;
  margin: 0 auto;
}
.soil-fields-list-actions__item:before {
  position: absolute;
  content: '';
  left: -6px;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: currentColor;
}
.soil-fields-list-actions__item:after {
  position: absolute;
  content: '';
  right: -6px;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: currentColor;
}
.soil-fields-list-actions:hover {
  color: #808f9b;
}

.soil-fields-list__pic {
  flex: 0 0 auto;
  width: 48px;
  height: 48px;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.1);
  background-size: cover;
  background-position: 50%;
  background-repeat: no-repeat;
  margin-right: 10px;
}

.soil-fields-list__wrapper {
  position: relative;
}

.soil-fields-list__indicator {
  position: absolute;
  width: 8px;
  height: 8px;
  right: 6px;
  top: -4px;
  background: #27AE60;
  border: 1px solid #FFFFFF;
  border-radius: 50%;
}

.soil-fields-list__pic[data-style='round'] {
  border-radius: 50%;
  width: 50px;
  height: 50px;
}
.soil-fields-list__pic[data-background-size='default'] {
  background-size: auto;
}
.soil-fields-list__pic[data-size='medium'] {
  width: 40px;
  height: 40px;
}
.soil-fields-list__content {
  flex: 1 1 auto;
  min-width: 0;
  font-size: 14px;
}
.soil-fields-list__header {
  position: relative;
  font-size: 1em;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin: 0 25px 0 0;
}
.soil-fields-list__header.u-select:hover .soil-fields-list__select-type {
  color: #014e86;
}
.soil-fields-list__header[data-wrap='normal'] {
  white-space: normal;
}
.soil-fields-list__select-type {
  color: #007aff;
}
.soil-fields-list__select-type .ico-chevron-down-small-os {
  vertical-align: top;
  display: inline-block;
  margin-left: -1px;
  margin-top: 8px;
}
.soil-fields-list__select-type:hover {
  color: #014e86;
}
.soil-fields-list-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-left: -10px;
}

.soil-fields-list-meta_description {
  font-size: 12px;
  line-height: 19px;
  color: #5E5E5E;
  overflow-x: hidden;
  margin-bottom: 8px;
}

.soil-fields-list-meta__item {
  margin-left: 10px;
}
.soil-fields-list-meta__code {
  color: #a5b2bc;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
  flex: 0 0 auto;
  margin-top: 1px;
}
.soil-fields-list-meta__places {
  flex: 1 0 auto;
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.soil-fields-list-meta__side {
  color: #a5b2bc;
  white-space: nowrap;
  flex: 0 0 auto;
}
.soil-fields-list-meta__date {
  color: #a5b2bc;
  font-size: 13px;
  white-space: nowrap;
  flex: 0 0 auto;
}
.soil-fields-list-meta__ndvi {
  font-size: 12px;
  font-weight: 500;
  flex: 0 1 auto;
  text-align: right;
  margin-top: 1px;
}
.soil-fields-list-meta__ndvi[data-state='neutral'] {
  color: #a5b2bc;
}
.soil-fields-list-meta__ndvi[data-state='progress'] {
  color: #a5b2bc;
  font-weight: normal;
}
.soil-fields-list-meta__ndvi[data-state='negative'] {
  color: #ff3b30;
}
.soil-fields-list-meta__ndvi[data-state='positive'] {
  color: #27ae60;
}
.soil-fields-list-meta__quest {
  color: rgba(165, 178, 188, 0.5);
  display: inline-block;
  vertical-align: top;
  margin-top: 2px;
  margin-bottom: -2px;
  cursor: pointer;
}
.soil-fields-list[data-type='files'] .soil-fields-list__pic {
  border-radius: 50%;
}
.soil-fields-list[data-group='true'] .soil-fields-list__item {
  padding-left: 30px;
}
.soil-fields-list[data-group='true'] .soil-fields-list__item,
.soil-fields-list[data-group='true'] .soil-notes-list__item,
.soil-fields-list[data-group='true'] .soil-old-notes-list__item {
  padding-left: 50px;
}
.soil-fields-header {
  z-index: 3;
  cursor: pointer;
  background-color: rgba(255, 255, 255, 0.9);
  border-bottom: 1px solid rgba(214, 220, 225, 0.5);
  padding: 9px 20px 10px 30px;
  display: flex;
}
.soil-fields-header__arrow {
  position: absolute;
  border-left: 4px solid #222;
  width: 0;
  height: 0;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  margin-left: -14px;
  margin-top: 6px;
  transition: -webkit-transform 0.1s linear;
  transition: transform 0.1s linear;
  transition: transform 0.1s linear, -webkit-transform 0.1s linear;
}
.soil-fields-header__content {
  flex: 1 1 auto;
  font-weight: 500;
}
.soil-fields-header__count {
  color: #a5b2bc;
  font-size: 12px;
  font-weight: normal;
}
.soil-fields-header__meta {
  flex: 0 0 auto;
  font-size: 12px;
  line-height: 1.25rem;
}
.soil-fields-header.__opened {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
}
.soil-fields-header.__opened .soil-fields-header__arrow {
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
}
.soil-sidebar-create {
  position: absolute;
  z-index: 2;
  background-color: #fff;
  border-radius: 4px 0 0 4px;
  background-color: #fff;
  top: 0;
  width: 100%;
  bottom: 0;
  left: 100%;
  -webkit-transform: translate(0, 0);
  transform: translate(0, 0);
  transition: -webkit-transform 0.2s ease-out;
  transition: transform 0.2s ease-out;
  transition: transform 0.2s ease-out, -webkit-transform 0.2s ease-out;
  display: flex;
  flex-direction: column;
}
.soil-sidebar-create__form {
  position: relative;
  overflow-y: auto;
  flex: 1 1 auto;
  min-height: 0;
  padding: 15px 20px 0;
}
.soil-sidebar-create__form:after {
  content: '';
  display: block;
  height: 100px;
}
.analyses-sidebar {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style: -ms-autohiding-scrollbar;
  min-height: 0;
  padding-bottom: 80px;
}
.analyses-sidebar__list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}
.analyses-sidebar__list-item {
  border-bottom: 1px solid rgba(214, 220, 225, 0.5);
  padding: 10px 20px 8px 30px;
}
.analyses-sidebar__list-item.__selected {
  background-color: #f6f8f9;
}
.analyses-sidebar__list-item[data-opened='false'] .analyses-sidebar-body {
  display: none;
}
.analyses-sidebar__list-item[data-opened='false']
  .analyses-sidebar-header__arrow:before {
  -webkit-transform: rotate(-90deg);
  transform: rotate(-90deg);
}
.analyses-sidebar__list-item[data-opened='false']
  .analyses-sidebar-header-name__visible,
.analyses-sidebar__list-item[data-opened='false']
  .analyses-sidebar-header-actions {
  display: none;
}
.analyses-sidebar__list-item[data-opened='false']
  .analyses-sidebar-header-name__meta {
  display: block;
}
.analyses-sidebar__list-item[data-visible='false']
  .analyses-sidebar-header-name {
  color: #a5b2bc;
}
.analyses-sidebar__list-item[data-visible='false']
  .analyses-sidebar-header-name__meta {
  display: none;
}
.analyses-sidebar__list-item[data-visible='false']
  .analyses-sidebar-header-name__visible {
  display: block;
  opacity: 1;
}
.analyses-sidebar__list-item[data-locked='true'] {
  background-color: #f6f8f9;
}
.analyses-sidebar__list-item[data-locked='true'] .analyses-sidebar-header {
  color: #a5b2bc;
}
.analyses-sidebar__list-item[data-locked='true']
  .analyses-sidebar-header-name__visible {
  display: none;
}
.analyses-sidebar__list-item[data-locked='true']
  .analyses-sidebar-header-actions {
  display: flex;
}
.analyses-sidebar__list-item[data-action='new'] {
  padding: 0;
}
.analyses-sidebar-addnew {
  padding: 10px 10px 10px 30px;
  margin: 0;
  border: none;
  background: none;
  font-weight: 500;
  width: 100%;
  text-align: left;
  line-height: 1.45;
}
.analyses-sidebar-addnew:hover {
  color: #27ae60;
}
.analyses-sidebar-addnew__ico {
  position: absolute;
  display: inline-block;
  width: 8px;
  height: 8px;
  vertical-align: top;
  margin-left: -17px;
  margin-top: 5px;
}
.analyses-sidebar-addnew__ico:before {
  position: absolute;
  content: '';
  width: 8px;
  height: 2px;
  background-color: currentColor;
  left: 0;
  top: 50%;
  margin-top: -1px;
}
.analyses-sidebar-addnew__ico:after {
  position: absolute;
  content: '';
  width: 8px;
  height: 2px;
  background-color: currentColor;
  left: 0;
  top: 50%;
  margin-top: -1px;
  -webkit-transform: rotate(-90deg);
  transform: rotate(-90deg);
}
.analyses-sidebar-header {
  position: relative;
  display: flex;
  cursor: pointer;
}
.analyses-sidebar-header__lock {
  position: absolute;
  border: none;
  padding: 0;
  margin: 0;
  background: none;
  color: currentColor;
  margin-left: -19px;
  margin-top: 1px;
}
.analyses-sidebar-header-name {
  flex: 1 1 auto;
  display: flex;
  min-width: 0;
}
.analyses-sidebar-header-name__value {
  cursor: pointer;
  position: relative;
  min-width: 0;
  flex: 0 1 auto;
  max-width: 100%;
  font-weight: 500;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.analyses-sidebar-header-name__meta {
  flex: 0 0 auto;
  display: none;
  margin-left: 5px;
  color: #a5b2bc;
  font-size: 12px;
  margin-top: 2px;
}
.analyses-sidebar-header-name__visible {
  margin: 1px 0 -1px 2px;
  background: none;
  padding: 0;
  border: none;
  flex: 0 0 auto;
  opacity: 0;
  color: currentColor;
}
.analyses-sidebar-header-name__visible:hover {
  color: #27ae60;
}
.analyses-sidebar-header-actions {
  flex: 0 0 auto;
  display: flex;
  margin-top: 2px;
  margin-bottom: -3px;
  opacity: 0;
}
.analyses-sidebar-header-actions__item {
  margin-left: 10px;
}
.analyses-sidebar-header-actions__btn {
  margin: 0;
  padding: 0;
  background: none;
  border: none;
  color: currentColor;
}
.analyses-sidebar-header-actions__btn:hover {
  color: #27ae60;
}
.analyses-sidebar-header__arrow {
  position: absolute;
  left: -24px;
  top: -3px;
  border: none;
  padding: 0;
  margin: 0;
  background: none;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.analyses-sidebar-header__arrow:before {
  content: '';
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid currentColor;
  transition: -webkit-transform 0.15s;
  transition: transform 0.15s;
  transition: transform 0.15s, -webkit-transform 0.15s;
}
.analyses-sidebar-header:hover .analyses-sidebar-header__arrow {
  color: #27ae60;
}
.analyses-sidebar-header:hover .analyses-sidebar-header-name__visible {
  opacity: 1;
}
.analyses-sidebar-header:hover .analyses-sidebar-header-actions {
  opacity: 1;
}
.analyses-sidebar-body {
  padding-top: 7px;
  padding-bottom: 7px;
}
.chart-params {
  list-style-type: none;
  padding: 0;
  margin: 0;
}
.chart-params__list-item {
  border-bottom: 1px solid rgba(214, 220, 225, 0.5);
}
.chart-params__list-item[data-type='muted'] {
  color: #a5b2bc;
}
.chart-params__list-item[data-type='muted'] .chart-params__eye {
  visibility: visible;
}
.chart-params__list-item:hover .chart-params__eye {
  visibility: visible;
}
.chart-params__list-item:hover
  .chart-params-legend
  .chart-params__legend-color {
  visibility: hidden;
}
.chart-params__list-item:hover
  .chart-params-legend
  .chart-params-legend__remove {
  visibility: visible;
}
.chart-params__action-ico {
  position: absolute;
}
.chart-params__eye {
  color: #a5b2bc;
  margin-left: 4px;
  visibility: hidden;
}
.chart-params__legend-color {
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 50%;
}
.chart-params-legend {
  list-style-type: none;
  padding: 0 40px 3px 35px;
  margin: -5px 0 0;
  font-size: 12px;
  line-height: 1.34;
}
.chart-params-legend__list-item {
  position: relative;
  margin-bottom: 5px;
}
.chart-params-legend .chart-params__legend-color {
  right: -20px;
  top: 2px;
}
.chart-params-legend__remove {
  position: absolute;
  color: #a5b2bc;
  border: none;
  background: none;
  padding: 0;
  right: -21px;
  top: 1px;
  visibility: hidden;
  font-size: 12px;
  line-height: 1;
}
.chart-params-legend__remove:hover {
  color: #333;
}
.chart-params-options {
  list-style-type: none;
  padding: 0 30px 0 0;
  margin: 0 9px 10px 10px;
  border-radius: 8px;
  background-color: #f5f7f9;
}
.chart-params-options__list {
  list-style-type: none;
  padding: 12px 0 0 10px;
  margin: 0;
}
.chart-params-options__list ul {
  padding-left: 26px;
  margin-top: -5px;
  margin-bottom: -9px;
}
.chart-params-options__list-item {
  position: relative;
  padding-bottom: 9px;
}
.chart-params-options .chart-params__legend-color {
  right: -20px;
  top: 5px;
}
.chart-params__header {
  padding: 10px 20px 9px 35px;
  margin: 0;
  font-weight: 500;
}
.chart-params__header .ico-eye-closed-os {
  vertical-align: top;
  margin-top: 3px;
}
.chart-params__header[data-type='to-add'] .chart-params__action-ico {
  width: 8px;
  height: 8px;
  margin-top: 5px;
  margin-left: -21px;
}
.chart-params__header[data-type='to-add'] .chart-params__action-ico:before,
.chart-params__header[data-type='to-add'] .chart-params__action-ico:after {
  position: absolute;
  top: 0;
  left: 3px;
  content: '';
  background-color: #222;
  width: 2px;
  height: 8px;
  font-size: 0;
  -webkit-transform-origin: center;
  transform-origin: center;
}
.chart-params__header[data-type='to-add'] .chart-params__action-ico:after {
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
}
.chart-params__header[data-type='selected'] .chart-params__action-ico {
  width: 8px;
  height: 8px;
  margin-left: -19px;
  margin-top: 5px;
  width: 0;
  height: 0;
  border-left: 4px solid #222;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
}
.chart-params__header[data-type='opened'] .chart-params__action-ico {
  width: 8px;
  height: 8px;
  margin-left: -19px;
  margin-top: 5px;
  width: 0;
  height: 0;
  border-left: 4px solid #222;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
  -webkit-transform-origin: center;
  transform-origin: center;
}
.weather-info {
  padding: 20px 30px;
}
.weather-info-highlight {
  margin-bottom: 30px;
  display: flex;
}
.weather-info-current {
  flex: 0 0 auto;
  width: 212px;
  padding-right: 40px;
}
.weather-info-current__title {
  margin-bottom: 14px;
}
.weather-info-current__title h2 {
  font-size: 20px;
  font-weight: 700;
  line-height: 1.3;
  margin-top: 0;
  margin-bottom: 0;
}
.weather-info-current__title small {
  display: block;
  color: #a5b2bc;
  font-size: 12px;
  line-height: 1;
  font-weight: normal;
}
.weather-info-current-metrics {
  display: flex;
}
.weather-info-current-metrics__ico {
  flex: 0 0 auto;
  width: 60px;
  margin-right: 10px;
  margin-top: 1px;
}
.weather-info-current-metrics__temp {
  flex: 1 1 auto;
  color: #222;
  font-size: 44px;
  font-weight: 500;
  line-height: 49px;
}
.weather-info-quickmetrics {
  border-left: 0.5px solid #d6dce1;
  flex: 1 1 auto;
}
.weather-info-quickmetrics__inner {
  display: flex;
  flex-wrap: wrap;
  margin: -3px 0 -27px 0;
}
.weather-info-quickmetrics__item {
  width: 143px;
  padding-left: 43px;
  padding-bottom: 23px;
}
.weather-info-quickmetrics__label {
  margin-top: 0;
  margin-bottom: -1px;
  color: #a5b2bc;
  font-weight: normal;
  font-size: 1em;
}
.weather-info-quickmetrics__value {
  margin-top: 0;
  margin-bottom: 0;
}
.weather-info-quickmetrics__status {
  display: inline-block;
  vertical-align: top;
  margin-top: 3px;
}
.weather-info-charts {
  position: relative;
  border-top: 10px solid #f6f8f9;
  margin: 0 -30px;
  padding: 20px 30px 0;
}
.weather-info-charts:before {
  position: absolute;
  content: '';
  height: 1px;
  border-top: 0.5px solid #d6dce1;
  top: -10px;
  left: 0;
  right: 0;
}
.weather-info-charts__title {
  font-size: 20px;
  font-weight: 700;
  line-height: 1.3;
  margin-top: 0;
  margin-bottom: 12px;
}
.weather-info-charts__description {
  margin-top: -13px;
  margin-bottom: 15px;
}
.weather-info-charts__description p {
  margin-top: 0;
  margin-bottom: 0;
}
.weather-info-charts-legend {
  margin-bottom: 18px;
  display: flex;
  margin-left: -20px;
  flex-wrap: wrap;
}
.weather-info-charts-legend__item {
  margin-left: 20px;
}
.weather-info-charts-loader {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.6);
  display: none;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  line-height: 1.25rem;
}
.weather-info-charts-loader .spinner-container {
  margin-right: 6px;
  vertical-align: top;
  margin-top: 1px;
}
.weather-info-charts-viewer {
  position: relative;
  margin-bottom: 22px;
  min-height: 250px;
}
.weather-info-charts-viewer[data-state='infostatus'] {
  border: 0.5px solid rgba(165, 178, 188, 0.45);
  min-height: 250px;
  display: flex;
}
.weather-info-charts-viewer[data-state='infostatus']
  .weather-info-charts-viewer__inner {
  margin: auto;
  width: 100%;
  padding: 20px;
  text-align: center;
}
.weather-info-charts-viewer[data-state='infostatus']
  .weather-info-charts-viewer__inner
  p {
  color: #a5b2bc;
  max-width: 310px;
  margin: 0 auto 10px;
}
.weather-info-charts-viewer[data-state='loading'] .weather-info-charts-loader {
  display: flex;
}
.weather-info-charts-viewer__title {
  font-size: 20px;
  font-weight: 700;
  line-height: 1.3;
  margin-top: 0;
  margin-bottom: 5px;
}
.weather-info-days {
  position: relative;
  border-top: 10px solid #f6f8f9;
  margin: 0 -30px;
  padding: 20px 0 0;
}
.weather-info-days:before {
  position: absolute;
  content: '';
  height: 1px;
  border-top: 0.5px solid #d6dce1;
  top: -10px;
  left: 0;
  right: 0;
}
.weather-info-days__title {
  font-size: 20px;
  font-weight: 700;
  line-height: 1.3;
  margin-top: 0;
  margin-bottom: 12px;
  padding-left: 30px;
}
.weather-info-days-timeline {
  position: relative;
  overflow-x: auto;
  overflow-y: hidden;
}
.weather-info-days-timeline__area {
  display: flex;
}
.weather-info-days-timeline__item {
  border-left: 0.5px solid #d6dce1;
  padding: 0 19px 0 19px;
  box-sizing: content-box;
  min-width: 123px;
}
.weather-info-days-timeline__item:first-child {
  padding-left: 30px;
  border-left-width: 0;
}
.weather-info-days-timeline__day {
  color: #a5b2bc;
  font-size: 12px;
  line-height: 1.34;
  margin-top: 0;
  margin-bottom: 6px;
}
.weather-info-days-timeline__title {
  font-size: 16px;
  line-height: 1.32;
  font-weight: normal;
  margin-top: 0;
  margin-bottom: 0;
}
.weather-info-days-timeline__ico {
  display: inline-block;
  vertical-align: top;
  margin-top: 2px;
  margin-right: 5px;
}
.weather-info-days-timeline__main {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 18px;
}
.weather-info-days-chars {
  list-style-type: none;
  padding: 0;
  margin: 0 0 -14px;
  font-weight: 500;
}
.weather-info-days-chars h4 {
  color: #a5b2bc;
  font-weight: normal;
  font-size: 1em;
  line-height: 1.37;
  margin-top: 0;
  margin-bottom: 0;
}
.weather-info-days-chars p {
  margin-top: 0;
  margin-bottom: 0;
}
.weather-info-days-chars__list-item {
  margin-bottom: 14px;
}
.weather-info-days__status {
  display: inline-block;
  vertical-align: top;
  margin-top: 2px;
  margin-right: 5px;
}
.weather-info-hourly {
  margin-bottom: 4px;
}
.weather-info-hourly__title {
  font-size: 14px;
  margin-bottom: 16px;
}
.weather-info-hourly-table {
  position: relative;
  overflow-x: auto;
}
.weather-info-hourly-table__table {
  width: 100%;
}
.weather-info-hourly-table__table th,
.weather-info-hourly-table__table td {
  padding-left: 24px;
}
.weather-info-hourly-table__table th {
  padding-bottom: 13px;
  text-align: left;
  color: #a5b2bc;
}
.weather-info-hourly-table__table td {
  padding-bottom: 13px;
}
.weather-info-hourly-table__table tr > :first-child {
  padding-left: 0;
}
@supports (display: grid) {
  .weather-info-quickmetrics__inner {
    display: grid;
    grid-template-rows: auto;
    grid-template-columns: repeat(auto-fit, minmax(143px, 1fr));
    grid-gap: 0;
  }
  .weather-info-quickmetrics__item {
    width: auto;
  }
}
.chart-bubble-note {
  position: relative;
  display: inline-flex;
  width: 24px;
  height: 26px;
  color: #fff;
  font-size: 11px;
  line-height: 16px;
  font-weight: bold;
  vertical-align: top;
  text-align: center;
  justify-content: center;
  align-items: flex-end;
  transition: -webkit-transform 0.15s;
  transition: transform 0.15s;
  transition: transform 0.15s, -webkit-transform 0.15s;
}
.chart-bubble-note:hover {
  -webkit-transform: scale(1.2);
  transform: scale(1.2);
}
.chart-bubble-note:hover .chart-bubble-note__value {
  -webkit-transform: translate(0, -0.1px);
  transform: translate(0, -0.1px);
}
.chart-bubble-note__value {
  min-width: 24px;
  flex: 1 1 auto;
  padding-bottom: 5px;
}
.chart-bubble-note__value + .chart-bubble-note__dot {
  margin-right: 7px;
  margin-left: -6px;
}
.chart-bubble-note__add {
  position: relative;
  width: 8px;
  height: 8px;
  flex: 0 0 auto;
  margin-bottom: 10px;
}
.chart-bubble-note__add:before {
  position: absolute;
  content: '';
  width: 8px;
  height: 2px;
  background-color: #fff;
  left: 0;
  top: 3px;
}
.chart-bubble-note__add:after {
  position: absolute;
  content: '';
  width: 2px;
  height: 8px;
  left: 3px;
  top: 0;
  background-color: #fff;
}
.chart-bubble-note__dot {
  flex: 0 0 auto;
  width: 6px;
  height: 6px;
  background-color: #fff;
  border-radius: 50%;
  margin-bottom: 11px;
}
.chart-bubble-note[data-color='gray'] {
  background-image: url("data:image/svg+xml,%3Csvg width='24' height='26' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cpath d='M2.3826302,17.6637152 C0.394290649,15.659512 -0.75,12.9442117 -0.75,10.0458584 C-0.75,4.08423665 4.06216883,-0.75 10,-0.75 C15.9378312,-0.75 20.75,4.08423665 20.75,10.0458584 C20.75,15.0847434 17.2847271,19.4214493 12.4629773,20.5568643 C12.3111789,20.5926095 12.158473,20.6250475 12.0049437,20.6541458 C11.8184293,20.6894957 11.6307417,20.7199089 11.4420274,20.7453343 L2.85988996,22.6908748 C2.05196297,22.874029 1.2485329,22.3675506 1.06537868,21.5596236 C0.996731406,21.2568078 1.02374782,20.9401512 1.14270063,20.6533411 L2.3826302,17.6637152 Z' transform='translate(2 2)' fill='%23A5B2BC' fill-rule='nonzero' stroke='%23FFF' stroke-width='1.5'/%3E%3C/svg%3E");
}
.chart-bubble-note[data-color='green'] {
  background-image: url("data:image/svg+xml,%3Csvg width='24' height='26' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cpath d='M2.3826302,17.6637152 C0.394290649,15.659512 -0.75,12.9442117 -0.75,10.0458584 C-0.75,4.08423665 4.06216883,-0.75 10,-0.75 C15.9378312,-0.75 20.75,4.08423665 20.75,10.0458584 C20.75,15.0847434 17.2847271,19.4214493 12.4629773,20.5568643 C12.3111789,20.5926095 12.158473,20.6250475 12.0049437,20.6541458 C11.8184293,20.6894957 11.6307417,20.7199089 11.4420274,20.7453343 L2.85988996,22.6908748 C2.05196297,22.874029 1.2485329,22.3675506 1.06537868,21.5596236 C0.996731406,21.2568078 1.02374782,20.9401512 1.14270063,20.6533411 L2.3826302,17.6637152 Z' transform='translate(2 2)' fill='%2327AE60' fill-rule='nonzero' stroke='%23FFF' stroke-width='1.5'/%3E%3C/svg%3E");
}
.chart-bubble-note[data-size='long'] {
  width: 39px;
}
.chart-bubble-note[data-size='long'][data-color='gray'] {
  background-image: url("data:image/svg+xml,%3Csvg width='39' height='26' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cpath d='M11.4214462,20.75 L2.85988996,22.6908748 C2.05196297,22.874029 1.2485329,22.3675506 1.06537868,21.5596236 C0.996731406,21.2568078 1.02374782,20.9401512 1.14270063,20.6533411 L2.40555251,17.6084466 C0.403496431,15.6110251 -0.75,12.8975858 -0.75,10 C-0.75,4.06293894 4.06293894,-0.75 10,-0.75 L25,-0.75 C30.9370611,-0.75 35.75,4.06293894 35.75,10 C35.75,15.9370611 30.9370611,20.75 25,20.75 L11.4214462,20.75 Z' transform='translate(2 2)' fill='%23A5B2BC' fill-rule='nonzero' stroke='%23FFF' stroke-width='1.5'/%3E%3C/svg%3E");
}
.chart-bubble-note[data-size='long'][data-color='green'] {
  background-image: url("data:image/svg+xml,%3Csvg width='39' height='26' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cpath d='M11.4214462,20.75 L2.85988996,22.6908748 C2.05196297,22.874029 1.2485329,22.3675506 1.06537868,21.5596236 C0.996731406,21.2568078 1.02374782,20.9401512 1.14270063,20.6533411 L2.40555251,17.6084466 C0.403496431,15.6110251 -0.75,12.8975858 -0.75,10 C-0.75,4.06293894 4.06293894,-0.75 10,-0.75 L25,-0.75 C30.9370611,-0.75 35.75,4.06293894 35.75,10 C35.75,15.9370611 30.9370611,20.75 25,20.75 L11.4214462,20.75 Z' transform='translate(2 2)' fill='%2327AE60' fill-rule='nonzero' stroke='%23FFF' stroke-width='1.5'/%3E%3C/svg%3E");
}
.chart-bubble-note[data-notification='show']:after {
  position: absolute;
  z-index: 2;
  content: '';
  width: 9px;
  height: 9px;
  border: 1.5px solid #fff;
  background-color: #eb4c4c;
  border-radius: 50%;
  right: -1px;
  top: -1px;
}
.map-controls {
  position: absolute;
  z-index: 3;
  top: 50%;
  right: 10px;
  -webkit-transform: translate(0, -50%);
  transform: translate(0, -50%);
  transition: margin-top 0.15s linear;
}
.map-controls-group {
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.25);
  background-color: #222;
  list-style-type: none;
  padding: 0;
  margin: 10px 0;
  border-radius: 20px;
}
.map-controls-group__list-item {
  position: relative;
  margin-bottom: 2px;
}
.map-controls-group__list-item:first-child .map-controls-item {
  border-top-right-radius: 20px;
  border-top-left-radius: 20px;
}
.map-controls-group__list-item:first-child .map-controls-item:before {
  display: none;
}
.map-controls-group__list-item:last-child {
  margin-bottom: 0;
}
.map-controls-group__list-item:last-child .map-controls-item {
  border-bottom-right-radius: 20px;
  border-bottom-left-radius: 20px;
}
.map-controls-group__list-item:last-child .map-controls-item:after {
  display: none;
}
.map-controls-item {
  border-width: 0;
  margin: 0;
  padding: 0;
  width: 40px;
  height: 40px;
  background-color: rgba(17, 17, 17, 0);
  transition: color 0.1s, background-color 0.1s;
  vertical-align: top;
  color: #ebebeb;
  display: flex;
  justify-content: center;
  align-items: center;
}
.map-controls-item:hover:not([disabled]) {
  color: #fff;
  background-color: #000;
}
.map-controls-item:hover:not([disabled]):after {
  background-color: #000;
  left: 0;
  right: 0;
  height: 4px;
  border-radius: 0;
}
.map-controls-item:hover:not([disabled]):before {
  background-color: #000;
  left: 0;
  right: 0;
  border-radius: 0;
  opacity: 1;
}
.map-controls-item[disabled] {
  color: rgba(235, 235, 235, 0.4);
}
.map-controls-item:after {
  position: absolute;
  content: '';
  bottom: -2px;
  left: 9px;
  right: 9px;
  height: 2px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 2px;
  transition: all 0.1s;
}
.map-controls-item:before {
  position: absolute;
  content: '';
  top: -2px;
  left: 9px;
  right: 9px;
  height: 2px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 2px;
  transition: all 0.1s;
  opacity: 0;
}
.map-controls-item svg {
  margin: auto;
}
.map-controls-item svg,
.map-controls-item use {
  vertical-align: top;
}
.map-controls-item[data-icon='location'] {
  padding-top: 1.5px;
  padding-right: 1px;
}
.map-controls-item[data-icon='ruler'] {
  padding-right: 0.5px;
}
.map-controls-item[data-icon='plus'] {
  padding-top: 2px;
}
.map-controls-item[data-icon='plus']:after {
  bottom: -4px;
}
.map-controls-item--active {
  background-color: #fff;
  color: #222;
}
.map-controls-item--active.map-controls-item--active:hover {
  background-color: #fff;
  color: #222;
}
.rb-toolbar {
  position: absolute;
  z-index: 3;
  right: 0;
  bottom: 10px;
  display: flex;
}
.rb-toolbar-item {
  margin-right: 10px;
  border-radius: 20px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.25);
  background-color: #222;
}
.top-toolbar {
  position: absolute;
  z-index: 3;
  right: 10px;
  top: 5px;
  left: 5px;
  display: flex;
  justify-content: space-between;
  transition: -webkit-transform 0.15s linear;
  transition: transform 0.15s linear;
  transition: transform 0.15s linear, -webkit-transform 0.15s linear;
}
.top-toolbar-item {
  margin-right: 10px;
  border-radius: 20px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.25);
  background-color: #222;
}
.top-toolbar-item.__fixed {
  flex: 0 0 auto;
}
.top-toolbar-item.__fluid {
  flex: 1 1 auto;
  min-width: 0;
}
.top-toolbar-item:last-child {
  margin-right: 0;
}
.map-search {
  position: absolute;
  z-index: 3;
  top: 5px;
  left: 5px;
  border-radius: 20px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.25);
  background-color: #222;
  width: 42px;
  color: #ebebeb;
  transition: width 0.2s, border-radius 0.2s, background-color 0.2s,
    translate 0.15s linear;
}
.map-search:hover {
  background-color: #101010;
}
.map-search__outer {
  position: relative;
  height: 42px;
}
.map-search__btn {
  position: absolute;
  z-index: 3;
  top: 0;
  left: 0;
  width: 42px;
  height: 42px;
  color: #ebebeb;
  border: none;
  background: none;
  margin: 0;
  padding: 0;
}
.map-search__btn svg {
  vertical-align: top;
}
.map-search__input {
  color: #fff;
  background: none;
  padding: 11.5px 20px 10.5px 42px;
  margin: 0;
  font-size: 14px;
  vertical-align: top;
  width: 100%;
  line-height: 1.25rem;
  border: none;
  border-radius: 8px;
  height: 42px;
}
.map-search-results {
  display: none;
  position: relative;
  overflow-y: auto;
  max-height: 300px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}
.map-search-results__empty {
  color: #ebebeb;
  padding: 15px 20px 12px;
  text-align: center;
}
.map-search--opened {
  border-radius: 8px;
  width: 270px;
}
.map-search--opened:hover {
  background-color: #222;
}
.map-search--loading .map-search__input {
  padding-right: 50px;
}
.map-search--results .map-search__input {
  padding-right: 50px;
}
.map-search--results .map-search__outer:after {
  position: absolute;
  content: '';
  bottom: -1px;
  left: 0;
  right: 0;
  height: 1px;
  border-top: 0.5px solid #353535;
}
.map-search--results .map-search-results {
  display: block;
  line-height: 1.25rem;
}
.map-search--results .map-search-results__group {
  position: relative;
  padding: 15px 20px 8px;
}
.map-search--results .map-search-results__group:before {
  position: absolute;
  content: '';
  top: -1px;
  left: 0;
  right: 0;
  height: 1px;
  border-top: 0.5px solid #353535;
}
.map-search--results .map-search-results__group:first-child:before {
  display: none;
}
.map-search--results .map-search-results__label {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  margin-bottom: 8px;
}
.map-search--results .map-search-results__list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}
.map-search--results .map-search-results__list-item {
  margin-bottom: 1px;
}
.map-search--results .map-search-results__item {
  display: block;
  color: rgba(235, 235, 235, 0.6);
  margin: 0 -20px;
  padding: 7px 20px;
}
.map-search--results .map-search-results__item:hover {
  background-color: #101010;
}
.map-search--results .map-search-results__hgh {
  color: #fff;
}
.map-view-type-item {
  padding: 0 20px;
  border-radius: 20px;
  border-width: 0;
  background-color: rgba(17, 17, 17, 0);
  transition: color 0.1s, background-color 0.1s;
  color: #ebebeb;
  min-height: 40px;
  display: flex;
  align-items: center;
}
.map-view-type-item:after {
  content: '';
  width: 5px;
  height: 5px;
  border-right: 1px solid currentColor;
  border-bottom: 1px solid currentColor;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  margin-left: 8px;
  margin-top: 1px;
  transition: -webkit-transform 0.1s;
  transition: transform 0.1s;
  transition: transform 0.1s, -webkit-transform 0.1s;
}
.map-view-type-item:hover {
  color: #fff;
  background-color: #000;
}
.map-view-type-item__ico {
  margin-right: 10px;
  line-height: 1px;
  margin-top: -2px;
}
.map-view-type-item__ico svg,
.map-view-type-item__ico use {
  vertical-align: top;
}
.map-view-type-item[data-arrow='bottom']:after {
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  margin-top: -1px;
}
.navigation-link {
  position: relative;
  vertical-align: top;
  border-radius: 20px;
  border-width: 0;
  background-color: rgba(17, 17, 17, 0);
  transition: color 0.1s, background-color 0.1s;
  width: 40px;
  height: 40px;
  color: #ebebeb;
  font-size: 0;
  line-height: 0;
  padding: 0;
}
.navigation-link:hover {
  color: #fff;
  background-color: #000;
}
.navigation-link:before {
  position: absolute;
  content: '';
  background-color: currentColor;
  width: 14px;
  left: 13px;
  top: 16px;
  height: 2px;
  border-radius: 1px;
}
.navigation-link:after {
  position: absolute;
  content: '';
  background-color: currentColor;
  width: 14px;
  height: 2px;
  border-radius: 1px;
  left: 13px;
  top: 22px;
}
.map-drop-bar {
  position: relative;
}
.map-drop-bar-item {
  padding: 0 20px;
  border-radius: 20px;
  border-width: 0;
  background-color: rgba(17, 17, 17, 0);
  transition: color 0.1s, background-color 0.1s;
  color: #ebebeb;
  min-height: 40px;
  display: flex;
  align-items: center;
  -webkit-transform: translate(0, 0);
  transform: translate(0, 0);
}
.map-drop-bar-item:after {
  content: '';
  width: 5px;
  height: 5px;
  border-left: 1px solid currentColor;
  border-bottom: 1px solid currentColor;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  margin-left: 8px;
  margin-top: -2px;
  transition: -webkit-transform 0.1s;
  transition: transform 0.1s;
  transition: transform 0.1s, -webkit-transform 0.1s;
}
.map-drop-bar-item:hover {
  color: #fff;
  background-color: #000;
}
.map-drop-bar-nav {
  position: absolute;
  min-width: 100px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.25);
  background-color: #222;
  border-radius: 4px;
  top: 50px;
  font-size: 10px;
  padding: 10px 0;
  left: 0;
  -webkit-transform: translate(0, 10px);
  transform: translate(0, 10px);
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.1s, visibility 0.1s, -webkit-transform 0.1s;
  transition: opacity 0.1s, visibility 0.1s, transform 0.1s;
  transition: opacity 0.1s, visibility 0.1s, transform 0.1s,
    -webkit-transform 0.1s;
  overflow-x: hidden;
  overflow-y: auto;
  max-height: 350px;
}
.map-drop-bar-nav__list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}
.map-drop-bar-nav__item {
  display: block;
  cursor: pointer;
  padding: 0 20px;
  min-height: 4em;
  display: flex;
  align-items: center;
  color: #ebebeb;
  background-color: rgba(17, 17, 17, 0);
  transition: color 0.1s, background-color 0.1s;
}
.map-drop-bar-nav__item:hover {
  color: #fff;
  background-color: #000;
}
.map-drop-bar-nav__value {
  font-size: 1.4em;
}
.map-drop-bar.__opened .map-drop-bar-item:after {
  -webkit-transform: rotate(-225deg) translate(1px, -2px);
  transform: rotate(-225deg) translate(1px, -2px);
}
.map-drop-bar.__opened .map-drop-bar-nav {
  opacity: 1;
  visibility: visible;
  -webkit-transform: translate(0, 0);
  transform: translate(0, 0);
}
.map-drop-bar[data-size='large'] .map-drop-bar-item {
  min-height: 42px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.25);
  border-radius: 8px;
  background-color: #222;
  font-size: 16px;
}
.map-drop-bar[data-size='large'] .map-drop-bar-item:after {
  -webkit-transform: rotate(0);
  transform: rotate(0);
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid currentColor;
  border-bottom-width: 0;
  margin: 0 0 0 5px;
}
.map-drop-bar[data-size='large'] .map-drop-bar-item:hover {
  color: #fff;
  background-color: #000;
}
.map-point {
  position: absolute;
  z-index: 4;
  width: 7px;
  height: 7px;
  background-color: #212121;
  border: 2px solid rgba(255, 255, 255, 0.95);
  border-radius: 50%;
}
.map-tooltip {
  position: absolute;
  z-index: 995;
  max-width: 290px;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.25);
  border-radius: 6px;
  background-color: #fff;
}
.map-tooltip-arrow {
  position: absolute;
  background-image: url("data:image/svg+xml,%3Csvg width='13' height='7' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cdefs%3E%3Cpath d='M10.9879413,11.9879413 C11.1363056,9.86001713 10.710565,8.45498783 9.71071965,7.77285344 C8.81298522,7.16038321 7.00847294,6.21384757 4.29718281,4.93324651 L4.29718921,4.93323296 C4.04750492,4.81530166 3.94069802,4.51729034 4.05862932,4.26760604 C4.1078526,4.16339044 4.19156935,4.07938098 4.2956125,4.02979423 C7.02466319,2.72913448 8.8296989,1.77845663 9.71071965,1.17776067 C10.6911208,0.509305353 11.1168613,-0.888847316 10.9879413,-3.01669734 L10.9879413,11.9879413 Z' id='a'/%3E%3C/defs%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg transform='translate(-738 -293)'%3E%3Cg transform='translate(737 292)'%3E%3Cg%3E%3Cg%3E%3Cuse fill='%23FFF' transform='rotate(-90 7.5137 4.4856)' xlink:href='%23a'/%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  width: 13px;
  height: 7px;
}
.map-tooltip-info {
  display: flex;
  margin-left: -13px;
}
.map-tooltip-item {
  margin-left: 13px;
}
.map-tooltip-item__value {
  font-weight: bold;
  font-size: 16px;
}
.map-tooltip-item__diff {
  color: rgba(34, 34, 34, 0.46);
}
.map-tooltip-status {
  font-weight: 500;
  margin-bottom: 4px;
}
.map-tooltip.__arrow-down .map-tooltip-arrow {
  bottom: -7px;
  left: 50%;
  margin-left: -6px;
}
.map-tooltip.__arrow-top .map-tooltip-arrow {
  top: -7px;
  left: 50%;
  margin-left: -6px;
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
}
.map-tooltip.__arrow-left .map-tooltip-arrow {
  left: -10px;
  top: 50%;
  margin-top: -3px;
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
}
.map-tooltip.__arrow-right .map-tooltip-arrow {
  right: -10px;
  top: 50%;
  margin-top: -3px;
  -webkit-transform: rotate(-90deg);
  transform: rotate(-90deg);
}
.map-popover {
  position: absolute;
  z-index: 11;
  background: rgba(255, 255, 255, 0.97);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.25);
  border-radius: 6px;
}
.map-popover-arrow {
  position: absolute;
  z-index: 2;
  top: -19px;
  left: 50%;
  margin-left: -13px;
  overflow: hidden;
  width: 26px;
  height: 20px;
}
.map-popover-arrow:before {
  position: absolute;
  content: '';
  background-color: #fff;
  border-top: 1px solid geyser;
  border-right: 1px solid geyser;
  width: 8px;
  height: 8px;
  border-top-right-radius: 2px;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  left: 6px;
  bottom: -7px;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.25);
}
.map-popover.__arrow-bottom .map-popover-arrow {
  top: auto;
  bottom: -19px;
}
.map-popover.__arrow-bottom .map-popover-arrow:before {
  bottom: auto;
  top: 0;
}
.popover-place {
  min-width: 310px;
  font-size: 12px;
  line-height: 1.25;
}
.popover-place .map-popover__body {
  padding: 20px;
}
.popover-place-actions {
  margin-bottom: 15px;
}
.popover-place-actions__item {
  margin-bottom: 5px;
}
.popover-place-chars {
  display: flex;
  flex-wrap: wrap;
  margin-left: -15px;
}
.popover-place-chars__item {
  padding-left: 15px;
  padding-bottom: 15px;
  width: 50%;
}
.popover-place-chars__label {
  font-size: 10px;
}
.popover-place-chars__value {
  font-size: 18px;
}
.popover-place-chars__diff {
  font-size: 12px;
  color: rgba(34, 34, 34, 0.46);
}
.popover-place-table {
  list-style-type: none;
  padding: 0;
  margin: 0 0 0 -15px;
}
.popover-place-table__list-item {
  margin-bottom: 7px;
  display: flex;
}
.popover-place-table__label {
  width: 50%;
  padding-left: 15px;
  color: rgba(34, 34, 34, 0.46);
}
.popover-place-table__value {
  width: 50%;
  padding-left: 15px;
}
.popover-place-table__value .online-cells {
  margin-top: 7px;
}
.map-timeline {
  display: flex;
}
.map-timeline__arrow {
  position: relative;
  flex: 0 0 auto;
  width: 40px;
  height: 40px;
  border-width: 0;
  margin: 0 0 0 2px;
  padding: 0;
  background-color: rgba(34, 34, 34, 0);
  transition: color 0.1s, background-color 0.1s;
  vertical-align: top;
  color: #ebebeb;
  display: flex;
  line-height: 1;
}
.map-timeline__arrow:hover:not([disabled]) {
  color: #fff;
  background-color: #000;
}
.map-timeline__arrow:hover:not([disabled]):before {
  background-color: #000;
  top: 0;
  height: 40px;
}
.map-timeline__arrow:hover:not([disabled]) + .map-timeline__arrow:before {
  background-color: #000;
  top: 0;
  height: 40px;
}
.map-timeline__arrow[disabled] {
  color: rgba(235, 235, 235, 0.4);
}
.map-timeline__arrow svg,
.map-timeline__arrow use {
  vertical-align: top;
}
.map-timeline__arrow svg {
  margin: auto;
}
.map-timeline__arrow:before {
  position: absolute;
  content: '';
  left: -2px;
  top: 9px;
  height: 22px;
  width: 2px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 2px;
  transition: background-color 0.1s, height 0.1s, top 0.1s;
}
.map-timeline__arrow:last-child {
  border-top-right-radius: 20px;
  border-bottom-right-radius: 20px;
}
.map-timeline-body {
  position: relative;
  flex: 1 1 auto;
  min-width: 0;
}
.map-timeline-body:first-child .map-timeline-scroll {
  border-top-left-radius: 20px;
  border-bottom-left-radius: 20px;
}
.map-timeline-body.__shd-left:before {
  position: absolute;
  z-index: 3;
  content: '';
  width: 55px;
  top: 0;
  bottom: 0;
  left: 0;
  background-image: linear-gradient(90deg, #222 10%, rgba(34, 34, 34, 0) 100%);
}
.map-timeline-body.__shd-right:after {
  position: absolute;
  z-index: 3;
  content: '';
  width: 55px;
  top: 0;
  bottom: 0;
  right: 0;
  background-image: linear-gradient(-90deg, #222 10%, rgba(34, 34, 34, 0) 100%);
}
.map-timeline-infostatus {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 4;
  padding: 0 5px 0 15px;
  background-color: #222;
  color: rgba(235, 235, 235, 0.4);
  display: flex;
  align-items: center;
}
.map-timeline-infostatus[data-align='left'] {
  border-top-left-radius: 20px;
  border-bottom-left-radius: 20px;
}
.map-timeline-infostatus[data-align='right'] {
  left: auto;
  right: 0;
}
.map-timeline-infostatus[data-align='right']:after {
  position: absolute;
  z-index: 3;
  content: '';
  width: 55px;
  top: 0;
  bottom: 0;
  right: -55px;
  background-image: linear-gradient(-90deg, #222 10%, rgba(34, 34, 34, 0) 100%);
}
.map-timeline-infostatus[data-align='left']:after {
  position: absolute;
  z-index: 3;
  content: '';
  width: 55px;
  top: 0;
  bottom: 0;
  right: -55px;
  background-image: linear-gradient(90deg, #222 10%, rgba(34, 34, 34, 0) 100%);
}
.map-timeline-infostatus .spinner-container {
  opacity: 0.4;
  margin-right: 8px;
  vertical-align: top;
  margin-top: 1px;
  margin-bottom: -1px;
}
.map-timeline-scroll {
  position: relative;
  z-index: 1;
  overflow-x: auto;
  width: 100%;
}
.map-timeline-header {
  position: relative;
  color: #ebebeb;
  font-weight: 500;
  line-height: 40px;
  padding: 0 0 0 20px;
}
.map-timeline-area {
  display: table;
  margin-left: auto;
}
.map-timeline-area[data-align='left'] {
  margin-left: 20px;
}
.map-timeline-area__list-item {
  display: table-cell;
  vertical-align: top;
}
.map-timeline-area__list-item:last-child {
  padding-right: 13px;
}
.map-timeline-area__loader-txt {
  margin-left: 5px;
}
.map-timeline-area__item {
  display: block;
  border-width: 0;
  background-color: rgba(17, 17, 17, 0);
  transition: color 0.1s, background-color 0.1s;
  color: #ebebeb;
  white-space: nowrap;
  line-height: 40px;
  min-height: 40px;
  padding: 0 13px;
  vertical-align: top;
}
.map-timeline-area__item.__selected {
  color: #fff;
  box-shadow: inset 0 -2px 0 var(--color-primary);
  font-weight: 500;
}
.map-timeline-area__item.__selected_cloud {
  color: #fff;
  background-color: #000;
}
.map-timeline-area__item.__selected:hover {
  background-color: rgba(17, 17, 17, 0);
}
.map-timeline-area__item[data-icon-type='cloud'] {
  font-size: 1px;
  line-height: 1;
  padding-top: 3px;
}
.map-timeline-area__item[data-icon-type='cloud']:after {
  content: '';
  display: inline-block;
  margin: 0 auto;
  width: 19px;
  height: 17px;
  background: url('../images/ico-cloud.png') no-repeat 50% / contain;
  vertical-align: top;
}
.map-timeline-area__item:hover {
  color: #fff;
  background-color: #000;
}
.map-container {
  position: absolute;
  z-index: 1;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: hidden;
}

.map-legend {
  position: absolute;
  z-index: 3;
  bottom: 0;
  left: 0;
  right: 0;
  min-height: 40px;
  display: flex;
  align-items: flex-end;
  padding-bottom: 8px;
}
.map-legend:before {
  position: absolute;
  content: '';
  height: 40px;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: linear-gradient(180deg, rgba(34, 34, 34, 0) 0%, #222 100%);
  opacity: 0.8;
}
.map-legend[data-theme='dark-mode'] {
  right: auto;
  left: 5px;
  bottom: 5px;
  padding: 0;
  border-radius: 8px;
  background-color: rgba(34, 34, 34, 0.8);
  padding: 28px 1px 8px;
}
.map-legend[data-theme='dark-mode']:before {
  display: none;
}
.map-legend__item {
  padding: 0 9px;
}
.map-legend__item.__full {
  flex: 1 1 auto;
}
.map-legend-scale {
  position: relative;
  display: flex;
  min-width: 100px;
}
.map-legend-scale.__mw-320 {
  min-width: 320px;
}
.map-legend-scale.__full {
  flex: 1 1 auto;
}
.map-legend-scale.__mw-160 {
  min-width: 160px;
}
.map-legend-scale.__mw-160 .map-legend-scale__item {
  min-width: 10px;
}
.map-legend-scale.__mw-60 {
  min-width: 60px;
}
.map-legend-scale.__mw-60 .map-legend-scale__item {
  min-width: 10px;
}
.map-legend-scale.__mw-300 {
  min-width: 300px;
}
.map-legend-scale.__mxw-440 {
  max-width: 440px;
  width: 100%;
}
.map-legend-scale:before {
  position: absolute;
  content: attr(data-from);
  color: #ebebeb;
  font-size: 10px;
  line-height: 1;
  top: -16px;
  left: 0;
}
.map-legend-scale:after {
  position: absolute;
  content: attr(data-to);
  color: #ebebeb;
  font-size: 10px;
  line-height: 1;
  top: -16px;
  right: 0;
}
.map-legend-scale__item {
  height: 4px;
  flex: 1.5;
  min-width: 18px;
}
.map-legend-scale-values {
  position: absolute;
  color: #ebebeb;
  font-size: 10px;
  line-height: 1;
  top: -16px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
}
.map-with-panels {
  display: flex;
  flex-direction: column;
  min-width: 0;
}
.map-with-panels__map {
  position: relative;
  flex: 0 0 auto;
  min-height: 100%;
  transition: min-height 0.15s;
}
.map-with-panels__content {
  flex: 1 1 auto;
  position: relative;
  overflow-y: auto;
  background-color: #f6f8f9;
}
.map-with-panels-message {
  background-color: #fdf3c4;
  padding: 12px 20px 11px;
}
.map-with-panels-message__ico {
  display: inline-block;
  vertical-align: top;
  font-size: 14px;
  line-height: 1;
  opacity: 0.75;
  margin: 1px 7px -2px 0;
}
.map-with-panels.__opened .map-with-panels__map {
  min-height: 312px;
}
.map-viewer {
  position: absolute;
  z-index: 1;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.map-viewer__inner {
  position: absolute;
  z-index: 1;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.map-viewer iframe {
  vertical-align: top;
}
.map-dashboard {
  position: relative;
  background-color: #fff;
  display: flex;
  min-height: 100%;
}
.map-dashboard__close {
  position: absolute;
  z-index: 3;
  right: 15px;
  top: 15px;
  border-width: 0;
  padding: 0;
  margin: 0;
  background: none;
  width: 13px;
  height: 12px;
  background-image: url("data:image/svg+xml,%3Csvg width='13' height='12' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:avocode='https://avocode.com/'%3E%3Cdefs%3E%3Cpath d='M1263.47413,367.3034c0.20096,-0.20096 0.52677,-0.20096 0.72773,0c0.20096,0.20096 0.20096,0.52677 0,0.72773l-4.97387,4.97387l4.97387,4.97387c0.20096,0.20096 0.20096,0.52677 0,0.72773c-0.20096,0.20096 -0.52677,0.20096 -0.72773,0l-4.97387,-4.97387l-4.97387,4.97387c-0.20096,0.20096 -0.52677,0.20096 -0.72773,0c-0.20096,-0.20096 -0.20096,-0.52677 0,-0.72773l4.97387,-4.97387l-4.97387,-4.97387c-0.20096,-0.20096 -0.20096,-0.52677 0,-0.72773c0.20096,-0.20096 0.52677,-0.20096 0.72773,0l4.97387,4.97387z' id='a'/%3E%3C/defs%3E%3Cg transform='matrix(1,0,0,1,-1252,-367)'%3E%3Cg%3E%3Cuse xlink:href='%23a' fill='%23a5b2bc'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}
.map-dashboard__close:hover {
  background-image: url("data:image/svg+xml,%3Csvg width='13' height='12' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:avocode='https://avocode.com/'%3E%3Cdefs%3E%3Cpath d='M1263.47413,367.3034c0.20096,-0.20096 0.52677,-0.20096 0.72773,0c0.20096,0.20096 0.20096,0.52677 0,0.72773l-4.97387,4.97387l4.97387,4.97387c0.20096,0.20096 0.20096,0.52677 0,0.72773c-0.20096,0.20096 -0.52677,0.20096 -0.72773,0l-4.97387,-4.97387l-4.97387,4.97387c-0.20096,0.20096 -0.52677,0.20096 -0.72773,0c-0.20096,-0.20096 -0.20096,-0.52677 0,-0.72773l4.97387,-4.97387l-4.97387,-4.97387c-0.20096,-0.20096 -0.20096,-0.52677 0,-0.72773c0.20096,-0.20096 0.52677,-0.20096 0.72773,0l4.97387,4.97387z' id='a'/%3E%3C/defs%3E%3Cg transform='matrix(1,0,0,1,-1252,-367)'%3E%3Cg%3E%3Cuse xlink:href='%23a' fill='%23808f9b'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}
.map-dashboard-actions {
  list-style-type: none;
  padding: 0;
  margin: 0;
  font-size: 13px;
}
.map-dashboard-actions__list-item {
  margin-bottom: 11px;
}
.map-dashboard__seeall {
  border: none;
  padding: 0;
  background: none;
  margin: 0;
  font-weight: 500;
  margin-bottom: 10px;
  font-size: 13px;
}
.map-dashboard__seeall:after {
  display: inline-block;
  content: '';
  width: 8px;
  height: 6px;
  margin-left: 3px;
  vertical-align: top;
  margin-top: 4px;
  background-image: url("data:image/svg+xml,%3Csvg width='8' height='6' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' %3E%3Cdefs%3E%3Cpath d='M1154.00871,786.72568l-3.02759,-2.97568l-0.98112,0.9948l4.00871,4.0052l3.82458,-4.0052l-1.02186,-0.9948z' id='a'/%3E%3C/defs%3E%3Cg transform='matrix(1,0,0,1,-1150,-783)'%3E%3Cg%3E%3Cuse xlink:href='%23a' fill='%23222'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}
.map-dashboard-chars {
  width: 100%;
}
.map-dashboard-chars td {
  vertical-align: top;
  padding-bottom: 6px;
  padding-left: 10px;
  font-size: 13px;
}
.map-dashboard-chars tr > td:first-child {
  padding-left: 0;
}
.map-dashboard-chars__date {
  color: #a5b2bc;
  white-space: nowrap;
}
.map-dashboard__hr {
  height: 10px;
  position: relative;
  background-color: #f6f8f9;
}
.map-dashboard__hr:before {
  position: absolute;
  content: '';
  height: 1px;
  border-top: 0.5px solid #d6dce1;
  top: 0;
  left: 0;
  right: 0;
}
.map-dashboard__hr-slim {
  height: 1px;
  border-top: 0.5px solid rgba(165, 178, 188, 0.45);
}
.map-dashboard__main {
  flex: 1 1 auto;
  min-width: 0;
}
.map-dashboard__side {
  position: relative;
  flex: 0 0 auto;
  width: 240px;
}
.map-dashboard__side:before {
  position: absolute;
  content: '';
  width: 1px;
  top: 0;
  left: -1px;
  bottom: 0;
  border-right: 0.5px solid rgba(165, 178, 188, 0.45);
}
.map-dashboard__section {
  padding: 20px 20px 0 20px;
}
.map-dashboard-header {
  display: flex;
  align-items: baseline;
  margin-bottom: 14px;
}
.map-dashboard-header__title {
  font-size: 19px;
  font-weight: 500;
  line-height: 1.12;
  letter-spacing: -0.4px;
  margin-top: 0;
  margin-bottom: 0;
  flex: 1 1 auto;
}
.map-dashboard-header__title small {
  font-size: 13px;
  font-weight: normal;
}
.map-dashboard-header__goto {
  flex: 0 0 auto;
}
.map-dashboard-header__goto:after {
  display: inline-block;
  vertical-align: top;
  margin-left: 2px;
  margin-top: 7px;
  width: 5px;
  height: 8px;
  content: '';
  background-image: url("data:image/svg+xml,%3Csvg width='5' height='8' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cdefs%3E%3Cpath d='M1014.99796,343.70907l0.70703,-0.70703l2.82813,2.83594l0.70703,0.69922l-0.70703,0.70703l-2.82813,2.83593l-0.70703,-0.70703l2.83203,-2.83203z' id='a'/%3E%3C/defs%3E%3Cg transform='matrix(1,0,0,1,-1015,-343)'%3E%3Cg%3E%3Cuse xlink:href='%23a' fill='%2307c'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}
.crop-rotation-details {
  font-size: 13px;
}
.crop-rotation-details h4 {
  margin-top: 0;
  margin-bottom: 2px;
  font-size: 1em;
  font-weight: normal;
}
.crop-rotation-details__list {
  color: #a5b2bc;
  list-style-type: none;
  padding: 0;
  margin: 0;
}
.crop-rotation-details__list-item {
  display: flex;
  padding-bottom: 1px;
}
.crop-rotation-details__title {
  flex: 1 1 auto;
  overflow: hidden;
  text-overflow: ellipsis;
}
.crop-rotation-details__aside {
  flex: 0 0 auto;
  padding-left: 15px;
}
.crop-rotation-details__row-item {
  border-bottom: 0.5px solid rgba(165, 178, 188, 0.45);
  margin-right: -20px;
  padding-right: 20px;
  padding-bottom: 12px;
  margin-bottom: 12px;
}
.crop-rotation-details__row-item:last-child {
  border-bottom-width: 0;
  padding-bottom: 0;
}
.map-dashboard-weather {
  display: flex;
  padding-bottom: 20px;
  min-height: 110px;
}
.map-dashboard-weather.__empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #a5b2bc;
  text-align: center;
}
.map-dashboard-weather.__loader {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #a5b2bc;
  text-align: center;
  flex-direction: column;
}
.map-dashboard-weather.__loader .spinner-container {
  margin-bottom: 10px;
}
.map-dashboard-weather .weather-info-quickmetrics {
  border-left-width: 0;
}
.map-dashboard-weather .weather-info-quickmetrics__inner {
  margin: 1px 0 -12px -15px;
}
.map-dashboard-weather .weather-info-quickmetrics__item {
  width: 105px;
  padding-left: 15px;
  padding-bottom: 12px;
}
.map-dashboard-weather .weather-info-quickmetrics__label {
  position: relative;
  overflow: hidden;
  font-size: 13px;
  line-height: 1.4;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.map-dashboard-weather .weather-info-quickmetrics__value {
  line-height: 1.37;
}
.map-dashboard-weather .weather-info-current {
  flex: 0 0 auto;
  width: 190px;
  padding-right: 15px;
}
.map-dashboard-weather .weather-info-current-metrics {
  align-items: flex-start;
}
.map-dashboard-weather .weather-info-current-metrics__ico {
  margin-top: -3px;
}
.map-dashboard-weather .weather-info-current-metrics__temp {
  font-size: 36px;
  font-weight: 500;
  line-height: 43px;
}
.map-dashboard-weather .weather-info-current-predict {
  margin-top: 7px;
  color: #a5b2bc;
  font-size: 13px;
  line-height: 18px;
  margin-bottom: 0;
}
@supports (display: grid) {
  .map-dashboard-weather .weather-info-quickmetrics__inner {
    display: grid;
    grid-template-rows: auto;
    grid-template-columns: repeat(auto-fit, minmax(105px, 1fr));
    grid-gap: 0;
  }
  .weather-info-quickmetrics__item {
    width: auto;
  }
}
.map-dashboard-notes {
  position: relative;
  z-index: 2;
  margin: 0 -20px;
  padding-bottom: 20px;
  overflow-x: auto;
}
.map-dashboard-notes-wrapper {
  position: relative;
}
.map-dashboard-notes-wrapper.__shadow-right:after {
  position: absolute;
  z-index: 4;
  content: '';
  right: -20px;
  top: 0;
  bottom: 0;
  width: 46px;
  border-right: 20px solid #fff;
  background-image: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    #fff 100%
  );
}
.map-dashboard-notes-wrapper.__shadow-left:before {
  position: absolute;
  z-index: 4;
  content: '';
  left: -20px;
  top: 0;
  bottom: 0;
  width: 46px;
  border-left: 20px solid #fff;
  background-image: linear-gradient(
    -90deg,
    rgba(255, 255, 255, 0) 0%,
    #fff 100%
  );
}
.map-dashboard-notes__area {
  display: flex;
}
.map-dashboard-notes__item {
  position: relative;
  flex: 0 0 auto;
  padding: 0 20px 23px;
  max-width: 300px;
}
.map-dashboard-notes__item:before {
  position: absolute;
  content: '';
  width: 1px;
  top: 0;
  left: -1px;
  bottom: 0;
  border-right: 0.5px solid rgba(165, 178, 188, 0.45);
}
.map-dashboard-notes__item:first-child:before {
  display: none;
}
.map-dashboard-notes__see {
  position: absolute;
  bottom: 1px;
  left: 20px;
  font-size: 13px;
  line-height: 1.32;
}
.map-dashboard-notes .soil-sidebar-gallery__area {
  border-left-width: 0;
  border-right-width: 0;
  overflow: hidden;
}
.map-dashboard-notes .soil-sidebar-gallery {
  margin-bottom: 0;
  margin-left: -5px;
  margin-right: -5px;
  min-width: 260px;
}
.map-dashboard-card {
  padding: 15px 20px 5px;
}
.map-dashboard-card h2 {
  font-size: 19px;
  font-weight: 500;
  line-height: 1.12;
  letter-spacing: -0.4px;
  padding-top: 5px;
  margin-top: 0;
  margin-bottom: 2px;
}
.map-dashboard-card h3 {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.37;
  margin-top: 0;
  margin-bottom: 0;
  padding-bottom: 10px;
}
.map-dashboard-card__code {
  font-size: 13px;
  color: #a5b2bc;
  text-transform: uppercase;
}
.map-dashboard-card__char {
  font-style: italic;
}
.map-dashboard-card__quest {
  display: inline-block;
  color: rgba(165, 178, 188, 0.5);
  margin: 2px 0 -2px 3px;
  vertical-align: top;
  font-size: 14px;
  line-height: 1;
}
.map-dashboard-card__quest svg {
  vertical-align: top;
}
.map-dashboard-card__content {
  margin-bottom: 11px;
}
.map-dashboard-card__content p {
  margin-top: 0;
  margin-bottom: 0;
}
.map-dashboard-card__action {
  margin-bottom: 10px;
}
.map-view-type-control {
  position: absolute;
  z-index: 4;
  width: 226px;
  margin-left: -113px;
  left: 50%;
  top: 5px;
}
.map-view-type-control
  + .map-viewer-grid
  .map-viewer-grid__header[data-align='left'] {
  padding-right: 130px;
}
.map-view-type-control
  + .map-viewer-grid
  .map-viewer-grid__header[data-align='right'] {
  padding-left: 130px;
}
.map-viewer-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  background-color: #d8d8d8;
  display: flex;
  flex-direction: column;
}
.map-viewer-grid .map-legend {
  visibility: hidden;
  opacity: 0;
}
.map-viewer-grid__header {
  position: absolute;
  z-index: 5;
  top: 15px;
  text-shadow: 0 1px 0 rgba(0, 0, 0, 0.52);
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.25rem;
}
.map-viewer-grid__header[data-align='left'] {
  left: 15px;
  right: 15px;
}
.map-viewer-grid__header[data-align='right'] {
  right: 15px;
  left: 15px;
}
.map-viewer-grid__header[data-align='center'] {
  left: 5px;
  right: 5px;
  text-align: center;
}
.map-viewer-grid-hrow {
  display: flex;
}
.map-viewer-grid-cell {
  position: relative;
  z-index: 1;
  overflow: hidden;
}
.map-viewer-grid-cell:hover .map-legend {
  visibility: visible;
  opacity: 1;
}
.map-viewer-grid-subgrid {
  display: flex;
}
.map-viewer-grid-subgrid-cell {
  display: flex;
  flex-direction: column;
}
.map-viewer-grid-vdivider {
  position: relative;
  z-index: 3;
  width: 2px;
  background-color: #222;
  cursor: col-resize;
}
.map-viewer-grid-vdivider__button {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -17px;
  margin-left: -17px;
  width: 34px;
  height: 34px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.25);
  border-radius: 50%;
  background-color: #222;
  border: none;
  cursor: col-resize;
  color: #fff;
}
.map-viewer-grid-vdivider__button:before {
  position: absolute;
  left: 10px;
  top: 50%;
  margin-top: -4px;
  content: '';
  width: 0;
  height: 0;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  border-right: 4px solid currentColor;
}
.map-viewer-grid-vdivider__button:after {
  position: absolute;
  right: 10px;
  top: 50%;
  margin-top: -4px;
  content: '';
  width: 0;
  height: 0;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  border-left: 4px solid currentColor;
}
.map-viewer-grid-hdivider {
  position: relative;
  z-index: 3;
  height: 2px;
  background-color: #222;
  cursor: row-resize;
}
.upload-container {
  position: fixed;
  z-index: 9991;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}
.upload-container:before {
  position: absolute;
  z-index: 1;
  content: '';
  top: 20px;
  left: 20px;
  right: 20px;
  bottom: 20px;
  border: 6px dashed rgba(255, 255, 255, 0.65);
}
.upload-container__outer {
  position: absolute;
  z-index: 3;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}
.upload-container__inner {
  text-align: center;
  width: 100%;
  padding: 30px;
}
.upload-container__inner p {
  margin-top: 0;
  margin-bottom: 0;
}
.upload-container__title {
  font-size: 48px;
  font-weight: 700;
  margin-top: 0;
  margin-bottom: 12px;
  line-height: 1;
}
.loader-inline {
  position: relative;
  overflow: hidden;
  height: 64px;
}
.loader-inline__slide {
  position: absolute;
  top: 0;
  width: 100%;
  text-align: center;
}
.loader-inline__slide img {
  vertical-align: top;
  -webkit-transform-origin: 50%;
  transform-origin: 50%;
  -webkit-transform: scale(0);
  transform: scale(0);
  transition: opacity 1.5s, -webkit-transform 1.5s;
  transition: transform 1.5s, opacity 1.5s;
  transition: transform 1.5s, opacity 1.5s, -webkit-transform 1.5s;
  opacity: 0;
}
.loader-inline__slide--active img {
  vertical-align: top;
  -webkit-transform-origin: 50%;
  transform-origin: 50%;
  -webkit-transform: scale(1);
  transform: scale(1);
  opacity: 1;
}
.onesoil-main-title {
  margin-bottom: 20px;
}
.onesoil-main-title__label {
  color: #a5b2bc;
  font-size: 14px;
  line-height: 20px;
  margin-bottom: 1px;
}
.onesoil-main-title__content {
  display: inline-flex;
}
.onesoil-main-title__title {
  flex: 1 1 auto;
  max-width: 100%;
  min-width: 0;
  white-space: nowrap;
  font-size: 36px;
  font-weight: bold;
  line-height: 40px;
  margin: 0;
}
.onesoil-main-title__percent {
  flex: 0 0 auto;
  margin-left: 8px;
  width: 84px;
}
.main-section {
  position: relative;
  z-index: 2;
  flex: 1 1 auto;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  transition: margin-top 0.15s linear;
}
.main-section--uploader {
  background-color: #fafafb;
}
.main-section--uploader .main-section__header {
  padding-bottom: 21px;
}
.main-section[data-status='upload'] .main-section__body {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.main-section[data-status='upload'] .main-section__body:after {
  content: '';
  height: 53px;
  font-size: 0;
}
.main-section__header {
  padding: 16px 0 17px 25px;
  display: flex;
  flex: 0 0 auto;
}
.main-section__header[data-type='sticky'] {
  border-bottom: 1px solid #e4e7ea;
  padding: 16px 20px 16px 46px;
}

.main-section-actions-cords {
  min-width: 0;
  flex: 0 1 auto;
  white-space: nowrap;
  display: flex;
  margin-right: 10px;
}
.main-section-actions-cords__item {
  margin-right: 10px;
}
.main-section-actions-cords__item:last-child {
  margin-right: 0;
}
.main-section-actions-cords .form-input {
  width: 120px;
}

.main-section__body {
  flex: 1 1 auto;
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style: -ms-autohiding-scrollbar;
  padding: 0 20px;
}
.main-section__body[data-layout='horizontal'] {
  overflow: scroll;
  padding: 0;
  display: flex;
}
.main-section__body[data-layout='horizontal'] .soil-sidebar-create {
  position: relative;
  z-index: 2;
  background-color: #fff;
  flex: 0 0 auto;
  width: 301px;
  border-right: 1px solid rgba(214, 220, 225, 0.5);
  overflow: hidden;
  transition: margin-top 0.15s linear;
  will-change: transform;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  display: flex;
  flex-direction: column;
  left: 0;
}

.edit-action-delete {
  color: #ff3b30;
  border: none;
  padding: 0;
  background: none;
  flex: 0 0 auto;
}
.edit-action-delete__ico {
  display: inline-block;
  vertical-align: top;
  margin-right: 6px;
}
.edit-action-delete__ico svg {
  vertical-align: top;
}
.edit-action-delete:hover {
  color: #a52c2c;
}
.field-edit {
  margin: 0 auto;
  max-width: 957px;
  padding: 5px 0 110px;
}
.field-edit__title {
  font-size: 24px;
  margin-top: 0;
  margin-bottom: 12px;
  font-weight: bold;
  line-height: 1.17;
}
.field-edit-maintitle {
  position: relative;
  cursor: pointer;
  padding: 22px 10px 6px 12px;
  margin: 0 309px 10px -12px;
  border-radius: 4px;
}
.field-edit-maintitle:before {
  position: absolute;
  top: 6px;
  left: 12px;
  white-space: nowrap;
  color: #a5b2bc;
  content: attr(data-placeholder);
}
.field-edit-maintitle:hover {
  background-color: #f4f6f7;
}
.field-edit-maintitle.__edit-field {
  padding-right: 0;
}
.field-edit-maintitle.__edit-field:hover {
  background: none;
}
.field-edit-maintitle__value {
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 36px;
  font-weight: bold;
  line-height: 40px;
  border: none;
  background: none;
  display: block;
  padding: 0;
}
input.field-edit-maintitle__value {
  width: 100%;
  outline: none;
  caret-color: #27ae60;
}
input.field-edit-maintitle__value::-webkit-input-placeholder {
  color: #d2d8dd;
  opacity: 1;
}
input.field-edit-maintitle__value::-ms-input-placeholder {
  color: #d2d8dd;
  opacity: 1;
}
input.field-edit-maintitle__value::placeholder {
  color: #d2d8dd;
  opacity: 1;
}
input.field-edit-maintitle__value:-ms-input-placeholder {
  color: #d2d8dd;
}
input.field-edit-maintitle__value::-ms-input-placeholder {
  color: #d2d8dd;
}
.field-edit-bound {
  display: flex;
  margin-bottom: 25px;
}
.field-edit-bound[data-type='fertilizers'] .field-edit-bound__map {
  order: 1;
  height: 440px;
}
.field-edit-bound[data-type='fertilizers'] .field-edit-bound__content {
  order: 0;
  padding: 10px 30px 10px 0;
  width: 300px;
}
.field-edit-bound__map {
  position: relative;
  z-index: 2;
  overflow: hidden;
  flex: 1 1 auto;
  border-radius: 8px;
  background-color: #d8d8d8;
  height: 268px;
}
.field-edit-bound__map iframe,
.field-edit-bound__map object,
.field-edit-bound__map svg {
  vertical-align: top;
}
.field-edit-bound__map:before {
  position: absolute;
  z-index: 15;
  right: 0;
  bottom: 0;
  content: attr(data-label);
  border-radius: 8px 0 0;
  background-color: rgba(0, 0, 0, 0.5);
  font-size: 16px;
  line-height: 20px;
  font-weight: 500;
  color: #fff;
  padding: 11px 15px;
}
.field-edit-bound__content {
  flex: 0 0 auto;
  width: 309px;
  padding-left: 27px;
  font-size: 16px;
  line-height: 1.45;
  padding-top: 20px;
}
.field-edit-bound__content p {
  margin-top: 0;
  margin-bottom: 10px;
}
.field-edit-bound__content[data-va='middle'] {
  align-self: center;
}
.field-edit-bound__ico {
  font-size: 70px;
  line-height: 1;
  margin-bottom: 5px !important;
}
.table-seasons td {
  font-size: 16px;
  line-height: 1.2;
  height: 62px;
}
.table-seasons td[rowspan] {
  vertical-align: top;
}
.table-seasons__content {
  padding-top: 3px;
}
.table-seasons .btn {
  line-height: 1.2;
  padding-top: 10px;
  padding-bottom: 9px;
  border-radius: 8px;
  min-height: 42px;
}
.table-seasons .btn-add {
  padding-top: 9.5px;
  padding-bottom: 9.5px;
}
.table-seasons .btn-add:hover {
  color: var(--color-primary);
}
.table-seasons .btn-remove:hover {
  color: #a52c2c;
}
.table-seasons__note {
  display: inline-block;
  line-height: 1;
  color: #a5b2bc;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  max-width: 100%;
  text-overflow: ellipsis;
}
.table-seasons .form-select {
  line-height: 1.2;
  min-height: 42px;
  padding-top: 10px;
  padding-bottom: 9px;
  border-radius: 8px;
}
.table-seasons-actions {
  display: flex;
  margin-left: -11px;
}
.table-seasons-actions__item {
  margin-left: 11px;
}
.table-seasons-addnew {
  border-bottom: 0.5px solid rgba(165, 178, 188, 0.45);
  padding: 10px 0;
}
.table-seasons .pd-l-0 {
  padding-left: 0 !important;
}
.table-seasons .pd-r-2 {
  padding-right: 16px;
}
.table-invite th {
  border-bottom: 1px solid #E4E7EA;
  padding: 0 8px 5px 0;
}
.table-invite td {
  border-bottom: 0;
  padding: 8px 8px 8px 0;
}
.main-uploader-header {
  text-align: center;
}
.main-uploader-header__ico {
  margin-bottom: 9px;
}
.main-uploader-header__ico img {
  vertical-align: top;
}
.main-uploader-header__title {
  font-size: 36px;
  font-weight: 700;
  line-height: 1;
  margin-top: 0;
  margin-bottom: 0;
}
.main-uploader-header__description {
  font-size: 16px;
  margin-top: 12px;
  margin-bottom: 0;
}
.main-uploader-header__description .ico-exclamation-mask-os {
  margin-top: 1px;
  vertical-align: top;
}
.main-uploader-ways {
  display: flex;
}
.main-uploader-ways__longway {
  flex: 1 1 auto;
  border-radius: 16px;
  background-color: #f5f7f9;
  min-width: 0;
}
.main-uploader-ways__or {
  align-self: center;
  padding: 0 32px;
  font-size: 36px;
  line-height: 45px;
  font-weight: bold;
}
.main-uploader-ways__shortway {
  flex: 1 1 auto;
  border-radius: 16px;
  background-color: #f5f7f9;
  /* align-self: center; */
}
.main-uploader-ways__shortway .main-uploader-types__md {
  margin-top: 6px;
  margin-bottom: 6px;
}
.main-uploader-ways__desc {
  margin-top: -21px;
  padding: 0 15px 25px;
  text-align: center;
}
.main-uploader-types {
  display: flex;
  padding: 28px 0 29px;
}
.main-uploader-types__ext {
  font-size: 24px;
  line-height: 32px;
  font-weight: bold;
}
.main-uploader-types__desc {
  margin-top: 10px;
}
.main-uploader-types__desc p {
  margin-top: 0;
  margin-bottom: 0;
}
.main-uploader-types__md {
  color: #a5b2bc;
  font-weight: 500;
}
.main-uploader-types-files {
  display: flex;
  align-items: center;
  justify-content: center;
}
.main-uploader-types-files__name {
  min-width: 0;
  flex: 0 1 auto;
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
  color: #27ae60;
}
.main-uploader-types-files__remove {
  flex: 0 0 auto;
  background: none;
  border: none;
  color: #a5b2bc;
  padding: 0;
  margin: 0 0 0 4px;
}
.main-uploader-types-files__remove .ico-close-os {
  width: 9px;
  height: 8px;
}
.main-uploader-types__list-item {
  position: relative;
  flex: 1;
  text-align: center;
  padding: 0 15px;
  min-width: 200px;
}
.main-uploader-types__list-item:before {
  position: absolute;
  background-image: url("data:image/svg+xml,%3Csvg width='14' height='14' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M6.997 10.5c-.506 0-1-.368-1-.86V7.999L4.37 8c-.499 0-.87-.495-.87-1s.371-1 .87-1h1.628V4.36c0-.492.493-.86.999-.86.505 0 1.003.361 1.003.86V6l1.63-.006c.505 0 .87.5.87 1.006 0 .505-.365.992-.87.992L8 7.998V9.64c0 .499-.498.86-1.003.86zm.006 3.5C10.851 14 14 10.853 14 7s-3.149-7-7.003-7C3.149 0 0 3.147 0 7s3.149 7 7.003 7z' fill='%23222'/%3E%3C/svg%3E");
  content: '';
  width: 14px;
  height: 14px;
  left: -7px;
  top: 10px;
}
.main-uploader-types__list-item:first-child:before {
  display: none;
}
.main-uploader-area {
  max-width: 934px;
  height: 248px;
  border-radius: 16px;
  border: 2px dashed rgba(165, 178, 188, 0.4);
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.main-uploader-area.__highlight {
  background-color: #f5f7f9;
}
.main-uploader-area__inner {
  text-align: center;
  padding: 15px 20px;
}
.main-uploader-area__title {
  font-size: 24px;
  font-weight: bold;
  margin-top: 0;
  margin-bottom: 0;
}
.main-uploader-area__description {
  font-size: 14px;
  color: #a5b2bc;
  margin-top: 9px;
}
.main-uploader-file {
  position: relative;
  display: inline-block;
  vertical-align: bottom;
  overflow: hidden;
  color: #007aff;
  cursor: pointer;
}
.main-uploader-file:hover {
  color: #014e86;
}
.main-uploader-file__emulate {
  position: absolute;
  right: 0;
  cursor: pointer;
  font-size: 66px;
  opacity: 0;
}
.main-uploader-file__emulate:hover + .main-uploader-file__label {
  color: #014e86;
}
.main-uploader-list {
  max-width: 934px;
  margin: 22px auto 0;
  padding-bottom: 120px;
}
.main-uploader-item {
  position: relative;
  margin-top: 20px;
  box-shadow: 0 0 12px #ececef;
  border-radius: 16px;
  border: 1px solid rgba(165, 178, 188, 0.25);
  background-color: #fff;
  padding: 25px 25px 16px;
}
.main-uploader-item .form-group {
  margin-bottom: 20px;
}
.main-uploader-item__toggle {
  position: absolute;
  z-index: 3;
  right: 25px;
  top: 25px;
}
.main-uploader-item-actions {
  flex: 0 0 auto;
  display: flex;
}
.main-uploader-item-actions__item {
  margin-left: 10px;
}
.main-uploader-item-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}
.main-uploader-item-header[data-type='slim'] {
  margin-bottom: 9px;
}
.main-uploader-item-header__pic {
  width: 50px;
  height: 50px;
  background-color: rgba(165, 178, 188, 0.15);
  flex: 0 0 auto;
  margin-right: 15px;
  border-radius: 50%;
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
}
.main-uploader-item-header__content {
  flex: 1 1 auto;
  min-width: 0;
}
.main-uploader-item-header__content:last-child {
  margin-right: 60px;
}
.main-uploader-item-header__content[data-hover='enable'] {
  border-radius: 4px;
  padding: 10px 12px 6px;
  margin-top: -10px;
  margin-bottom: -6px;
  margin-left: -12px;
}
.main-uploader-item-header__content[data-hover='enable']:hover {
  background-color: #f4f6f7;
}
.main-uploader-item-header__label {
  color: #a5b2bc;
  font-size: 14px;
  line-height: 1;
}
.main-uploader-item-header__title {
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-top: 0;
  margin-bottom: 0;
  font-size: 24px;
  line-height: 1.2;
  font-weight: bold;
}
.main-uploader-item-header__name {
  font-size: 24px;
  line-height: 1.2;
  font-weight: bold;
  border: none;
  padding: 0;
  width: 100%;
  margin: 0;
  vertical-align: top;
  outline: none;
  caret-color: #27ae60;
  background: none;
}
.main-uploader-item-header__name::-webkit-input-placeholder {
  color: #d2d8dd;
  opacity: 1;
}
.main-uploader-item-header__name::-ms-input-placeholder {
  color: #d2d8dd;
  opacity: 1;
}
.main-uploader-item-header__name::placeholder {
  color: #d2d8dd;
  opacity: 1;
}
.main-uploader-item-header__name:-ms-input-placeholder {
  color: #d2d8dd;
}
.main-uploader-item-header__name::-ms-input-placeholder {
  color: #d2d8dd;
}
.main-uploader-item-chars {
  margin: 0 -25px;
}
.main-uploader-item-chars + .main-uploader-item-subheader {
  margin-top: 10px;
}
.main-uploader-item-chars__table {
  width: 100%;
}
.main-uploader-item-chars th {
  padding: 0 15px 7px 25px;
  text-align: left;
  font-weight: 500;
  white-space: nowrap;
}
.main-uploader-item-chars td {
  border-top: 1px solid #eaeef0;
  padding: 15px 15px 16px 25px;
  font-size: 16px;
  line-height: 1.25rem;
}
.main-uploader-item-chars tr > :last-child {
  padding-right: 25px;
}
.main-uploader-item-chars__name-field {
  border: none;
  padding: 0;
  margin: 0;
  background: none;
  line-height: 1.25rem;
  outline: none;
  width: 100%;
  min-width: 120px;
}
.main-uploader-item-chars__name-field::-webkit-input-placeholder {
  color: #a5b2bc;
}
.main-uploader-item-chars__name-field::-moz-placeholder {
  color: #a5b2bc;
}
.main-uploader-item-chars__name-field:-ms-input-placeholder {
  color: #a5b2bc;
}
.main-uploader-item-chars__name-field:-moz-placeholder {
  color: #a5b2bc;
}
.main-uploader-item-chars__select {
  line-height: 1.25rem;
  border: none;
  padding: 0;
  margin: 0;
  background: none;
}
.main-uploader-item-chars__select:after {
  display: inline-block;
  content: '';
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid currentColor;
  vertical-align: top;
  margin: 8px 0 0 5px;
}
.main-uploader-item-chars__select:hover {
  color: #27ae60;
}
.main-uploader-item-subheader {
  margin-bottom: 17px;
}
.main-uploader-item-subheader[data-mode='slim'] {
  margin-bottom: 10px;
}
.main-uploader-item-subheader__title {
  font-size: 18px;
  font-weight: 700;
  margin-top: 0;
  margin-bottom: 0;
}
.main-uploader-item-subheader p {
  margin-top: 3px;
  margin-bottom: 0;
}
.main-uploader[data-type='compact'] {
  max-width: 934px;
  margin: 0 auto;
  padding: 30px 0 143px;
}
.main-uploader[data-type='compact'] .main-uploader-header {
  text-align: left;
  margin-bottom: 20px;
}
.main-uploader[data-type='compact'] .main-uploader-header__title {
  font-size: 24px;
  line-height: 1.34;
}
.main-uploader[data-type='compact'] .main-uploader-header__description {
  margin-top: 0;
}
.main-uploader[data-type='columns'] {
  width: 482px;
  margin: 0 auto;
  padding: 30px 0 143px;
}
.main-uploader[data-type='columns'] .main-uploader-header {
  text-align: left;
  margin-bottom: 20px;
}
.main-uploader[data-type='columns'] .main-uploader-header__title {
  font-size: 24px;
  line-height: 1.34;
}
.main-uploader[data-type='columns'] .main-uploader-header__description {
  margin-top: 0;
}
.main-uploader-naming-chars {
  list-style-type: none;
  padding: 0;
  margin: -2px 0 -5px;
  color: #a5b2bc;
}
.main-uploader-naming-chars__list-item {
  margin-bottom: 4px;
}
.main-uploader-naming__col {
  width: 220px;
}
.main-uploader-naming__header {
  font-weight: 500;
  display: flex;
  justify-content: space-between;
  padding-bottom: 10px;
}
.main-uploader-naming__row {
  position: relative;
  border-top: 1px solid #e4e7ea;
  padding: 10px 0;
  display: flex;
  justify-content: space-between;
}
.main-uploader-naming__row[data-column='selected'] .main-uploader-naming__arr {
  color: #27ae60;
}
.main-uploader-naming__title {
  position: relative;
  min-height: 34px;
  font-size: 14px;
  border-radius: 6px;
  padding: 7px 0;
  font-weight: normal;
  margin: 0;
  line-height: 1.25rem;
  overflow: hidden;
  text-overflow: ellipsis;
}
.main-uploader-naming__arr {
  position: absolute;
  left: 50%;
  margin-left: -7px;
  margin-top: 10px;
  color: #b7c1c9;
}
.main-uploader-naming[data-type='compact'] .main-uploader-naming__header {
  border-bottom: 1px solid #e4e7ea;
}
.main-uploader-naming[data-type='compact'] .main-uploader-naming__row {
  padding-top: 7px;
  padding-bottom: 8px;
  border-top-width: 0;
}
.fertilizers-content {
  padding: 60px 40px 30px;
  max-width: 724px;
  margin: 0 auto;
}
.list-of-calculations {
  list-style-type: none;
  padding: 0;
  margin: 0 0 40px;
}
.list-of-calculations__list-item {
  margin-bottom: 15px;
}
.list-of-calculations__item {
  display: flex;
  box-shadow: 0 0 6px rgba(236, 236, 239, 0.5);
  border-radius: 12px;
  border: 1px solid rgba(214, 220, 225, 0.5);
  background-color: #fff;
  padding: 14px 14px 14px 19px;
  font-size: 16px;
  color: #a5b2bc;
  align-items: center;
}
.list-of-calculations__item:hover {
  color: #a5b2bc;
  box-shadow: none;
  background-color: #fafafb;
}
.list-of-calculations__title {
  font-size: 1em;
  margin-top: 0;
  margin-bottom: 1px;
  color: #222;
}
.list-of-calculations p {
  margin-top: 0;
  margin-bottom: 0;
}
.list-of-calculations__body {
  flex: 1 1 auto;
}
.list-of-calculations-scale {
  position: relative;
  width: 409px;
  flex: 0 0 auto;
  display: flex;
  min-height: 42px;
}
.list-of-calculations-scale:before {
  position: absolute;
  z-index: 2;
  content: '';
  border: 1px solid rgba(0, 0, 0, 0.08);
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  border-radius: 6px;
}
.list-of-calculations-scale-item {
  flex: 1;
  padding: 0 10px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  color: #fff;
  font-weight: 500;
}
.list-of-calculations-scale-item:first-child {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}
.list-of-calculations-scale-item:last-child {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}
.list-of-calculations-scale-item br {
  display: none;
}
.list-of-calculations-scale-item[data-text-color='black'] {
  color: #222;
}
.list-of-calculations-scale__value {
  line-height: 1;
  padding-bottom: 1px;
}
.list-of-calculations-scale__label {
  font-size: 12px;
  line-height: 1.5;
}
.list-of-calculations .btn {
  background-color: #fff;
}
.articles-enters {
  list-style-type: none;
  padding: 0;
  margin: 0 0 0 -15px;
  display: flex;
  flex-wrap: wrap;
}
.articles-enters__list-item {
  width: 50%;
  display: flex;
  padding: 0 0 15px 15px;
}
.articles-enters__ico {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  height: 150px;
}
.articles-enters__ico img {
  vertical-align: top;
  margin: auto;
}
.articles-enters__item {
  position: relative;
  width: 100%;
  border-radius: 12px;
  background-color: #f2f3f5;
  font-size: 16px;
  line-height: 1.19;
  font-weight: 500;
  text-align: center;
  color: #222;
  padding: 150px 20px 20px;
  min-height: 227px;
}
.articles-enters__item:hover {
  color: #222;
  background-color: #e8eaed;
}
.articles-enters__item:hover:before {
  width: 17px;
  height: 17px;
  right: 13px;
  top: 13px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' viewBox='0 0 17 17'%3E%3Cpath d='M15.99705,1l-7.59705,7.59705' fill='none' stroke='%23a5b2bc' stroke-miterlimit='50' stroke-width='2'/%3E%3Cpath d='M12,1v0h4v0v4v0' fill='none' stroke='%23a5b2bc' stroke-miterlimit='50' stroke-width='2'/%3E%3Cg%3E%3Cpath d='M14,10.00043v0v2.99957c0,1.65685 -1.34315,3 -3,3h-7c-1.65685,0 -3,-1.34315 -3,-3v-7c0,-1.65685 1.34315,-3 3,-3h3v0' fill='none' stroke='%23a5b2bc' stroke-miterlimit='50' stroke-width='2'/%3E%3C/g%3E%3Cg%3E%3Cpath d='M14,10.00043v0v2.99957c0,1.65685 -1.34315,3 -3,3h-7c-1.65685,0 -3,-1.34315 -3,-3v-7c0,-1.65685 1.34315,-3 3,-3h3v0' fill='none' stroke='%23a5b2bc' stroke-miterlimit='50' stroke-width='2'/%3E%3Cpath d='M12,1v0h4v0v4v0' fill='none' stroke='%23a5b2bc' stroke-miterlimit='50' stroke-width='2'/%3E%3Cg%3E%3Cpath d='M15.99705,1l-7.59705,7.59705' fill='none' stroke='%23a5b2bc' stroke-miterlimit='50' stroke-width='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}
.articles-enters__item:before {
  position: absolute;
  content: '';
  right: 15px;
  top: 15px;
  width: 15px;
  height: 15px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' viewBox='0 0 15 15'%3E%3Cpath d='M13.99705,1l-7.59705,7.59705' fill='none' stroke='%23a5b2bc' stroke-miterlimit='50' stroke-width='2'/%3E%3Cpath d='M10,1v0h4v0v4v0' fill='none' stroke='%23a5b2bc' stroke-miterlimit='50' stroke-width='2'/%3E%3Cg%3E%3Cpath d='M14,8.00043v0v2.99957c0,1.65685 -1.34315,3 -3,3h-7c-1.65685,0 -3,-1.34315 -3,-3v-7c0,-1.65685 1.34315,-3 3,-3h3v0' fill='none' stroke='%23a5b2bc' stroke-miterlimit='50' stroke-width='2'/%3E%3C/g%3E%3Cg%3E%3Cpath d='M14,8.00043v0v2.99957c0,1.65685 -1.34315,3 -3,3h-7c-1.65685,0 -3,-1.34315 -3,-3v-7c0,-1.65685 1.34315,-3 3,-3h3v0' fill='none' stroke='%23a5b2bc' stroke-miterlimit='50' stroke-width='2'/%3E%3Cpath d='M10,1v0h4v0v4v0' fill='none' stroke='%23a5b2bc' stroke-miterlimit='50' stroke-width='2'/%3E%3Cg%3E%3Cpath d='M13.99705,1l-7.59705,7.59705' fill='none' stroke='%23a5b2bc' stroke-miterlimit='50' stroke-width='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}
.steps-timeline {
  list-style-type: none;
  padding: 0;
  margin: 0;
  display: flex;
}
.steps-timeline__list-item {
  margin-left: 2px;
  text-align: center;
}
.steps-timeline__list-item:first-child {
  margin-left: 0;
}
.steps-timeline__list-item:first-child .steps-timeline__progress {
  border-bottom-left-radius: 2px;
  border-top-left-radius: 2px;
}
.steps-timeline__list-item:last-child .steps-timeline__progress {
  border-bottom-right-radius: 2px;
  border-top-right-radius: 2px;
}
.steps-timeline__list-item.__checked .steps-timeline__progress {
  background-color: #27ae60;
}
.steps-timeline__list-item.__checked .steps-timeline__label {
  color: rgba(34, 34, 34, 0.5);
}
.steps-timeline__list-item.__checked .steps-timeline__label:before {
  content: '✔️';
  font-size: 8px;
  display: inline-block;
  vertical-align: top;
  margin-top: 5px;
  margin-right: 1px;
}
.steps-timeline__progress {
  width: 66px;
  height: 4px;
  background-color: #a5b2bc;
  margin-bottom: 11px;
}
.sensors-section {
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
}
.sensors-section-header {
  padding: 22px 80px 13px 20px;
  flex: 0 0 auto;
  display: flex;
}
.sensors-section-header h1 {
  margin: 0;
  font-size: 36px;
  line-height: 40px;
  font-weight: bold;
  flex: 0 1 auto;
  min-width: 0;
}
.sensors-section-header__infosensor {
  flex: 0 0 auto;
  line-height: 17px;
  padding-left: 17px;
  padding-top: 2px;
}
.sensors-section-header__infosensor small {
  color: #a5b2bc;
  font-size: 12px;
}
.sensors-section-toolbar {
  padding: 0 0 30px;
  flex: 0 0 auto;
  display: flex;
}
.sensors-section-toolbar__item {
  padding-left: 20px;
}
.sensors-section-toolbar[data-view='popup'] {
  padding-top: 20px;
  padding-bottom: 20px;
}
.sensors-section-toolbar__back {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  border: none;
  background-color: #e9ecef;
  padding: 0;
}
.sensors-section-toolbar__back .ico-arrow-long-left-os {
  vertical-align: top;
  margin-top: 1px;
}
.sensors-section-toolbar__back:hover {
  background-color: #d5dbdf;
}
.sensors-section-body {
  padding: 20px;
  position: relative;
  flex: 1 1 auto;
  overflow-y: auto;
  overflow-x: hidden;
}
.sensors-section-body__fullscreen {
  position: absolute;
  top: 0;
  left: 20px;
  right: 20px;
  bottom: 0;
}
.sensors-viewer {
  display: flex;
  flex-wrap: wrap;
  margin-left: -20px;
}
.sensors-viewer__col {
  width: calc(50% - 20px);
  margin: 0 0 45px 20px;
}
.sensors-viewer__col:hover .sensors-viewer-openfull {
  visibility: visible;
}
.sensors-viewer__col:hover .sensors-viewer-chars {
  opacity: 0;
}
.sensors-viewer-header {
  position: relative;
  display: flex;
  margin-bottom: 7px;
}
.sensors-viewer-header h2 {
  flex: 1 1 auto;
  font-size: 20px;
  font-weight: 500;
  margin-top: 0;
  margin-bottom: 0;
}
.sensors-viewer-openfull {
  position: absolute;
  z-index: 3;
  right: 0;
  top: 5px;
  border: none;
  background: none;
  color: #007aff;
  font-weight: 500;
  visibility: hidden;
}
.sensors-viewer-openfull:hover {
  color: #014e86;
}
.sensors-viewer-openfull .ico-fullscreen-os {
  vertical-align: top;
  margin-top: -1px;
  margin-right: 2px;
}
.sensors-viewer-chars {
  display: flex;
  align-items: flex-end;
}
.sensors-viewer-chars__item {
  padding-left: 10px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
.sensors-viewer-chars__value {
  font-size: 20px;
  font-weight: bold;
}
.sensors-viewer-chars__label {
  color: #aeb9c2;
  font-size: 12px;
  line-height: 16px;
  margin-top: -16px;
}
.onesoil-hello-page {
  background-color: #fafafb;
}
.onesoil-hello-page .page-container {
  align-items: center;
  justify-content: space-between;
  flex-direction: column;
  display: flex;
  padding: 0 10px 15px;
  height: auto;
  min-height: 100%;
}
.onesoil-hello-page .page-container:before {
  content: '';
  height: 56px;
}
.onesoil-hello {
  box-shadow: 0 0 12px #ececef;
  border-radius: 10px;
  border: 1px solid rgba(165, 178, 188, 0.25);
  background-color: #fff;
  max-width: 320px;
  width: 100%;
  padding: 20px;
  flex: 0 1 auto;
}
.onesoil-hello__header {
  text-align: center;
  margin: 0 auto 25px;
}
.onesoil-hello__header .logo {
  margin: 0 auto;
  width: 92px;
}
.onesoil-hello-backto {
  margin-top: -10px;
  margin-bottom: -5px;
}
.onesoil-hello__description {
  text-align: center;
  max-width: 230px;
  margin: -11px auto 15px;
}
.onesoil-hello-rules {
  position: relative;
}
.onesoil-hello-rules:after {
  position: absolute;
  z-index: 2;
  content: '';
  bottom: -5px;
  left: 0;
  right: 0;
  height: 56px;
  background-image: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0) 0%,
    #fff 70%
  );
  transition: opacity 0.3s, visibility 0.3s;
}
.onesoil-hello-rules__content {
  position: relative;
  z-index: 1;
  overflow: hidden;
  max-height: 72px;
  padding-top: 2px;
  padding-bottom: 12px;
  transition: max-height 0.3s;
}
.onesoil-hello-rules__showmore {
  position: absolute;
  z-index: 3;
  left: 0;
  right: 0;
  bottom: -5px;
  border: none;
  margin: 0;
  background: none;
  color: #007aff;
  font-size: 12px;
  line-height: 14px;
  padding: 0;
}
.onesoil-hello-rules__showmore:after {
  display: inline-block;
  content: '';
  border-left: 3px solid transparent;
  border-right: 3px solid transparent;
  border-top: 3px solid currentColor;
  vertical-align: top;
  margin: 6px 0 0 2px;
  transition: -webkit-transform 0.2s;
  transition: transform 0.2s;
  transition: transform 0.2s, -webkit-transform 0.2s;
}
.onesoil-hello-rules__showmore:hover {
  color: #014e86;
}
.onesoil-hello-rules .form-checkbox {
  font-size: 14px;
}
.onesoil-hello-rules.__active .onesoil-hello-rules__content {
  max-height: 999px;
  transition: max-height 0.5s;
}
.onesoil-hello-rules.__active:after {
  opacity: 0;
  visibility: hidden;
}
.onesoil-hello-rules.__active .onesoil-hello-rules__showmore:after {
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
}
.onesoil-hello-footer {
  width: 100%;
  display: flex;
  padding-top: 17px;
  font-size: 12px;
  line-height: 14px;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  color: #a5b2bc;
}
.onesoil-hello-footer__item {
  padding: 10px 15px 0;
}
.onesoil-hello-footer a {
  color: currentColor;
}
.onesoil-hello-footer a:hover {
  color: #8798a5;
}
.onesoil-hello-footer a[data-arrow='show']:after {
  display: inline-block;
  content: '';
  border-left: 3px solid transparent;
  border-right: 3px solid transparent;
  border-top: 3px solid currentColor;
  vertical-align: top;
  margin: 6px 0 0 2px;
  transition: -webkit-transform 0.2s;
  transition: transform 0.2s;
  transition: transform 0.2s, -webkit-transform 0.2s;
}
.onesoil-hello-footer a[data-arrow='show'].__active:after {
  -webkit-transform: rotate(180deg) translate(0, 1px);
  transform: rotate(180deg) translate(0, 1px);
}
.onesoil-hello-divider {
  position: relative;
  margin: 0 -20px 15px;
  text-align: center;
}
.onesoil-hello-divider:before {
  position: absolute;
  content: '';
  height: 1px;
  top: 50%;
  background-color: rgba(165, 178, 188, 0.25);
  left: 0;
  right: 0;
}
.onesoil-hello-divider__value {
  position: relative;
  z-index: 3;
  background-color: #fff;
  padding: 0 6px;
  color: #a5b2bc;
}
.onesoil-hello-socials {
  display: flex;
  margin-left: -10px;
  margin-bottom: -10px;
  flex-wrap: wrap;
}
.onesoil-hello-socials__item {
  width: 50%;
  padding-left: 10px;
  margin-bottom: 10px;
}
.onesoil-hello-socials .btn {
  position: relative;
  padding-left: 42px;
  padding-right: 4px;
}
.onesoil-hello-socials .btn:before {
  position: absolute;
  content: '';
  width: 34px;
  height: 34px;
  border-radius: 6px;
  background-color: #fff;
  top: 4px;
  left: 4px;
}
.onesoil-hello-socials .btn[data-social='google'] {
  color: #fff;
  background-color: #4285f4;
}
.onesoil-hello-socials .btn[data-social='google']:after {
  position: absolute;
  z-index: 2;
  content: '';
  top: 12px;
  left: 12px;
  width: 18px;
  height: 18px;
  background-image: url("data:image/svg+xml,%3Csvg width='18' height='18' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M17.82422,7.23691h-0.72422v0v-0.03691v0h-8.1v0v3.6v0h5.08711c-0.35684,1.00723 -1.00547,1.87383 -1.84043,2.50664c0.00176,0 0.00176,0 0.00176,0v0l2.78613,2.35723c-0.19687,0.17754 2.96543,-2.16387 2.96543,-6.66387c0,-0.60293 -0.06152,-1.1918 -0.17578,-1.76309z' fill='%231976d2'/%3E%3Cpath d='M9.00211,17.99539c2.32383,0 4.43672,-0.88945 6.03457,-2.33613l-2.78613,-2.35723c-0.90352,0.68379 -2.02676,1.09336 -3.24844,1.09336c-2.34141,0 -4.32773,-1.49238 -5.07656,-3.57539l-2.93555,2.26055c1.48887,2.91445 4.51406,4.91484 8.01211,4.91484z' fill='%234caf50'/%3E%3Cpath d='M1.04,4.81113l2.9584,2.16914c0.7998,-1.98105 2.73691,-3.38027 5.00449,-3.38027c1.37637,0 2.62969,0.51855 3.58242,1.36758l2.54532,-2.54531c-1.60665,-1.49766 -3.75645,-2.42227 -6.12774,-2.42227c-3.45762,0 -6.45469,1.95117 -7.96289,4.81113z' fill='%23ff3d00'/%3E%3Cg%3E%3Cpath d='M17.82422,7.23691h-0.72422v0v-0.03691v0h-8.1v0v3.6v0h5.08711c-0.74356,2.09531 -2.73691,3.6 -5.08711,3.6c-2.98301,0 -5.4,-2.41699 -5.4,-5.4c0,-2.98301 2.41699,-5.4 5.4,-5.4c1.37637,0 2.62969,0.51855 3.58242,1.36758l2.54531,-2.54531c-1.60664,-1.49766 -3.75644,-2.42227 -6.12773,-2.42227c-4.97109,0 -9,4.02891 -9,9c0,4.97109 4.02891,9 9,9c4.97109,0 9,-4.02891 9,-9c0,-0.60293 -0.06152,-1.1918 -0.17578,-1.76309z' fill='%23ffc107'/%3E%3C/g%3E%3Cg%3E%3Cpath d='M17.82422,7.23691h-0.72422v0v-0.03691v0h-8.1v0v3.6v0h5.08711c-0.74356,2.09531 -2.73691,3.6 -5.08711,3.6c-2.98301,0 -5.4,-2.41699 -5.4,-5.4c0,-2.98301 2.41699,-5.4 5.4,-5.4c1.37637,0 2.62969,0.51855 3.58242,1.36758l2.54531,-2.54531c-1.60664,-1.49766 -3.75644,-2.42227 -6.12773,-2.42227c-4.97109,0 -9,4.02891 -9,9c0,4.97109 4.02891,9 9,9c4.97109,0 9,-4.02891 9,-9c0,-0.60293 -0.06152,-1.1918 -0.17578,-1.76309z' fill='%23ffc107'/%3E%3Cpath d='M1.04,4.81113l2.9584,2.16914c0.7998,-1.98105 2.73691,-3.38027 5.00449,-3.38027c1.37637,0 2.62969,0.51855 3.58242,1.36758l2.54532,-2.54531c-1.60665,-1.49766 -3.75645,-2.42227 -6.12774,-2.42227c-3.45762,0 -6.45469,1.95117 -7.96289,4.81113z' fill='%23ff3d00'/%3E%3Cg%3E%3Cpath d='M9.00211,17.99539c2.32383,0 4.43672,-0.88945 6.03457,-2.33613l-2.78613,-2.35723c-0.90352,0.68379 -2.02676,1.09336 -3.24844,1.09336c-2.34141,0 -4.32773,-1.49238 -5.07656,-3.57539l-2.93555,2.26055c1.48887,2.91445 4.51406,4.91484 8.01211,4.91484z' fill='%234caf50'/%3E%3C/g%3E%3Cg%3E%3Cpath d='M17.82422,7.23691h-0.72422v0v-0.03691v0h-8.1v0v3.6v0h5.08711c-0.35684,1.00723 -1.00547,1.87383 -1.84043,2.50664c0.00176,0 0.00176,0 0.00176,0v0l2.78613,2.35723c-0.19687,0.17754 2.96543,-2.16387 2.96543,-6.66387c0,-0.60293 -0.06152,-1.1918 -0.17578,-1.76309z' fill='%231976d2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}
.onesoil-hello-socials .btn[data-social='google']:hover {
  background-color: #1669f1;
}
.onesoil-hello-socials .btn[data-social='facebook'] {
  color: #fff;
  background-color: #4267b2;
}
.onesoil-hello-socials .btn[data-social='facebook']:after {
  position: absolute;
  z-index: 2;
  content: '';
  top: 12px;
  left: 16px;
  width: 10px;
  height: 18px;
  background-image: url("data:image/svg+xml,%3Csvg width='10' height='18' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M2.7,18v0v-9v0h-2.7v0v-2.7v0h2.7v0v-2.7c0,-2.25 0.9,-3.6 3.6,-3.6h2.7v0v2.7v0h-1.1707c-0.8086,0 -1.5293,0.7207 -1.5293,1.5293v2.0707v0h3.6v0l-0.45,2.7v0h-3.15v0v9v0z' fill='%234267b2'/%3E%3C/svg%3E");
}
.onesoil-hello-socials .btn[data-social='facebook']:hover {
  background-color: #385897;
}
.onesoil-hello-socials .btn[data-social='apple'] {
  color: #fff;
  background-color: #222;
  padding-left: 4px;
}
.onesoil-hello-socials .btn[data-social='apple']:before {
  display: none;
}
.onesoil-hello-socials .btn[data-social='apple']:hover {
  background-color: #1d1d1d;
}
.hide {
  display: none !important;
}
.image-overlay-layer {
  user-select: none;
}
.text-muted {
  color: #afafaf;
}
.text-right {
  text-align: right;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center !important;
}
.fl-r {
  float: right;
}
.fl-l {
  float: right;
}
.nowrap {
  white-space: nowrap;
}
.text-wrap {
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
  -webkit-hyphens: auto;
  -ms-hyphens: auto;
  hyphens: auto;
}
.h-ellipsis {
  display: inline-block;
  max-width: 100%;
  position: relative;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.text-nowrap {
  white-space: nowrap;
}
.text-link {
  cursor: pointer;
  color: #007aff;
}
.text-link:hover {
  color: #014e86;
}
.c-error {
  color: #a52c2c;
}
.c-success {
  color: var(--color-primary);
}
.c-danger {
  color: #e00;
}
.c-exclamation {
  color: #ff3b30;
}
.c-neutral {
  color: #a5b2bc;
}
.size-full {
  width: 100%;
}
.size-half {
  width: 50%;
}
.size-quarter {
  width: 25%;
}
.size-short {
  width: 95px;
}
.size-normal {
  width: 110px;
}
.size-medium {
  width: 210px;
}
.size-xnormal {
  width: 150px;
}
.size-sx {
  width: 75px;
}
.shake-error {
  -webkit-animation: shake 0.82s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
  animation: shake 0.82s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}
.mb-0 {
  margin-bottom: 0 !important;
}
.mt-0 {
  margin-top: 0 !important;
}
.mr-0 {
  margin-right: 0 !important;
}
.ml-0 {
  margin-left: 0 !important;
}
@-webkit-keyframes shake {
  10%,
  90% {
    -webkit-transform: translate3d(-1px, 0, 0);
    transform: translate3d(-1px, 0, 0);
  }
  20%,
  80% {
    -webkit-transform: translate3d(2px, 0, 0);
    transform: translate3d(2px, 0, 0);
  }
  30%,
  50%,
  70% {
    -webkit-transform: translate3d(-4px, 0, 0);
    transform: translate3d(-4px, 0, 0);
  }
  40%,
  60% {
    -webkit-transform: translate3d(4px, 0, 0);
    transform: translate3d(4px, 0, 0);
  }
}
@keyframes shake {
  10%,
  90% {
    -webkit-transform: translate3d(-1px, 0, 0);
    transform: translate3d(-1px, 0, 0);
  }
  20%,
  80% {
    -webkit-transform: translate3d(2px, 0, 0);
    transform: translate3d(2px, 0, 0);
  }
  30%,
  50%,
  70% {
    -webkit-transform: translate3d(-4px, 0, 0);
    transform: translate3d(-4px, 0, 0);
  }
  40%,
  60% {
    -webkit-transform: translate3d(4px, 0, 0);
    transform: translate3d(4px, 0, 0);
  }
}
@media screen and (min-height: 600px) {
  .app-closed-visual {
    max-height: 625px;
  }
}
@media screen and (min-width: 900px) {
  .app-closed {
    padding-left: 430px;
    align-items: center;
    justify-content: center;
  }
  .app-closed__inner {
    padding: 60px 0;
    width: 510px;
    text-align: left;
    font-size: 24px;
    line-height: 31px;
  }
  .app-closed__title {
    font-size: 64px;
    line-height: 70px;
    margin-bottom: 19px;
  }
  .app-closed-nav {
    position: absolute;
    bottom: 0;
  }
  .app-closed-nav__list {
    font-size: 16px;
    justify-content: flex-start;
    width: 510px;
    padding-bottom: 23px;
  }
  .app-closed-nav__list-item {
    margin-left: 0;
    margin-right: 36px;
  }
  .app-closed-visual {
    max-height: none;
    width: 100%;
  }
  .app-closed-visual__inner {
    height: auto;
    top: 0;
  }
  .app-closed-visual__field:after {
    top: 0;
    left: 0;
    height: 670px;
    width: 650px;
    background-image: repeating-linear-gradient(
      -45deg,
      transparent,
      transparent 22px,
      #27ae60 22px,
      #27ae60 33px
    );
  }
  .app-closed-visual__field:before {
    top: 800px;
    width: 1400px;
  }
  .app-closed-visual-car {
    top: 210px;
    left: 74px;
  }
  .app-closed-visual-car__el {
    width: 294px;
    height: 255px;
  }
  .app-closed-visual-car__el:before {
    width: 294px;
    height: 255px;
  }
  .app-closed-visual-car__el:after {
    width: 67px;
    height: 30px;
    left: -15px;
  }
  .app-closed-visual-car__bg {
    left: -15px;
    height: 255px;
  }
}
@media screen and (max-width: 767px) {
  .onesoil-hello-page .page-container {
    padding: 10px 10px 15px;
  }
  .onesoil-hello-page .page-container:before {
    height: 1px;
  }
  .onesoil-hello-footer {
    padding-top: 0;
  }
}

.fit-content {
  width: fit-content;
}
