import toasts from 'modules/toasts';

import {
  BULK_UPLOAD_TOAST_KEY,
  BULK_UPLOAD_PAGE_TOAST_KEY,
} from 'features/fields/BulkUpload/constants';
import { successToastIcon } from 'features/fields/BulkUpload/styles';
import { ToastProps } from 'features/fields/BulkUpload/types';
import { isBulkUploadPage } from 'features/fields/BulkUpload/utils';

export const getMessageForSuccess = ({
  titleKey,
  contentKey,
  linkKey,
  ttl,
  showOnBulkUploadPage,
}: ToastProps) => {
  if (showOnBulkUploadPage || !isBulkUploadPage()) {
    const viewport = window.location.pathname.split('/')[1];

    const group = showOnBulkUploadPage
      ? BULK_UPLOAD_PAGE_TOAST_KEY
      : BULK_UPLOAD_TOAST_KEY;

    return toasts.actions.push(group, titleKey, {
      getLabel: ({ t, label }) => t(label) as string,
      content: contentKey,
      ttl,
      link: linkKey
        ? {
            text: linkKey,
            to: `/${viewport}/fields/add/upload`,
          }
        : undefined,
      iconClassName: successToastIcon,
      position: 'bottom',
      icon: 'check',
    });
  }
};
