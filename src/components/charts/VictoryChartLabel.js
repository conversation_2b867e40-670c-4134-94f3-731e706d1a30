import React from 'react';
import { Line, Circle } from 'victory';

import ForeignObject from './ForeignObject';

const VictoryChartLabel = ({
  x,
  y,
  height,
  label,
  color,
  style,
  popoverProps,
}) => (
  <>
    <Line
      x1={x}
      x2={x}
      y1={y}
      y2={y + style.padding}
      style={{ stroke: color }}
    />
    <Circle cx={x} cy={y + style.padding} r={3} style={{ fill: color }} />
    <ForeignObject x={x} y={y} align='middle-top'>
      <div className='chart-label' {...popoverProps}>
        <span style={{ color }}>{label}</span>
      </div>
    </ForeignObject>
  </>
);

export default VictoryChartLabel;
