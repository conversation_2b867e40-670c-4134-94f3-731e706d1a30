import invert from 'lodash/invert';
import mapValues from 'lodash/mapValues';

export const VRATypes = ['planting', 'fertilizing', 'spraying'];

export const ZoneNamesByIndex = {
  [-3]: 'm3',
  [-2]: 'm2',
  [-1]: 'm1',
  0: 'p0',
  1: 'p1',
  2: 'p2',
  3: 'p3',
};

export const ZoneIndexesByName = mapValues(invert(ZoneNamesByIndex), item =>
  Number(item),
);

export const FertilizerTypes = [
  'desiccants',
  'fungicides',
  'growth_regulators',
  'herbicides',
  'insecticides',
  'nitrogen_stabilizer',
];

// It is important to keep number of items the same in these arrays
// This is required to allow automatically converting them to different unit system

export const RateUnitsByType = {
  planting: {
    metric: ['seeds/ha', 'kg/ha'],
    imperial: ['seeds/ac', 'lb/ac'],
    hybrid: ['seeds/ac', 'lb/ac'],
  },
  fertilizing: {
    metric: ['kg/ha', 'l/ha'],
    imperial: ['lb/ac', 'gal/ac'],
    hybrid: ['lb/ac', 'gal/ac'],
  },
  spraying: {
    metric: ['kg/ha', 'l/ha'],
    imperial: ['lb/ac', 'gal/ac'],
    hybrid: ['lb/ac', 'gal/ac'],
  },
};

export const VRAZoneColors = {
  3: ['#ffd0ff', '#ce79c9', '#9c0091'],
  5: ['#ffd0ff', '#e6a5e4', '#ce79c9', '#b54bad', '#9c0091'],
  7: [
    '#ffd0ff',
    '#eeb3ed',
    '#de96db',
    '#ce79c9',
    '#bd5bb6',
    '#ad3aa4',
    '#9c0091',
  ],
};
