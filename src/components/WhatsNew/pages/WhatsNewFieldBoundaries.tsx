import React, { VFC } from 'react';
import { Trans } from 'react-i18next';
import {
  StyledPage,
  styles,
} from 'components/WhatsNew/WhatsNewContent/WhatsNewContent.style';
import Button from 'components/ui/Button/Button';

import WhatsNewImg from 'assets/images/whatsnew/whatsnew_field-boundaries.gif';
import { WhatsNewProps } from '../types';
import { FeaturesKeyEnum } from '../consts';

const FieldBoundaries: VFC<WhatsNewProps> = ({ style, onClick, isLast, t }) => (
  <StyledPage style={{ ...style }}>
    <div className={styles.semiPage}>
      <div className={styles.pageContent}>
        <div className={styles.textContent}>
          <h2 className={styles.title}>
            {t('whatsnew.fieldboundaries.title')}
          </h2>
          <p className={styles.text}>
            <Trans>{t('whatsnew.fieldboundaries.text')}</Trans>
          </p>
        </div>
        <Button className='btn btn-success fit-content' onClick={onClick}>
          {t(`whatsnew.${isLast ? 'end' : 'next'}.action_text`)}
        </Button>
      </div>
    </div>
    <div className={styles.semiPage}>
      <picture
        className={styles.pagePictureCentered}
        style={{ paddingLeft: '12px' }}
      >
        <img
          src={WhatsNewImg}
          alt={t('whatsnew.fieldboundaries.title') as string}
        />
      </picture>
    </div>
  </StyledPage>
);

const WhatsNewFieldBoundaries = {
  key: FeaturesKeyEnum.fieldBoundaries,
  component: FieldBoundaries,
  filter: {},
};

export default WhatsNewFieldBoundaries;
