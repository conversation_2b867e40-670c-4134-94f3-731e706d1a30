import React, { useEffect } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';
import {
  Form,
  Control,
  getField,
  actions as formActions,
} from 'react-redux-form';
import cx from 'classnames';

import Button from 'components/ui/Button/Button';
import Toggle from 'components/Toggle';
import Saga from 'components/saga/Saga';
import BodyClassName from 'components/BodyClassName';
import ClearableInput from 'components/ClearableInput';
import SubmitButton from 'components/forms/SubmitButton';
import AcceptTermsSection from './AcceptTermsSection';
import SocialLoginButtons from './SocialLoginButtons/SocialLoginButtons';
import Icon from 'components/Icon';
import Logo from 'components/Logo';

import { INVITATION_TYPES } from 'features/multiaccount/constants';

import authLoginSaga from 'sagas/local/auth-login';
import { parseQuery } from 'utils/query';
import { required } from 'utils/validators';
import { useTranslate } from 'utils/use-translate';

import forms from 'constants/forms';
import auth from 'modules/auth';
import toasts from 'modules/toasts';

import logEvent from 'sagas/global/logEvent';

import config from 'config';

const AuthorizePage = ({
  match: { params },
  disableEmail,
  submitFailed,
  expandTerms,
  hideHeader = false,
  partner,
  history,
  location,
  pendingProvider,
  onAuthorize,
  onSocialLogin,
  onResetErrors,
  onHideToast,
  authLoginSagaProps,
}) => {
  useEffect(() => {
    if (config.whiteLabel?.redirectUrl) {
      // Redirect for whiteLabel sites
      window.location.href = config.whiteLabel.redirectUrl;
    }
  }, []);

  const { t } = useTranslate();
  let partnerType = partner === 'welcome' ? undefined : partner;
  // dont send invitation info as partner
  if (Object.values(INVITATION_TYPES).includes(partner)) {
    partnerType = undefined;
  }
  if (config.whiteLabel?.whiteLabel) {
    partnerType = config.whiteLabel.whiteLabel;
  }

  return (
    <div className={cx('onesoil-hello', { 'shake-error': submitFailed })}>
      {!partner && <BodyClassName className='onesoil-hello-page' />}
      <Saga
        saga={authLoginSaga}
        {...authLoginSagaProps}
        {...parseQuery(location)}
        history={history}
      />

      {!hideHeader && (
        <div className='onesoil-hello__header'>
          <Logo href={partner === 'welcome' ? 'welcome' : '/'} />
        </div>
      )}

      <Form
        className='onesoil-hello-form'
        model={forms.login}
        validators={{
          accept_terms: {
            checked: accepted => params.type !== 'signup' || accepted,
          },
        }}
        onSubmit={form => {
          const query = parseQuery(location);
          const formParams = {
            ...form,
            login: form.login.trim(),
            password: form.password.trim(),
          };
          onAuthorize(params.type, formParams, false, partnerType, query.d);
          onHideToast();

          if (
            ['accept-workspace-invite', 'accept-client-invite'].includes(
              partner,
            )
          ) {
            logEvent(
              partner === 'accept-workspace-invite'
                ? 'workspace_open_invitation_web'
                : 'workspace_dealer_clients_open',
              {
                platform: 'web',
              },
            );
          } else if (partner === 'accept-consultant-invite') {
            logEvent('consultant_open_invitation', {
              platform: 'web',
            });
          }
        }}
      >
        {(!config.whiteLabel?.hideSignup || !!partner) && (
          <div className='form-group'>
            <Toggle
              value={params.type}
              titleKey='label'
              options={['login', 'signup'].map(id => ({
                label: t(`auth.type.${id}`),
                id,
              }))}
              onChange={type => {
                history.replace({
                  pathname: `/auth/${type}${partner ? `/${partner}` : ''}`,
                  search: location.search,
                });
                onResetErrors();
                onHideToast();
              }}
            />
          </div>
        )}

        <div className='form-group'>
          <div className='form-label'>{t('auth.email.label')}</div>
          <Control.text
            tabIndex={1}
            model='.login'
            component={ClearableInput}
            validators={{ required }}
            placeholder={t('auth.email.placeholder')}
            disabled={disableEmail}
          />
        </div>
        <div className='form-group'>
          <div className='form-label'>
            {params.type === 'login' && (
              <div className='form-label__side'>
                <Link
                  to={{ pathname: '/auth/forgot', search: location.search }}
                  onClick={onResetErrors}
                >
                  {t('auth.forgot')}
                </Link>
              </div>
            )}
            {t(`auth.password.label.${params.type}`)}
          </div>
          <Control.password
            tabIndex={1}
            model='.password'
            className='form-input size-full'
            validators={{ required }}
            placeholder={t('auth.password.placeholder')}
          />
        </div>
        {params.type === 'signup' && (
          <div className='form-group'>
            <AcceptTermsSection forceExpand={expandTerms} />
          </div>
        )}
        <div className='form-group'>
          <SubmitButton
            native
            model={forms.login}
            render={({ ...otherProps }) => (
              <Button
                className='btn btn-success btn-lg size-full'
                tabIndex={1}
                type='submit'
                {...otherProps}
              >
                {t(`auth.confirm.${params.type}`)}
              </Button>
            )}
          />
        </div>
        {!config.whiteLabel?.hideSignup &&
          !config.whiteLabel?.hideSocialNetworks && (
            <>
              <div className='onesoil-hello-divider'>
                <span className='onesoil-hello-divider__value'>
                  {t('auth.social_separator')}
                </span>
              </div>
              <div className='onesoil-hello-socials'>
                <SocialLoginButtons
                  location={location}
                  partnerType={partnerType}
                  pendingProvider={pendingProvider}
                  type={params.type}
                  onSocialLogin={onSocialLogin}
                />
                <div className='onesoil-hello-socials__item size-full'>
                  <Button
                    className='btn size-full'
                    data-social='apple'
                    onClick={async () => {
                      const { AppleID } = window;
                      if (!AppleID) {
                        return;
                      }
                      try {
                        const query = parseQuery(location);
                        const { authorization } = await AppleID.auth.signIn();
                        const { code, id_token } = authorization;
                        onSocialLogin(
                          'apple',
                          { token: id_token, code },
                          partnerType,
                          query.d,
                        );
                      } catch (error) {
                        alert(t('auth.social.error'));
                      }
                    }}
                  >
                    <Icon className='ico-apple-utf' name='apple-utf' />
                    {t(`auth.${params.type}.apple`)}
                  </Button>
                </div>
              </div>
            </>
          )}
      </Form>
    </div>
  );
};

const mapStateToProps = state => ({
  submitFailed: getField(state, forms.login).submitFailed,
  pendingProvider: auth.selectors.getPendingProvider(state),
});

const mapDispatchToProps = {
  onAuthorize: auth.actions.authorize,
  onSocialLogin: auth.actions.socialLogin,
  onResetErrors: () => formActions.resetValidity(forms.login),
  onHideToast: () => toasts.actions.removeGroup('auth-error'),
};

export default connect(mapStateToProps, mapDispatchToProps)(AuthorizePage);
