import React, { useCallback, useMemo, useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useForm } from 'react-hook-form';
import Adjust from '@adjustcom/adjust-web-sdk';

import entities from 'modules/entities';
import settings from 'modules/settings';

import { logClevertapEvent } from 'sagas/global/integrations';

import { getViewportHash } from 'utils/get-root-scouting-url';
import { CropSource } from 'constants/crops';
import { useTranslate } from 'utils/use-translate';
import { getInitialYieldUnit } from 'utils/units';

import EnterExitTransition from 'components/EnterExitTransition';
import RemoveEntityPopup from './RemoveEntityPopup';
import EditFieldCropRow from './EditFieldCropRow';
import Button from 'components/ui/Button/Button';
import Icon from 'components/Icon';

import { styles } from './CropRotationView/CropRotationView.style';
import { FEATURES, FeatureDisabledTooltip } from 'features/permissions';
import { PopoverDeprecatedAlign } from 'components/ui/Popover';

const CropDetailsPopup = ({
  fieldSeasonId,
  cropIndex,
  match,
  history,
  location,
  noDelete,
  logEvent,
  onClose,
}) => {
  const [showRemoveCropPopup, setShowRemoveCropPopup] = useState(false);

  const { t } = useTranslate();
  const dispatch = useDispatch();

  const saveRequest = useSelector(state =>
    entities.selectors.getRequestStatus(state, 'save-field-crops'),
  );

  const fieldSeason = useSelector(state =>
    entities.selectors.getByID(state, 'fieldSeason', fieldSeasonId),
  );

  const season = useSelector(state =>
    entities.selectors.getByID(state, 'season', fieldSeason?.season_id),
  );
  const field = useSelector(state =>
    entities.selectors.getByID(state, 'field', fieldSeason?.field_user_id),
  );
  const yieldUnitsByCrop = useSelector(settings.selectors.getYieldUnitsByCrop);

  const cropSelector = useMemo(() => entities.selectors.makeGetIndexed(), []);
  const initialCrops = useSelector(state =>
    cropSelector(state, 'fieldCrop', 'field_user_season_id', fieldSeason?.id),
  );

  const initialComms = useSelector(state =>
    cropSelector(
      state,
      'recommendation',
      'field_user_season_id',
      fieldSeason?.id,
    ),
  );

  const recommendations = [...initialComms];

  const index = match.params.cropIndex || cropIndex;
  const cropData =
    index !== undefined ? initialCrops[index] : [...initialCrops];

  const formMethods = useForm({
    defaultValues: {
      crop: '',
      variety: '',
      sowing_date: null,
      harvest_date: null,
      yield_value: '',
      yield_value_units: null,
      field_user_season_id: null,
    },
    shouldUnregister: false,
  });

  // Load initial state into form
  useEffect(() => {
    if (!field || !season) {
      return;
    }

    const index = match.params.cropIndex || cropIndex;

    let initialState = cropData;

    if (index !== undefined) {
      initialState = {
        ...initialState,
        yield_value_units: getInitialYieldUnit(
          initialState.yield_value_units,
          yieldUnitsByCrop[initialState.crop],
        ),
      };
    }

    // We append an empty crop row when no crops initially
    if (index === undefined) {
      // Initial null values required by useForm

      initialState = {
        field_user_season_id: fieldSeason.id,
        crop: history?.location?.state?.crop || null,
        variety: null,
        sowing_date: null,
        harvest_date: null,
        yield_value: null,
        yield_value_units: getInitialYieldUnit(
          null,
          yieldUnitsByCrop[history?.location?.state?.crop],
        ),
      };
    }

    formMethods.reset(initialState);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [field, season, cropIndex]);

  const close = () => {
    const viewport = getViewportHash(history.location.pathname);
    history.replace({
      pathname: `/${viewport}/fields/crop-rotation`,
      // We assume close was intentional and skip `unsaved changes` prompt
      ignorePrompt: true,
    });
  };

  const onRemove = useCallback(
    id => {
      logEvent('crop_rotation_remove_crop_click', {
        field_season_id: fieldSeason.id,
      });

      setShowRemoveCropPopup(true);
    },
    [fieldSeason, logEvent],
  );

  const submit = form => {
    if (!cropData.sowing_date && form.sowing_date) {
      Adjust.trackEvent({
        eventToken: '6y2n77',
      });
    }

    if (saveRequest.status === 'pending') {
      // Additional guard against double-save here, as form submit button is
      // outside of the form, and it can be manually submitted by user while
      // request still pending
      return;
    }

    const operations = [];
    const stats = { created: 0, updated: 0, deleted: 0 };

    // Create / update;
    if (form.crop) {
      stats[form.id ? 'updated' : 'created'] += 1;
      operations.push({
        type: form.id ? 'update' : 'create',
        path: form.id && { id: form.id },
        entityType: 'fieldCrop',
        query: {
          include: ['color'],
        },
        body: {
          ...form,
          yield_value: form.yield_value || null,
          yield_value_units: form.yield_value_units || null,
          source: form.source || 'rotated',
        },
      });
    }

    if (operations.length === 0) {
      close();
      return;
    }

    dispatch(entities.actions.request('save-field-crops', operations));

    dispatch(
      settings.actions.updateLocal({
        yieldUnitsByCrop: {
          ...yieldUnitsByCrop,
          ...operations.reduce((acc, cur) => {
            if (cur.body.yield_value_units !== null) {
              acc[cur.body.crop] = cur.body.yield_value_units;
            }
            return acc;
          }, {}),
        },
      }),
    );

    logEvent('crop_rotation_save_crops', {
      field_season_id: fieldSeasonId,
      ...stats,
    });

    if (!form.id) {
      logClevertapEvent('crop_new', {
        crop_type: form.crop,
        crop_count: 1,
        season: season.id,
        ...(form.sowing_date && { sowing_date: form.sowing_date }),
        ...(form.harvest_date && { harvest_date: form.harvest_date }),
        ...(form.yield_value && {
          average_yield: form.yield_value,
          yield_value_units: form.yield_value_units,
        }),
        source: CropSource.rotation,
      });
    }

    onClose();
  };

  const yieldValueErrors = formMethods.formState.errors.yield_value;

  return (
    <>
      <EnterExitTransition variant='popup'>
        {showRemoveCropPopup && (
          <RemoveEntityPopup
            entityType='crop'
            onConfirm={() => {
              const id = formMethods.getValues('id').toString();
              dispatch(
                entities.actions.request('delete-field-crops', [
                  { type: 'delete', path: { id }, entityType: 'fieldCrop' },
                ]),
              );

              setShowRemoveCropPopup(false);
              onClose();
            }}
            onCancel={() => {
              setShowRemoveCropPopup(false);
            }}
          />
        )}
      </EnterExitTransition>
      <div className={styles.cropDetailsContainer}>
        <section className={styles.cropDetailsContent}>
          <form
            className={styles.cropForm}
            onSubmit={formMethods.handleSubmit(submit)}
          >
            <div className='form-group'>
              <div className={styles.cropsContainer}>
                <EditFieldCropRow
                  noDelete={noDelete}
                  formMethods
                  fieldSeason={fieldSeason}
                  recommendations={recommendations}
                  season={season}
                  t={t}
                  onRemove={onRemove}
                  control={formMethods.control}
                  setValue={formMethods.setValue}
                  getValues={formMethods.getValues}
                />

                <FeatureDisabledTooltip
                  feature={FEATURES.CROP_EDIT}
                  data={fieldSeason}
                  direction={PopoverDeprecatedAlign.MiddleTop}
                >
                  <div className={styles.cropFormBtnArea}>
                    <Button
                      disabled={yieldValueErrors}
                      className='btn btn-success btn-lg size-full'
                      pending={saveRequest.status === 'pending'}
                      onClick={formMethods.handleSubmit(submit)}
                    >
                      <span className='btn__ico'>
                        <Icon className='ico-check-os' name='check' />
                      </span>
                      {t('forms.save')}
                    </Button>
                  </div>
                </FeatureDisabledTooltip>
              </div>
            </div>
          </form>
        </section>
      </div>
    </>
  );
};

export default CropDetailsPopup;
