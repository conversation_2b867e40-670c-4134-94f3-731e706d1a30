import { css } from 'linaria';

export const styles = {
  container: css`
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    width: 700px;
    margin: 20px auto;
  `,
  about: css`
    width: 300px;
    display: flex;
    flex-direction: column;
    margin: 20px 0;
  `,
  content: css`
    flex: 1;
  `,
  logo: css`
    margin-bottom: 40px;

    svg {
      display: block;
    }
  `,
  title: css`
    font-size: 24px;
    line-height: 32px;
    font-weight: bold;
    margin-top: 0;
    margin-bottom: 10px;
    word-break: break-word;
  `,
  text: css`
    font-size: 16px;
    line-height: 22px;
  `,
  disclaimer: css`
    font-size: 14px;
    line-height: 18px;
    color: #a5b2bc;
  `,
  logoDivider: css`
    width: 1px;
    height: 32px;
    background-color: #1c1c1e;
    opacity: 0.2;
    margin: 0 16px;
  `,
  dealerLogo: css`
    max-width: 110px;
    max-height: 110px;
  `,
  invitationLogo: css`
    display: inline-flex;
    align-items: center;
  `,
};
