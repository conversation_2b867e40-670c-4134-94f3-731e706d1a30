import React, { useEffect, useCallback, useMemo, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  VictoryHistogram,
  VictoryChart,
  VictoryTheme,
  VictoryAxis,
  VictoryLabel,
  VictoryTooltip,
} from 'victory';
import { css, cx } from 'linaria';
import { styled } from 'linaria/react';
import scrollIntoView, { Options } from 'scroll-into-view-if-needed';
import moment from 'moment';

import map from 'modules/map';

import Button from './ui/Button/Button';
import Icon from './Icon';
import RangeLegend from './RangeLegend';

import { styles } from 'features/fields/styles/fieldsLegend.styles';

import logEvent from 'sagas/global/logEvent';
import { round } from 'utils/format-number';
import { formatDate } from 'utils/format-date';
import { useTranslate } from 'utils/use-translate';

import {
  BarsCount,
  LegendColorsMap,
  LegendChartStylePadding,
  LegendChartVerticalAxisStyle,
  LegendChartHorizontalAxisStyle,
  LegendChartTooltipStyle,
  LegendChartFlyoutStyle,
} from 'constants/style';
import { AllCropsTitle } from 'modules/map/consts';

const ScrollOptions: Options = {
  behavior: 'smooth',
  block: 'nearest',
  inline: 'center',
};

// eslint-disable-next-line  @typescript-eslint/no-explicit-any
const formatLabelInteger = (value: any) =>
  Number.isInteger(value) ? value : null;

const btn = css`
  white-space: nowrap;
  flex-shrink: 0;
  background: #303437;
  transition: background-color 0.3s;
  border: none;
  border-radius: 4px;
  color: white;
  padding-left: 10px;
  padding-right: 10px;
  font-size: 12px;
  &:hover {
    background: #000;
  }
  &:disabled {
    opacity: 0.25;
  }
  &:disabled:hover {
    background: #303437;
  }
  &.__active {
    background: #007aff;
    font-weight: 500;
  }
  & + & {
    margin-left: 5px;
  }
`;

type StyledGroupControlsProps = {
  expanded: boolean;
};
const StyledGroupControls = styled.div<StyledGroupControlsProps>`
  height: 24px;
  display: flex;
  max-width: 100%;
  opacity: ${props => (props.expanded ? 1 : 0)};
  visibility: ${props => (props.expanded ? 'visible' : 'hidden')};
  transition-property: opacity, visibility;
  transition-duration: 0.3s, 0.3s;
  transition-delay: ${props => (props.expanded ? '.3s, .3s' : '0s')};
`;

const chartButtons = css`
  display: flex;
  flex-grow: 0;
  flex-shrink: 1;
  overflow: hidden;
`;
const controls = css`
  display: flex;
  flex-shrink: 0;
  flex-grow: 0;
  margin-left: 5px;
`;

type StyledChartsContainerProps = {
  expanded: boolean;
};
const StyledChartsContainer = styled.div<StyledChartsContainerProps>`
  position: relative;
  height: ${props => (props.expanded ? '190px' : '24px')};
  width: 360px;
  transition: height 0.3s;
`;

type StyledLegendContainerProps = {
  expanded: boolean;
};
const StyledLegendContainer = styled.div<StyledLegendContainerProps>`
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  opacity: ${props => (props.expanded ? 0 : 1)};
  visibility: ${props => (props.expanded ? 'hidden' : 'visible')};
  transition: opacity 0.3s, visibility 0.3s;
`;

type StyledChartContainerProps = {
  expanded: boolean;
};
const StyledChartContainer = styled.div<StyledChartContainerProps>`
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  opacity: ${props => (props.expanded ? 1 : 0)};
  visibility: ${props => (props.expanded ? 'visible' : 'hidden')};
  transition: opacity 0.3s, visibility 0.3s;
`;

const ChartLegend = () => {
  const scrollList = useRef({});

  const dispatch = useDispatch();

  const { t } = useTranslate();

  const mapFilling = useSelector(map.selectors.getMapFilling);
  const chartExpanded = useSelector(map.selectors.getChartExpanded);

  const { labels, data, range, crops, tooltips } = useSelector(
    map.selectors.getMapLegend,
  );

  const filterCrop = useSelector(map.selectors.getMapFilterCrop);
  const filterRange = useSelector(map.selectors.getMapFilterRange);

  useEffect(() => {
    // @ts-ignore
    const el = scrollList.current[filterCrop];
    if (el && chartExpanded) {
      scrollIntoView(el, ScrollOptions);
    }
  }, [filterCrop, scrollList, chartExpanded]);

  const isLastCrop = useMemo(
    () => crops.findIndex(crop => crop === filterCrop) === crops.length - 1,
    [filterCrop, crops],
  );

  const labelsTranslation = useMemo(() => {
    if (['sowing_date', 'harvest_date'].includes(mapFilling)) {
      const isTheSameYear =
        // @ts-ignore
        moment(labels[0]).year() === moment(labels[labels.length - 1]).year();
      return t(
        `fields.ndvi.${isTheSameYear ? 'date_format' : 'full_date_format'}`,
      );
    } else {
      return false;
    }
  }, [mapFilling, labels, t]);

  const formatLabel = useCallback(
    label => {
      return labelsTranslation
        ? formatDate(label, labelsTranslation as string)
        : round(label, 2);
    },
    [labelsTranslation],
  );

  const onCropClick = useCallback(
    crop => {
      dispatch(map.actions.filterMap({ filterCrop: crop, filterRange: null }));
      logEvent('fields_chart_crop', {
        map_filling: mapFilling,
        crop,
        range: null,
      });
    },
    [dispatch, mapFilling],
  );

  const onControlClick = useCallback(
    type => {
      const index = crops.findIndex(crop => crop === filterCrop);
      let crop = null;
      if (type === 'next' && index !== crops.length - 1) {
        crop = crops[index + 1];
      }
      if (type === 'prev' && index !== 0) {
        crop = crops[index - 1];
      }
      crop && onCropClick(crop);
    },
    [crops, filterCrop, onCropClick],
  );

  const onRangeClick = useCallback(
    (_, props) => {
      dispatch(
        map.actions.filterMap({
          filterCrop,
          filterRange: filterRange === props.index ? null : props.index,
        }),
      );
      logEvent('fields_chart_range', {
        map_filling: mapFilling,
        crop: filterCrop,
        // @ts-ignore
        range: filterRange === props.index ? null : tooltips[props.index],
      });
    },
    [dispatch, filterCrop, filterRange, mapFilling, tooltips],
  );

  return (
    <StyledChartsContainer expanded={chartExpanded}>
      <StyledLegendContainer expanded={chartExpanded}>
        <RangeLegend
          // @ts-ignore
          colorsRange={LegendColorsMap[mapFilling].colors(BarsCount)}
          labels={labels.map(formatLabel) as string[]}
          onExpand={() => {
            dispatch(map.actions.expandChart());

            logEvent('fields_chart_expand', {
              map_filling: mapFilling,
              chart_expand: true,
            });
          }}
        />
      </StyledLegendContainer>
      <StyledChartContainer expanded={chartExpanded}>
        <div className={styles.widgetWrapper}>
          <div style={{ width: '360px', position: 'relative' }}>
            <StyledGroupControls expanded={chartExpanded}>
              <div className={chartButtons}>
                {crops.map(crop => {
                  return (
                    <Button
                      key={crop}
                      ref={el => {
                        // @ts-ignore
                        scrollList.current[crop] = el;
                      }}
                      onClick={() => onCropClick(crop)}
                      className={cx(btn, crop === filterCrop && '__active')}
                    >
                      {t(`crop.${crop}`)}
                    </Button>
                  );
                })}
              </div>
              <div className={controls}>
                <Button
                  className={btn}
                  onClick={() => onControlClick('prev')}
                  disabled={filterCrop === AllCropsTitle}
                >
                  <Icon name='page-prev' />
                </Button>
                <Button
                  className={btn}
                  onClick={() => onControlClick('next')}
                  disabled={isLastCrop}
                >
                  <Icon name='page-next' />
                </Button>
              </div>
            </StyledGroupControls>
            <div>
              <VictoryChart
                padding={LegendChartStylePadding}
                width={360}
                height={160}
                theme={VictoryTheme.material}
              >
                <VictoryHistogram
                  labelComponent={
                    <VictoryTooltip
                      constrainToVisibleArea
                      pointerLength={0}
                      dy={-10}
                      flyoutStyle={LegendChartFlyoutStyle}
                      style={LegendChartTooltipStyle}
                    />
                  }
                  labels={tooltips}
                  animate={{
                    duration: 500,
                    onLoad: { duration: 500 },
                  }}
                  data={data}
                  bins={range}
                  height={160}
                  events={[
                    {
                      target: 'data',
                      eventHandlers: {
                        onClick: onRangeClick,
                      },
                    },
                  ]}
                  style={{
                    data: {
                      fill: ({ index }) =>
                        // @ts-ignore
                        LegendColorsMap[mapFilling].colors(BarsCount)[index],
                      opacity: ({ index }) =>
                        [index, null].includes(filterRange as number) ? 1 : 0.5,
                      strokeWidth: 0,
                      cursor: 'pointer',
                    },
                  }}
                />
                <VictoryAxis
                  style={LegendChartHorizontalAxisStyle}
                  tickValues={labels}
                  tickFormat={formatLabel}
                  tickLabelComponent={
                    <VictoryLabel
                      renderInPortal
                      // @ts-ignore
                      textAnchor={({ index }) => {
                        if (index === 0) return 'start';
                        if (labels && index === labels.length - 1) return 'end';
                        return 'middle';
                      }}
                    />
                  }
                />
                <VictoryAxis
                  dependentAxis
                  orientation='right'
                  padding={10}
                  style={LegendChartVerticalAxisStyle}
                  tickFormat={formatLabelInteger}
                />
              </VictoryChart>
            </div>
            <div className={cx(styles.actions, '__fixed')}>
              <Button
                className={styles.actionBtn}
                onClick={() => {
                  dispatch(map.actions.expandChart());

                  logEvent('fields_chart_expand', {
                    map_filling: mapFilling,
                    chart_expand: false,
                  });
                }}
              >
                <Icon name='collapseChart' />
              </Button>
            </div>
          </div>
        </div>
      </StyledChartContainer>
    </StyledChartsContainer>
  );
};

export default ChartLegend;
