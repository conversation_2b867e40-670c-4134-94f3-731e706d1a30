import React from 'react';

import { cx } from 'linaria';
import { styled } from 'linaria/react';

export enum StyledLoaderSize {
  'S' = 'S',
  'L' = 'L',
}

interface StyledProps extends Props {
  $loading: boolean;
  $size: StyledLoaderSize;
}

const StyledLoader = styled.div<StyledProps>`
  width: ${({ $size }) => ($size === StyledLoaderSize.L ? '32px' : '14px')};
  height: ${({ $size }) => ($size === StyledLoaderSize.L ? '32px' : '14px')};
  border-radius: 50%;
  background-color: transparent;
  border: 2px solid #ffffff55;
  animation: 1s inlineSpin linear infinite;
  border-top-color: var(--color-primary);
  border-right-color: var(--color-primary);
  transition: opacity 0.3s, visibility 0.3s;
  opacity: ${({ $loading }) => ($loading ? '1' : '0')};
  visibility: ${({ $loading }) => ($loading ? 'visible' : 'hidden')};

  &.fill {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    animation: 1s spin linear infinite;
  }

  @keyframes spin {
    from {
      transform: translate(-50%, -50%) rotate(0deg);
    }
    to {
      transform: translate(-50%, -50%) rotate(360deg);
    }
  }

  @keyframes inlineSpin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
`;

export type Props = {
  color?: string;
  loading?: boolean;
  inline?: boolean;
  size?: StyledLoaderSize;
};

const Progress = ({
  color,
  loading = true,
  inline,
  size = StyledLoaderSize.L,
}: Props) => {
  return (
    <StyledLoader
      color={color}
      className={cx('loader', !inline && 'fill')}
      $loading={loading}
      $size={size}
    />
  );
};

export default Progress;
