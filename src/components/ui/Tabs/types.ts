import { ReactElement, ReactNode } from 'react';

export type TabsContextProps = {
  activeIndex: number;
  setActiveIndex: (activeIndex: number) => void;
};

export interface BaseElementProps {
  children: ReactNode | ReactElement | ReactElement[];
}

export interface TabListProps {
  children: ReactElement[];
  customStyleClassName?: string;
}
export interface TabsProps extends BaseElementProps {
  activeIndex: number;
  setActiveIndex: (activeIndex: number) => void;
  customStyleClassName?: string;
}

export interface TabProps extends BaseElementProps {
  onClick?: Function;
  tabIndex?: number;
  disabled?: boolean;
  baseClassName?: string;
  activeClassName?: string;
  customStyleClassName?: string;
}

export interface TabPanelProps extends BaseElementProps {
  panelIndex?: number;
  activeIndex?: number;
  customStyleClassName?: string;
}
