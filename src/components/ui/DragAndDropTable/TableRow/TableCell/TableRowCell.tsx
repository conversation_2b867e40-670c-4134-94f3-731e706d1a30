import { cx } from 'linaria';
import React from 'react';
import {
  tableCell,
  tableCellFixedLast,
  tableCellGroupStart,
  tableHeaderCellContent,
  tableCellFixed,
  tableCellHovered,
  tableCellDragDropHovered,
} from '../TableRow.style';
import { ColumnInfo } from '../types';

type TableRowCellProps = {
  columnInfo: ColumnInfo;
  children: React.ReactNode;
  hovered: boolean;
  isOver: boolean;
};

export const TableRowCell = ({
  columnInfo,
  children,
  hovered,
  isOver,
}: TableRowCellProps) => {
  const width = columnInfo.styledInfo?.width;
  const rightBorder = columnInfo.styledInfo?.rightBorder;
  const columnsGroupName = columnInfo.columnsGroupName;

  return (
    <td
      key={columnInfo.key}
      className={cx(
        tableCell,
        width ? tableCellFixed : '',
        rightBorder ? tableCellFixedLast : '',
        columnsGroupName ? tableCellGroupStart : '',
        hovered ? tableCellHovered : '',
        isOver ? tableCellDragDropHovered : '',
      )}
      style={{
        ...(width
          ? {
              width: `${width}px`,
              minWidth: `${width}px`,
              maxWidth: `${width}px`,
              left: `-108px`,
            }
          : {}),
      }}
    >
      <div className={tableHeaderCellContent}>{children}</div>
    </td>
  );
};
