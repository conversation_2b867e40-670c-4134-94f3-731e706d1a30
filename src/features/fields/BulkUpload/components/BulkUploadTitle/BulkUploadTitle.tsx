import React, { VFC } from 'react';
import { useSelector } from 'react-redux';

import { selectors } from 'features/fields/BulkUpload/redux/selectors';
import { BulkUploadStep } from 'features/fields/BulkUpload/types';

import { useTranslate } from 'utils/use-translate';

export const BulkUploadTitle: VFC = () => {
  const { t } = useTranslate();

  const currentStep: BulkUploadStep = useSelector(selectors.getCurrentStep);
  const uploadingInProgress = useSelector(selectors.getUploadingInProgress);

  function getTitle() {
    switch (currentStep) {
      case BulkUploadStep.FILE_SELECTION:
        return t('fields.bulk_upload.add_files.title');
      case BulkUploadStep.PROCESSING:
        return t('fields.bulk_upload.processing.title');
      case BulkUploadStep.CHECK_DATA:
        return t('fields.bulk_upload.check_data.title');
      case BulkUploadStep.UPLOADING:
        return uploadingInProgress
          ? t('fields.bulk_upload.uploading.title')
          : t('fields.bulk_upload.uploading.done.title');
      default:
        return null;
    }
  }

  return <h3 className='main-uploader-area__title'>{getTitle()}</h3>;
};
