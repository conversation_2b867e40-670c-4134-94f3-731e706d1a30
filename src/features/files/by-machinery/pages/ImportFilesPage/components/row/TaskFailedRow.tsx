import React, { useState, VFC } from 'react';
import { useDispatch } from 'react-redux';

import { useTranslate } from 'utils/use-translate';
import Icon from 'components/Icon';

import {
  ActiveTaskRowContainer,
  ActiveTaskRowStyle,
  ActiveTaskStatusWrapper,
  ActiveTaskSubTitleStyle,
  ActiveTaskTitleStyle,
  CancelButtonStyle,
  clearButtonIconClass,
  failedButtonIconClass,
} from '../styles';
import {
  DataTypesKey,
  FileImportError,
  TaskFailedType,
} from 'features/files/by-machinery/types';
import { actions } from 'features/files/by-machinery/redux/actions';
import Alert from 'components/ui/Alert/Alert';

type TaskFailedRowProps = {
  task: TaskFailedType;
};

const TaskFailedRow: VFC<TaskFailedRowProps> = ({ task }) => {
  const { t } = useTranslate();
  const dispatch = useDispatch();
  const [isShowRemoveAlert, setIsShowRemoveAlert] = useState(false);

  return (
    <ActiveTaskRowStyle disabled={task.disabled}>
      <ActiveTaskRowContainer>
        <ActiveTaskStatusWrapper>
          <Icon name='alert-circle' className={failedButtonIconClass} />
        </ActiveTaskStatusWrapper>
        <div>
          <ActiveTaskTitleStyle>{task.name}</ActiveTaskTitleStyle>
          <div>
            <ActiveTaskSubTitleStyle>
              {[
                FileImportError.empty,
                FileImportError.no_matched_fields,
              ].includes(task.err_code) &&
              task?.data_type === DataTypesKey.soilSampling
                ? t(`import.by-machinery.failed.sampling.${task.err_code}`)
                : t(`import.by-machinery.failed.${task.err_code}`)}
            </ActiveTaskSubTitleStyle>
          </div>
        </div>
      </ActiveTaskRowContainer>
      {isShowRemoveAlert && (
        <Alert
          show={true}
          text={t(`import.by-machinery.alert.remove.text`)}
          onDanger={() => {
            dispatch(actions.removeTask(task));
            setIsShowRemoveAlert(false);
          }}
          onCancel={() => setIsShowRemoveAlert(false)}
          danger={t('import.by-machinery.alert.remove.confirm') as string}
          success={t('import.by-machinery.alert.remove.cancel') as string}
        />
      )}
      <CancelButtonStyle
        onClick={e => {
          e.preventDefault();
          setIsShowRemoveAlert(true);
        }}
        disabled={task.disabled}
      >
        <Icon name='close' className={clearButtonIconClass} />
      </CancelButtonStyle>
    </ActiveTaskRowStyle>
  );
};

export default TaskFailedRow;
