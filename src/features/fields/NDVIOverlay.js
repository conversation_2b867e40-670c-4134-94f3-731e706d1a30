import React, { useEffect, useRef, useState } from 'react';
import { ImageOverlay, useLeaflet } from 'react-leaflet';
import { useSelector } from 'react-redux';

import { NDVITooltip } from 'features/fields/NDVITooltip/NDVITooltip';

import fields from 'modules/fields';

import config from 'config';

import useNdviTooltip from 'utils/use-ndvi-tooltip';
import bbox2leaflet from 'utils/bbox2leaflet/bbox2leaflet';

export const NDVIOverlay = ({ fieldId, uuid, type, bbox }) => {
  const { map } = useLeaflet();

  const imageOverlay = useRef();
  const [hoveredPoint, setHoveredPoint] = useState(null);

  const highlighted = useSelector(
    state => fields.selectors.getHighlightedID(state) === fieldId,
  );

  useEffect(() => {
    if (!highlighted || !imageOverlay?.current) {
      return;
    }

    const handleMouseMove = event => {
      const { clientX, clientY } = event.originalEvent;

      const img = imageOverlay?.current?.leafletElement?._image;
      const imageBox = img?.getBoundingClientRect();

      const x = (clientX - imageBox.left) / imageBox.width;
      const y = (clientY - imageBox.top) / imageBox.height;

      setHoveredPoint({
        mouse: [clientX, clientY],
        image: [x, y],
      });
    };

    map.on('mousemove', handleMouseMove);
    return () => map.off('mousemove', handleMouseMove);
  }, [map, highlighted, fieldId]);

  const hoveredValue = useNdviTooltip({
    uuid: highlighted && uuid,
    hoveredPoint: hoveredPoint?.image,
  });

  // Check bbox values
  if (bbox.length === 0) {
    return null;
  }

  return (
    <>
      <ImageOverlay
        ref={imageOverlay}
        crossOrigin
        interactive={false}
        // Leaflet does not handle changing bounds well, so we re-create overlay each time
        key={`${fieldId}-ndvi`}
        url={`${config.apiHost}/en/v2/fields-users-seasons-ndvi/${uuid}/${
          type === 'default' ? 'rgb' : 'contrasted'
        }.png`}
        bounds={bbox2leaflet(bbox)}
      />
      <NDVITooltip
        isVisible={highlighted && hoveredValue}
        position={hoveredPoint?.mouse}
        value={hoveredValue}
      />
    </>
  );
};

export default NDVIOverlay;
