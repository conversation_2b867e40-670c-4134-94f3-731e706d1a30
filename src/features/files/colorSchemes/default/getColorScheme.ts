import { ColorScale } from 'constants/style';
import { ColorSchemeGetter } from 'features/files/colorSchemes/types';

export const getDefaultColorScheme: ColorSchemeGetter = (
  column: TEMPORARY_ANY,
) => (properties: TEMPORARY_ANY, zoom: number, type: number) => {
  const baseColor = ColorScale(
    (properties[column.name] - column.min) / (column.max - column.min),
  ).hex();
  return {
    color: type === 1 ? baseColor : 'gray',
    fillColor: baseColor,
    fillOpacity: 1,
    weight: 0.5,
    radius: 3.75,
  };
};
