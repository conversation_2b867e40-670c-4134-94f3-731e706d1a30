import { css } from 'linaria';
import { styled } from 'linaria/react';

export const styles = {
  modalOuter: css`
    width: 100%;
    max-width: 800px;
    height: 600px;
  `,
  autoFillSidebar: css`
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    width: 290px;
    height: 600px;
    border-right: 1px solid #e4e7ea;
  `,
  body: css`
    padding-left: 20px;
    padding-right: 20px;
    display: flex;
    flex-direction: column;
  `,
  cropsListWrapper: css`
    height: calc(100% - 65px);
    overflow-y: auto;
    padding: 15px 14px 0 15px;
  `,
  cropsListSection: css``,
  cropsListSectionHeader: css`
    text-transform: uppercase;
    font-size: 10px;
    line-height: 16px;
    letter-spacing: 1.2px;
    color: #a5b2bc;
  `,
  cropsList: css`
    padding: 0;
    margin: 0;
    list-style-type: none;
    padding-bottom: 15px;
  `,
  cropsListItem: css`
    display: flex;
    align-items: center;
    padding-top: 2px;
    padding-bottom: 2px;
    margin-top: 13px;
    margin-bottom: 13px;

    .form-checkbox {
      flex-direction: row-reverse;
      width: 100%;
      font-size: 14px;
      line-height: 18px;
    }

    .form-checkbox__value {
      padding-left: 0;
    }
  `,

  searchInput: css`
    position: relative;
    border-bottom: 1px solid #e4e7ea;
    padding: 15px;

    &::after {
      position: absolute;
      pointer-events: none;
      content: '';
      height: 10px;
      bottom: -11px;
      left: 0;
      right: 0;
      background-image: linear-gradient(
        180deg,
        rgba(0, 0, 0, 0.04) 0%,
        rgba(0, 0, 0, 0.0001) 100%
      );
    }
  `,
  autofillBody: css`
    display: flex;
    flex-direction: column;
    width: 100%;
  `,
  emptyArea: css`
    padding: 0 80px;
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: center;
  `,
  emptyAreaTitle: css`
    font-size: 24px;
    line-height: 32px;
    color: #000000;
    font-weight: 700;
    text-align: center;
  `,
  emptyAreaAltTitle: css`
    margin-top: 15px;
    font-size: 16px;
    font-weight: 400;
    line-height: 22px;
    text-align: center;
  `,
  autofillIntroArrow: css`
    margin-left: -52px;
  `,
  closeButton: css`
    position: absolute;
    right: 15px;
    top: 15px;
    width: 26px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #a5b2bc;
    border-radius: 50%;
    border: 0;
    transition: background-color 0.3s;

    &:hover {
      background-color: #5e5e5e;
    }
  `,
  formHead: css`
    padding-right: 25px;
  `,
  formArea: css`
    padding: 30px 5px 0 30px;
  `,
  formTitle: css`
    font-size: 24px;
    line-height: 32px;
    color: #000000;
    font-weight: 700;
  `,
  formSeasonTitle: css`
    font-size: 20px;
    font-weight: 500;
    line-height: 24px;
    margin-top: 5px;
  `,
  stackBarHolder: css`
    margin-top: 23px;
  `,

  areasList: css`
    height: 330px;
    overflow-y: auto;
    margin-top: 20px;
    padding-right: 25px;
  `,

  floatingButton: css`
    position: absolute;
    bottom: 15px;
    right: 16px;
  `,
};

export const StyledModalAutofillContent = styled.div`
  display: flex;
  flex-direction: row;
`;

type StyledColorPreviewProps = {
  color: string;
};

export const StyledColorPreview = styled.span<StyledColorPreviewProps>`
  display: inline-block;
  vertical-align: middle;
  width: 16px;
  height: 16px;
  background-color: ${props => props.color};
  border-radius: 2px;
  margin-right: 6px;
`;
