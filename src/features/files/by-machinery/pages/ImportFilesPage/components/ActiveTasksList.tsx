import React, { VFC } from 'react';

import TaskProcessingRow from './row/TaskProcessingRow';
import TaskUploadingRow from './row/TaskUploadingRow';
import TaskSuccessRow from './row/TaskSuccessRow';
import TaskFailedTaskRow from './row/TaskFailedRow';
import { ImportTaskType, TaskStatus } from 'features/files/by-machinery/types';

type ActiveTaskProps = {
  task: ImportTaskType;
};
export const Row: VFC<ActiveTaskProps> = ({ task }) => {
  if (task.status === TaskStatus.uploading) {
    return <TaskUploadingRow task={task} />;
  }

  if (task.status === TaskStatus.waiting) {
    return <TaskProcessingRow task={task} />;
  }

  if (task.status === TaskStatus.processing) {
    return <TaskProcessingRow task={task} />;
  }

  if (task.status === TaskStatus.processed) {
    return <TaskSuccessRow task={task} />;
  }

  if (task.status === TaskStatus.done) {
    return <TaskSuccessRow task={task} />;
  }

  if (task.status === TaskStatus.importing) {
    return <TaskProcessingRow task={task} />;
  }

  if (task.status === TaskStatus.error) {
    return <TaskFailedTaskRow task={task} />;
  }
  return null;
};

type ActiveTasksListProps = {
  tasks: ImportTaskType[];
};

const ActiveTasksList: VFC<ActiveTasksListProps> = ({ tasks }) => {
  return (
    <>
      {tasks.map(task => (
        <Row key={task?.uuid || task?.temporaryUuid} task={task} />
      ))}
    </>
  );
};

export default ActiveTasksList;
