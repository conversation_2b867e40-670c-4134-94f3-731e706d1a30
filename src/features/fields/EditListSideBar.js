import React, {
  useEffect,
  useCallback,
  useState,
  useRef,
  useMemo,
} from 'react';
import { AutoSizer, List } from 'react-virtualized';
import { useDispatch, useSelector } from 'react-redux';

import Icon from 'components/Icon';
import Button from 'components/ui/Button/Button';
import Checkbox from 'components/ui/Checkbox/Checkbox';
import ModernSpinner from 'components/ui/ModernSpinner/ModernSpinner';
import {
  FieldListSortedItemMemo,
  FantomFieldListItemId,
} from './FieldListItem';
import LinkedNotesWarningPopup from 'features/fields/LinkedNotesWarningPopup';
import EnterExitTransition from 'components/EnterExitTransition';

import { FieldRowHeight, ExpandableRowHeight } from './FieldListSideBar';

import entities from 'modules/entities';
import fields from 'modules/fields';
import notes from 'modules/notes';
import seasons from 'modules/seasons';
import settings from 'modules/settings';

import getRootScoutingUrl from 'utils/get-root-scouting-url';
import { useTranslate } from 'utils/use-translate';

import logEvent from 'sagas/global/logEvent';
import { FEATURES, FeatureDisabledTooltip } from 'features/permissions';
import { PopoverDeprecatedAlign } from 'components/ui/Popover';

let shouldUndoRedirect = false;

const EditListSideBar = ({ match, history, location }) => {
  const list = useRef(null);

  const { t } = useTranslate();

  const fieldData = useSelector(fields.selectors.getData);
  const status = useSelector(fields.selectors.getOwnStatus);
  const fieldIDs = useSelector(fields.selectors.getAllSortedIDs);
  const allFieldIDs = useSelector(fields.selectors.getOwnIDs);
  const checkedIDs = useSelector(fields.selectors.getCheckedIDs);
  const currentSeason = useSelector(seasons.selectors.getCurrent);
  const fieldListSettings = useSelector(settings.selectors.getFieldList);
  const notesInSeason = useSelector(state =>
    notes.selectors.getInSeason(state, currentSeason?.id),
  );
  const fieldSeasons = useSelector(state =>
    entities.selectors.getAll(state, 'fieldSeason'),
  );

  const fieldSeasonsInSeason = fieldSeasons.filter(
    fs => fs.season_id === currentSeason.id,
  );

  const dispatch = useDispatch();

  const [showWarning, setShowWarning] = useState(false);

  const lastLocation = history.location;

  useEffect(() => {
    if (status === 'resolved' && fieldIDs.length === 0) {
      const baseUrl = getRootScoutingUrl(history.location.pathname);
      shouldUndoRedirect = true;
      history.replace(baseUrl);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fieldIDs.length]);

  useEffect(() => {
    shouldUndoRedirect = false;
    return () => dispatch(fields.actions.removeAllChecked());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const allChecked = useMemo(
    () => checkedIDs.length === allFieldIDs.length,
    [checkedIDs.length, allFieldIDs.length],
  );

  const onExpand = useCallback(
    crop => {
      dispatch(fields.actions.expandCrop(crop));
      logEvent('fields_list_grouped_crop_expand', {
        crop,
      });
    },
    [dispatch],
  );

  const handleCheck = useCallback(
    (event, id) => {
      if (checkedIDs.includes(id)) {
        dispatch(fields.actions.removeCheck(id));
      } else {
        dispatch(fields.actions.check(id));
      }
    },
    [checkedIDs, dispatch],
  );

  const handleCheckAll = checked => {
    if (checked) {
      dispatch(fields.actions.checkAll(allFieldIDs));
    } else {
      dispatch(fields.actions.removeAllChecked());
    }
  };

  const handleUndoRemove = useCallback(() => {
    if (shouldUndoRedirect) {
      history.push(lastLocation);
      shouldUndoRedirect = false;
    }
  }, [history, lastLocation]);

  const hasAttachedNotes = useCallback(
    fields => {
      const fieldSeasonsToDelete = fieldSeasonsInSeason.filter(fs =>
        fields.some(field => field.fieldUserSeasonId === fs.id),
      );
      return notesInSeason.some(note =>
        fieldSeasonsToDelete.some(
          fs => fs.uuid === note.field_user_season_uuid,
        ),
      );
    },
    [notesInSeason, fieldSeasonsInSeason],
  );

  const fieldArray = useMemo(() => {
    const array = [];
    checkedIDs.forEach(v => {
      array.push(fieldData[v]);
    });
    return array;
  }, [fieldData, checkedIDs]);

  const removeFields = () => {
    if (hasAttachedNotes(fieldArray)) {
      setShowWarning({ type: 'fieldWithNotes' });
    } else {
      setShowWarning({ type: 'field' });
    }
  };

  const calculateRowHeight = useCallback(
    ({ index }) =>
      fieldIDs[index]?.expandable ? ExpandableRowHeight : FieldRowHeight,
    [fieldIDs],
  );

  return (
    <>
      <EnterExitTransition variant='popup'>
        {showWarning && (
          <LinkedNotesWarningPopup
            t={t}
            title={t('fields.warning.title')}
            text={
              showWarning.type === 'field'
                ? t('fields.delete-field.warning.text', {
                    count: checkedIDs.length,
                  })
                : t('fields.linked-notes.warning.text', {
                    count: checkedIDs.length,
                  })
            }
            onConfirm={() => {
              dispatch(
                fields.actions.removeBatch(
                  fieldArray,
                  currentSeason.id,
                  handleUndoRemove,
                ),
              );
              dispatch(fields.actions.removeAllChecked());
              setShowWarning(false);
            }}
            onCancel={() => {
              setShowWarning(false);
            }}
          />
        )}
      </EnterExitTransition>

      <div className='soil-sidebar-create'>
        <div className='soil-sidebar-header'>
          <Button
            className='app-nav-back'
            onClick={() => {
              history.push(getRootScoutingUrl(history.location.pathname));
            }}
          >
            <Icon className='ico-arrow-long-left-os' name='arrow-left' />
          </Button>
          <div className='soil-sidebar-header__inner'>
            <h1 className='soil-sidebar-header__main'>
              {t('fields.list.edit.title')}
            </h1>
          </div>
        </div>
        {status === 'pending' && (
          <div className='soil-sidebar-empty'>
            <div className='soil-sidebar-empty__ico with_loader'>
              <ModernSpinner large />
            </div>
            <h2 className='soil-sidebar-empty__title'>
              {t('fields.loading.title')}
            </h2>
          </div>
        )}
        {status === 'resolved' && fieldIDs.length !== 0 && (
          <>
            <div className='soil-sidebar-selall'>
              <Checkbox
                checked={allChecked}
                onChange={event => {
                  handleCheckAll(event.target.checked);
                }}
              >
                {t('fields.list.edit.select_all')}
              </Checkbox>
            </div>
            <div className='soil-sidebar-body'>
              <AutoSizer>
                {({ width, height }) => (
                  <List
                    ref={list}
                    className='soil-fields-list __with-actions'
                    containerProps={{ 'data-mode': 'edit' }}
                    width={width}
                    height={height}
                    rowCount={fieldIDs.length + 1}
                    overscanRowCount={10}
                    rowHeight={calculateRowHeight}
                    rowRenderer={props => (
                      <FieldListSortedItemMemo
                        data={[...fieldIDs, { id: FantomFieldListItemId }]}
                        notesInSeason={notesInSeason}
                        match={match}
                        history={history}
                        checkedIDs={checkedIDs}
                        isGroupedBy={fieldListSettings.groupBy}
                        handleCheck={handleCheck}
                        onExpand={onExpand}
                        onClick={() => {}}
                        {...props}
                      />
                    )}
                  />
                )}
              </AutoSizer>
            </div>
            <div className='soil-sidebar-addnew'>
              <ul className='soil-sidebar-addnew__list'>
                <li className='soil-sidebar-addnew__list-item'>
                  <Button
                    className='btn btn-primary btn-lg'
                    onClick={() => {
                      history.push(
                        getRootScoutingUrl(history.location.pathname),
                      );
                    }}
                  >
                    {t('forms.cancel')}
                  </Button>
                </li>
                <FeatureDisabledTooltip
                  feature={FEATURES.FIELD_DELETE}
                  direction={PopoverDeprecatedAlign.MiddleTop}
                >
                  <li className='soil-sidebar-addnew__list-item'>
                    <Button
                      className='btn btn-danger btn-lg'
                      disabled={checkedIDs.length <= 0}
                      onClick={() => removeFields()}
                    >
                      {t('fields.actions.remove')}
                    </Button>
                  </li>
                </FeatureDisabledTooltip>
              </ul>
            </div>
          </>
        )}
      </div>
    </>
  );
};

export default EditListSideBar;
