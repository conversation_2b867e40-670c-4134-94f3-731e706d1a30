import { useEffect } from 'react';
import { useSelector } from 'react-redux';

import { selectCropsProperties } from 'features/files/by-machinery/redux/selectors';
import {
  useFetchActiveDataLayer,
  useFieldDataValuesLoader,
} from 'features/files/by-machinery/dataLoaders';

export const useUpdateCropsStatus = () => {
  const cropsProperty = useSelector(selectCropsProperties);
  const cropsPropertyIsReady = cropsProperty.every(v => v.is_values_imported);
  const fetchLayers = useFetchActiveDataLayer();
  const [, , fetchValues] = useFieldDataValuesLoader();

  useEffect(() => {
    const interval = setInterval(() => {
      if (!cropsPropertyIsReady) {
        fetchLayers();
        fetchValues();
      }
    }, 5000);

    return () => {
      clearInterval(interval);
    };
  }, [cropsPropertyIsReady, fetchValues, fetchLayers]);
};
