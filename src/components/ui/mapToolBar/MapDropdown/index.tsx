import React, { FC, useState } from 'react';

import useOutsideClick from 'utils/use-outside-click';

import MapButton from 'components/ui/mapToolBar/MapButton';

import {
  MapDropBarStyle,
  mapDropdownItemButton,
  MapDropdownListStyle,
  MapDropdownPopoverStyle,
} from './styles';
import { MapDropdownItem, MapDropdownOption } from './MapDropdownItem';
import {
  MapDropdownSection,
  MapDropdownSectionItem,
} from './MapDropdownSectionItem';

export type MapDropdownProps = {
  disabled?: boolean;
  options: (MapDropdownOption | MapDropdownSection)[];
  label: string;
  onToggle?: (isOpened: boolean) => void;
  onChange: (id: string) => void;
  value: string | null;
  prefix?: string;
};
const MapDropdown: FC<MapDropdownProps> = ({
  disabled,
  options,
  label,
  onToggle,
  onChange,
  value,
  prefix,
}) => {
  const [expanded, setExpanded] = useState(false);
  const onInsideClick = useOutsideClick(() => setExpanded(false));

  return (
    <MapDropBarStyle
      onClickCapture={onInsideClick}
      className={expanded ? 'opened' : ''}
    >
      <MapButton
        className={mapDropdownItemButton}
        onClick={() => {
          setExpanded(!expanded);
          onToggle?.(!expanded);
        }}
        disabled={disabled}
        prefix={prefix}
        label={label}
      />

      <MapDropdownPopoverStyle>
        <MapDropdownListStyle>
          {options.map(option => {
            if ('type' in option && option.type === 'section') {
              return <MapDropdownSectionItem key={option.id} option={option} />;
            }

            return (
              <MapDropdownItem
                key={option.id}
                value={value}
                option={option}
                onClick={() => {
                  setExpanded(!expanded);
                  onToggle?.(!expanded);
                  onChange(option.id);
                }}
              />
            );
          })}
        </MapDropdownListStyle>
      </MapDropdownPopoverStyle>
    </MapDropBarStyle>
  );
};

export default MapDropdown;
