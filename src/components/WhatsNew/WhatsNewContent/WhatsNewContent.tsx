import React, { useCallback, useState } from 'react';
import { useSpring, useTrail, useTransition } from 'react-spring';
import usePrevious from 'utils/use-previous';

import { HistoryLinkEn, NewFeatureComponent } from 'constants/settings';
import { TranslateFunction } from 'utils/use-translate';
import logEvent from 'sagas/global/logEvent';

import {
  StyledBullet,
  StyledContainer,
  StyledPrevLink,
  styles,
} from 'components/WhatsNew/WhatsNewContent/WhatsNewContent.style';
import Icon from 'components/Icon';

interface WhatsNewContentProps {
  onClose: () => void;
  t: TranslateFunction;
  language: string;
  pages: NewFeatureComponent[];
}

const WhatsNewContent = ({
  onClose,
  t,
  language,
  pages,
}: WhatsNewContentProps) => {
  const [index, setIndex] = useState<number>(0);
  const [reverse, setReverse] = useState<boolean>(false);
  const prevIndex = usePrevious(index);

  const onClick = useCallback(
    (index: number) => {
      logEvent('whats_new_page_open', { page_index: index + 1 });
      setIndex(index);
      if (index > prevIndex) {
        setReverse(false);
      } else {
        setReverse(true);
      }
    },
    [prevIndex],
  );

  const transitions = useTransition([index], p => p, {
    from: {
      opacity: 0,
      transform: `translate3d(${reverse ? '-100%' : '100%'},0,0)`,
    },
    enter: { opacity: 1, transform: 'translate3d(0%,0,0)' },
    leave: {
      opacity: 0,
      transform: `translate3d(${reverse ? '50%' : '-50%'},0,0)`,
    },
    config: {
      tension: 400,
      friction: 30,
    },
  });

  const bulletsTrail = useTrail(pages.length, {
    from: { opacity: 0, transform: 'scale(1.5)' },
    to: { opacity: 1, transform: 'scale(1)' },
    delay: 1500,
  });

  const prevLinkTransition = useSpring({
    opacity: 1,
    transform: 'scale(1)',
    from: { opacity: 0, transform: 'scale(0.5)' },
    delay: 1000,
  });

  return (
    <StyledContainer id='whats-new-popup'>
      {pages?.length > 0 &&
        transitions.map(({ item, props, key }) => {
          const Page: NewFeatureComponent | null = pages?.[item] ?? null;
          return Page ? (
            <Page
              key={key}
              style={props}
              t={t}
              language={language}
              isLast={item === pages.length - 1}
              onClick={() => {
                item === pages.length - 1 ? onClose?.() : onClick?.(item + 1);
              }}
            />
          ) : null;
        })}
      <div className={styles.pageHeader}>
        <span
          className={styles.pageHeaderIcon}
          role='img'
          aria-label='hot news emoji'
        >
          💥
        </span>
        <div>
          <div className={styles.pageHeaderTitle}>{t('whatsnew.headline')}</div>
          <div className={styles.pageHeaderDate}>
            {t('whatsnew.release_date')}
          </div>
        </div>
      </div>
      <div className={styles.bullets}>
        {pages?.length > 1 &&
          bulletsTrail.map((props, i) => {
            return (
              <StyledBullet
                key={`bullet${i}`}
                onClick={() => onClick(i)}
                style={props}
                isActive={index === i}
              />
            );
          })}
      </div>
      <StyledPrevLink
        //@ts-ignore
        href={
          t('whatsnew.history_link', undefined, {
            //@ts-ignore
            missingTranslationMsg: '',
          }) || HistoryLinkEn
        }
        target={'_blank'}
        style={prevLinkTransition}
      >
        <Icon name='external-link' />
        <span>{t('whatsnew.history')}</span>
      </StyledPrevLink>
    </StyledContainer>
  );
};

export default WhatsNewContent;
