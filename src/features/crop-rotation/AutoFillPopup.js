import React, { useMemo, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { createSelector } from 'reselect';
import { useForm, useFieldArray } from 'react-hook-form';
import keyBy from 'lodash/keyBy';
import size from 'lodash/size';
import omit from 'lodash/omit';
import sumBy from 'lodash/sumBy';
import { cx } from 'linaria';

import api from 'modules/api';
import entities from 'modules/entities';
import fields from 'modules/fields';
import seasons from 'modules/seasons';
import settings from 'modules/settings';

import RenderAboveEverything from 'components/RenderAboveEverything';
import EnterExitTransition from 'components/EnterExitTransition';
import DocumentKeyHandler from 'components/DocumentKeyHandler';
import AutoFillWarningPopup from './AutoFillWarningPopup';
import TextInput from 'components/forms/TextInput';
import StackBar from 'components/ui/StackBar/StackBar';
import Icon from 'components/Icon';
import Button from 'components/ui/Button/Button';
import Checkbox from 'components/ui/Checkbox/Checkbox';
import TooMuchCropTooltip from './TooMuchCropTooltip';
import CropAreaRow from './CropAreaRow';

import logEvent from 'sagas/global/logEvent';

import fieldSeason from 'modules/fieldSeason';
import { convert } from 'utils/units';

import i18n from 'utils/i18n';
import { useTranslate } from 'utils/use-translate';
import { generateRandomColor } from 'utils/generate-random-color';

import {
  StyledModalAutofillContent,
  StyledColorPreview,
  styles,
} from './AutoFillPopup.style';

const getUniqueCrops = createSelector(
  state => entities.selectors.getAll(state, 'fieldCrop'),
  crops => {
    const { t } = i18n;
    const uniqueCrops = Object.keys(keyBy(crops, 'crop'));
    return uniqueCrops.sort((a, b) => {
      const titleA = t(`crop.${a}`);
      const titleB = t(`crop.${b}`);
      return titleA.localeCompare(titleB);
    });
  },
);

const getIndexed = entities.selectors.makeGetIndexed();
export const getTotalArea = createSelector(
  (state, seasonId) => getIndexed(state, 'fieldSeason', 'season_id', seasonId),
  fieldSeasons => sumBy(fieldSeasons, 'area'),
);
const AutoFillPopup = ({ seasonId, cropColors, onClose }) => {
  const { t } = useTranslate();
  const formatUnit = useSelector(settings.selectors.getUnitFormatter);
  const season = useSelector(state =>
    seasons.selectors.getByID(state, seasonId),
  );
  const [selectedCrops, setSelectedCrops] = useState({});
  const [searchQuery, setSearchQuery] = useState('');
  const [showAutoFillWarning, setShowAutoFillWarning] = useState(false);
  const [assignedColors, setAssignedColors] = useState({});

  const units = useSelector(settings.selectors.getUnits);
  const recentCrops = useSelector(getUniqueCrops);
  const getIndexed = useMemo(() => entities.selectors.makeGetIndexed(), []);
  const fieldSeasonCount = useSelector(
    state => getIndexed(state, 'fieldSeason', 'season_id', seasonId).length,
  );
  const sortedCropTypes = useSelector(fields.selectors.getSortedCropTypes);
  const cropsInSeason = useSelector(state =>
    fieldSeason.selectors.getCropStatistics(state, seasonId),
  );

  const getColorForCrop = crop => {
    if (cropColors[crop]) {
      return cropColors[crop];
    }

    if (assignedColors[crop]) {
      return assignedColors[crop];
    }

    const newColor = generateRandomColor();

    setAssignedColors(prev => ({
      ...prev,
      [crop]: newColor,
    }));

    return newColor;
  };

  const dispatch = useDispatch();

  const formMethods = useForm({
    defaultValues: { crops: [] },
    shouldUnregister: false,
  });

  const crops = useFieldArray({
    control: formMethods.control,
    keyName: 'field_id',
    name: 'crops',
  });

  const watchedCrops = formMethods.watch('crops');
  const filledArea = sumBy(watchedCrops, 'area');
  const totalArea = useSelector(state => getTotalArea(state, seasonId));

  const convertedTotalArea = convert(totalArea).from('ha').to(units.area.unit);

  const selectedCropCount = size(crops.fields);
  const canContinue = selectedCropCount > 0;
  const cropCountValid = selectedCropCount < fieldSeasonCount;
  const filledAreaValid = filledArea > 0 && filledArea <= convertedTotalArea;

  const toggleCrop = crop => {
    setSelectedCrops(currentCrops => {
      if (currentCrops[crop]) {
        const item = crops.fields.find(field => field.crop === crop);
        const index = crops.fields.indexOf(item);
        crops.remove(index);
        return omit(currentCrops, crop);
      } else {
        crops.append({
          crop: crop,
          area: 0,
          color: getColorForCrop(crop),
        });
        return { ...currentCrops, [crop]: true };
      }
    });
  };

  let cropSections = [];
  if (recentCrops.length !== 0) {
    cropSections.push({
      id: 'recent',
      title: t('croprotation.crops.sections.recent'),
      crops: recentCrops,
      colors: recentCrops.map(crop => getColorForCrop(crop)),
    });
  }

  cropSections.push({
    id: 'all',
    title: t('croprotation.crops.sections.all'),
    crops: sortedCropTypes,
    colors: sortedCropTypes.map(crop => getColorForCrop(crop)),
  });

  if (searchQuery.trim()) {
    const query = searchQuery.trim().toLowerCase();
    const filteredCrops = sortedCropTypes.filter(crop =>
      t(`crop.${crop}`).toLowerCase().includes(query),
    );

    cropSections = [
      {
        id: 'search-results',
        crops: filteredCrops,
        colors: filteredCrops.map(crop => getColorForCrop(crop)),
      },
    ];
  }

  const autoFillRequest = useSelector(state =>
    api.selectors.getRequestStatus(state, 'auto-fill-crops'),
  );

  const onSubmit = form => {
    if (cropsInSeason.length) {
      setShowAutoFillWarning(true);
    } else {
      const { crops } = form;
      const convertedCrops =
        units.area.unit === 'ac'
          ? watchedCrops.map(c => ({
              ...c,
              area: convert(c.area).from(units.area.unit).to('ha'),
            }))
          : watchedCrops;
      dispatch(fields.actions.autoFillCrops(seasonId, convertedCrops));

      logEvent('crop_rotation_autofill_click', {
        has_current_crops: crops.length > 0,
        has_prev_crops: cropsInSeason.length > 0,
        season_id: showAutoFillWarning,
      });

      onClose();
    }
  };

  return (
    <RenderAboveEverything>
      <EnterExitTransition variant='popup'>
        {showAutoFillWarning && (
          <AutoFillWarningPopup
            season={season}
            onConfirm={warnings => {
              logEvent('crop_rotation_autofill_confirm', {
                season_id: +seasonId,
                crops: watchedCrops,
              });

              const crops =
                units.area.unit === 'ac'
                  ? watchedCrops.map(c => ({
                      ...c,
                      area: convert(c.area).from(units.area.unit).to('ha'),
                    }))
                  : watchedCrops;

              dispatch(fields.actions.autoFillCrops(seasonId, crops));
              onClose();
            }}
            onCancel={() => {
              setShowAutoFillWarning(false);
            }}
          />
        )}
      </EnterExitTransition>
      <DocumentKeyHandler hotkey='Escape' onPress={() => onClose()} />
      <div className='modal modal-autofill'>
        <div className={cx('modal-outer', styles.modalOuter)}>
          <StyledModalAutofillContent>
            <Button className={styles.closeButton} onClick={onClose}>
              <Icon name={'close'} color={'#ffffff'} />
            </Button>
            <div className={styles.autoFillSidebar}>
              <div className={styles.searchInput}>
                <TextInput
                  size='small'
                  placeholder={t(
                    'croprotation.autofill.crop_search_placeholder',
                  )}
                  prefixIcon={
                    <Icon
                      color={'#A5B2BC'}
                      width={15}
                      height={15}
                      name={'search'}
                    />
                  }
                  clearable
                  value={searchQuery}
                  onChange={event => {
                    setSearchQuery(event.target.value);
                  }}
                />
              </div>
              <div className={styles.cropsListWrapper}>
                {cropSections.map(section => (
                  <div key={section.id} className={styles.cropsListSection}>
                    <div className={styles.cropsListSectionHeader}>
                      {section.title}
                    </div>
                    <ul className={styles.cropsList}>
                      {section.crops.map((crop, index) => (
                        <TooMuchCropTooltip
                          key={crop}
                          active={!selectedCrops[crop] && !cropCountValid}
                          offset={[15, 0]}
                          align='middle-right'
                          arrow='left'
                          animate='from-right'
                          renderTooltip={() => (
                            <p style={{ maxWidth: 200 }}>
                              {t('croprotation.autofill.too_many_crops', {
                                count: fieldSeasonCount,
                              })}
                            </p>
                          )}
                          renderTrigger={props => (
                            <li className={styles.cropsListItem} {...props}>
                              <Checkbox
                                oneLine
                                disabled={
                                  !selectedCrops[crop] && !cropCountValid
                                }
                                checked={!!selectedCrops[crop]}
                                onChange={() => toggleCrop(crop)}
                              >
                                <StyledColorPreview
                                  color={section.colors[index]}
                                />
                                {t(`crop.${crop}`)}
                              </Checkbox>
                            </li>
                          )}
                        />
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </div>
            <div className={styles.autofillBody}>
              {!canContinue && (
                <div className={styles.emptyArea}>
                  <div className={styles.emptyAreaTitle}>
                    {t('croprotation.autofill.popup.title')}
                  </div>
                  <div className={styles.emptyAreaAltTitle}>
                    {t('croprotation.autofill.popup.alt_title')}
                  </div>
                  <Icon
                    className={styles.autofillIntroArrow}
                    name={'autofill-intro-arrow'}
                  />
                </div>
              )}
              {canContinue && (
                <div className={styles.formArea}>
                  <form onSubmit={formMethods.handleSubmit(onSubmit)}>
                    <div className={styles.formHead}>
                      <div className={styles.formTitle}>
                        {t('croprotation.autofill.form.title')}
                      </div>
                      <div className={styles.formSeasonTitle}>
                        {season.title}
                      </div>
                      <div className={styles.stackBarHolder}>
                        <StackBar
                          data={watchedCrops}
                          total={convertedTotalArea}
                          valueKey={'area'}
                          t={t}
                          dynamic={true}
                          legendBottomText={t(
                            'croprotation.stats.title.label',
                            {
                              filledArea: `${filledArea} ${t(
                                `units.${units.area.unit}.short`,
                              )}`,
                              totalArea: formatUnit(totalArea, 'ha', 'area'),
                            },
                          )}
                        />
                      </div>
                    </div>
                    <div className={styles.areasList}>
                      {crops.fields.map((crop, index) => (
                        <CropAreaRow
                          key={crop.field_id}
                          text={t(`crop.${crop.crop}`)}
                          filledArea={filledArea}
                          totalArea={convertedTotalArea}
                          control={formMethods.control}
                          index={index}
                          onRemove={() => toggleCrop(crop.crop)}
                        />
                      ))}
                    </div>
                    <div className={styles.floatingButton}>
                      <Button
                        className='btn btn-success btn-lg'
                        disabled={!filledAreaValid}
                        type='submit'
                        pending={autoFillRequest.status === 'pending'}
                      >
                        <span className='btn__ico'>
                          <Icon className='ico-check-os' name='check' />
                        </span>
                        {t('croprotation.autofill.field_autofill')}
                      </Button>
                    </div>
                  </form>
                </div>
              )}
            </div>
          </StyledModalAutofillContent>
        </div>
      </div>
    </RenderAboveEverything>
  );
};

export default AutoFillPopup;
