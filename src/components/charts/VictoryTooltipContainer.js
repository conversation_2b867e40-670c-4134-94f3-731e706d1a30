import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { VictoryContainer } from 'victory';
import throttle from 'lodash/throttle';
import pick from 'lodash/pick';

const MouseMoveThrottle = 30;

const getMouseCoords = event => {
  const { top, left } = event.currentTarget.getBoundingClientRect();
  return {
    x: event.clientX - left,
    y: event.clientY - top,
  };
};

const extractData = children =>
  children.filter(child => child.props.data).map(child => child.props);

const findHoveredPoints = ({ charts, xValues }, nearValue) => {
  let hoveredValue = null;
  for (let i = 0; i < xValues.length; i++) {
    const min = i > 0 ? (xValues[i - 1] + xValues[i]) / 2 : xValues[i];
    const max =
      i < xValues.length - 1 ? (xValues[i + 1] + xValues[i]) / 2 : xValues[i];
    if (nearValue >= min && nearValue <= max) {
      hoveredValue = xValues[i];
      break;
    }
  }
  const points = [];
  for (const { name, data, x } of charts) {
    for (const point of data) {
      if (point[x] === hoveredValue) {
        points.push({ ...point, childName: name });
        break;
      }
    }
  }
  return points;
};

const mergeEvents = (a = {}, b = {}) => {
  const result = {};
  const eventKeys = { ...a, ...b };
  for (const key of Object.keys(eventKeys)) {
    if (!a[key] || !b[key]) {
      result[key] = a[key] || b[key];
      continue;
    }
    result[key] = (...props) => {
      a[key](...props);
      b[key](...props);
    };
  }
  return result;
};

const VictoryTooltipContainer = ({
  containerComponent = <VictoryContainer />,
  tooltipComponent,
  force,
  children,
  onPointsHovered,
  ...props
}) => {
  const [position, setPosition] = useState(null);
  const onMouseEnter = useCallback(e => {
    setPosition(getMouseCoords(e));
  }, []);

  const onMouseMove = throttle(e => {
    setPosition(getMouseCoords(e));
  }, MouseMoveThrottle);

  const onMouseLeave = useCallback(e => {
    setPosition(null);
  }, []);

  const index = useMemo(() => {
    const charts = extractData(children);
    const xValuesMap = {};
    for (const { data, x } of charts) {
      for (const point of data) {
        xValuesMap[point[x]] = true;
      }
    }
    return {
      charts,
      xValues: Object.keys(xValuesMap)
        .map(Number)
        .sort(),
    };
  }, [children]);

  let withinBounds =
    position &&
    position.x >= props.padding.left &&
    position.x <= props.width - props.padding.right &&
    position.y >= props.padding.top &&
    position.y <= props.height - props.padding.bottom;

  const hoveredValue = withinBounds && props.scale.x.invert(position.x);
  const showTooltip = withinBounds || force;
  const tooltipY =
    props.padding.top +
    (props.height - props.padding.top - props.padding.bottom) / 2;

  const points = showTooltip && findHoveredPoints(index, force || hoveredValue);
  const pointsKey = points ? points.map(p => p.date).join(',') : 'none';

  useEffect(() => {
    if (onPointsHovered) {
      onPointsHovered(points || []);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pointsKey]);

  return React.cloneElement(
    containerComponent,
    {
      ...props,
      events: mergeEvents(props.events, {
        onMouseEnter,
        onMouseMove,
        onMouseLeave,
      }),
    },
    children,
    points &&
      points.length !== 0 &&
      React.cloneElement(tooltipComponent, {
        ...pick(props, 'padding', 'scale', 'width', 'height'),
        x: position ? position.x : props.scale.x(force),
        y: tooltipY,
        datum: points[0],
        points,
      }),
  );
};

export default VictoryTooltipContainer;
