import React, { CSSProperties } from 'react';

import Document<PERSON>eyHandler from 'components/DocumentKeyHandler';

import { SimpleTooltip } from 'components/ui/Tooltip/SimpleTooltip/SimpleTooltip';

interface CropRotationTooltipProps extends React.PropsWithChildren<{}> {
  style: CSSProperties;
  width?: number;
  minWidth?: number;
  onEscapeClickHandler: () => void;
}

export const CropRotationTooltip = React.forwardRef(
  (
    {
      style,
      width,
      minWidth,
      onEscapeClickHandler,
      children,
      ...props
    }: CropRotationTooltipProps,
    ref: React.ForwardedRef<HTMLDivElement>,
  ) => {
    return (
      <>
        <SimpleTooltip
          style={{
            ...(width !== null && width !== undefined ? { width } : {}),
            ...(minWidth !== null && minWidth !== undefined
              ? { minWidth }
              : {}),
            ...style,
          }}
          className={'overlap-tooltip crop-tooltip'}
          ref={ref}
          {...props}
        >
          <DocumentKeyHandler hotkey='Escape' onPress={onEscapeClickHandler} />
          {children}
        </SimpleTooltip>
      </>
    );
  },
);
