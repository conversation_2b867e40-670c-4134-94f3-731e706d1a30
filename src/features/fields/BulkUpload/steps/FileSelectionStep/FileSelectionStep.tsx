import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { FilesList } from 'features/fields/BulkUpload/components';
import { DropArea } from 'components/ui/DropArea';
import {
  ErrorText,
  UploadingTasksArea,
} from 'features/fields/BulkUpload/styles';
import { actions } from 'features/fields/BulkUpload/redux/actions';
import { selectors } from 'features/fields/BulkUpload/redux/selectors';
import { BulkUploadStep } from 'features/fields/BulkUpload/types';

import { getTotalSize, BULK_UPLOAD_MAX_TOTAL_SIZE } from 'utils/import-fields';
import { useTranslate } from 'utils/use-translate';

// @ts-ignore
import { convertBytes } from 'utils/units';

import { FilesListWrapper } from './styles';

export const FileSelectionStep = () => {
  const { t } = useTranslate();
  const dispatch = useDispatch();
  const selectedFiles: File[] = useSelector(selectors.getFiles);

  function handleLoadFiles(files: File[]) {
    const updatedFiles = [...selectedFiles, ...files];
    dispatch(actions.resetBulkUploadState());
    dispatch(actions.setFiles(updatedFiles));
    dispatch(actions.setReachedStep(BulkUploadStep.FILE_SELECTION));
  }

  function handleFileDelete(index: Number) {
    const updatedFiles = selectedFiles.filter((file, i) => i !== index);
    dispatch(actions.resetBulkUploadState());
    dispatch(actions.setFiles(updatedFiles));
    dispatch(actions.setReachedStep(BulkUploadStep.FILE_SELECTION));
  }

  function renderDropAreaDescription() {
    const totalSize = getTotalSize(selectedFiles);

    if (totalSize > BULK_UPLOAD_MAX_TOTAL_SIZE) {
      return (
        <ErrorText>
          {`${convertBytes(totalSize, t)} / ${convertBytes(
            BULK_UPLOAD_MAX_TOTAL_SIZE,
            t,
          )}`}
        </ErrorText>
      );
    }

    return `${convertBytes(BULK_UPLOAD_MAX_TOTAL_SIZE, t)} ${t(
      'fields.bulk_upload.max_total_files_size',
    )}`;
  }

  return (
    <UploadingTasksArea>
      <DropArea
        title={t('fields.bulk_upload.drag_n_drop_title')}
        description={renderDropAreaDescription()}
        buttonLabel={t('fields.bulk_upload.drag_n_drop_button_label')}
        onLoadFiles={handleLoadFiles}
      />
      {Boolean(selectedFiles.length) && (
        <FilesListWrapper>
          <FilesList files={selectedFiles} onFileDelete={handleFileDelete} />
        </FilesListWrapper>
      )}
    </UploadingTasksArea>
  );
};
