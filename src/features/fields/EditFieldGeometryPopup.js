import React, { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Form, getModel, actions as formActions } from 'react-redux-form';
import area from '@turf/area';

import Saga from 'components/saga/Saga';
import RRFFloatingFormActions from 'components/ui/FloatingActions/RRFFloatingFormActions';
import RenderAboveEverything from 'components/RenderAboveEverything';
import ToastContainer from 'features/platform/ToastContainer/ToastContainer';
import EmbeddedMap from 'features/platform/EmbeddedMap';
import EditFieldGeometryToolbar from './EditFieldGeometryToolbar';
import EditFieldLayer from './EditFieldLayer';

import fields from 'modules/fields';
import seasons from 'modules/seasons';
import settings from 'modules/settings';
import toasts from 'modules/toasts';

import forms from 'constants/forms';
import { formatTitle } from 'utils/fields-formatters';
import { formatTitle as formatSeasonTitle } from 'utils/seasons-formatters';
import { getViewportHash } from 'utils/get-root-scouting-url';
import { useTranslate } from 'utils/use-translate';
import { hasIntersections } from 'utils/field-has-intersection';
import editFieldGeometrySaga, {
  MaxFieldArea,
} from 'sagas/local/edit-field-geometry';
import { VegetationFieldToolbar } from './ToolBar/VegetationFieldToolbar';
import SelectedFieldScouting from '../scouting/SelectedFieldScouting/SelectedFieldScouting';
import ZIndex from '../../constants/zindex';
import { Pane } from 'react-leaflet';

const EditFieldGeometryPopup = ({ location, field, onClose }) => {
  const { t } = useTranslate();
  const formatUnit = useSelector(settings.selectors.getUnitFormatter);
  const dispatch = useDispatch();

  const geometry = useSelector(state =>
    getModel(state, `${forms.fieldGeometry}.geometry`),
  );

  const hasSelfIntersection = hasIntersections(geometry);
  const fieldArea = geometry && area(geometry);
  const hasBigArea = fieldArea >= MaxFieldArea;

  const currentSeason = useSelector(seasons.selectors.getCurrent);

  const [mode, setMode] = useState(null);
  const [hasSelected, setHasSelected] = useState(false);
  const [changes, setChanges] = useState([]);

  const geometryEditor = useRef();

  let title = formatTitle(t, field);
  if (geometry) {
    title += `, ≈ ${formatUnit(fieldArea, 'm2', 'area')}`;
  }

  useEffect(() => {
    if (hasBigArea) {
      dispatch(
        toasts.actions.push(
          'field-editor',
          t('toasts.fields.too_large', {
            area: formatUnit(MaxFieldArea, 'm2', 'area'),
          }),
          {
            type: 'error',
            ttl: Infinity,
          },
        ),
      );
    } else if (hasSelfIntersection) {
      dispatch(
        toasts.actions.push(
          'field-editor',
          t(`fields.edit.geometry.has_kinks`),
          {
            type: 'error',
            ttl: Infinity,
          },
        ),
      );
    } else if (mode) {
      dispatch(
        toasts.actions.push(
          'field-editor',
          t(`fields.edit.geometry.${mode}.toast`),
          {
            type: 'success',
            ttl: Infinity,
          },
        ),
      );
    }
    if (geometry === null) {
      dispatch(
        toasts.actions.push(
          'field-editor',
          t(`fields.edit.geometry.no_geometry_toast`),
          {
            type: 'error',
            ttl: Infinity,
          },
        ),
      );
    }

    return () => {
      dispatch(toasts.actions.removeGroup('field-editor'));
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [mode, !!geometry, hasBigArea, hasSelfIntersection, dispatch]);

  return (
    <RenderAboveEverything>
      <Saga saga={editFieldGeometrySaga} field={field} onDone={onClose} />
      <Form
        model={forms.fieldGeometry}
        onSubmit={form => {
          dispatch(
            fields.actions.update({
              field: {
                ...field,
                geometry: form.geometry,
                crops: field.all_crops,
              },
              seasonId: currentSeason.id,
              model: forms.fieldGeometry,
              origin: field,
            }),
          );
        }}
      />
      <div className='popup-edit-bound __opened'>
        <ToastContainer id='global' />
        <div className='popup-edit-bound__content'>
          <div className='popup-edit-bound-inner-wrapper'>
            <div className='popup-edit-bound-inner'>
              <VegetationFieldToolbar
                fieldId={field.id}
                className='popup-edit-bound-container__content'
              />
            </div>
            <div className='field-geometry-toolbar-wrapper'>
              <EditFieldGeometryToolbar
                cutMode={mode === 'cut'}
                selectMode={mode === 'select'}
                hasSelected={mode === 'select' && hasSelected}
                canUndo={changes.length !== 0}
                onCutModeChange={cutMode => {
                  setMode(cutMode ? 'cut' : null);
                }}
                onSelectModeChange={selectMode => {
                  setMode(selectMode ? 'select' : null);
                }}
                onRemoveSelected={() => {
                  geometryEditor.current.removeSelectedMarkers();
                }}
                onUndo={() => {
                  const geometry = changes[changes.length - 1];
                  setChanges(changes.slice(0, -1));
                  dispatch(
                    formActions.change(
                      `${forms.fieldGeometry}.geometry`,
                      geometry,
                    ),
                  );
                }}
              />
            </div>
          </div>
          <div className='popup-edit-bound__map'>
            <EmbeddedMap
              showControls
              id='edit-field'
              initialViewport={getViewportHash(location.pathname)}
            >
              <SelectedFieldScouting fieldId={field.id} hideBorders />
              <Pane
                className='ndvi-pane'
                style={{ zIndex: ZIndex.SelectionPane + 100 }}
              >
                <EditFieldLayer
                  ref={geometryEditor}
                  editMode={mode}
                  geometry={geometry}
                  hasErrors={hasBigArea || hasSelfIntersection}
                  onSelectionChange={count => {
                    setHasSelected(count !== 0);
                  }}
                  onChange={nextGeometry => {
                    setChanges([...changes, geometry]);
                    dispatch(
                      formActions.change(
                        `${forms.fieldGeometry}.geometry`,
                        nextGeometry,
                      ),
                    );
                  }}
                />
              </Pane>
            </EmbeddedMap>
          </div>
        </div>
        <RRFFloatingFormActions
          model={forms.fieldGeometry}
          title={t('fields.edit.geometry.action', {
            season: formatSeasonTitle(t, currentSeason),
          })}
          entityName={title}
          submitLabel={t('forms.save')}
          cancelLabel={t('forms.cancel')}
          submitDisabled={!geometry || hasBigArea || hasSelfIntersection}
          onCancel={onClose}
        />
      </div>
    </RenderAboveEverything>
  );
};

export default EditFieldGeometryPopup;
