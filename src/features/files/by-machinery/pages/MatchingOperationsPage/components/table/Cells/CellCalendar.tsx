import React, { useState, VFC } from 'react';
import moment from 'moment';
import { styled } from 'linaria/react';

import DateInput from 'components/ui/DateInput/DateInput';
import { useTranslate } from 'utils/use-translate';
import { dateFormatFactory } from 'utils/dates/dateFormatFactory';

import { OnSeSetDateType } from 'features/files/by-machinery/pages/MatchingOperationsPage/service/useSetDate';
import { ApiDataFieldOperations } from 'features/files/by-machinery/types/server';

import { CellStyle } from './styles';
import { getOperationsRange } from 'features/files/by-machinery/utils/getOperationsRange';

const CellTextStyle = styled.span`
  padding-right: 30px;
`;

export type CellCalendarProps = {
  hovered: boolean;
  setDate: OnSeSetDateType;
  operations: ApiDataFieldOperations[];
};

export const CellCalendar: VFC<CellCalendarProps> = ({
  hovered,
  setDate,
  operations,
}) => {
  const { t } = useTranslate();
  const dateFormat = dateFormatFactory(t('seasons.edit.date_format') as string);
  const [opened, setOpened] = useState<boolean>(false);

  const range = getOperationsRange(operations, dateFormat);

  const content =
    range.length === 0
      ? t('import.matching_operations.select_date')
      : range.join(' - ');

  if (!hovered && !opened) {
    return (
      <CellStyle hovered={false}>
        <CellTextStyle>{content}</CellTextStyle>
      </CellStyle>
    );
  }

  return (
    <CellStyle hovered={hovered}>
      <DateInput
        noIcon
        value={moment()}
        onChange={value => {
          setDate(operations, value);
        }}
        placeholder=''
        calendarProps={{
          openToDate: moment(),
          visibilitySelected: false,
        }}
        onVisibilityToggle={setOpened}
        renderValue={() => content}
      />
    </CellStyle>
  );
};
