import { MutableRefObject, useCallback } from 'react';
import { useDispatch } from 'react-redux';

import entities from 'modules/entities';
import {
  MetaAttributesApi,
  MetaPropertiesGroups,
} from 'features/files/by-machinery/types';
import { DataStoreType } from 'features/files/by-machinery/pages/MatchingAttributesPage/service/useDataStore';
import { UpdatePropertyType } from 'modules/entities/actions';

export const getSetPropertyRequestName = (
  attributeName: string,
  layerUuid: string,
) => `updateProperty/${attributeName}/${layerUuid}`;

export type OnSetPropertyType = (
  newPropertyName: string | null,
  attributeName: string,
  layerUuid: string,
) => void;

export const useSetAttribute = (
  dataStore: MutableRefObject<DataStoreType>,
): OnSetPropertyType => {
  const dispatch = useDispatch();

  return useCallback(
    (
      newPropertyName: string | null,
      attributeName: string,
      layerUuid: string,
    ) => {
      const attributes: MetaAttributesApi[] = dataStore.current.meta
        ? Object.values<MetaPropertiesGroups>(dataStore.current.meta.groups)
            .map(v => v.properties)
            .flat(1)
        : [];

      const attribute: MetaAttributesApi = attributes.find(
        item => item.name === attributeName,
      ) as MetaAttributesApi;

      const customAttribute = !attribute
        ? { name: attributeName, units: [] }
        : null;

      const dataLayersForUpdate = dataStore.current.dataLayers.filter(item =>
        dataStore.current.layersLinks[layerUuid]?.includes(item.uuid),
      );

      const allProperties = dataLayersForUpdate
        .map(item => item.properties)
        .flat(1);

      const propertyWithThisAttribute = allProperties.filter(
        property => property.target_name === attributeName,
      );

      const requests: UpdatePropertyType[] = [];
      if (propertyWithThisAttribute.length > 0) {
        requests.push({
          payload: {
            target_name: null,
            unit: null,
            is_custom_target_name: false,
            extra_fields: null,
          },
          properties: propertyWithThisAttribute.map(v => v.uuid),
        });
      }

      const propertyForAttribute = allProperties.filter(property => {
        return property.name === newPropertyName;
      });

      if (propertyForAttribute.length > 0) {
        if (customAttribute) {
          requests.push({
            payload: {
              target_name: customAttribute.name,
              is_custom_target_name: true,
            },
            properties: propertyForAttribute.map(v => v.uuid),
          });
        } else {
          requests.push({
            payload: {
              target_name: attribute.name,
              is_custom_target_name: false,
              unit: attribute.units?.[0] || null,
              extra_fields: null,
            },
            properties: propertyForAttribute.map(v => v.uuid),
          });
        }
      }

      dispatch(entities.actions.updateFieldsDataLayerProperty(requests));
    },
    [dataStore, dispatch],
  );
};
