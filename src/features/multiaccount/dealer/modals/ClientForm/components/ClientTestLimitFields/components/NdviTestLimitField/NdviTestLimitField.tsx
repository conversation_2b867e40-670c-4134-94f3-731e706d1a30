// import React, { forwardRef } from 'react';
// import { useSelector } from 'react-redux';

// import { type UnitSystemType } from 'types/units';
// import {
//   type BaseTestLimitFieldProps,
//   BaseTestLimitField,
// } from 'features/multiaccount/dealer/modals/ClientForm/components/ClientTestLimitFields/components/BaseTestLimitField';
// import { MAX_LIMIT } from 'features/multiaccount/dealer/modals/ClientForm/components/ClientTestLimitFields/constants';

// import settings from 'modules/settings';
// import { useTranslate } from 'utils/use-translate';

// export type NdviTestLimitFieldProps = Omit<
//   BaseTestLimitFieldProps,
//   | 'errorKey'
//   | 'fieldName'
//   | 'iconName'
//   | 'maxLimit'
//   | 'subtitleKey'
//   | 'titleKey'
//   | 'usedLimitValue'
// >;

// const NdviTestLimitField = forwardRef<
//   HTMLInputElement,
//   NdviTestLimitFieldProps
// >((props, ref) => {
//   const { t } = useTranslate();
//   const units: UnitSystemType = useSelector(settings.selectors.getUnits);

//   return (
//     <BaseTestLimitField
//       {...props}
//       errorKey='multiaccount.dealer.error.ndvi'
//       fieldName='ndvi_test_limits'
//       iconName='plant'
//       maxLimit={MAX_LIMIT.NDVI as number}
//       ref={ref}
//       suffixText={t(`units.${units.area.unit}.short`)}
//       titleKey='multiaccount.invite-client-modal.column.ndvi-test-limits.title'
//       usedLimitValue={props.client?.usedTestLimit || 0}
//     />
//   );
// });

// export default NdviTestLimitField;
export {};
