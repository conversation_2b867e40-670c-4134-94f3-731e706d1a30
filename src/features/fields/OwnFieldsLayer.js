import React, { memo, useMemo, useCallback } from 'react';
import { GeoJSON, Pane } from 'react-leaflet';
import { useSelector, useDispatch } from 'react-redux';
import L from 'leaflet';

import logEvent from 'sagas/global/logEvent';

import SelectedFieldScouting from 'features/scouting/SelectedFieldScouting/SelectedFieldScouting';
import OwnFieldsNDVILayer, {
  NDVIMapFillings,
  RenderFromZoomLevel,
} from './OwnFieldsNDVILayer';

import map from 'modules/map';
import fields from 'modules/fields';
import auth from 'modules/auth';
import seasons from 'modules/seasons';

import ZIndex from 'constants/zindex';
import { colors } from 'constants/style';

import getRootScoutingUrl from 'utils/get-root-scouting-url';
import { isLocationEqual } from 'utils/location-urls';

export const DefaultStyle = {
  color: colors.defaultBorder,
  opacity: 1,
  fillColor: colors.transparent,
  fillOpacity: 0,
  weight: 2,
};

const HiddenStyle = {
  color: colors.transparent,
  opacity: 0,
  fillColor: colors.transparent,
  fillOpacity: 0,
  weight: 0,
};

const AddedStyle = {
  color: colors.defaultBorder,
  opacity: 1,
  fillColor: colors.defaultBorder,
  fillOpacity: 0.25,
  weight: 2,
};

const SelectedStyle = {
  color: colors.selectedBorder,
  opacity: 1,
  fillColor: colors.transparent,
  fillOpacity: 0.75,
  weight: 4,
};

const getFieldStyle = (
  location,
  featureId,
  fieldId,
  highlightedId,
  fieldStyle,
  hideFilling,
) => {
  if (location.endsWith('/edit') && fieldId === featureId) {
    return HiddenStyle;
  }

  if (location.endsWith('/add/select') || location.endsWith('/add/create')) {
    return AddedStyle;
  }

  // selected field
  if (fieldId === featureId) {
    return SelectedStyle;
  }

  // filled field
  if (fieldStyle && fieldStyle.visible) {
    let {
      fillColor = colors.transparent,
      color = colors.defaultBorder,
      fillOpacity = 1,
      opacity = 1,
      weight = 2,
    } = fieldStyle;

    // Make a hole for ndvi images
    if (hideFilling && fieldStyle.hasFilling) {
      fillColor = colors.transparent;
      color = colors.defaultBorder;
      weight = 2;
    }

    if (highlightedId === featureId) {
      color = colors.selectedBorder;
      weight = 4;
    }

    return { fillOpacity, opacity, fillColor, color, weight };
  }

  return DefaultStyle;
};
const getShouldShowNDVILayer = (location, mapFilling) => {
  if (location.endsWith('/edit')) {
    return false;
  }
  if (location.endsWith('/add/select') || location.endsWith('/add/create')) {
    return false;
  }
  if (NDVIMapFillings.includes(mapFilling)) {
    return true;
  }
  return false;
};

const OwnFieldsLayer = ({ location, match, history }) => {
  const ownLayer = useSelector(fields.selectors.getOwnLayer);
  const highlightedId = useSelector(fields.selectors.getHighlightedID);
  const hoveredInListId = useSelector(fields.selectors.getHoveredInListId);
  const locked = useSelector(map.selectors.isNavigationLocked);
  const mapFilling = useSelector(map.selectors.getMapFilling);
  const mapData = useSelector(map.selectors.getMapData);
  const hideFilling = useSelector(
    state =>
      NDVIMapFillings.includes(map.selectors.getMapFilling(state)) &&
      map.selectors.getZoomLevel(state) >= RenderFromZoomLevel,
  );
  const fieldFillingType = useSelector(fields.selectors.getFieldFillingType);
  const userID = useSelector(auth.selectors.getUserID);
  const currentSeason = useSelector(seasons.selectors.getCurrent);

  const dispatch = useDispatch();

  const baseUrl = useMemo(
    () => getRootScoutingUrl(location.pathname),
    [location.pathname],
  );

  const isFieldSelected = useMemo(
    () => match.params.fieldId && !location.pathname.endsWith('/edit'),
    [location.pathname, match.params.fieldId],
  );

  const getStyle = useCallback(
    feature => {
      const fieldId = feature?.properties?.id;
      return getFieldStyle(
        location.pathname,
        fieldId,
        match.params?.fieldId,
        hoveredInListId || highlightedId,
        mapData[fieldId],
        hideFilling,
      );
    },
    [
      highlightedId,
      hoveredInListId,
      location.pathname,
      match.params,
      mapData,
      hideFilling,
    ],
  );
  const shouldShowNDVILayer = getShouldShowNDVILayer(
    location.pathname,
    mapFilling,
  );

  const onMouseOver = useCallback(
    mapEvent => {
      const { feature } = mapEvent.layer;
      dispatch(fields.actions.highlight(feature.properties.id));
    },
    [dispatch],
  );

  const onMouseOut = useCallback(() => {
    dispatch(fields.actions.highlight(null));
  }, [dispatch]);

  const onClick = useCallback(
    mapEvent => {
      if (locked) {
        return;
      }
      L.DomEvent.stopPropagation(mapEvent);
      const { feature } = mapEvent.layer;

      logEvent('field_open', {
        ndvi_type: fieldFillingType,
        field_user_season_id: feature.properties?.fieldUserSeasonId,
        user_id: userID,
        season_id: currentSeason?.id ?? null,
      });

      const replaceUrl = baseUrl.endsWith('/analysis')
        ? `${baseUrl}/fields/${feature.properties.id}/grid`
        : `${baseUrl}/${feature.properties.id}`;
      history.replace(replaceUrl, { scrollEnabled: true });
    },
    [history, locked, baseUrl, fieldFillingType, userID, currentSeason],
  );

  const geoData = useMemo(() => {
    if (fieldFillingType === 'operation') {
      return {
        ...ownLayer.geojson,
        features: ownLayer.geojson.features.filter(
          v => v.properties.id !== match.params.fieldId,
        ),
      };
    }

    return ownLayer.geojson;
  }, [ownLayer.geojson, match.params.fieldId, fieldFillingType]);

  return (
    <>
      {shouldShowNDVILayer && (
        <OwnFieldsNDVILayer fieldId={match.params.fieldId} />
      )}
      <Pane style={{ zIndex: ZIndex.FieldsPane }}>
        <GeoJSON
          key={ownLayer.key + geoData.features.length}
          data={geoData}
          style={getStyle}
          onMouseover={onMouseOver}
          onMouseout={onMouseOut}
          onClick={onClick}
        />
        {isFieldSelected && (
          <SelectedFieldScouting fieldId={match.params.fieldId} />
        )}
      </Pane>
    </>
  );
};

export default memo(OwnFieldsLayer, isLocationEqual);
