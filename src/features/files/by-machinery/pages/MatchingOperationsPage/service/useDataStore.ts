import { useRef } from 'react';

import { ApiDataFieldOperations } from '../../../types/server';
import { FieldEntityType } from 'types/fields';

export type DataStoreType = {
  operations: ApiDataFieldOperations[];
  fields: FieldEntityType[];
};

export const useDataStore = (data: DataStoreType) => {
  const dataStore = useRef<DataStoreType>(data);

  dataStore.current = data;

  return dataStore;
};
