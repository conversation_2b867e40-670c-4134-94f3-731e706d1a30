import { ReactNode } from 'react';
import { SortDirections } from 'constants/sortDirections';

export const ItemTypes = {
  ROW: 'row',
};

export interface ColumnInfo {
  key: string;
  styledInfo?: {
    width?: number;
    rightBorder?: boolean;
  };
  sorted?: Nullable<SortDirections>;
  columnsGroupName?: Nullable<string>;
  headerLabel?: ReactNode;
}
export interface TableRowProps {
  id: string;
  columnsInfo: ColumnInfo[];

  leftSideControl?: ({ hovered }: { hovered: boolean }) => ReactNode;
  children?: ({ hovered }: { hovered: boolean }) => ReactNode[];
  canDrag?: boolean;
  isChild?: boolean;
  parentId?: string | null;
  isLastInGroup?: boolean;
  isFirstInGroup?: boolean;
  isParent?: boolean;
  header?: boolean;
  dragPreview?: TEMPORARY_ANY;
  onCellClick?: (columnId: string) => void;
  onChildrenDrop?: ({
    parentId,
    childId,
  }: {
    parentId: string | null;
    childId: string;
  }) => void;
  style?: React.CSSProperties;
}
