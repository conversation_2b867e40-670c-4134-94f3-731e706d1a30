import React, { FC, useRef, useState, useEffect, useContext } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import { getEmptyImage } from 'react-dnd-html5-backend';
import { cx } from 'linaria';
import classNames from 'classnames';

import {
  tableRow,
  draggingRow,
  tableCell,
  tableCellDragDropHovered,
  SideControlElement,
  SideControl,
  tableRowLeftSideControl,
  SideControlHidden,
  moveFromGroupLine,
  SideControlHovered,
  hoveredRow,
  moveFromGroupLineTop,
  moveFromGroupLineBottom,
} from './TableRow.style';
import { TableRowProps, ItemTypes } from './types';
import { TableRowDragPreview } from 'components/ui/DragAndDropTable/TableRow/TableRowDragPreview/TableRowDragPreview';
import {
  LeftSideControlDrag,
  LeftSideControlDragProps,
} from 'components/ui/DragAndDropTable/TableRow/LeftSideControlDrag/LeftSideControlDrag';
import { TableHeaderCell } from './TableCell/TableHeaderCell';
import { TableRowCell } from './TableCell/TableRowCell';
import {
  SimpleTooltip,
  SimpleTooltipTheme,
} from '../../Tooltip/SimpleTooltip/SimpleTooltip';
import { PopoverDeprecatedAlign } from 'components/ui/Popover';
import { ModernTooltip } from '../../Tooltip/ModernTooltip/ModernTooltip';
import { useTranslate } from 'utils/use-translate';
import { DragAndDropTableContext } from '../DragAndDropTableContext';

export const TableRow: FC<TableRowProps> = ({
  id,
  columnsInfo,
  children,
  isChild,
  parentId,
  isLastInGroup,
  isFirstInGroup,
  isParent,
  leftSideControl = ({ hovered }: LeftSideControlDragProps) => (
    <LeftSideControlDrag hovered={hovered} />
  ),
  dragPreview = <TableRowDragPreview />,
  header,
  onCellClick,
  onChildrenDrop,
  style,
  canDrag = true,
}) => {
  const dragUuid = useContext(DragAndDropTableContext);
  const { t } = useTranslate();
  const ref = useRef<HTMLTableRowElement>(null);
  const [hovered, setHovered] = useState<boolean>(false);
  const [buildedData, setBuildedData] = useState<TEMPORARY_ANY[]>([]);

  const [dragBounds, setDragBounds] = useState<
    [
      { x: number | null; y: number | null },
      { x: number | null; y: number | null },
    ]
  >([
    { x: null, y: null },
    { x: null, y: null },
  ]);
  const [isCanMoveFromGroup, setIsCanMoveFromGroup] = useState<boolean>(false);
  const [tableParentElement, setTableParentElement] =
    useState<HTMLTableElement | null>(null);

  useEffect(() => {
    const buildedData = children?.({ hovered }) ?? [];
    setBuildedData(buildedData);
  }, [children, hovered]);

  useEffect(() => {
    let firstRowSideControlCell = null;
    let firstRowDataCell = null;
    for (let i = 0; i < (ref.current?.children?.length ?? 0); i++) {
      const cell = ref.current?.children?.[i] ?? null;
      if (!cell?.classList.contains(SideControl)) {
        if (!firstRowDataCell) {
          firstRowDataCell = cell;
        }
      } else {
        if (!firstRowSideControlCell) {
          firstRowSideControlCell = cell;
        }
      }
    }

    const controlCellBounds =
      firstRowSideControlCell?.getBoundingClientRect() ?? null;
    const topLeftPosition = controlCellBounds
      ? { x: controlCellBounds.x, y: controlCellBounds.y }
      : { x: null, y: null };

    const dataCellBounds = firstRowDataCell?.getBoundingClientRect() ?? null;
    const bottomRightPosition = dataCellBounds
      ? {
          x: dataCellBounds.x + dataCellBounds.width,
          y: dataCellBounds.y + dataCellBounds.height,
        }
      : { x: null, y: null };

    const bounds: [
      { x: number | null; y: number | null },
      { x: number | null; y: number | null },
    ] = [topLeftPosition, bottomRightPosition];
    setDragBounds(bounds);

    let parentTableElement: HTMLElement | null = ref.current;
    while (
      parentTableElement?.tagName !== 'TABLE' &&
      parentTableElement?.tagName !== 'BODY'
    ) {
      parentTableElement = parentTableElement?.parentElement ?? null;
    }
    if (parentTableElement?.tagName === 'TABLE') {
      setTableParentElement((parentTableElement as HTMLTableElement) ?? null);
    } else {
      setTableParentElement(null);
    }
  }, [buildedData]);

  const getMovingFromGroupStatus = (monitor: TEMPORARY_ANY) => {
    if (!ref.current) {
      return false;
    }
    if (!monitor.isOver()) {
      return false;
    }
    if (monitor.getItem().parentId !== parentId) {
      return false;
    }

    if (isLastInGroup) {
      return true;
    }

    if (isFirstInGroup) {
      return true;
    }

    return false;
  };

  const [{ isDragging }, drag, previewRef] = useDrag({
    type: ItemTypes.ROW + dragUuid,
    item: {
      dragPreview,
      id,
      parentId: parentId ?? null,
    },
    collect: monitor => ({
      isDragging: monitor.isDragging(),
    }),
    canDrag: monitor => {
      const initialCoords = monitor.getInitialClientOffset() ?? { x: 0, y: 0 };

      const isInsideDragBounds =
        initialCoords?.x >= (dragBounds[0].x ?? 0) &&
        initialCoords?.x <= (dragBounds[1].x ?? 0);

      return isInsideDragBounds && !header && canDrag;
    },
  });

  useEffect(() => {
    previewRef(getEmptyImage(), { captureDraggingState: true });
  }, [previewRef]);

  const [{ isOver }, drop] = useDrop<{}, {}, { isOver: boolean }>({
    accept: ItemTypes.ROW + dragUuid,
    collect: monitor => {
      return {
        id,
        isOver: !!monitor.isOver() && !isChild,
        parentId: parentId ?? null,
      };
    },
    canDrop: (_, monitor) =>
      !isChild ||
      (getMovingFromGroupStatus(monitor) &&
        (!!isLastInGroup || !!isFirstInGroup)),
    drop: (item: TEMPORARY_ANY, monitor) => {
      const isMovingFromGroup = getMovingFromGroupStatus(monitor);
      const parentId = isMovingFromGroup ? null : id ?? '';
      if (parentId !== item?.id) {
        onChildrenDrop?.({
          parentId: parentId,
          childId: item?.id ?? '',
        });
        if (isMovingFromGroup) {
          setIsCanMoveFromGroup(false);
        }
      }
      return {};
    },
    hover(_, monitor) {
      setIsCanMoveFromGroup(getMovingFromGroupStatus(monitor));
    },
  });

  drag(drop(ref));

  return (
    <tr
      ref={ref}
      onMouseOver={e => {
        // @ts-ignore
        if (ref.current?.contains(e?.target)) {
          setHovered(true);
        }
      }}
      onMouseLeave={e => {
        // @ts-ignore
        if (ref.current?.contains(e?.target)) {
          setHovered(false);
        }
      }}
      className={cx(
        tableRow,
        isDragging && draggingRow,
        hovered && !header && !isDragging && hoveredRow,
      )}
      style={style}
    >
      {isCanMoveFromGroup ? (
        <div
          className={classNames(
            moveFromGroupLine,
            isFirstInGroup && moveFromGroupLineTop,
            isLastInGroup && moveFromGroupLineBottom,
          )}
          style={{
            ...(tableParentElement !== null
              ? {
                  left:
                    tableParentElement?.clientLeft +
                    tableParentElement?.scrollLeft,
                  width: tableParentElement?.clientWidth,
                }
              : {}),
          }}
        />
      ) : null}

      <ModernTooltip
        active={!header && !isParent && !isDragging && hovered}
        theme={SimpleTooltipTheme.DARK}
        align={PopoverDeprecatedAlign.BottomLeft}
        offset={[0, 10]}
        // @ts-ignore
        renderTrigger={props => (
          <td
            className={cx(
              tableCell,
              SideControl,
              tableRowLeftSideControl,
              hovered && SideControlHovered,
              header && SideControlHidden,

              isOver && !header ? tableCellDragDropHovered : '',
            )}
            style={{ minHeight: '51px' }}
            {...props}
          >
            <div className={SideControlElement}>
              {leftSideControl({ hovered })}
            </div>
          </td>
        )}
        renderTooltip={() => (
          <div>
            <SimpleTooltip.Text>
              {t('import.matching_operations.drag.tooltip')}
            </SimpleTooltip.Text>
          </div>
        )}
      />

      {columnsInfo?.map((columnInfo, index) => {
        if (header) {
          return (
            <TableHeaderCell
              key={columnInfo.key}
              onCellClick={onCellClick}
              columnInfo={columnInfo}
            >
              {buildedData[index]}
            </TableHeaderCell>
          );
        }

        return (
          <TableRowCell
            key={columnInfo.key}
            columnInfo={columnInfo}
            hovered={hovered}
            isOver={isOver}
          >
            {buildedData[index]}
          </TableRowCell>
        );
      })}
    </tr>
  );
};
