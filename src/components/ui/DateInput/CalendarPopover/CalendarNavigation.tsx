import Button from '../../Button/Button';
import Icon from '../../../Icon';
import React, { useEffect, useRef, VFC } from 'react';
import { Calendar, RenderProps } from 'dayzed';

export type CalendarNavigationProps = {
  getBackProps: RenderProps['getBackProps'];
  getForwardProps: RenderProps['getForwardProps'];
  calendars: Calendar[];
  calendar: Calendar;

  onBackArrowClick: (month: number) => void;
  onNextArrowClick: (month: number) => void;
};

export const CalendarNavigation: VFC<CalendarNavigationProps> = ({
  getBackProps,
  getForwardProps,
  calendars,
  calendar,
  onBackArrowClick,
  onNextArrowClick,
}) => {
  const monthNav = useRef<HTMLDivElement>(null);
  useEffect(() => {
    if (!monthNav.current) {
      return;
    }
    monthNav.current.focus();
  }, []);

  return (
    <div
      ref={monthNav}
      className='modal-calendar-arrow'
      tabIndex={0}
      onKeyDown={event => {
        if (event.key === 'ArrowLeft') {
          getBackProps({ calendars }).onClick(event);
        }
        if (event.key === 'ArrowRight') {
          getForwardProps({ calendars }).onClick(event);
        }
      }}
    >
      <Button
        className='modal-calendar-arrow__item'
        {...getBackProps({ calendars })}
        onClick={event => {
          getBackProps({ calendars }).onClick(event);
          onBackArrowClick(calendar.month);
        }}
      >
        <Icon className='ico-chevron-left-os' name='chevron-left' />
      </Button>
      <Button
        className='modal-calendar-arrow__item'
        {...getForwardProps({ calendars })}
        onClick={event => {
          getForwardProps({ calendars }).onClick(event);
          onNextArrowClick(calendar.month);
        }}
      >
        <Icon className='ico-chevron-right-os' name='chevron-right' />
      </Button>
    </div>
  );
};
