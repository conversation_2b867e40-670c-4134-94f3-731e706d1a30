import { styled } from 'linaria/react';
import { css } from 'linaria';
import { animated } from 'react-spring';

type GridProps = {
  equalColumns?: boolean;
  columns: number;
  top?: string;
};

export const Skeleton = styled.div`
  position: relative;
  background-color: #e4e7ea;
  border-radius: 4px;
  height: 46px;
  width: 100%;

  &::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    transform: translateX(-100%);
    background-image: linear-gradient(
      90deg,
      #ffffff00 0%,
      #ffffff30 20%,
      #ffffff88 60%,
      #ffffff00 100%
    );
    animation: shimmer 2s infinite;
    content: '';
  }

  @keyframes shimmer {
    100% {
      transform: translateX(100%);
    }
  }
`;

export const unitSelectBoxWrapper = css`
  flex: 1;
  margin-left: 4px;
`;

export const CustomAttributeGroupStyle = styled.div<GridProps>`
  grid-column: 1 / ${({ columns }) => columns + 2};
  font-weight: 700;
  font-size: 16px;
  line-height: 22px;
  color: var(--color-primary);
`;

export const AttributeGroupStyle = styled.div<GridProps>`
  grid-column: 1 / ${({ columns }) => columns + 2};
  font-weight: 500;
  font-size: 14px;
  line-height: 18px;
  color: var(--color-primary);
`;

export const CellStyle = styled(animated.div)`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  overflow: hidden;
  margin: 0 0 10px;
`;

export const Delimiter = styled.div<GridProps>`
  grid-column: 1 / ${({ columns }) => columns + 2};
  border-bottom: rgba(165, 178, 188, 0.5) solid 1px;
  margin: ${({ top }) => (top ? `${top}px` : '24px')} 0 24px;
`;

export const MatchingTableStyle = styled.div<GridProps>`
  margin-top: 32px;
  align-self: flex-start;
  grid-template-columns: ${({ equalColumns, columns }) =>
    equalColumns
      ? `repeat(${columns + 1}, 300px)`
      : `206px repeat(${columns}, 414px)`};
  display: grid;
  grid-row-gap: 8px;
  grid-column-gap: ${({ equalColumns }) => (equalColumns ? '40px' : '20px')};

  & + & {
    margin-top: 0;
  }
`;

export const ColumnHeadCellStyle = styled.div`
  padding: 8px 0 0;
`;

type ExpandButtonProps = {
  open: boolean;
};

export const ExpandButtonStyle = styled.div<ExpandButtonProps>`
  font-weight: 500;
  font-size: 14px;
  line-height: 22px;
  color: #636366;
  cursor: pointer;
  position: relative;
  padding-right: 20px;

  &:after {
    content: ' ';
    position: absolute;
    top: 10px;
    right: 0;
    width: 0;
    height: 0;
    transform: rotate(${({ open }) => (open ? 0 : 180)}deg);
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 4px solid #636366;
    transition: 0.8s;

    display: inline-block;
  }
`;

export const addAttributeBtn = css`
  display: inline-block;
  font-size: 14px;
  font-weight: 500;
  color: #636366;
  margin-top: 20px;
  &:hover {
    color: #48484a;
  }
`;

export const ColumnTitleStyle = styled.div`
  font-weight: 700;
  font-size: 20px;
  line-height: 27px;
  color: #000000;
`;

export const ColumnSubTitleStyle = styled.div`
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  color: #5e5e5e;
`;

export const RowTitleStyle = styled.div`
  font-weight: 500;
  font-size: 16px;
  line-height: 22px;
  color: #222222;
  white-space: nowrap;
  gap: 5px;
  display: flex;
`;

export const CustomRowTitleStyle = styled.div`
  font-weight: 500;
  font-size: 16px;
  line-height: 22px;
  color: #222222;
  white-space: nowrap;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 5px;
`;

export const RowSubTitleStyle = styled.div`
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  color: #636366;
`;

export const attributeSelectBoxWrapper = css`
  flex: 2;
`;

export const maKebab = css`
  width: 32px;
  position: relative;
  display: flex;

  &:before {
    content: '';
    width: 32px;
    height: 32px;
    position: absolute;
    margin-top: -8px;
    margin-left: -8px;
    border-radius: 6px;
    transition: background-color 0.3s, opacity 0.3s, visibility 0.3s;
  }

  &:hover {
    &:before {
      background-color: #f5f7f9;
    }
  }
`;
