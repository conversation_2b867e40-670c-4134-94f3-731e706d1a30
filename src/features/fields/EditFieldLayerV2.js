import React, { useMemo } from 'react';
import { featureCollection, feature } from '@turf/helpers';

import GeometryEditor from './GeometryEditor';

const EditFieldStyle = hasErrors => ({
  color: hasErrors ? '#eb4e4e' : 'white',
  fillOpacity: 0.3,
  weight: 2,
});

const EditFieldLayer = (
  { geometry, hasErrors, onChange, ...otherProps },
  ref,
) => {
  const geojson = useMemo(() => featureCollection([feature(geometry)]), [
    geometry,
  ]);

  return (
    <GeometryEditor
      ref={ref}
      style={() => EditFieldStyle(hasErrors)}
      data={geojson}
      onUpdate={onChange}
      {...otherProps}
    />
  );
};

export default React.forwardRef(EditFieldLayer);
