import React, { Fragment, useMemo } from 'react';

import randomId from 'utils/random-id';

const Toggle = ({
  theme = 'light',
  boxes = false,
  options,
  idKey = 'id',
  titleKey,
  renderOption,
  value,
  onChange,
}) => {
  const id = useMemo(() => randomId(), []);
  return (
    <div className='form-tooglebox' data-theme={theme} data-boxes={boxes}>
      <div className='form-tooglebox__inner'>
        {options.map((option, index) => (
          <Fragment key={option[idKey]}>
            <input
              className='form-tooglebox__input'
              data-input={
                index === 0
                  ? 'first'
                  : index === options.length - 1
                  ? 'last'
                  : 'none'
              }
              checked={value === option[idKey]}
              id={`${id}-${index}`}
              type='radio'
              onChange={event => {
                if (!event.target.checked) {
                  return;
                }
                onChange(option.id, option);
              }}
            />
            <label className='form-tooglebox__item' htmlFor={`${id}-${index}`}>
              <span className='form-tooglebox__value'>
                {titleKey
                  ? option[titleKey]
                  : renderOption(option, index, options)}
              </span>
            </label>
          </Fragment>
        ))}
        {!boxes && <div className='form-tooglebox__bg' />}
      </div>
    </div>
  );
};

export default Toggle;
