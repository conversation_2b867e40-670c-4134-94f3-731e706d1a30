import React from 'react';
import { TransitionGroup, CSSTransition } from 'react-transition-group';

import { Variants } from './EnterExitTransition';

const CircularProgress = ({ interval, progress }) => (
  <TransitionGroup appear>
    <CSSTransition classNames={Variants.withExit} timeout={100}>
      <div className='progress-bar' data-sec={interval / 1000}>
        <div className='progress-bar-numbers'>
          <TransitionGroup component={null}>
            <CSSTransition
              key={progress}
              classNames={Variants.withExit}
              timeout={300}
            >
              <div className='progress-bar-numbers__count'>{progress}</div>
            </CSSTransition>
          </TransitionGroup>
        </div>
        <svg className='progress-bar__ring' width={24} height={24}>
          <circle
            className='progress-bar__circle'
            stroke='white'
            strokeWidth={2}
            fill='transparent'
            r={10.5}
            cx={12.5}
            cy={12}
          />
        </svg>
      </div>
    </CSSTransition>
  </TransitionGroup>
);

export default CircularProgress;
