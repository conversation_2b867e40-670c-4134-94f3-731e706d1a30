import React from 'react';
import cx from 'classnames';

import Icon from 'components/Icon';
import Button from 'components/ui/Button/Button';
import {
  ModernTooltip,
  ModernTooltipDirection,
} from 'components/ui/Tooltip/ModernTooltip/ModernTooltip';
import { PopoverDeprecatedAlign } from 'components/ui/Popover';
import { SimpleTooltipTheme } from 'components/ui/Tooltip/SimpleTooltip/SimpleTooltip';

import { useTranslate } from 'utils/use-translate';

const EditFieldGeometryToolbar = ({
  canUndo,
  cutMode,
  selectMode,
  hasSelected,
  onSelectModeChange,
  onRemoveSelected,
  onCutModeChange,
  onUndo,
}) => {
  const { t } = useTranslate();

  return (
    <div className='popup-edit-bound-tools'>
      <ul className='popup-edit-bound-tools-section'>
        {canUndo && (
          <li className='popup-edit-bound-tools-section__list-item'>
            <Button className='popup-edit-bound-tools__item' onClick={onUndo}>
              <span className='popup-edit-bound-tools__ico'>
                <Icon className='ico-revert-os' name='revert' />
              </span>
              {t('fields.edit.geometry.undo')}
            </Button>
          </li>
        )}
      </ul>
      <ul className='popup-edit-bound-tools-section'>
        <li className='popup-edit-bound-tools-section__list-item'>
          <ModernTooltip
            active
            offset={[0, 8]}
            align={PopoverDeprecatedAlign.BottomRight}
            animate={ModernTooltipDirection.FromTop}
            small
            theme={SimpleTooltipTheme.DARK}
            renderTooltip={() => t('fields.edit.geometry.select.tooltip')}
            renderTrigger={props => (
              <Button
                className={cx(
                  'popup-edit-bound-tools__item',
                  selectMode && '__selected',
                )}
                onClick={() => {
                  onSelectModeChange(!selectMode);
                }}
                {...props}
              >
                <span className='popup-edit-bound-tools__ico'>
                  <Icon className='ico-select-os' name='select' />
                </span>
                {t('fields.edit.geometry.select')}
              </Button>
            )}
          />
          {hasSelected && (
            <ul className='popup-edit-bound-tools-sub'>
              <li className='popup-edit-bound-tools-sub__list-item'>
                <Button
                  className='popup-edit-bound-tools__item'
                  onClick={onRemoveSelected}
                >
                  <span className='popup-edit-bound-tools__ico'>
                    <Icon
                      className='ico-trash-light-os'
                      name='trashcan-light'
                    />
                  </span>
                  {t('fields.edit.geometry.select.remove')}
                </Button>
              </li>
            </ul>
          )}
        </li>
        <li className='popup-edit-bound-tools-section__list-item'>
          <ModernTooltip
            active
            offset={[0, 8]}
            align={PopoverDeprecatedAlign.BottomRight}
            animate={ModernTooltipDirection.FromTop}
            small
            theme={SimpleTooltipTheme.DARK}
            renderTooltip={() => t('fields.edit.geometry.cut.tooltip')}
            renderTrigger={props => (
              <Button
                className={cx(
                  'popup-edit-bound-tools__item',
                  cutMode && '__selected',
                )}
                onClick={() => {
                  onCutModeChange(!cutMode);
                }}
                {...props}
              >
                <span className='popup-edit-bound-tools__ico'>
                  <Icon className='ico-cut-os' name='cut' />
                </span>
                {t('fields.edit.geometry.cut')}
              </Button>
            )}
          />
        </li>
      </ul>
    </div>
  );
};

export default EditFieldGeometryToolbar;
