import React, { VFC } from 'react';
import { useForm } from 'react-hook-form';

import { useTranslate } from 'utils/use-translate';
import Button from 'components/ui/Button/Button';
import Modal from 'components/ui/Modal/Modal';
// @ts-ignore
import TextInput from 'components/forms/TextInput';
import Icon from 'components/Icon';
import { CustomAttributeType } from '../MatchingCustomAttributesTable';

export type EditNameFormType = {
  name: string;
};

export type EditAttributeFormProps = {
  mode: string;
  defaultValue?: string | null;
  attributes: CustomAttributeType[];
  onClose: () => void;
  onSubmit: (data: EditNameFormType) => void;
};

export const EditAttributeForm: VFC<EditAttributeFormProps> = ({
  mode,
  defaultValue,
  attributes,
  onClose,
  onSubmit,
}) => {
  const { t } = useTranslate();
  const form = useForm<EditNameFormType>({
    mode: 'onChange',
    defaultValues: {
      name: defaultValue!,
    },
  });

  const {
    handleSubmit,
    register,
    formState: { errors, isDirty, isValid },
  } = form;

  return (
    <>
      <Modal.Head>
        {mode === 'add'
          ? t('import.by-machinery.modal.add-attribute.title')
          : t('import.by-machinery.modal.edit-attribute.title')}
      </Modal.Head>
      <Modal.Body>
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className='form-group'>
            <label className='form-label' htmlFor='name'>
              {t('import.by-machinery.modal.attribute.name')}
            </label>
            <TextInput
              tabIndex={1}
              id='name'
              name='name'
              autoComplete='off'
              type='text'
              ref={register({
                validate: {
                  empty: (v: string) =>
                    v.trim() === ''
                      ? (t(
                          'import.by-machinery.modal.attribute.error.required',
                        ) as string)
                      : true,
                  exists: (v: string) =>
                    !attributes
                      .map(attribute => attribute.name.toLowerCase())
                      .includes(v.trim().toLowerCase()) ||
                    (t(
                      'import.by-machinery.modal.attribute.error.exists',
                    ) as string),
                },
              })}
              isValid={!errors.name}
              clearable
              defaultValue={defaultValue}
              placeholder={t('import.by-machinery.modal.attribute.placeholder')}
            />
            {errors?.name && (
              <div className='error-message c-error'>{errors.name.message}</div>
            )}
          </div>
        </form>
      </Modal.Body>
      <Modal.Actions>
        <Button
          className='btn btn-primary btn-lg'
          onClick={() => {
            onClose();
          }}
        >
          {t('import.by-machinery.modal.attribute.cancel')}
        </Button>
        <Button
          className='btn btn-success btn-lg'
          disabled={!isDirty || !isValid}
          type='submit'
          onClick={handleSubmit(onSubmit)}
        >
          <span className='btn__ico'>
            <Icon className='ico-check-os' name='check' />
          </span>
          {mode === 'add'
            ? t('import.by-machinery.modal.attribute.confirm')
            : t('import.by-machinery.modal.attribute.edit.confirm')}
        </Button>
      </Modal.Actions>
    </>
  );
};
