import { css } from 'linaria';
import { styled } from 'linaria/react';

export const groupName = css`
  font-family: Graphik, sans-serif;
  font-weight: 500;
  font-size: 14px;
  color: #222222;
  line-height: 20px;
`;

export const formSection = css`
  padding: 10px 20px;
  &:first-child {
    padding-top: 20px;
  }
  &:last-child {
    padding-bottom: 20px;
  }
`;

export const StyledRadioGroupRow = styled.div`
  flex-wrap: nowrap;
  display: flex;
  justify-content: space-between;
  margin: 5px 0;
`;
