import React, { FC } from 'react';
import { SagaMiddleware } from '@redux-saga/core';

// @ts-ignore
export const SagaContext = React.createContext<SagaMiddleware<TEMPORARY_ANY>>();

type SagaProviderProps = {
  middleware: SagaMiddleware,
}

const SagaProvider: FC<SagaProviderProps> = ({ middleware, children }) => (
  <SagaContext.Provider value={middleware}>{children}</SagaContext.Provider>
);

export default SagaProvider;
