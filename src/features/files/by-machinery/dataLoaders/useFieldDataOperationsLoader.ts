import { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import entities from 'modules/entities';

import { selectors } from 'features/files/by-machinery/redux/selectors';
import {
  DataLoader,
  ApiDataFieldOperations,
} from 'features/files/by-machinery/types';
import RootState from 'types/rootState';

export const OPERATIONS_REQUEST_ID = 'useFieldDataOperationsLoader';

export const useFieldDataOperationsLoader = (): DataLoader<
  ApiDataFieldOperations[]
> => {
  const dispatch = useDispatch();
  const uuids = useSelector((state: RootState) =>
    selectors.getMatchingTasksUuid(state),
  );
  const uuidsStr = uuids.join(',');

  const fetch = useCallback(() => {
    if (!uuidsStr) {
      return;
    }
    dispatch(
      entities.actions.request(OPERATIONS_REQUEST_ID, [
        {
          type: 'fetchMany',
          entityType: 'fieldsDataOperation',
          query: {
            include: ['data_source.layer.properties'],
            'filter{data_source.layer.source.upload.in}': uuidsStr.split(','),
          },
        },
      ]),
    );
  }, [uuidsStr, dispatch]);

  const data: ApiDataFieldOperations[] = useSelector(
    selectors.getMatchingOperations,
  );
  const request = useSelector(state =>
    entities.selectors.getRequestStatus(state, OPERATIONS_REQUEST_ID),
  );

  useEffect(fetch, [fetch]);

  return [data, request, fetch];
};
