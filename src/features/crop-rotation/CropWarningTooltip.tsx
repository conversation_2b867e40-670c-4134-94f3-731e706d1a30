import React, { useState, useRef, VFC } from 'react';
import cx from 'classnames';
import { css } from 'linaria';

import {
  PopoverDeprecated,
  PopoverDeprecatedAlign,
} from 'components/ui/Popover';
import { SimpleTooltip } from 'components/ui/Tooltip/SimpleTooltip/SimpleTooltip';
import Icon from 'components/Icon';

import { useTranslate } from 'utils/use-translate';
import { styles } from './CropRotationView/CropRotationView.style';

const CloseDelay = 50;

const warningTooltip = css`
  background-color: #ff3b30;
  padding: 5px 10px;
  font-size: 12px;
  font-weight: 500;
  line-height: 16px;
  color: #ffffff;
`;

type CropWarningTooltipProps = {
  inline?: boolean;
  recommendation: TEMPORARY_ANY;
};

const CropWarningTooltip: VFC<CropWarningTooltipProps> = ({
  inline,
  recommendation,
}) => {
  const { t } = useTranslate();

  const [hovered, setHovered] = useState<boolean>(false);
  const timer = useRef<number>();

  const onMouseEnter = () => {
    clearTimeout(timer.current);
    setHovered(true);
  };
  const onMouseLeave = () => {
    clearTimeout(timer.current);
    // @ts-ignore TS incorrectly infers timer's type assuming we're in NodeJs environment
    timer.current = setTimeout(setHovered, CloseDelay, false);
  };

  const recomendationText = t('croprotation.recommendation.crop');
  // @ts-ignore
  const prevCrop = t(`crop.${recommendation?.crop_prev}`).toLowerCase();

  return (
    <PopoverDeprecated
      active={hovered}
      offset={[0, 5]}
      align={PopoverDeprecatedAlign.BottomLeft}
      transitionVariant='legacy'
      renderTrigger={({ ref, ...otherProps }) => (
        <div
          ref={ref}
          className={cx(
            styles.attentionIconContainer,
            inline && styles.attentionIconContainerInline,
          )}
          onMouseEnter={onMouseEnter}
          onMouseLeave={onMouseLeave}
          {...otherProps}
        >
          <Icon className={cx(styles.attentionIcon)} name={'attention'} />
        </div>
      )}
      renderPopover={({ style, ...props }) => (
        <SimpleTooltip
          className={warningTooltip}
          maxWidth={205}
          style={style}
          onMouseEnter={onMouseEnter}
          onMouseLeave={onMouseLeave}
          onClick={event => {
            event.stopPropagation();
          }}
          {...props}
        >
          {recomendationText} {prevCrop}
        </SimpleTooltip>
      )}
    />
  );
};

export default CropWarningTooltip;
