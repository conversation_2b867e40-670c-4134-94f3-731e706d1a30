import React, { Fragment } from 'react';
import { Errors } from 'react-redux-form';

const ErrorMessage = ({ className, model, render }) => (
  <Errors
    model={model}
    show={field => field.errors.server && field.validated}
    wrapper={({ children }) => <Fragment>{children}</Fragment>}
    component={({ children }) => (
      <p className={className}>{render ? render(children) : children}</p>
    )}
  />
);

ErrorMessage.defaultProps = {
  className: 'error-common',
};

export default ErrorMessage;
