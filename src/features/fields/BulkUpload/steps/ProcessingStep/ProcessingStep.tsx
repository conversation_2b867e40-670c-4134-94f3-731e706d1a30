import React, { VFC } from 'react';
import { useSelector } from 'react-redux';

import { selectors } from 'features/fields/BulkUpload/redux/selectors';

import { UploadingTasksArea } from 'features/fields/BulkUpload/styles';
import { FileProcessingList } from 'features/fields/BulkUpload/components/FileProcessingList';

interface ProcessingStepProps {}

export const ProcessingStep: VFC<ProcessingStepProps> = () => {
  const selectedFiles: File[] = useSelector(selectors.getFiles);
  const processingStatus = useSelector(selectors.getProcessingStatus);

  return (
    <UploadingTasksArea>
      <FileProcessingList files={selectedFiles} status={processingStatus} />
    </UploadingTasksArea>
  );
};
