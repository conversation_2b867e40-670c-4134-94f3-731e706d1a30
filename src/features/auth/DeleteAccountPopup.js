import React from 'react';

import Button from 'components/ui/Button/Button';
import RenderAboveEverything from 'components/RenderAboveEverything';

import { useTranslate } from 'utils/use-translate';

const DeleteAccountPopup = ({ onClose, onNext }) => {
  const { t } = useTranslate();

  return (
    <RenderAboveEverything>
      <div className='modal modal-editseasons'>
        <div className='modal-outer'>
          <div className='modal-editseasons-content'>
            <div className='modal-editseasons-content__header'>
              {t('delete_account.title')}
            </div>
            <div className='modal-editseasons-content__body'>
              <div className='form-group'>
                {t('delete_account.notification')}
              </div>
            </div>
            <div className='modal-editseasons-content__actions'>
              <Button
                className='btn btn-primary btn-lg'
                onClick={() => {
                  onClose();
                }}
              >
                {t('forms.cancel')}
              </Button>
              <Button className='btn btn-success btn-lg' onClick={onNext}>
                {t('delete_account.button.continue')}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </RenderAboveEverything>
  );
};

export default DeleteAccountPopup;
