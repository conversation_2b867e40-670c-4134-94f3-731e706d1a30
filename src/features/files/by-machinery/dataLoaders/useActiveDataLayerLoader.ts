import { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import entities from 'modules/entities';

import {
  getActiveDataLayers,
  selectors,
} from 'features/files/by-machinery/redux/selectors';
import {
  ApiDataFieldLayer,
  DataLoader,
} from 'features/files/by-machinery/types';

export const ACTIVE_LAYERS_REQUEST_ID = 'useActiveDataLayerLoader';

export const useFetchActiveDataLayer = (): VoidFunction => {
  const dispatch = useDispatch();
  const uuids = useSelector(root => selectors.getMatchingTasksUuid(root));
  const uuidsStr = uuids.join(',');

  return useCallback(() => {
    if (!uuidsStr) {
      return;
    }

    dispatch(
      entities.actions.request(ACTIVE_LAYERS_REQUEST_ID, [
        {
          type: 'fetchMany',
          entityType: 'fieldsDataLayer',
          query: {
            include: ['source', 'properties'],
            'filter{source.upload.in}': uuidsStr.split(','),
          },
        },
      ]),
    );
  }, [uuidsStr, dispatch]);
};

export const useActiveDataLayerLoader = (): DataLoader<ApiDataFieldLayer[]> => {
  const fetch = useFetchActiveDataLayer();
  useEffect(fetch, [fetch]);

  const data: ApiDataFieldLayer[] = useSelector(getActiveDataLayers);
  const request = useSelector(state =>
    entities.selectors.getRequestStatus(state, ACTIVE_LAYERS_REQUEST_ID),
  );

  return [data, request, fetch];
};
