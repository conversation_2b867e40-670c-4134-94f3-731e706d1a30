import React, { VFC } from 'react';
import { useSelector } from 'react-redux';
import { Trans } from 'react-i18next';

import { selectors } from 'features/fields/BulkUpload/redux/selectors';
import { BulkUploadStep } from 'features/fields/BulkUpload/types';

import { PopoverDeprecatedAlign } from 'components/ui/Popover';
import { ModernTooltip } from 'components/ui/Tooltip/ModernTooltip/ModernTooltip';
import {
  SimpleTooltipArrow,
  SimpleTooltipTheme,
} from 'components/ui/Tooltip/SimpleTooltip/SimpleTooltip';

import { useTranslate } from 'utils/use-translate';

import {
  HintDescription,
  HintLink,
  HintTitle,
  HintTooltip,
  BulkUploadDescriptionText,
} from './styles';

export const BulkUploadDescription: VFC = () => {
  const { t } = useTranslate();

  const currentStep: BulkUploadStep = useSelector(selectors.getCurrentStep);

  const allBundles = useSelector(selectors.getFileBundles);
  const validBundles = useSelector(selectors.getValidFileBundles);
  const isImportDone = useSelector(selectors.isImportDone);
  const uploadedBundles = useSelector(selectors.getUploadedFileBundles);

  const shapefilesHint = (
    <ModernTooltip
      active
      arrow={SimpleTooltipArrow.TOP}
      theme={SimpleTooltipTheme.DARK}
      align={PopoverDeprecatedAlign.BottomMiddle}
      offset={[0, 10]}
      // @ts-ignore
      renderTrigger={props => (
        <HintLink {...props}>
          {t('fields.bulk_upload.shapefiles_hint.link')}
        </HintLink>
      )}
      renderTooltip={() => (
        <HintTooltip>
          <HintTitle>{t('fields.bulk_upload.shapefiles_hint.title')}</HintTitle>
          <HintDescription>
            {t('fields.bulk_upload.shapefiles_hint.text')}
          </HintDescription>
        </HintTooltip>
      )}
    />
  );

  function renderText() {
    switch (currentStep) {
      case BulkUploadStep.FILE_SELECTION:
        return (
          <Trans
            i18nKey='fields.bulk_upload.add_files.description'
            components={[shapefilesHint]}
          />
        );
      case BulkUploadStep.PROCESSING:
        return t('fields.bulk_upload.processing.description');
      case BulkUploadStep.CHECK_DATA:
        return validBundles.length === allBundles.length
          ? t('fields.bulk_upload.check_data.description')
          : // @ts-ignore
            t('fields.bulk_upload.check_data.some_bundles_are_invalid', {
              validBundlesNum: validBundles.length,
              allBundlesNum: allBundles.length,
            });
      case BulkUploadStep.UPLOADING:
        return isImportDone && uploadedBundles.length === 0
          ? t('fields.bulk_upload.uploading.failed.description')
          : t('fields.bulk_upload.uploading.description');
      default:
        return null;
    }
  }

  return <BulkUploadDescriptionText>{renderText()}</BulkUploadDescriptionText>;
};
