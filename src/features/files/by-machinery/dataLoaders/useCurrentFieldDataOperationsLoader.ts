import { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import entities from 'modules/entities';

import { getOperationsValue } from 'features/files/by-machinery/redux/selectors';
import {
  DataLoader,
  ApiDataFieldLayer,
} from 'features/files/by-machinery/types';
import { fieldsSelectors } from '../../../../modules/fields/selectors';

const REQUEST_ID = 'useCurrentFieldDataOperationsLoader';

export const useCurrentFieldDataOperationsLoader =
  (): DataLoader<ApiDataFieldLayer> => {
    const dispatch = useDispatch();
    const allOperations = useSelector(getOperationsValue);
    const operationUuid = useSelector(fieldsSelectors.getFieldFillingOperation);
    const operation = allOperations.find(v => v.uuid === operationUuid);
    const uuid = operation?.data_source.layer.uuid;

    const fetch = useCallback(() => {
      if (!uuid) {
        return;
      }
      dispatch(
        entities.actions.request(REQUEST_ID, [
          {
            type: 'fetchOne',
            entityType: 'fieldsDataLayer',
            path: { id: uuid },
            query: {
              include: ['properties', 'source'],
            },
          },
        ]),
      );
    }, [uuid, dispatch]);

    const data: ApiDataFieldLayer = useSelector(state => {
      return entities.selectors
        .getAll(state, 'fieldsDataLayer')
        .find(record => uuid === record.uuid);
    });
    const request = useSelector(state =>
      entities.selectors.getRequestStatus(state, REQUEST_ID),
    );

    useEffect(fetch, [fetch]);

    return [data, request, fetch];
  };
