import React, { FC } from 'react';

import { MapDropdownSectionStyle, MapDropdownListItemStyle } from './styles';

export type MapDropdownSection = {
  id: string;
  label: string;
  type: 'section';
};

export type MapDropdownSectionProps = {
  option: MapDropdownSection;
};

export const MapDropdownSectionItem: FC<MapDropdownSectionProps> = ({
  option,
}) => {
  return (
    <MapDropdownListItemStyle>
      <MapDropdownSectionStyle>{option.label}</MapDropdownSectionStyle>
    </MapDropdownListItemStyle>
  );
};
