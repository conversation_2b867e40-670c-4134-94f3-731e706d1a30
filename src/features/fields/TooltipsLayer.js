import React, { memo } from 'react';
import { Pane, Tooltip, Circle } from 'react-leaflet';
import { createSelector } from 'reselect';
import { useSelector } from 'react-redux';

import map from 'modules/map';

import ZIndex from 'constants/zindex';

const RenderFromZoomLevel = 12;

const pointInBbox = (point, bbox) =>
  point[1] >= bbox[0] &&
  point[1] <= bbox[2] &&
  point[0] >= bbox[1] &&
  point[0] <= bbox[3];

export const getTooltipsInViewport = createSelector(
  state => map.selectors.getMapTooltips(state),
  state => map.selectors.getViewport(state),
  (tooltips, viewport) => {
    if (viewport.properties.zoom < RenderFromZoomLevel) {
      return [];
    }
    return tooltips.filter(tooltip =>
      pointInBbox(tooltip.position, viewport.bbox),
    );
  },
);

const TooltipsLayer = () => {
  const mapTooltips = useSelector(getTooltipsInViewport);

  return (
    <Pane style={{ zIndex: ZIndex.FieldsPane }}>
      {mapTooltips.map(({ position, tooltip, id }) => (
        <Circle
          key={id}
          center={position}
          fill={false}
          radius={0}
          stroke={false}
        >
          <Tooltip
            opacity={1}
            direction='center'
            interactive={false}
            className='map-settings-tooltip'
            permanent
          >
            {tooltip}
          </Tooltip>
        </Circle>
      ))}
    </Pane>
  );
};

export default memo(TooltipsLayer);
