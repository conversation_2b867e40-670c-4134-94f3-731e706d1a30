import { matchOperationToFieldCheck } from './checks/matchOperationToFieldCheck';
import { matchOperationSpecifyCheck } from './checks/matchOperationSpecifyCheck';
import { matchOperationEnableCheck } from './checks/matchOperationEnableCheck';
import { attributeStepCheck } from './checks/attributeStepCheck';
import { defaultCheck } from './checks/defaultCheck';
import { cropStepCheck } from './checks/cropStepCheck';

export const baseRoutes = [
  { to: '/', title: 'fields.upload.nav.upload_file' },
  {
    to: '/matching/attributes',
    title: 'fields.upload.nav.match_attributes',
    check: attributeStepCheck,
  },
  {
    to: '/matching/operations',
    title: 'fields.upload.nav.match_operations',
    check: defaultCheck,
    validation: [
      matchOperationToFieldCheck,
      matchOperationSpecifyCheck,
      matchOperationEnableCheck,
    ],
  },
  {
    to: '/matching/crops',
    title: 'fields.upload.nav.match_crops',
    check: cropStepCheck,
  },
  {
    to: '/matching/finish',
    title: 'fields.upload.nav.save_fields',
    check: defaultCheck,
  },
];

export const soilSamplingRoutes = [
  { to: '/', title: 'fields.upload.nav.upload_file' },
  {
    to: '/matching/attributes',
    title: 'fields.upload.nav.match_attributes',
    check: defaultCheck,
  },
];

export const electroconductivityRoutes = [
  { to: '/', title: 'fields.upload.nav.upload_file' },
  {
    to: '/matching/attributes',
    title: 'fields.upload.nav.match_attributes',
    check: defaultCheck,
  },
];
