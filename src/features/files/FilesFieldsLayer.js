import React from 'react';
import { GeoJSON, Pane } from 'react-leaflet';
import { connect } from 'react-redux';
import L from 'leaflet';

import fields from 'modules/fields';

import ZIndex from 'constants/zindex';
import getRootScoutingUrl from 'utils/get-root-scouting-url';

const DefaultStyle = {
  color: '#FFE767',
  fillOpacity: 0,
  opacity: 1,
  weight: 2,
};

const SelectedStyle = {
  color: 'white',
  fillOpacity: 0,
  opacity: 1,
  weight: 2,
};

const FilesFieldsLayer = ({
  ownLayer,
  match,
  location,
  history,
  onHighlight,
}) => (
  <Pane style={{ zIndex: ZIndex.FieldsPane }}>
    <GeoJSON
      key={ownLayer.key}
      data={ownLayer.geojson}
      style={feature => {
        if (match.params.fieldId === feature.properties.id) {
          return SelectedStyle;
        }
        return DefaultStyle;
      }}
      onClick={mapEvent => {
        L.DomEvent.stopPropagation(mapEvent);
        const { feature } = mapEvent.layer;
        const baseUrl = getRootScoutingUrl(history.location.pathname);
        history.replace(`${baseUrl}/by-field/${feature.properties.id}`);
      }}
    />
  </Pane>
);

const mapStateToProps = state => ({
  ownLayer: fields.selectors.getOwnLayer(state),
  highlightedId: fields.selectors.getHighlightedID(state),
});

const mapDispatchToProps = {
  onHighlight: fields.actions.highlight,
};

export default connect(mapStateToProps, mapDispatchToProps)(FilesFieldsLayer);
