import { ApiDataFieldLayer } from 'features/files/by-machinery/types';

export type LayersLinksType = Record<string, string[]>;

const createLayerHash = (dataLayer: ApiDataFieldLayer) => {
  const temp = [...dataLayer.properties];

  return temp
    .sort((a, b) => a.name.localeCompare(b.name))
    .map(i => [i.type, i.name].join(':'))
    .join(',');
};

export const createLayersLinks = (
  dataLayers: ApiDataFieldLayer[],
): LayersLinksType => {
  const map = dataLayers.reduce<Record<string, string[]>>(
    (acc, item, index) => {
      const hash = createLayerHash(item); // `${index % 3}` +
      if (!acc[hash]) {
        acc[hash] = [];
      }
      acc[hash]?.push(item.uuid);

      return acc;
    },
    {},
  );

  return Object.values(map).reduce<LayersLinksType>((acc, items) => {
    acc[items[0] as string] = items;
    return acc;
  }, {});
};
