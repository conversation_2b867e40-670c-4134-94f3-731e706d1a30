import React, { VFC } from 'react';
import { Trans } from 'react-i18next';
import {
  StyledPage,
  styles,
} from 'components/WhatsNew/WhatsNewContent/WhatsNewContent.style';
import Button from 'components/ui/Button/Button';

import WhatsNewImg from 'assets/images/whatsnew/whatsnew_shared.png';
import WhatsNewImg2x from 'assets/images/whatsnew/<EMAIL>';
import { WhatsNewProps } from '../types';
import { FeaturesKeyEnum } from '../consts';

const WhatsNewSharedFile: VFC<WhatsNewProps> = ({
  style,
  onClick,
  isLast,
  t,
}) => (
  <StyledPage style={{ ...style }}>
    <div className={styles.semiPage}>
      <div className={styles.pageContent}>
        <div className={styles.textContent}>
          <h2 className={styles.title}>{t('whatsnew.shared_file.title')}</h2>
          <p className={styles.text}>
            <Trans>{t('whatsnew.shared_file.text')}</Trans>
          </p>
        </div>
        <Button className='btn btn-success fit-content' onClick={onClick}>
          {t(`whatsnew.${isLast ? 'end' : 'next'}.action_text`)}
        </Button>
      </div>
    </div>
    <div className={styles.semiPage}>
      <picture
        className={styles.pagePictureCentered}
        style={{ paddingLeft: '12px' }}
      >
        <source srcSet={`${WhatsNewImg} 1x, ${WhatsNewImg2x} 2x`} />
        <img
          src={WhatsNewImg}
          alt={t('whatsnew.shared_file.title') as string}
        />
      </picture>
    </div>
  </StyledPage>
);

const WhatsNewSharedFileConfig = {
  key: FeaturesKeyEnum.sharedFile,
  component: WhatsNewSharedFile,
  filter: {},
};

export default WhatsNewSharedFileConfig;
