import React, { VFC } from 'react';
import { useRouteMatch } from 'react-router-dom';

import {
  NavProgress,
  NavProgressLinkMode,
} from 'components/ui/NavProgress/NavProgress';
import { useImportFilesSteps } from '../service/steps';

export const ImportProcessedHeader: VFC = () => {
  const match = useRouteMatch();
  const importFilesSteps = useImportFilesSteps();
  if (match.isExact) {
    return null; //prevent render on first page
  }

  return (
    <NavProgress
      routes={importFilesSteps}
      linkMode={NavProgressLinkMode.enabledBeforeActiveElement}
    />
  );
};
