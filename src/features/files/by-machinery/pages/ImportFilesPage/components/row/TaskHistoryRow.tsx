import React, { useState, VFC } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import uniq from 'lodash/uniq';

import { formatDate } from 'utils/format-date';
import { useTranslate } from 'utils/use-translate';

import { CustomDropdown } from 'components/ui/CustomDropdown/CustomDropdown';
import Alert from 'components/ui/Alert/Alert';
import {
  ApiDataFieldOperations,
  DataTypesKey,
  TaskSuccessType,
} from 'features/files/by-machinery/types';
import { actions } from 'features/files/by-machinery/redux/actions';
import {
  HistoryTaskLogoStyle,
  HistoryTaskRowBlockStyle,
  HistoryTaskRowStyle,
  HistoryTaskSubInfoStyle,
  HistoryTaskSubTitleStyle,
  HistoryTaskTitleStyle,
  HistoryTaskTooltipBlockStyle,
  tooltipDotsClass,
} from '../styles';
import entities from '../../../../../../../modules/entities';

type TaskFailedRowProps = {
  task: TaskSuccessType;
};

const HistoryTaskDefaultLogo = () => (
  <svg
    width='40'
    height='40'
    viewBox='0 0 40 40'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
  >
    <path
      fillRule='evenodd'
      clipRule='evenodd'
      d='M22.8103 6C23.3251 6 23.8188 6.21071 24.1829 6.58579L30.4314 13.0237C30.7955 13.3988 31 13.9075 31 14.4379V32C31 32.5523 30.7827 33.0523 30.4314 33.4142C30.0802 33.7761 29.5949 34 29.0588 34H10.9412C10.4051 34 9.91984 33.7761 9.56856 33.4142C9.21727 33.0523 9 32.5523 9 32V8C9 7.44772 9.21727 6.94772 9.56856 6.58579C9.91984 6.22386 10.4051 6 10.9412 6H22.8103Z'
      fill='white'
      stroke='#B7C1C9'
      strokeWidth='2'
    />
    <path
      fillRule='evenodd'
      clipRule='evenodd'
      d='M23 6L31 14H24.6C23.7163 14 23 13.2837 23 12.4V6Z'
      fill='#B7C1C9'
    />
    <rect x='13' y='7' width='3' height='2' fill='#B7C1C9' />
    <rect x='16' y='9' width='3' height='2' fill='#B7C1C9' />
    <rect x='13' y='11' width='3' height='2' fill='#B7C1C9' />
    <rect x='16' y='13' width='3' height='2' fill='#B7C1C9' />
    <rect x='13' y='15' width='3' height='2' fill='#B7C1C9' />
    <rect x='16' y='17' width='3' height='2' fill='#B7C1C9' />
    <rect x='13' y='19' width='3' height='2' fill='#B7C1C9' />
    <rect x='16' y='21' width='3' height='2' fill='#B7C1C9' />
    <rect x='13' y='23' width='3' height='2' fill='#B7C1C9' />
  </svg>
);

const TaskHistoryRow: VFC<TaskFailedRowProps> = ({ task }) => {
  const { t } = useTranslate();
  const [isShowRemoveAlert, setIsShowRemoveAlert] = useState(false);
  const operations: ApiDataFieldOperations[] = useSelector(state => {
    const sources = task.sources.map(v => v.uuid);

    return entities.selectors
      .getAll(state, 'fieldsDataOperation')
      .filter(record => sources.includes(record.data_source.layer.source_uuid))
      .filter(item => item.is_enabled);
  });

  const fieldsCount = uniq(operations.map(v => v.field_uuid)).length;
  const typesCount = uniq(operations.map(v => v.type)).length;

  const dispatch = useDispatch();

  return (
    <HistoryTaskRowStyle disabled={task.disabled}>
      <HistoryTaskRowBlockStyle>
        <HistoryTaskLogoStyle>
          <HistoryTaskDefaultLogo />
        </HistoryTaskLogoStyle>
        <HistoryTaskTitleStyle>
          {formatDate(task.created_at, 'D MMM  YYYY')}
        </HistoryTaskTitleStyle>
        {task.data_type === DataTypesKey.machinery && (
          <HistoryTaskSubTitleStyle>
            {t('import.by-machinery.completed.fields', { count: fieldsCount })}
          </HistoryTaskSubTitleStyle>
        )}
      </HistoryTaskRowBlockStyle>
      <HistoryTaskRowBlockStyle>
        {task.data_type === DataTypesKey.machinery && (
          <HistoryTaskSubInfoStyle>
            {t('import.by-machinery.completed.jobs', {
              count: typesCount,
            })}
          </HistoryTaskSubInfoStyle>
        )}
        <HistoryTaskTooltipBlockStyle>
          <CustomDropdown
            disabled={task.disabled}
            className={tooltipDotsClass}
            renderValue={() => (
              <span className='soil-fields-list-actions__item' />
            )}
            options={[
              {
                label: t(`import.by-machinery.completed.remove`) as string,
                id: '0',
              },
            ]}
            onChange={() => {
              setIsShowRemoveAlert(true);
            }}
          />
        </HistoryTaskTooltipBlockStyle>
        {isShowRemoveAlert && (
          <Alert
            show={true}
            text={t(`import.by-machinery.alert.remove.text`)}
            onDanger={() => {
              dispatch(actions.removeTask(task));
              setIsShowRemoveAlert(false);
            }}
            onCancel={() => setIsShowRemoveAlert(false)}
            danger={t('import.by-machinery.alert.remove.confirm') as string}
            success={t('import.by-machinery.alert.remove.cancel') as string}
          />
        )}
      </HistoryTaskRowBlockStyle>
    </HistoryTaskRowStyle>
  );
};

export default TaskHistoryRow;
