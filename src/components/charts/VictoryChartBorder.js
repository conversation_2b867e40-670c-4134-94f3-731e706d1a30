import React from 'react';

const VictoryChartBorder = ({ padding, width, height }) => {
  const x1 = padding.left;
  const x2 = width - padding.right;
  const y1 = padding.top;
  const y2 = height - padding.bottom;
  const props = {
    stroke: '#A5B2BC',
    strokeOpacity: 0.45,
    vectorEffect: 'non-scaling-stroke',
    shapeRendering: 'auto',
  };
  return (
    <>
      <line x1={x1} y1={y1} x2={x2} y2={y1} {...props} />
      <line x1={x2} y1={y1} x2={x2} y2={y2} {...props} />
      <line x1={x2} y1={y2} x2={x1} y2={y2} {...props} />
      <line x1={x1} y1={y2} x2={x1} y2={y1} {...props} />
    </>
  );
};

export default VictoryChartBorder;
