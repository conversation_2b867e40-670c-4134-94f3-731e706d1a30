import { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import api from 'modules/api';

import { selectors } from 'features/files/by-machinery/redux/selectors';
import importTaskModule from 'features/files/by-machinery/redux';
import {
  DataLoader,
  TasksByMachineryMeta,
  DataTypesKey,
} from 'features/files/by-machinery/types';

// @ts-ignore
import useQuery from 'utils/use-query';

export const META_REQUEST_ID = 'useMetaLoader';

export const useMetaLoader = (): DataLoader<Nullable<TasksByMachineryMeta>> => {
  const dispatch = useDispatch();

  const [query] = useQuery();
  const dataType = query['data-type'] || DataTypesKey.machinery;

  const fetch = useCallback(() => {
    dispatch(importTaskModule.actions.requestMeta(META_REQUEST_ID, dataType));
  }, [dispatch, dataType]);

  const data: Nullable<TasksByMachineryMeta> = useSelector(selectors.getMeta);
  const request = useSelector(state =>
    api.selectors.getRequestStatusV2(state, META_REQUEST_ID),
  );

  useEffect(fetch, [fetch]);

  return [data, request, fetch];
};
