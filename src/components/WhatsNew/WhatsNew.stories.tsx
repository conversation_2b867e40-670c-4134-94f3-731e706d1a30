import React from 'react';
import { Provider } from 'react-redux';
//@ts-ignore
import setupStore from 'utils/setup-store';
import { UserSettingsStatusMap } from 'modules/settings/types';

import WhatsNew from './WhatsNew';

const MockStore = ({
  storyStore,
  children,
}: {
  storyStore: TEMPORARY_ANY;
  children: TEMPORARY_ANY;
}) => <Provider store={storyStore}>{children}</Provider>;

export default {
  title: 'common/WhatsNew',
  component: WhatsNew,
};

export const Simple = () => {
  const { store } = setupStore({
    settings: {
      status: UserSettingsStatusMap.resolved,
      featuresUsed: { id: '1', value: [] },
    },
  });
  return (
    <MockStore storyStore={store}>
      <WhatsNew />
    </MockStore>
  );
};
