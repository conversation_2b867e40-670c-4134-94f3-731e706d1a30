import React, { FC, useEffect, useState } from 'react';
import firebase from 'firebase';

const RemoteConfigLoader: FC = ({ children }) => {
  const [status, setStatus] = useState('no-fetch-yet');

  useEffect(() => {
    const remoteConfig = firebase.remoteConfig();
    remoteConfig.fetchAndActivate().then(() => {
      setStatus(remoteConfig.lastFetchStatus);
    });
  }, []);

  if (status === 'no-fetch-yet') {
    return null;
  }

  return <>{children}</>;
};

export default RemoteConfigLoader;
