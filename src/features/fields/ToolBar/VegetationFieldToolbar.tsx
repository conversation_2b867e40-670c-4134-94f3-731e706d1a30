import React from 'react';
import { useSelector } from 'react-redux';

import { useTranslate } from 'utils/use-translate';

import RangeLegend from 'components/RangeLegend';
import {
  VegetationControl,
  VegetationCalendar,
} from 'features/scouting/VegetationControls';
import VegetationControlRGB from 'features/scouting/VegetationControlRGB/VegetationControlRGB';

import VegetationTypeDropdown from './VegetationTypeDropdown';

import {
  StyledBottomMapWidget,
  StyledMapTopPanel,
  StyledTopPanelItem,
} from 'components/ui/mapToolBar/styles';

import { BarsCount, LegendColorsMap } from 'constants/style';

import { fieldsSelectors } from 'modules/fields/selectors';

export type VegetationFieldToolbarProps = {
  fieldId: string;
  className?: string;
};

export const VegetationFieldToolbar = ({
  fieldId,
  className,
}: VegetationFieldToolbarProps) => {
  const { t } = useTranslate();
  const fieldFillingType = useSelector(fieldsSelectors.getFieldFillingType);
  const isNDVILayer = ['default', 'contrasted'].includes(fieldFillingType);
  const isRGBLayer = fieldFillingType === 'rgb';

  return (
    <>
      <StyledMapTopPanel className={className}>
        <StyledTopPanelItem>
          <VegetationTypeDropdown fieldId={fieldId} />
        </StyledTopPanelItem>
        <StyledTopPanelItem isFluid={fieldFillingType !== 'operation'}>
          {isRGBLayer && <VegetationControlRGB fieldId={fieldId} />}
          {isNDVILayer && <VegetationControl fieldId={fieldId} />}
        </StyledTopPanelItem>

        {(isNDVILayer || isRGBLayer) && (
          <StyledTopPanelItem>
            <VegetationCalendar fieldId={fieldId} />
          </StyledTopPanelItem>
        )}
      </StyledMapTopPanel>
      {fieldFillingType === 'rgb' || fieldFillingType !== 'operation' ? null : (
        <StyledBottomMapWidget>
          <RangeLegend
            colorsRange={LegendColorsMap[
              // @ts-ignore
              fieldFillingType === 'default' ? 'field_ndvi' : 'contrasted_ndvi'
            ].colors(BarsCount)}
            labels={[t('fields.ndvi.legend.low'), t('fields.ndvi.legend.high')]}
            withClouds
          />
        </StyledBottomMapWidget>
      )}
    </>
  );
};
