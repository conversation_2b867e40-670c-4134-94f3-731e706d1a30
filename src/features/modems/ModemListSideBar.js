import React, { useRef, useCallback, useMemo } from 'react';
import { AutoSizer, List } from 'react-virtualized';
import { Redirect } from 'react-router-dom';
import { useSelector } from 'react-redux';

import ModernSpinner from 'components/ui/ModernSpinner/ModernSpinner';
import ModemListItem from './ModemListItem';

import modems from 'modules/modems';

import getRootScoutingUrl from 'utils/get-root-scouting-url';
import { useTranslate } from 'utils/use-translate';
import logEvent from 'sagas/global/logEvent';

const ModemRowHeight = 70;

const ModemItemWrapper = React.memo(({ index, style, data }) => {
  const { selectedModemId, modemIDs, selectModem } = data;
  const modemId = modemIDs[index];

  return (
    <ModemListItem
      style={style}
      modemId={modemId}
      selected={modemId === selectedModemId}
      onClick={() => selectModem(modemId)}
    />
  );
});

const ModemListSideBar = ({ match, history, location }) => {
  const { t } = useTranslate();
  const modemIDs = useSelector(modems.selectors.getIDs);
  const status = useSelector(modems.selectors.getStatus);

  const selectedModemId = match.params.modemId || null;

  const list = useRef(null);
  const skipRealign = useRef(false);

  const selectModem = useCallback(
    modemId => {
      const baseUrl = getRootScoutingUrl(history.location.pathname);
      if (selectedModemId !== modemId) {
        logEvent('modems_list_open', {
          modem_id: modemId,
        });
        history.replace(`${baseUrl}/${modemId}`);
      }
    },
    [selectedModemId, history],
  );

  const listData = useMemo(
    () => ({ modemIDs, selectedModemId, selectModem }),
    [modemIDs, selectedModemId, selectModem],
  );

  const onResize = ({ height }) => {
    alignSelectedModem(height);
  };

  const alignSelectedModem = height => {
    if (skipRealign.current) {
      return;
    }
    if (!list.current?.scrollToPosition) {
      return;
    }

    const index = modemIDs.findIndex(id => id === selectedModemId);
    const offset = list.current.getOffsetForRow({ index });
    const listHeight = listData.modemIDs.length * ModemRowHeight;
    const el = list.current.Grid._scrollingContainer;
    const distanceToBottom = el.scrollHeight - el.offsetHeight - el.scrollTop;
    const offsetAtBottom = listHeight - el.offsetHeight;

    if (listHeight <= height) {
      // dont scroll if no need to scroll, because first rows disappear
      return;
    }
    if (
      distanceToBottom === 0 &&
      (offset === offsetAtBottom || offsetAtBottom < 0)
    ) {
      return;
    }

    skipRealign.current = true;
    list.current.scrollToPosition(index * ModemRowHeight);
  };

  return (
    <div className='soil-sidebar-viewer'>
      <div className='soil-sidebar-header'>
        <div className='soil-sidebar-header__inner'>
          <h1 className='soil-sidebar-header__main'>
            {t('platform.apps.modems')}
          </h1>
        </div>
      </div>
      {status === 'pending' && (
        <div className='soil-sidebar-empty'>
          <div className='soil-sidebar-empty__spinner'>
            <ModernSpinner large />
          </div>
          <h2 className='soil-sidebar-empty__title'>
            {t('modems.loading.title')}
          </h2>
        </div>
      )}
      {status === 'resolved' && (
        <div className='soil-sidebar-body'>
          {!selectedModemId && modemIDs.length !== 0 && (
            <Redirect to={`${match.url}/${modemIDs[0]}`} replace />
          )}
          <AutoSizer onResize={onResize}>
            {({ width, height }) => (
              <List
                ref={list}
                className='soil-fields-list'
                width={width}
                height={height}
                rowCount={modemIDs.length}
                rowHeight={ModemRowHeight}
                rowRenderer={props => (
                  <ModemItemWrapper data={listData} {...props} />
                )}
              />
            )}
          </AutoSizer>
        </div>
      )}
    </div>
  );
};

export default ModemListSideBar;
