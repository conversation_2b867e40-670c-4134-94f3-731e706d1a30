import React, { forwardRef } from 'react';
import type { Control } from 'react-hook-form';
import type { FieldErrors } from 'react-hook-form/dist/types/errors';

import { ClientField } from 'features/multiaccount/dealer/modals/ClientForm/components/ClientField';
import {
  ClientForm,
  ClientFormType,
} from 'features/multiaccount/dealer/modals/ClientForm/types';

import { ErrorData } from 'modules/entities/types';

type ClientTaxNumberFieldProps = {
  control: Control<ClientForm>;
  formErrors: FieldErrors<ClientForm>;
  isPending: boolean;
  requestErrors: Nullable<ErrorData>;
  type: ClientFormType;
  name?: string;
};

const ClientTaxNumberField = forwardRef<
  HTMLInputElement,
  ClientTaxNumberFieldProps
>((props, ref) => {
  return (
    <ClientField
      {...props}
      ref={ref}
      name={props.name || 'tax_number'}
      columnTranslationKey={`multiaccount.${props.type}-modal.column.tax-number`}
      placeholderTranslationKey={`multiaccount.${props.type}-modal.placeholder.tax-number`}
    />
  );
});

export default ClientTaxNumberField;
