import React, { ReactNode } from 'react';

import Icon from 'components/Icon';

import Icons from 'constants/icons';

export default {
  title: 'common/Icon',
  component: Icon,
  decorators: [
    (storyFn: () => ReactNode) => (
      <div style={{ display: 'flex', flexWrap: 'wrap' }}>{storyFn()}</div>
    ),
  ],
};

const IconWrapper = ({ name }: { name: keyof typeof Icons }) => (
  <div style={{ display: 'flex', alignItems: 'center', padding: 10 }}>
    <Icon name={name} style={{ width: 24, height: 24 }} />
    <span style={{ marginLeft: 5 }}>{name}</span>
  </div>
);

export const List = () =>
  (Object.keys(Icons) as Array<keyof typeof Icons>).map(icon => (
    <IconWrapper key={icon} name={icon} />
  ));
