import React, { Component } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import { withTranslation } from 'react-i18next';
import { ImageOverlay, Tooltip } from 'react-leaflet';
import cx from 'classnames';

import fields from 'modules/fields';
import ui from 'modules/ui';
import { parseQuery } from 'utils/query';
import { getNdviValue } from 'utils/images';

import config from 'config';
import bbox2leaflet from 'utils/bbox2leaflet/bbox2leaflet';

const getImageData = image => {
  const canvas = document.createElement('canvas');
  canvas.width = image.naturalWidth;
  canvas.height = image.naturalHeight;
  const context = canvas.getContext('2d');
  context.drawImage(image, 0, 0, canvas.width, canvas.height);
  return context.getImageData(0, 0, canvas.width, canvas.height);
};

class SelectedFieldNDVIFiles extends Component {
  state = {
    hoveredValue: null,
  };

  handleGetImageData = el => {
    if (!el) {
      return;
    }
    el.addEventListener('load', () => {
      this.image = getImageData(el);
    });
  };

  calculateHoveredValue = (x, y) => {
    if (!this.image) {
      return;
    }

    const offset = (x + y * this.image.width) * 4;
    if (offset >= this.image.data.length) {
      return;
    }

    const ndviValue = getNdviValue(this.image.data[offset + 0]);
    const cloudMaskValue = this.image.data[offset + 1];
    const fieldMaskValue = this.image.data[offset + 2];

    return fieldMaskValue
      ? cloudMaskValue
        ? { key: 'fields.ndvi.legend.clouds' }
        : ndviValue
      : null;
  };

  updateTooltip = (x, y) => {
    const hoveredValue = this.calculateHoveredValue(x, y);
    this.props.updateTooltipValue?.(hoveredValue);
  };

  changeColor = (x, y) => {
    const hoveredValue = this.calculateHoveredValue(x, y);
    if (this.state.hoveredValue !== hoveredValue) {
      this.setState({ hoveredValue });
    }
  };

  handleMouseMove = event => {
    if (!this.image) {
      return;
    }
    const { _image } = event.target;

    const x = Math.floor(
      (event.originalEvent.offsetX / _image.clientWidth) * this.image.width,
    );

    const y = Math.floor(
      (event.originalEvent.offsetY / _image.clientHeight) * this.image.height,
    );

    this.changeColor(x, y);

    this.props.changeTooltipIndex?.(this.props.mapIndex ? 0 : 1);

    if (this.props.mapIndex !== this.props.tooltipIndex) {
      this.props.onSetTooltipPosition({
        x,
        y,
        translateX: event.containerPoint.x,
        translateY: event.containerPoint.y,
      });
    }
  };

  componentDidUpdate(prevProps) {
    if (
      (this.props.interactiveTooltip.translateX !==
        prevProps.interactiveTooltip.translateX ||
        this.props.interactiveTooltip.translateY !==
          prevProps.interactiveTooltip.translateY) &&
      this.props.mapIndex === this.props.tooltipIndex
    ) {
      const { interactiveTooltip } = this.props;
      this.updateTooltip(interactiveTooltip.x, interactiveTooltip.y);
    }
  }

  render() {
    const {
      fieldId,
      ndvi,
      fieldFillingType,
      index,
      location,
      t,
      hideVegetationTooltip,
      onToggleTooltipVisibility,
    } = this.props;

    const { hoveredValue } = this.state;

    const query = parseQuery(location);
    const rulerMode = 'ruler_mode' in query;

    const itemsWithShots =
      ndvi.items && ndvi.items.filter(item => item.status !== 2);

    return (
      ndvi.status === 'resolved' &&
      index < itemsWithShots.length &&
      index !== -1 && (
        <>
          <img
            ref={this.handleGetImageData}
            className='hide'
            crossOrigin='anonymous'
            src={`${config.apiHost}/en/v2/fields-users-seasons-ndvi/${itemsWithShots[index].uuid}/raw.png`}
            alt=''
          />
          <ImageOverlay
            crossOrigin
            interactive={!rulerMode}
            // Leaflet does not handle changing bounds well, so we re-create overlay each time
            key={`${fieldId}-ndvi`}
            url={`${config.apiHost}/en/v2/fields-users-seasons-ndvi/${
              itemsWithShots[index].uuid
            }/${fieldFillingType === 'default' ? 'rgb' : 'contrasted'}.png`}
            bounds={bbox2leaflet(ndvi.bbox)}
            onMousemove={this.handleMouseMove}
            onMouseOver={() => onToggleTooltipVisibility()}
            onMouseOut={() => onToggleTooltipVisibility()}
          >
            <Tooltip
              className={cx('tooltip-dark', {
                hide: hideVegetationTooltip || hoveredValue == null,
              })}
              direction='top'
              sticky
            >
              {hoveredValue != null && (
                <span>
                  {hoveredValue.key
                    ? t(hoveredValue.key)
                    : hoveredValue.toFixed(2)}
                </span>
              )}
            </Tooltip>
          </ImageOverlay>
        </>
      )
    );
  }
}

const mapStateToProps = (state, ownProps) => ({
  ndvi: fields.selectors.getNDVIByID(state, ownProps.fieldId),
  fieldFillingType: fields.selectors.getFieldFillingType(state),
  interactiveTooltip: ui.selectors.interactiveTooltip(state),
});

const mapDispatchToProps = {
  onSetTooltipPosition: ui.actions.setTooltipCoordinates,
  onToggleTooltipVisibility: ui.actions.toggleTooltipVisibility,
};

export default withRouter(
  connect(
    mapStateToProps,
    mapDispatchToProps,
  )(withTranslation()(SelectedFieldNDVIFiles)),
);
