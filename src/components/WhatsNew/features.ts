//import WhatsNewColors from './components/WhatsNewColors';
//import WhatsNewImport from './components/WhatsNewImport';
//import WhatsNewGuide from './components/WhatsNewGuide';
//import WhatsNewCrops from './components/WhatsNewCrops';
// import WhatsNewCropRotation from './components/WhatsNewCropRotation';
import WhatsNewHungarian from './pages/WhatsNewHungarian';
import WhatsNewCloudFree from './pages/WhatsNewCloudFree';
import WhatsNewUkrainian from './pages/WhatsNewUkrainian';
import WhatsNewCropRotationV2 from './pages/WhatsNewCropRotationV2';
import WhatsNewContrastNDVI from './pages/WhatsNewContrastNDVI';
import WhatsNewAllFieldsNDVI from './pages/WhatsNewAllFieldsNDVI';
import WhatsNewPolish from './pages/WhatsNewPolish';
import WhatsNewTurkish from './pages/WhatsNewTurkish';
import WhatsNewFieldBoundaries from './pages/WhatsNewFieldBoundaries';
import WhatsNewSoilAnalysisConfig from './pages/WhatsNewSoilAnalysis';
import WhatsNewSharedFileConfig from './pages/WhatsNewSharedFile';

import { WhatsNewConfigType } from './types';
import WhatsNewMultiAccountConfig from './pages/WhatsNewMultiAccount';

export const NewFeatures: WhatsNewConfigType[] = [
  //  WhatsNewColors,
  //  WhatsNewImport,
  //  WhatsNewGuide,
  //  WhatsNewCrops,
  WhatsNewUkrainian,
  WhatsNewHungarian,
  WhatsNewCloudFree,
  WhatsNewContrastNDVI,
  // WhatsNewCropRotation,
  WhatsNewCropRotationV2,
  WhatsNewAllFieldsNDVI,
  WhatsNewPolish,
  WhatsNewTurkish,
  WhatsNewFieldBoundaries,
  WhatsNewSoilAnalysisConfig,
  WhatsNewSharedFileConfig,
  WhatsNewMultiAccountConfig,
];

export const HistoryLinkEn =
  'https://www.notion.so/onesoil/What-s-new-in-the-OneSoil-web-app-4a5867cc512f4f4ca4152f16273a19ff';
