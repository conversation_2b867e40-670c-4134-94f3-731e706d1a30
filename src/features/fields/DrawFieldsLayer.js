import React, { Fragment } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { createSelector } from 'reselect';
import { featureCollection, feature } from '@turf/helpers';
import area from '@turf/area';

import BodyClassName from 'components/BodyClassName';
import GeometryEditor from './GeometryEditor';
import ToastContainer from 'features/platform/ToastContainer/ToastContainer';

import fields from 'modules/fields';

import randomID from 'utils/random-id';
import { hasIntersections } from 'utils/field-has-intersection';
import { MaxFieldArea } from 'sagas/local/edit-field-geometry';

const NewFieldStyle = hasError => ({
  color: hasError ? '#eb4e4e' : '#FFE767',
  fillOpacity: 0,
  weight: 2,
});

const getLayer = createSelector(
  state => fields.selectors.getSelectedIDs(state),
  ({ fields }) => fields.data,
  (ids, data) => ({
    key: randomID(),
    geojson: featureCollection(
      ids.map(id => feature(data[id].geometry, { id })),
    ),
  }),
);

const getFieldsErrors = createSelector(
  state => fields.selectors.getSelectedIDs(state),
  ({ fields }) => fields.data,
  (ids, fields) => {
    const errors = {};
    for (const id of ids) {
      errors[id] =
        hasIntersections(fields[id].geometry) ||
        area(fields[id].geometry) > MaxFieldArea;
    }
    return errors;
  },
);

const DrawFieldsLayer = () => {
  const dispatch = useDispatch();
  const layer = useSelector(getLayer);
  const fieldsErrors = useSelector(getFieldsErrors);

  return (
    <Fragment>
      <BodyClassName className='map-draw-mode' />
      <ToastContainer id='global' />
      <GeometryEditor
        allowCreate
        key={layer.key}
        data={layer.geojson}
        style={feature => NewFieldStyle(fieldsErrors[feature.properties.id])}
        noKeyDeleteHandle
        onCreate={geometry => {
          const id = randomID();
          dispatch(
            fields.actions.setLocal(id, {
              source: 'drawn',
              geometry,
            }),
          );
          dispatch(fields.actions.addSelectedId(id));
        }}
        onUpdate={(geometry, event) => {
          if (!event.sourceTarget.feature) {
            // Feature was updated, but not yet finished
            return;
          }
          const { properties } = event.sourceTarget.feature;
          dispatch(fields.actions.setLocal(properties.id, { geometry }));
        }}
      />
    </Fragment>
  );
};

export default DrawFieldsLayer;
