import React, {
  ForwardRefExoticComponent,
  PropsWithoutRef,
  RefAttributes,
} from 'react';
import cx from 'classnames';
import { ModernTooltipDirection } from 'components/ui/Tooltip/ModernTooltip/ModernTooltip';
import {
  Small,
  SimpleTooltipContainer,
  SimpleTooltipArrowContainer,
  SimpleTooltipArrowContainerInner,
  SimpleTooltipContent,
  TooltipTitleStyle,
  TooltipDescriptionStyle,
  TooltipDescriptionListStyle,
  TooltipTextStyle,
} from './SimpleTooltip.style';

export enum SimpleTooltipArrow {
  BOTTOM = 'bottom',
  TOP = 'top',
  LEFT = 'left',
  RIGHT = 'right',
}

export enum SimpleTooltipTheme {
  DARK = 'dark',
  ONBOARDING = 'onboarding',
}

export interface SimpleTooltipProps extends React.PropsWithChildren<{}> {
  style: React.CSSProperties;
  className?: string;
  maxWidth?: number;
  onMouseEnter?: React.MouseEventHandler<HTMLDivElement>;
  onMouseLeave?: React.MouseEventHandler<HTMLDivElement>;
  onClick?: React.MouseEventHandler<HTMLDivElement>;
  direction?: ModernTooltipDirection;
  arrow?: SimpleTooltipArrow | null;
  theme?: SimpleTooltipTheme;
  small?: boolean;
}

type ChildComponents = {
  Title: typeof TooltipTitleStyle;
  Description: typeof TooltipDescriptionStyle;
  Text: typeof TooltipTextStyle;
  List: typeof TooltipDescriptionListStyle;
};

type SimpleTooltipType = ForwardRefExoticComponent<
  PropsWithoutRef<SimpleTooltipProps> & RefAttributes<TEMPORARY_ANY>
> &
  ChildComponents;

export const SimpleTooltip = React.forwardRef(
  (
    {
      style,
      className,
      onMouseEnter,
      onMouseLeave,
      onClick,
      maxWidth,
      direction,
      arrow,
      theme,
      children,
      small,
      ...popoverProps
    }: SimpleTooltipProps,
    ref: TEMPORARY_ANY,
  ) => {
    return (
      <div
        className={cx(
          SimpleTooltipContainer,
          small ? Small : '',
          arrow ? `__arrow-${arrow}` : '',
          className,
        )}
        style={{
          ...(maxWidth !== null && maxWidth !== undefined ? { maxWidth } : {}),
          ...style,
        }}
        data-theme={theme ?? ''}
        data-direction={direction ?? ''}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        onClick={event => (onClick ? onClick?.(event) : null)}
        ref={ref}
        {...popoverProps}
      >
        {arrow ? (
          <div className={SimpleTooltipArrowContainer}>
            <div className={SimpleTooltipArrowContainerInner} />
          </div>
        ) : null}
        <div className={SimpleTooltipContent}>{children}</div>
      </div>
    );
  },
) as SimpleTooltipType;

SimpleTooltip.Title = TooltipTitleStyle;
SimpleTooltip.Description = TooltipDescriptionStyle;
SimpleTooltip.List = TooltipDescriptionListStyle;
SimpleTooltip.Text = TooltipTextStyle;
