import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import {
  BulkUploadStep,
  BulkUploadStepState,
} from 'features/fields/BulkUpload/types';
import { actions } from 'features/fields/BulkUpload/redux/actions';
import { selectors } from 'features/fields/BulkUpload/redux/selectors';

import { useTranslate } from 'utils/use-translate';

import {
  NavProgressListItemStyle,
  NavProgressListStyle,
  NavProgressStyle,
} from './NavProgress.style';

export const NavProgress = () => {
  const { t } = useTranslate();
  const dispatch = useDispatch();

  const currentStep: BulkUploadStep = useSelector(selectors.getCurrentStep);

  const availableSteps: Record<BulkUploadStep, BulkUploadStepState> =
    useSelector(selectors.getAvailableSteps);

  function getDataStatus(step: BulkUploadStep, stepState: BulkUploadStepState) {
    if (step === currentStep) {
      return 'active';
    }

    if (stepState.available && stepState.clickable) {
      return 'clickable';
    }

    if (stepState.available) {
      return 'available';
    }

    return null;
  }

  function onStepClick(step: BulkUploadStep, stepState: BulkUploadStepState) {
    if (!stepState.clickable) {
      return;
    }

    dispatch(actions.setCurrentStep(step));
  }

  return (
    <NavProgressStyle>
      <NavProgressListStyle>
        {Object.entries(availableSteps).map(([key, stepState]) => {
          const step = key as BulkUploadStep;
          const title = t(`fields.bulk_upload.step.${step}`);

          if (!stepState.visible) {
            return null;
          }

          return (
            <NavProgressListItemStyle
              key={step}
              data-status={getDataStatus(step, stepState)}
              onClick={() => onStepClick(step, stepState)}
            >
              {title}
            </NavProgressListItemStyle>
          );
        })}
      </NavProgressListStyle>
    </NavProgressStyle>
  );
};
