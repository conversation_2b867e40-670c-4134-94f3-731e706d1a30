import React, { useState } from 'react';
import { call, put, take, takeEvery } from 'redux-saga/effects';
import { useSelector, useDispatch } from 'react-redux';
import { useForm } from 'react-hook-form';
import { styled } from 'linaria/react';
import groupBy from 'lodash/groupBy';

import Icon from 'components/Icon';
import Button from 'components/ui/Button/Button';
import LinkButton from 'components/LinkButton';
import TextInput from 'components/forms/TextInput';
import VisiblePasswordInput from 'components/VisiblePasswordInput';
import RenderAboveEverything from 'components/RenderAboveEverything';

import api from 'modules/api';
import auth from 'modules/auth';
import toasts from 'modules/toasts';

import { resolved, rejected } from 'sagas/global/api';
import logEvent from 'sagas/global/logEvent';
import useQuery from 'utils/use-query';
import useSaga from 'utils/use-saga';
import { useTranslate } from 'utils/use-translate';

const ErrorLabel = styled.div`
  margin-top: 2px;
  font-size: 12px;
  color: #ff3b30;
`;

const ChangeEmailPopup = ({ onClose }) => {
  const { t } = useTranslate();
  const userID = useSelector(auth.selectors.getUserID);
  const dispatch = useDispatch();

  // eslint-disable-next-line no-unused-vars
  const [query, mergeQuery] = useQuery();
  const [errors, setErrors] = useState([]);
  const errorsByField = groupBy(errors, 'field');

  const { register, formState, handleSubmit } = useForm({
    mode: 'onChange',
  });

  const submitRequest = useSelector(state =>
    api.selectors.getRequestStatus(state, 'change-email'),
  );

  const onSubmit = form => {
    dispatch(auth.actions.changeEmail(userID, form.username, form.password));
  };

  useSaga(function*() {
    yield takeEvery(rejected(auth.actions.changeEmail), action => {
      const { error_details = [] } = action.payload;
      setErrors(error_details);
    });

    const action = yield take(resolved(auth.actions.changeEmail));
    const { email } = action.meta.requestAction.payload;

    yield put(
      toasts.actions.push('change-email', 'toasts.change_email_sent.title', {
        getLabel: ({ t, label }) => t(label),
        getContent: ({ t, content }) =>
          t(content, {
            new_email: email,
          }),
        target: 'map-pane',
        position: 'bottom',
        icon: 'modem-done',
        ttl: Infinity,
        content: 'toasts.change_email_sent.text',
      }),
    );

    yield call(logEvent, 'change_email_sent');
    yield call(onClose);
  }, []);

  return (
    <RenderAboveEverything>
      <div className='modal modal-editseasons'>
        <div className='modal-outer'>
          <div className='modal-editseasons-content'>
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className='modal-editseasons-content__header'>
                {t('change_email.title')}
              </div>
              <div className='modal-editseasons-content__body'>
                <div className='form-group'>
                  <label className='form-label' htmlFor='username'>
                    {t('change_email.new_email.label')}
                  </label>
                  <TextInput
                    tabIndex={1}
                    id='username'
                    name='username'
                    autoComplete='off'
                    type='text'
                    ref={register({ required: true })}
                    clearable
                    isValid={!errorsByField.username}
                  />
                  {errorsByField.username?.map(({ code }) => (
                    <ErrorLabel key={code}>
                      {t(`change_email.new_email.error.${code}`)}
                    </ErrorLabel>
                  ))}
                </div>
                <div className='form-group'>
                  <label className='form-label' htmlFor='password'>
                    {t('change_email.password.label')}
                    <div className='form-label__side'>
                      <LinkButton
                        onClick={() => {
                          mergeQuery({ settings: 'reset' }, 'push');
                          logEvent('forgot_password_click');
                        }}
                      >
                        {t('auth.forgot')}
                      </LinkButton>
                    </div>
                  </label>
                  <VisiblePasswordInput
                    tabIndex={1}
                    id='password'
                    name='password'
                    ref={register({ required: true })}
                    isValid={!errorsByField.password}
                  />
                  {errorsByField.password?.map(({ code }) => (
                    <ErrorLabel key={code}>
                      {t(`change_email.password.error.${code}`)}
                    </ErrorLabel>
                  ))}
                </div>
              </div>
              <div className='modal-editseasons-content__actions'>
                <Button
                  className='btn btn-primary btn-lg'
                  onClick={() => {
                    onClose();
                  }}
                >
                  {t('forms.cancel')}
                </Button>
                <Button
                  className='btn btn-success btn-lg'
                  pending={submitRequest.status === 'pending'}
                  disabled={!formState.isValid}
                  type='submit'
                >
                  <span className='btn__ico'>
                    <Icon className='ico-check-os' name='check' />
                  </span>
                  {t(`change_email.submit`)}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </RenderAboveEverything>
  );
};

export default ChangeEmailPopup;
