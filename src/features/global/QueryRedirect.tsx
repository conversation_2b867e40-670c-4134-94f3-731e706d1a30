import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

import { parseQuery } from 'utils/query';

const QueryRedirect = () => {
  const location = useLocation();
  const query = parseQuery(location);

  useEffect(() => {
    window.location.href = query.next as string;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return null;
};

export default QueryRedirect;
