import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import entities from 'modules/entities';

import { isEntityHaveUpdate } from '../utils/isEntityHaveUpdate';
import { ApiDataFieldOperations } from '../types';

export const useMarkOperationAsShowed = (
  operation: ApiDataFieldOperations | undefined,
) => {
  const dispatch = useDispatch();
  const requestId = `useMarkOperationAsShowed${operation?.uuid}`;

  const requestMark = useSelector(state =>
    entities.selectors.getRequestStatus(state, requestId),
  );

  useEffect(() => {
    if (
      operation &&
      isEntityHaveUpdate(operation) &&
      requestMark.status !== 'pending'
    ) {
      dispatch(
        entities.actions.request(requestId, [
          {
            type: 'patch',
            entityType: 'fieldsDataOperation',
            path: {
              id: operation.uuid,
            },
            body: {
              viewed_at: new Date(),
            },
            query: {
              include: ['data_source.layer.properties'],
            },
          },
        ]),
      );
    }
  }, [operation, dispatch, requestId, requestMark.status]);
};
