import { Component } from 'react';
import { RouteComponentProps } from 'react-router-dom';
import { connect } from 'react-redux';

import auth from 'modules/auth';

import { INVITATION_TYPES } from 'features/multiaccount/constants';

type LogoutInvitationPageProps = {
  onLogout: () => void;
} & RouteComponentProps<{
  token: string;
  invitationType: INVITATION_TYPES;
}>;

class LogoutInvitationPage extends Component<LogoutInvitationPageProps> {
  componentWillMount() {
    const { onLogout } = this.props;
    onLogout();
  }

  componentDidMount() {
    setTimeout(() => {
      // DeepLinkPage logic
      const target = `/fields/invitation/${this.props.match.params.invitationType}/${this.props.match.params.token}`;
      // @ts-ignore
      window.location = `/auth/login/${this.props.match.params.invitationType}?data=${this.props.match.params.token}&next=${target}`;
    }, 500);
  }

  render() {
    return null;
  }
}

const mapDispatchToProps = {
  onLogout: auth.actions.logout,
};

export default connect(null, mapDispatchToProps)(LogoutInvitationPage);
