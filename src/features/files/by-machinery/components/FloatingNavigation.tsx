import React, { MouseEvent, useState, VFC } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { matchPath, useHistory, useRouteMatch } from 'react-router-dom';
import uniq from 'lodash/uniq';
// @ts-ignore
import { resolved } from 'sagas/global/api';

import { useTranslate } from 'utils/use-translate';
import entities from 'modules/entities';
import alert from 'modules/alert';
import RootState from 'types/rootState';
// @ts-ignore
import { apiCall } from 'utils/effects';

import {
  ImportTaskType,
  TaskMatchingType,
  ApiDataFieldOperations,
  DataTypesKey,
} from 'features/files/by-machinery/types';
import {
  StepRoute,
  StepStatus,
  StepValidationStatus,
  useImportFilesSteps,
} from 'features/files/by-machinery/service/steps';

import { selectors } from 'features/files/by-machinery/redux/selectors';
import { actions } from 'features/files/by-machinery/redux/actions';
import { filters } from 'features/files/by-machinery/redux/filters';

import Icon from 'components/Icon';
import SmartButton, {
  SmartButtonProps,
} from 'components/ui/Button/SmartButton';

import { FloatingActionsStyle } from 'components/ui/FloatingActions/styles';
import { useUpdateCropsStatus } from '../service/steps/hooks/useUpdateCropsStatus';
import { actions as toastActions } from '../../../platform/ToastModule/redux/actions';
import { TOAST_KEY_TO_IMPORT } from '../constants';
import {
  OPERATIONS_REQUEST_ID,
  useActiveDataLayerLoader,
} from '../dataLoaders';
import { parseQuery, stringifyQuery } from 'utils/query';
// @ts-ignore
import useSaga from 'utils/use-saga';
import { useFieldsElectroconductivityLoader } from 'features/files/electroconductivity/dataLoaders';
import { all, call, put, select, takeEvery } from 'redux-saga/effects';
import Alert from '../../../../components/ui/Alert/Alert';

const selectActiveOperations = (state: RootState): ApiDataFieldOperations[] => {
  const matchingTasks: TaskMatchingType[] = selectors
    .getAllTasks(state)
    .filter(filters.isMatchingTask);
  const sources = matchingTasks
    .map(task => task.sources.map(v => v.uuid))
    .flat(1);

  return entities.selectors
    .getAll(state, 'fieldsDataOperation')
    .filter(record => sources.includes(record.data_source.layer.source_uuid));
};

export const FloatingNavigation: VFC = () => {
  useUpdateCropsStatus();

  const { t } = useTranslate();
  const match = useRouteMatch();
  const history = useHistory();
  const query = parseQuery(history.location);
  const [dataLayers] = useActiveDataLayerLoader();
  const [electroconductivity] = useFieldsElectroconductivityLoader();
  const [isShowRemoveAlert, setIsShowRemoveAlert] = useState(false);

  const redirectToNextPage = () => {
    if (query.next) {
      //@ts-ignore
      window.location.replace(decodeURIComponent(query.next));
    }
  };

  useSaga(function* () {
    function* handleFinishSoilSamplingTasks() {
      try {
        const activeTasks: ImportTaskType[] = yield select(
          selectors.getActiveTasks,
        );
        const activeSoilSamplingTasks = activeTasks.filter(
          task => task.data_type === DataTypesKey.soilSampling,
        );

        yield all(
          activeSoilSamplingTasks.map(task =>
            call(apiCall, actions.finishTask(task)),
          ),
        );
        yield call(redirectToNextPage);
      } catch (error) {
        console.error('Error finishing soil sampling tasks:', error);
      }
    }
    function* handleFinishOperations() {
      const operations: ApiDataFieldOperations[] = yield select(
        selectActiveOperations,
      );
      try {
        yield call(apiCall, actions.finished(operations));
        const sourceWithEnabledOperation = uniq(
          operations
            .filter(v => v.is_enabled)
            .map(v => v.data_source.layer.source_uuid),
        );
        const taskWithoutActiveOperation = activeTasks.filter(
          task =>
            !task.sources.some(v =>
              sourceWithEnabledOperation.includes(v.uuid),
            ),
        );
        taskWithoutActiveOperation.forEach(v => put(actions.removeTask(v)));
        if (query.next) {
          yield call(redirectToNextPage);
        } else history.push(match.url);
      } catch (error) {
        console.error('Error finishing soil sampling tasks:', error);
      }
    }
    yield takeEvery(
      actions.finishSoilSamplingTasks,
      handleFinishSoilSamplingTasks,
    );
    yield takeEvery(actions.finishOperations, handleFinishOperations);
    yield takeEvery(
      [
        resolved(actions.finishedElectroconductivity),
        resolved(actions.finished),
      ],
      redirectToNextPage,
    );
  }, []);

  const hasMatchedAttributes = dataLayers.some(layer =>
    layer.properties.some(property => !!property.target_name),
  );

  const dispatch = useDispatch();
  const importFilesSteps = useImportFilesSteps();
  const activeTasks: ImportTaskType[] = useSelector(selectors.getActiveTasks);

  const dataType = query['data-type'] || DataTypesKey.machinery;
  const strQuery = stringifyQuery(query);

  const hasUnmatchedLayer = activeTasks.some(item =>
    item.sources.some(source =>
      (source?.layers || []).some(layer => !layer.is_spatial_matched),
    ),
  );

  if (importFilesSteps.length <= 1) {
    return null;
  }

  const activeIndex = importFilesSteps.findIndex(step => {
    return matchPath(window.location.pathname, {
      path: `${match.url}${step.to}`,
      exact: true,
    });
  });

  const currentStep = importFilesSteps[activeIndex] as StepRoute;
  const nextStep = importFilesSteps[activeIndex + 1];
  const stepReducer = dataType === DataTypesKey.machinery ? 2 : 1;
  const isLastStep = activeIndex === importFilesSteps.length - stepReducer;

  if (!currentStep) {
    return null;
  }

  const nextButtonProps: SmartButtonProps = {};
  if (currentStep?.valid === StepValidationStatus.valid) {
    if (
      dataType !== DataTypesKey.electroconductivity &&
      (hasUnmatchedLayer || (isLastStep && !hasMatchedAttributes))
    ) {
      //@ts-ignore
      nextButtonProps.disabled = true;
    }
    if (!isLastStep && nextStep && !hasUnmatchedLayer) {
      //@ts-ignore
      nextButtonProps.to = match.url + nextStep?.to + `?${strQuery}`;
      if (nextStep.status === StepStatus.loading) {
        nextButtonProps.pending = true;
      }
    } else {
      nextButtonProps.onClick = () => {
        if (dataType === DataTypesKey.soilSampling) {
          dispatch(actions.finishSoilSamplingTasks());
        } else if (dataType === DataTypesKey.electroconductivity) {
          dispatch(actions.finishedElectroconductivity(electroconductivity));
        } else {
          if (!query.next) {
            dispatch(
              toastActions.push({
                id: TOAST_KEY_TO_IMPORT,
                title: 'import.by-machinery.toast.processing.start.title',
                text: 'import.by-machinery.toast.processing.start.description',
                icon: 'checkSuccess',
                actions: [
                  {
                    label: 'import.by-machinery.toast.processing.success.ok',
                  },
                ],
              }),
            );
          }
          dispatch(actions.finishOperations());
        }
      };
    }
  } else if (currentStep.valid === StepValidationStatus.needFields) {
    //@ts-ignore
    nextButtonProps.disabled = true;
  } else {
    nextButtonProps.onClick = () =>
      dispatch(
        alert.actions.show({
          show: true,
          title: t(
            `import.matching_operations.${currentStep.valid}.title`,
          ) as string,
          text: t(
            `import.matching_operations.${currentStep.valid}.description`,
          ) as string,
          danger: t('alert.ok') as string,
          onDanger: () => {
            dispatch(alert.actions.close());
          },
        }),
      );
  }

  const handleCancel = () => {
    activeTasks.forEach(task => {
      dispatch(actions.removeTask(task));
    });
    dispatch(entities.actions.clearRequest(OPERATIONS_REQUEST_ID));
    history.push(match.url + `?${strQuery}`);
  };
  return (
    <FloatingActionsStyle>
      <SmartButton
        className='btn btn-dark btn-lg'
        onClick={(e: MouseEvent) => {
          e.preventDefault();
          setIsShowRemoveAlert(true);
        }}
      >
        {t('import.by-machinery.import.cancel')}
      </SmartButton>

      <SmartButton className='btn btn-success btn-lg' {...nextButtonProps}>
        {!isLastStep && t('import.by-machinery.import.continue')}
        {isLastStep && t('import.by-machinery.import.save')}
        <span className='btn__ico btn__ico-right'>
          <Icon className='ico-check-os' name='arrow-right2' />
        </span>
      </SmartButton>
      {isShowRemoveAlert && (
        <Alert
          show={true}
          text={t(`import.by-machinery.alert.remove.text`)}
          onDanger={handleCancel}
          onCancel={() => setIsShowRemoveAlert(false)}
          danger={t('import.by-machinery.alert.remove.confirm') as string}
          success={t('import.by-machinery.alert.remove.cancel') as string}
        />
      )}
    </FloatingActionsStyle>
  );
};
