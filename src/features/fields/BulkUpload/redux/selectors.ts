import { createSelector } from 'reselect';

import RootState from 'types/rootState';
import { isBundleValid, isStepReached } from 'features/fields/BulkUpload/utils';
import {
  BulkUploadStep,
  FileBundle,
  FileBundleStatus,
} from 'features/fields/BulkUpload/types';

import { getTotalSize, BULK_UPLOAD_MAX_TOTAL_SIZE } from 'utils/import-fields';

import { isExtensionValid } from 'utils/import-fields';
import { Notification } from 'features/platform/PlatformSideBar/types';

const getFiles = (state: RootState) => state.bulkUpload.files;

const getValidFiles = createSelector(
  (state: RootState) => getFiles(state),
  files => files.filter(isExtensionValid),
);

const isProcessingDisabled = createSelector(
  (state: RootState) => getValidFiles(state),
  validFiles => {
    const noValidFilesSelected = validFiles.length === 0;
    const totalSizeExceedsLimit =
      getTotalSize(validFiles) > BULK_UPLOAD_MAX_TOTAL_SIZE;

    return noValidFilesSelected || totalSizeExceedsLimit;
  },
);

const getCurrentStep = (state: RootState) => state.bulkUpload.currentStep;

const getReachedStep = (state: RootState) => state.bulkUpload.reachedStep;

const getProcessingProgress = (state: RootState) =>
  state.bulkUpload.processingProgress;

const getProcessingStatus = (state: RootState) =>
  state.bulkUpload.processingStatus;

const getFileBundles = (state: RootState) => state.bulkUpload.fileBundles;

const getValidFileBundles = createSelector(
  (state: RootState) => getFileBundles(state),
  bundles => bundles.filter(bundle => isBundleValid(bundle)),
);

const getInvalidFileBundles = createSelector(
  (state: RootState) => getFileBundles(state),
  bundles => bundles.filter(bundle => !isBundleValid(bundle)),
);

const isUploadingDisabled = createSelector(
  (state: RootState) => getValidFileBundles(state),
  validBundles => {
    return validBundles.length === 0;
  },
);

const getValidFileBundlesWithProps = createSelector(
  (state: RootState) => getValidFileBundles(state),
  validBundles =>
    validBundles.filter(bundle => {
      return (
        bundle.uploadingResult && !bundle.uploadingResult.has_empty_properties
      );
    }),
);

const getValidFileIdsWithProps = createSelector(
  (state: RootState) => getValidFileBundlesWithProps(state),
  (bundles: FileBundle[]) => bundles.map(bundle => bundle.uploadingResult!.id),
);

const getUploadedFileBundles = createSelector(
  (state: RootState) => getValidFileBundles(state),
  validBundles =>
    validBundles.filter(bundle => Boolean(bundle.uploadingResult)),
);

const getFailedToUploadFileBundles = createSelector(
  (state: RootState) => getValidFileBundles(state),
  validBundles => validBundles.filter(bundle => Boolean(bundle.uploadingError)),
);

const getUploadingInProgress = createSelector(
  (state: RootState) => getValidFileBundles(state),
  validBundles => {
    return validBundles.some(bundle => {
      return (
        bundle.status === FileBundleStatus.WAITING_FOR_UPLOAD ||
        bundle.status === FileBundleStatus.UPLOADING
      );
    });
  },
);

const getFieldsFetchingPending = (state: RootState) =>
  state.bulkUpload.fieldsFetchingPending;

const isImportDone = createSelector(
  (state: RootState) => getValidFileBundles(state),
  validBundles => {
    return validBundles.every(bundle => {
      return Boolean(bundle.uploadingError) || Boolean(bundle.uploadingResult);
    });
  },
);

const getFileBundlesUploadingResults = createSelector(
  (state: RootState) => getUploadedFileBundles(state),
  uploadedBundles => uploadedBundles.map(bundle => bundle.uploadingResult),
);

const getUploadedFilesIds = createSelector(
  (state: RootState) => getFileBundlesUploadingResults(state),
  results => results.map(result => result?.id).filter(Boolean) as number[],
);

const getImportedFields = (state: RootState) => state.bulkUpload.importedFields;

const getBbox = (state: RootState) => state.bulkUpload.bbox;

const getCropMatchingAvailable = (state: RootState) =>
  state.bulkUpload.cropMatchingAvailable;

const getCheckedIDs = (state: RootState) => state.bulkUpload.checkedIDs;

const getAvailableSteps = createSelector(
  getReachedStep,
  getUploadingInProgress,
  getValidFileIdsWithProps,
  getCropMatchingAvailable,
  (
    reachedStep,
    uploadingInProgress,
    validFileIdsWithProps,
    cropMatchingAvailable,
  ) => {
    const isUploadingStepAvailable = isStepReached(
      BulkUploadStep.UPLOADING,
      reachedStep,
    );
    const isUploadingStepClickable =
      isUploadingStepAvailable && uploadingInProgress;

    const isColumnMatchingAvailable =
      validFileIdsWithProps.length === 1 &&
      isStepReached(BulkUploadStep.COLUMN_MATCHING, reachedStep);

    const isCropsMatchingAvailable: boolean =
      cropMatchingAvailable &&
      isStepReached(BulkUploadStep.CROP_MATCHING, reachedStep);

    return {
      [BulkUploadStep.FILE_SELECTION]: {
        available: true,
        clickable: true,
        visible: true,
      },
      [BulkUploadStep.PROCESSING]: {
        available: isStepReached(BulkUploadStep.PROCESSING, reachedStep),
        clickable: false, // accessible only by transition from FILE_SELECTION
        visible: true,
      },
      [BulkUploadStep.CHECK_DATA]: {
        available: isStepReached(BulkUploadStep.CHECK_DATA, reachedStep),
        clickable: isStepReached(BulkUploadStep.CHECK_DATA, reachedStep),
        visible: true,
      },
      [BulkUploadStep.UPLOADING]: {
        available: isUploadingStepAvailable,
        clickable: isUploadingStepClickable,
        visible: true,
      },
      [BulkUploadStep.COLUMN_MATCHING]: {
        available: isColumnMatchingAvailable,
        clickable: isColumnMatchingAvailable,
        visible: isColumnMatchingAvailable,
      },
      [BulkUploadStep.CROP_MATCHING]: {
        available: isCropsMatchingAvailable,
        clickable: isCropsMatchingAvailable,
        visible: isCropsMatchingAvailable,
      },
      [BulkUploadStep.SAVING]: {
        available: isStepReached(BulkUploadStep.SAVING, reachedStep),
        clickable: isStepReached(BulkUploadStep.SAVING, reachedStep),
        visible: true,
      },
    };
  },
);

const getBulkUploadNotifications = createSelector(
  getCurrentStep,
  getProcessingProgress,
  getValidFileBundles,
  getInvalidFileBundles,
  isImportDone,
  getUploadedFileBundles,
  getFailedToUploadFileBundles,
  (
    currentStep,
    processingProgress,
    validBundles,
    invalidBundles,
    isImportDone,
    uploadedBundles,
    failedToUploadBundles,
  ): Notification => {
    if (currentStep === BulkUploadStep.PROCESSING) {
      return {
        progress: processingProgress,
      };
    }

    if (currentStep === BulkUploadStep.CHECK_DATA) {
      return invalidBundles.length !== 0
        ? { icon: 'alert-circle-bordered' }
        : { icon: 'success-circle-bordered' };
    }

    if (
      currentStep === BulkUploadStep.UPLOADING ||
      currentStep === BulkUploadStep.COLUMN_MATCHING ||
      currentStep === BulkUploadStep.CROP_MATCHING
    ) {
      if (isImportDone) {
        return failedToUploadBundles.length !== 0
          ? { icon: 'alert-circle-bordered' }
          : { icon: 'success-circle-bordered' };
      } else {
        const uploaded = uploadedBundles.length + failedToUploadBundles.length;
        return {
          progress: uploaded / validBundles.length,
        };
      }
    }
  },
);

export const selectors = {
  getFiles,
  getValidFiles,
  isProcessingDisabled,
  getProcessingProgress,
  getProcessingStatus,
  getCurrentStep,
  getReachedStep,
  getFileBundles,
  getValidFileBundles,
  getInvalidFileBundles,
  isUploadingDisabled,
  getValidFileBundlesWithProps,
  getValidFileIdsWithProps,
  getUploadingInProgress,
  getUploadedFileBundles,
  getFailedToUploadFileBundles,
  getFieldsFetchingPending,
  isImportDone,
  getFileBundlesUploadingResults,
  getUploadedFilesIds,
  getImportedFields,
  getAvailableSteps,
  getBulkUploadNotifications,
  getCheckedIDs,
  getBbox,
};
