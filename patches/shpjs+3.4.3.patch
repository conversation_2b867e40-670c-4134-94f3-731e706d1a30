diff --git a/node_modules/shpjs/lib/parseShp.js b/node_modules/shpjs/lib/parseShp.js
index eb5666d..8c9bdde 100644
--- a/node_modules/shpjs/lib/parseShp.js
+++ b/node_modules/shpjs/lib/parseShp.js
@@ -255,6 +255,8 @@ ParseShp.prototype.getRows = function() {
     offset += current.len;
     if (current.type) {
       out.push(this.parseFunc(current.data));
+    } else {
+      out.push(null);
     }
   }
   return out;
