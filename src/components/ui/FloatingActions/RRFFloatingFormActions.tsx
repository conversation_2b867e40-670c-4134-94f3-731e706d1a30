import React, { VFC } from 'react';
import cx from 'classnames';

import Icon from 'components/Icon';
import SmartButton from 'components/ui/Button/SmartButton';
// @ts-ignore
import SubmitButton from 'components/forms/SubmitButton';

import {
  FloatingActionsStyle,
  FloatingActionsDescriptionStyle,
  FloatingActionsNameStyle,
} from './styles';

// RRF - react-redux-form

type RRFFloatingFormActionsProps = {
  title: string;
  entityName: string;
  cancelLabel: string;
  submitLabel: string;
  onCancel: VoidCallBack;
  submitDisabled?: boolean;
  model: string;
};

const RRFFloatingFormActions: VFC<RRFFloatingFormActionsProps> = ({
  title,
  entityName,
  model,
  cancelLabel,
  submitLabel,
  submitDisabled,
  onCancel,
}) => (
  <FloatingActionsStyle>
    <FloatingActionsDescriptionStyle>
      <small>{title}</small>
      <br />
      <FloatingActionsNameStyle>{entityName}</FloatingActionsNameStyle>
    </FloatingActionsDescriptionStyle>
    <SmartButton
      className='btn btn-dark btn-lg'
      onClick={() => {
        onCancel();
      }}
    >
      {cancelLabel}
    </SmartButton>
    <SubmitButton
      model={model}
      disabled={submitDisabled}
      // @ts-ignore
      render={props => (
        <SmartButton
          className={cx('btn btn-success btn-lg', {
            '__no-visual-disable': !submitDisabled,
          })}
          {...props}
        >
          <span className='btn__ico'>
            <Icon className='ico-check-os' name='check' />
          </span>
          {submitLabel}
        </SmartButton>
      )}
    />
  </FloatingActionsStyle>
);

export default RRFFloatingFormActions;
