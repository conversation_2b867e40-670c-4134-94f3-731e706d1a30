import React from 'react';
import { connect } from 'react-redux';

import Icon from 'components/Icon';
import Button from 'components/ui/Button/Button';

import settings from 'modules/settings';

import { formatYieldUnit } from 'utils/units';
import { formatDate } from 'utils/format-date';
import { useTranslate } from 'utils/use-translate';

const EditFieldOtherSeasonRow = ({
  season,
  units,
  yieldUnitsByCrop,
  crops,
  onSelect,
  styles,
}) => {
  const { t } = useTranslate();
  return crops.map((crop, rowIndex) => (
    <tr
      className='cursor-pointer'
      key={rowIndex}
      onClick={() => {
        onSelect();
      }}
    >
      <td>
        {crop.crop ? (
          t(`crop.${crop.crop}`)
        ) : (
          <span className='c-neutral'>{t('fields.edit.crops.unknown')}</span>
        )}
      </td>
      <td>
        {crop.variety ? (
          crop.variety
        ) : (
          <span className='c-neutral'>{t('fields.edit.crops.unknown')}</span>
        )}
      </td>
      <td>
        {crop.sowing_date ? (
          formatDate(crop.sowing_date, t('fields.edit.crops.date_format'))
        ) : (
          <span className='c-neutral'>{t('fields.edit.crops.unknown')}</span>
        )}
      </td>
      <td>
        {crop.harvest_date ? (
          formatDate(crop.harvest_date, t('fields.edit.crops.date_format'))
        ) : (
          <span className='c-neutral'>{t('fields.edit.crops.unknown')}</span>
        )}
      </td>
      <td>
        {crop.yield_value ? (
          `${crop.yield_value} ${formatYieldUnit(
            crop.yield_value_units,
            units,
            yieldUnitsByCrop[crop.crop],
            t,
          )}`
        ) : (
          <span className='c-neutral'>{t('fields.edit.crops.unknown')}</span>
        )}
      </td>
      <td>
        <div className='table-seasons-actions hid-v'>
          <div className='table-seasons-actions__item'>
            {/* onClick is used on tr above */}
            <Button className='btn btn-default btn-icon'>
              <span className='btn__ico'>
                <Icon width={14} height={14} name='pencil' />
              </span>
            </Button>
          </div>
        </div>
      </td>
    </tr>
  ));
};

const mapStateToProps = (state, ownProps) => ({
  units: settings.selectors.getUnits(state),
  yieldUnitsByCrop: settings.selectors.getYieldUnitsByCrop(state),
});

export default connect(mapStateToProps)(EditFieldOtherSeasonRow);
