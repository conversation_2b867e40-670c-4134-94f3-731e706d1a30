import {
  BulkUploadState,
  BulkUploadStep,
} from 'features/fields/BulkUpload/types';

export const InitialState: BulkUploadState = {
  files: [],
  currentStep: BulkUploadStep.FILE_SELECTION,
  reachedStep: BulkUploadStep.FILE_SELECTION,
  processingProgress: 0,
  processingStatus: {},
  fileBundles: [],
  importedFields: [],
  fieldsFetchingPending: false,
  cropMatchingAvailable: false,
  checkedIDs: [],
  bbox: null,
};
