import React, { CSSProperties, useCallback, useState, VFC } from 'react';
import { match, Route, Switch, useHistory } from 'react-router-dom';
import { cx } from 'linaria';
import { useSelector } from 'react-redux';

import useOutsideClick from 'utils/use-outside-click';
import settings from 'modules/settings';
import {
  StyledAddCrop,
  StyledCropGroup,
  StyledCropGroupWrap,
  StyledEmptyContainer,
  StyledFieldsCropsCell,
  StyledKebabButton,
  styles,
} from '../CropRotationView.style';

// @ts-ignore
import AddCropPopup from '../../AddCropPopup';
// @ts-ignore
import CropDetailsPopup from '../../CropDetailsPopup';

import {
  PopoverDeprecated,
  PopoverDeprecatedAlign,
} from 'components/ui/Popover';
import Icon from 'components/Icon';
import logEvent from 'sagas/global/logEvent';
import AutoCreateFieldSeason from './AutoCreateFieldSeason';
import { StyledColorPreview } from 'features/crop-rotation/AutoFillPopup.style';
import CropWarningTooltip from 'features/crop-rotation/CropWarningTooltip';
import { CropCellType } from '../types';
import { Notes } from 'types/api/index';
import { formatDate } from 'utils/format-date';
import { TranslateFunction } from 'utils/use-translate';
// @ts-ignore
import { formatYieldUnit } from 'utils/units';
import { UnitSystems } from 'constants/units';
import SettingsState from 'modules/settings/types';
import { FeatureDisabledTooltip, FEATURES } from 'features/permissions';

import { CropRotationTooltip } from 'features/crop-rotation/CropRotationTooltip/CropRotationTooltip';
import fieldSeasonModule from 'modules/fieldSeason';

type FieldsCropCellProps = {
  seasonId: number;
  currentSeasonId: number;
  fieldId: number;

  notes: Notes[];

  columnIndex: number;
  rowIndex: number;
  rowsCount: number;

  match: match;
  cell: CropCellType;
  t: TranslateFunction;

  openEditGeometry: (fieldSeasonId?: number) => void;
  setShowRemoveEntityPopup: (fieldSeasonId?: number) => void;
  setShowRemoveFieldWithNotesPopup: (fieldSeasonId?: number) => void;
  style?: CSSProperties;

  // possible deprecated
  activeRowIndex?: number;
  activeCellIndex?: number;
  onMouseEnter?: string;
  setActiveCellIndex?: string;
};

const FieldsCropCell: VFC<FieldsCropCellProps> = ({
  seasonId,
  currentSeasonId,
  fieldId,
  columnIndex,
  rowIndex,
  activeRowIndex,
  activeCellIndex,
  rowsCount,
  cell,
  notes,
  match,
  t,
  onMouseEnter,
  setActiveCellIndex,
  openEditGeometry,
  setShowRemoveEntityPopup,
  setShowRemoveFieldWithNotesPopup,
  ...props
}) => {
  const [kebabMenuOpened, setKebabMenuOpened] = useState(false);
  const [fieldPopoverOpened, setFieldPopoverOpened] = useState<
    boolean | string
  >(false);
  const [cropListPopoverOpened, setCropListPopoverOpened] = useState(false);

  const closeFieldPopover = useCallback(() => setFieldPopoverOpened(false), []);
  const onInsideFieldClick = useOutsideClick(closeFieldPopover);

  const closeKebabMenu = useCallback(() => setKebabMenuOpened(false), []);
  const onInsideKebabClick = useOutsideClick(closeKebabMenu);
  const onInsideCropListClick = useOutsideClick(() =>
    setCropListPopoverOpened(false),
  );

  const fieldBySeason = useSelector(state => {
    return fieldSeasonModule.selectors.getFieldBydSeason(
      state,
      currentSeasonId,
      fieldId,
    );
  });

  const units = useSelector(
    settings.selectors.getUnits,
  ) as typeof UnitSystems.metric;
  const yieldUnitsByCrop = useSelector(
    settings.selectors.getYieldUnitsByCrop,
  ) as SettingsState['yieldUnitsByCrop'];
  const history = useHistory();
  const crops = cell.crops;

  const hasLinkedNotes = useCallback(
    (fieldSeasonUuid: string | undefined) => {
      return notes.some(n => n.field_user_season_uuid === fieldSeasonUuid);
    },
    [notes],
  );

  const removeField = useCallback(() => {
    if (hasLinkedNotes(cell.fieldSeasonUuid)) {
      setShowRemoveFieldWithNotesPopup(cell.fieldSeasonId);
    } else {
      setShowRemoveEntityPopup(cell.fieldSeasonId);
    }
  }, [
    cell,
    setShowRemoveFieldWithNotesPopup,
    setShowRemoveEntityPopup,
    hasLinkedNotes,
  ]);

  if (!fieldBySeason) {
    return null;
  }

  if (!crops || crops.length === 0) {
    return (
      <StyledFieldsCropsCell
        style={props.style}
        columnIndex={columnIndex}
        isActive={false}
      >
        <StyledAddCrop onClickCapture={onInsideFieldClick}>
          {!!cell.fieldSeasonId && (
            <>
              <PopoverDeprecated
                active={!!fieldPopoverOpened}
                offset={[0, 0]}
                align={PopoverDeprecatedAlign.Overlap}
                timeout={50}
                renderTrigger={({ ref, ...otherProps }) => (
                  <FeatureDisabledTooltip
                    feature={FEATURES.CROP_ADD}
                    data={fieldBySeason}
                    direction={PopoverDeprecatedAlign.MiddleRight}
                  >
                    <StyledEmptyContainer
                      ref={ref}
                      active={activeCellIndex === cell.fieldSeasonId}
                      onClick={() => {
                        setFieldPopoverOpened(!fieldPopoverOpened);
                        history.replace(`${match.url}/add-crop`);

                        logEvent('crop_rotation_field_season_click', {
                          field_season_id: cell.fieldSeasonId,
                          has_crops: false,
                        });
                      }}
                      {...otherProps}
                    >
                      <div className={cx(styles.add, 'add-crop')}>
                        {t('croprotation.cell.add_crop')}
                      </div>
                    </StyledEmptyContainer>
                  </FeatureDisabledTooltip>
                )}
                renderPopover={({ style, ...props }) => (
                  <CropRotationTooltip
                    style={style}
                    width={360}
                    onEscapeClickHandler={() =>
                      setFieldPopoverOpened(!fieldPopoverOpened)
                    }
                    {...props}
                  >
                    <Switch>
                      <Route
                        path={'*/add-crop'}
                        exact
                        render={props => (
                          <AddCropPopup
                            fieldId={fieldId}
                            seasonId={seasonId}
                            fieldSeasonId={cell.fieldSeasonId}
                            {...props}
                          />
                        )}
                      />
                      <Route
                        path={'*/crop-details'}
                        render={props => (
                          <CropDetailsPopup
                            fieldSeasonId={cell.fieldSeasonId}
                            noDelete
                            logEvent={logEvent}
                            onClose={closeFieldPopover}
                            {...props}
                          />
                        )}
                      />
                    </Switch>
                  </CropRotationTooltip>
                )}
              />
              <div onClickCapture={onInsideKebabClick}>
                <PopoverDeprecated
                  active={kebabMenuOpened}
                  offset={[0, 0]}
                  align={PopoverDeprecatedAlign.Overlap}
                  timeout={50}
                  renderTrigger={({ ref, ...otherProps }) => (
                    <StyledKebabButton
                      ref={ref}
                      emptyField
                      onClick={() => {
                        history.replace(`${match.url}`);
                        logEvent('crop_rotation_kebab_open_click');
                        setKebabMenuOpened(!kebabMenuOpened);
                      }}
                      {...otherProps}
                    />
                  )}
                  renderPopover={({ style, ...props }) => (
                    <CropRotationTooltip
                      style={style}
                      minWidth={211}
                      onEscapeClickHandler={closeKebabMenu}
                      {...props}
                    >
                      <Switch>
                        <Route
                          path={'*/crop-details'}
                          render={props => (
                            <CropDetailsPopup
                              logEvent={logEvent}
                              noDelete
                              fieldSeasonId={cell.fieldSeasonId}
                              onClose={closeKebabMenu}
                              {...props}
                            />
                          )}
                        />
                        <Route
                          path={'*/add-crop'}
                          render={props => (
                            <AddCropPopup
                              fieldId={fieldId}
                              seasonId={seasonId}
                              fieldSeasonId={cell.fieldSeasonId}
                              {...props}
                            />
                          )}
                        />
                        <Route
                          render={() => {
                            return (
                              <ul className='popover-select__list'>
                                <FeatureDisabledTooltip
                                  feature={FEATURES.CROP_ADD}
                                  data={fieldBySeason}
                                  direction={PopoverDeprecatedAlign.MiddleRight}
                                >
                                  <li className='popover-select__list-item'>
                                    <div
                                      className='popover-select__item'
                                      onClick={() =>
                                        history.replace(`${match.url}/add-crop`)
                                      }
                                    >
                                      <span className='btn__ico'>
                                        <Icon
                                          className='ico-plus-os'
                                          name='plus-btn'
                                        />
                                      </span>
                                      {t('croprotation.cell.kebab.add_crop')}
                                    </div>
                                  </li>
                                </FeatureDisabledTooltip>
                                <FeatureDisabledTooltip
                                  data={fieldBySeason}
                                  feature={FEATURES.FIELD_EDIT_BOUNDARIES}
                                  direction={PopoverDeprecatedAlign.MiddleRight}
                                >
                                  <li className='popover-select__list-item'>
                                    <div
                                      className='popover-select__item'
                                      onClick={e => {
                                        openEditGeometry(cell.fieldSeasonId);
                                        setKebabMenuOpened(!kebabMenuOpened);

                                        logEvent(
                                          'crop_rotation_edit_border_click',
                                          {
                                            field_season_id: cell.fieldSeasonId,
                                          },
                                        );
                                      }}
                                    >
                                      <span className='btn__ico'>
                                        <Icon
                                          width={16}
                                          height={16}
                                          name='pencil'
                                        />
                                      </span>
                                      {t(
                                        'croprotation.cell.kebab.edit_geometry',
                                      )}
                                    </div>
                                  </li>
                                </FeatureDisabledTooltip>
                                <FeatureDisabledTooltip
                                  data={fieldBySeason}
                                  feature={FEATURES.FIELD_DELETE}
                                  direction={PopoverDeprecatedAlign.MiddleRight}
                                >
                                  <li className='popover-select__list-item'>
                                    <div
                                      className='popover-select__item popover-select__item-danger'
                                      onClick={e => {
                                        removeField();
                                        setKebabMenuOpened(!kebabMenuOpened);
                                      }}
                                    >
                                      <span className='btn__ico'>
                                        <Icon
                                          className='ico-trash-n-os'
                                          name='trashcan'
                                        />
                                      </span>
                                      {t(
                                        'croprotation.cell.kebab.remove_field',
                                      )}
                                    </div>
                                  </li>
                                </FeatureDisabledTooltip>
                              </ul>
                            );
                          }}
                        />
                      </Switch>
                    </CropRotationTooltip>
                  )}
                />
              </div>
            </>
          )}
          {!cell.fieldSeasonId && (
            <PopoverDeprecated
              active={!!fieldPopoverOpened}
              offset={[0, 0]}
              align={PopoverDeprecatedAlign.Overlap}
              timeout={50}
              renderTrigger={({ ref, ...otherProps }) => (
                <FeatureDisabledTooltip
                  feature={FEATURES.FIELD_ADD_TO_SEASON}
                  data={fieldBySeason}
                  direction={PopoverDeprecatedAlign.MiddleRight}
                >
                  <StyledCropGroup
                    ref={ref}
                    flex={1}
                    color={'#E4E7EA'}
                    isEmpty
                    onClick={() => {
                      history.replace(`${match.url}/add-crop`);
                      setFieldPopoverOpened(!fieldPopoverOpened);
                      logEvent('crop_rotation_field_click', {
                        field_id: fieldId,
                        season_id: seasonId,
                      });
                    }}
                    {...otherProps}
                  >
                    <span className={cx(styles.cellText, '__inactive')}>
                      {t('croprotation.cell.field_removed')}
                    </span>
                    <div className={cx(styles.add, 'add-crop')}>
                      {t('croprotation.cell.add_crop')}
                    </div>
                  </StyledCropGroup>
                </FeatureDisabledTooltip>
              )}
              renderPopover={({ style, ...props }) => (
                <CropRotationTooltip
                  style={style}
                  width={360}
                  onEscapeClickHandler={closeFieldPopover}
                  {...props}
                >
                  <Switch>
                    <Route
                      path={'*/add-crop'}
                      render={() => (
                        <AutoCreateFieldSeason
                          fieldId={fieldId}
                          seasonId={seasonId}
                          fieldSeasonId={cell.fieldSeasonId}
                        />
                      )}
                    />
                  </Switch>
                </CropRotationTooltip>
              )}
            />
          )}
        </StyledAddCrop>
      </StyledFieldsCropsCell>
    );
  }

  if (crops.length > 2) {
    return (
      <StyledFieldsCropsCell
        style={props.style}
        columnIndex={columnIndex}
        isActive={activeRowIndex === rowIndex}
      >
        <div className={styles.cellLink}>
          <StyledCropGroupWrap flex={0.5} onClickCapture={onInsideFieldClick}>
            <PopoverDeprecated
              active={fieldPopoverOpened === '0'}
              offset={[0, 0]}
              align={PopoverDeprecatedAlign.Overlap}
              timeout={50}
              renderTrigger={({ ref, ...otherProps }) => (
                <StyledCropGroup
                  ref={ref}
                  flex={1}
                  color={crops[0]?.color}
                  onClick={() => {
                    history.replace(`${match.url}/crop-details`);
                    setFieldPopoverOpened('0');

                    logEvent('crop_rotation_field_season_click', {
                      field_season_id: cell.fieldSeasonId,
                      has_crops: true,
                    });
                  }}
                  {...otherProps}
                >
                  <span className={cx(styles.cellText, styles.oneLine)}>
                    {crops[0]?.name}
                  </span>
                </StyledCropGroup>
              )}
              renderPopover={({ style, ...props }) => (
                <CropRotationTooltip
                  style={style}
                  width={360}
                  onEscapeClickHandler={closeFieldPopover}
                  {...props}
                >
                  <Route
                    path={'*/crop-details'}
                    render={props => (
                      <CropDetailsPopup
                        fieldSeasonId={cell.fieldSeasonId}
                        cropIndex={'0'}
                        logEvent={logEvent}
                        onClose={closeFieldPopover}
                        {...props}
                      />
                    )}
                  />
                </CropRotationTooltip>
              )}
            />
          </StyledCropGroupWrap>

          <StyledCropGroupWrap
            flex={0.5}
            onClickCapture={onInsideCropListClick}
          >
            <PopoverDeprecated
              active={cropListPopoverOpened}
              offset={[0, 0]}
              align={PopoverDeprecatedAlign.Overlap}
              timeout={50}
              renderTrigger={({ ref, ...otherProps }) => (
                <StyledCropGroup
                  ref={ref}
                  flex={1}
                  color={crops[1]?.color}
                  onClick={() => {
                    history.replace(`${match.url}/crop-list`);
                    setCropListPopoverOpened(!cropListPopoverOpened);

                    logEvent('crop_rotation_field_season_click', {
                      field_season_id: cell.fieldSeasonId,
                      has_crops: true,
                    });
                  }}
                  {...otherProps}
                >
                  <span className={styles.cellText}>
                    {t(
                      // @ts-ignore
                      'croprotation.cell.many_crops',
                      {
                        count: crops.length - 1,
                      },
                    )}
                  </span>
                </StyledCropGroup>
              )}
              renderPopover={({ style, ...props }) => (
                <CropRotationTooltip
                  style={style}
                  minWidth={225}
                  onEscapeClickHandler={() => setCropListPopoverOpened(false)}
                  {...props}
                >
                  <Route
                    path={'*/crop-list'}
                    render={() => (
                      <ul className='popover-select__list'>
                        {crops.slice(1).map((crop, index) => (
                          <li className='popover-select__list-item'>
                            <div
                              className='popover-select__item'
                              onClick={() => {
                                history.replace(
                                  `${match.url}/crop-details/${index + 1}`,
                                );
                              }}
                            >
                              <StyledColorPreview
                                color={crops[index + 1]?.color as string}
                              />
                              {crop.name}
                            </div>
                          </li>
                        ))}
                      </ul>
                    )}
                  />
                  <Route
                    path={'*/crop-details/:cropIndex'}
                    render={props => (
                      <CropDetailsPopup
                        fieldSeasonId={cell.fieldSeasonId}
                        logEvent={logEvent}
                        onClose={() => setCropListPopoverOpened(false)}
                        {...props}
                      />
                    )}
                  />
                </CropRotationTooltip>
              )}
            />
          </StyledCropGroupWrap>

          {crops.some(
            crop => crop.recommendation && !crop.recommendation.is_rotation_ok,
          ) && <CropWarningTooltip recommendation={crops[0]?.recommendation} />}
        </div>
        <div onClickCapture={onInsideKebabClick}>
          <PopoverDeprecated
            active={kebabMenuOpened}
            offset={[0, 0]}
            align={PopoverDeprecatedAlign.Overlap}
            timeout={50}
            renderTrigger={({ ref, ...otherProps }) => (
              <StyledKebabButton
                ref={ref}
                onClick={e => {
                  history.replace(`${match.url}`);
                  logEvent('crop_rotation_kebab_open_click');
                  setKebabMenuOpened(!kebabMenuOpened);
                }}
                {...otherProps}
              />
            )}
            renderPopover={({ style, ...props }) => (
              <CropRotationTooltip
                style={style}
                minWidth={211}
                onEscapeClickHandler={closeKebabMenu}
                {...props}
              >
                <Switch>
                  <Route
                    path={'*/crop-details'}
                    render={props => (
                      <CropDetailsPopup
                        fieldSeasonId={cell.fieldSeasonId}
                        noDelete
                        logEvent={logEvent}
                        onClose={closeKebabMenu}
                        {...props}
                      />
                    )}
                  />
                  <Route
                    path={'*/add-crop'}
                    render={props => (
                      <AddCropPopup
                        fieldId={fieldId}
                        seasonId={seasonId}
                        fieldSeasonId={cell.fieldSeasonId}
                        {...props}
                      />
                    )}
                  />
                  <Route
                    render={() => {
                      return (
                        <ul className='popover-select__list'>
                          <FeatureDisabledTooltip
                            feature={FEATURES.CROP_ADD}
                            data={fieldBySeason}
                            direction={PopoverDeprecatedAlign.MiddleRight}
                          >
                            <li className='popover-select__list-item'>
                              <div
                                className='popover-select__item'
                                onClick={() =>
                                  history.replace(`${match.url}/add-crop`)
                                }
                              >
                                <span className='btn__ico'>
                                  <Icon
                                    className='ico-plus-os'
                                    name='plus-btn'
                                  />
                                </span>
                                {t('croprotation.cell.kebab.add_crop')}
                              </div>
                            </li>
                          </FeatureDisabledTooltip>
                          <FeatureDisabledTooltip
                            data={fieldBySeason}
                            feature={FEATURES.FIELD_EDIT_BOUNDARIES}
                            direction={PopoverDeprecatedAlign.MiddleRight}
                          >
                            <li className='popover-select__list-item'>
                              <div
                                className='popover-select__item'
                                onClick={e => {
                                  openEditGeometry(cell.fieldSeasonId);
                                  setKebabMenuOpened(!kebabMenuOpened);

                                  logEvent('crop_rotation_edit_border_click', {
                                    field_season_id: cell.fieldSeasonId,
                                  });
                                }}
                              >
                                <span className='btn__ico'>
                                  <Icon width={16} height={16} name='pencil' />
                                </span>
                                {t('croprotation.cell.kebab.edit_geometry')}
                              </div>
                            </li>
                          </FeatureDisabledTooltip>
                          <FeatureDisabledTooltip
                            data={fieldBySeason}
                            feature={FEATURES.FIELD_EDIT}
                            direction={PopoverDeprecatedAlign.MiddleRight}
                          >
                            <li className='popover-select__list-item'>
                              <div
                                className='popover-select__item popover-select__item-danger'
                                onClick={() => {
                                  removeField();
                                  setKebabMenuOpened(!kebabMenuOpened);
                                }}
                              >
                                <span className='btn__ico'>
                                  <Icon
                                    className='ico-trash-n-os'
                                    name='trashcan'
                                  />
                                </span>
                                {t('croprotation.cell.kebab.remove_field')}
                              </div>
                            </li>
                          </FeatureDisabledTooltip>
                        </ul>
                      );
                    }}
                  />
                </Switch>
              </CropRotationTooltip>
            )}
          />
        </div>
      </StyledFieldsCropsCell>
    );
  }

  return (
    <StyledFieldsCropsCell
      style={props.style}
      columnIndex={columnIndex}
      isActive={rowIndex === activeRowIndex}
    >
      <div className={styles.cellLink} onClickCapture={onInsideFieldClick}>
        {crops.map((crop, index) => {
          const flex = crops.length > 1 ? 0.5 : 1;
          return (
            <PopoverDeprecated
              key={index}
              active={fieldPopoverOpened === `${index}`}
              offset={[0, 0]}
              align={PopoverDeprecatedAlign.Overlap}
              timeout={50}
              renderTrigger={({ ref, ...otherProps }) => (
                <StyledCropGroup
                  ref={ref}
                  flex={flex}
                  color={crop.color}
                  key={`group${index}`}
                  active={activeCellIndex === cell.fieldSeasonId}
                  onClick={() => {
                    history.replace(`${match.url}/crop-details`);
                    setFieldPopoverOpened(`${index}`);

                    logEvent('crop_rotation_field_season_click', {
                      field_season_id: cell.fieldSeasonId,
                      has_crops: true,
                    });
                  }}
                  {...otherProps}
                >
                  <div className={styles.oneLineBlock}>
                    <div className={cx(styles.cellText, styles.oneLine)}>
                      {crop.name}
                    </div>
                    {crops.length === 1 && crop.variety && (
                      <div
                        className={cx(styles.cellVarietyText, styles.oneLine)}
                      >
                        {crop.variety}
                      </div>
                    )}
                  </div>
                  {crops.length === 1 &&
                    (crop.sowing_date ||
                      crop.harvest_date ||
                      crop.yield_value) && (
                      <div className={styles.cellTextRow}>
                        <span className={styles.cellDateText}>
                          {crop.sowing_date
                            ? formatDate(
                                crop.sowing_date,
                                t('croprotation.crop.date_format') as string,
                              )
                            : crop.harvest_date
                            ? '...'
                            : ''}
                          {(crop.sowing_date || crop.harvest_date) && (
                            <Icon
                              className={styles.cellDatesIcon}
                              name={'small-right-arrow'}
                            />
                          )}
                          {crop.harvest_date
                            ? formatDate(
                                crop.harvest_date,
                                t('croprotation.crop.date_format') as string,
                              )
                            : crop.sowing_date
                            ? '...'
                            : ''}
                        </span>
                        {(crop.yield_value || crop.yield_value === 0) && (
                          <span className={styles.cellDateText}>
                            {crop.yield_value}{' '}
                            {formatYieldUnit(
                              crop.yield_value_units,
                              units,
                              yieldUnitsByCrop[crop.name],
                              t,
                            )}
                          </span>
                        )}
                      </div>
                    )}
                </StyledCropGroup>
              )}
              renderPopover={({ style, ...props }) => (
                <CropRotationTooltip
                  style={style}
                  width={360}
                  onEscapeClickHandler={closeFieldPopover}
                  {...props}
                >
                  <Route
                    path={'*/crop-details'}
                    render={props => (
                      <CropDetailsPopup
                        fieldSeasonId={cell.fieldSeasonId}
                        cropIndex={index}
                        logEvent={logEvent}
                        onClose={closeFieldPopover}
                        {...props}
                      />
                    )}
                  />
                </CropRotationTooltip>
              )}
            />
          );
        })}

        {crops.some(
          crop => crop.recommendation && !crop.recommendation.is_rotation_ok,
        ) && <CropWarningTooltip recommendation={crops[0]?.recommendation} />}
      </div>
      <div onClickCapture={onInsideKebabClick}>
        <PopoverDeprecated
          active={kebabMenuOpened}
          offset={[0, 0]}
          align={PopoverDeprecatedAlign.Overlap}
          timeout={50}
          renderTrigger={({ ref, ...otherProps }) => (
            <StyledKebabButton
              ref={ref}
              onClick={e => {
                history.replace(`${match.url}`);
                logEvent('crop_rotation_kebab_open_click');
                setKebabMenuOpened(!kebabMenuOpened);
              }}
              {...otherProps}
            />
          )}
          renderPopover={({ style, ...props }) => (
            <CropRotationTooltip
              style={style}
              minWidth={211}
              onEscapeClickHandler={closeKebabMenu}
              {...props}
            >
              <Switch>
                <Route
                  path={'*/crop-details'}
                  render={props => (
                    <CropDetailsPopup
                      fieldSeasonId={cell.fieldSeasonId}
                      logEvent={logEvent}
                      onClose={closeKebabMenu}
                      noDelete
                      {...props}
                    />
                  )}
                />
                <Route
                  path={'*/add-crop'}
                  render={props => (
                    <AddCropPopup
                      fieldId={fieldId}
                      seasonId={seasonId}
                      fieldSeasonId={cell.fieldSeasonId}
                      {...props}
                    />
                  )}
                />
                <Route
                  render={() => {
                    return (
                      <ul className='popover-select__list'>
                        <FeatureDisabledTooltip
                          data={fieldBySeason}
                          feature={FEATURES.CROP_ADD}
                          direction={PopoverDeprecatedAlign.MiddleRight}
                        >
                          <li className='popover-select__list-item'>
                            <div
                              className='popover-select__item'
                              onClick={() =>
                                history.replace(`${match.url}/add-crop`)
                              }
                            >
                              <span className='btn__ico'>
                                <Icon className='ico-plus-os' name='plus-btn' />
                              </span>
                              {t('croprotation.cell.kebab.add_crop')}
                            </div>
                          </li>
                        </FeatureDisabledTooltip>
                        <FeatureDisabledTooltip
                          data={fieldBySeason}
                          feature={FEATURES.FIELD_EDIT_BOUNDARIES}
                          direction={PopoverDeprecatedAlign.MiddleRight}
                        >
                          <li className='popover-select__list-item'>
                            <div
                              className='popover-select__item'
                              onClick={e => {
                                openEditGeometry(cell.fieldSeasonId);
                                setKebabMenuOpened(!kebabMenuOpened);

                                logEvent('crop_rotation_edit_border_click', {
                                  field_season_id: cell.fieldSeasonId,
                                });
                              }}
                            >
                              <span className='btn__ico'>
                                <Icon width={16} height={16} name='pencil' />
                              </span>
                              {t('croprotation.cell.kebab.edit_geometry')}
                            </div>
                          </li>
                        </FeatureDisabledTooltip>
                        <FeatureDisabledTooltip
                          data={fieldBySeason}
                          feature={FEATURES.FIELD_DELETE}
                          direction={PopoverDeprecatedAlign.MiddleRight}
                        >
                          <li className='popover-select__list-item'>
                            <div
                              className='popover-select__item popover-select__item-danger'
                              onClick={e => {
                                removeField();
                                setKebabMenuOpened(!kebabMenuOpened);
                              }}
                            >
                              <span className='btn__ico'>
                                <Icon
                                  className='ico-trash-n-os'
                                  name='trashcan'
                                />
                              </span>
                              {t('croprotation.cell.kebab.remove_field')}
                            </div>
                          </li>
                        </FeatureDisabledTooltip>
                      </ul>
                    );
                  }}
                />
              </Switch>
            </CropRotationTooltip>
          )}
        />
      </div>
    </StyledFieldsCropsCell>
  );
};

export default FieldsCropCell;
