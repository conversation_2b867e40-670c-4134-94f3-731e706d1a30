import React, { useEffect, useRef, useState } from 'react';
import Cleave from 'cleave.js/react';
import { css, cx } from 'linaria';
import { styled } from 'linaria/react';

const isNumber = v => !isNaN(parseFloat(v)) && isFinite(v);

const StyledSuffixText = styled.span`
  flex-grow: 0;
  flex-shrink: 0;
  font-size: ${props => (props.size === 'small' ? '14px' : '16px')};
  line-height: ${props => (props.size === 'small' ? '19px' : '22px')};
  color: #a5b2bc;
  align-self: center;
  padding-right: 6px;
`;

const clearableWrapper = css`
  flex-grow: 1;
  flex-shrink: 1;
  position: relative;
  display: flex;
  align-items: center;
`;

const StyledInput = styled.input`
  padding-top: ${props => (props.size === 'small' ? '7px' : '8px')};
  padding-right: ${props => {
    if (props.clearable && props.filled) {
      return '40px';
    }
    if (props.suffixed) return '40px';
    if (props.size === 'small') {
      return '8px';
    } else {
      return '12px';
    }
  }};
  padding-bottom: ${props => (props.size === 'small' ? '5px' : '8px')};
  padding-left: ${props =>
    props.size === 'small' ? '8px' : props.prefixed ? '40px' : '12px'};
  border: none;
  vertical-align: top;
  font-size: 1rem;
  line-height: ${props => (props.size === 'small' ? '22px' : '1.375rem')};
  resize: vertical;
  outline: none;
  width: 100%;
  margin: 0;

  &::-webkit-input-placeholder {
    color: #a5b2bc;
  }

  &::-moz-placeholder {
    color: #a5b2bc;
  }

  &:-ms-input-placeholder {
    color: #a5b2bc;
  }

  &:-moz-placeholder {
    color: #a5b2bc;
  }

  &._noarrows {
    -moz-appearance: textfield;
    &::-webkit-outer-spin-button,
    &::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }
  }

  &[disabled] {
    color: rgba(165, 178, 188, 0.5);
    border-color: rgba(165, 178, 188, 0.2);

    &::-webkit-input-placeholder {
      color: rgba(165, 178, 188, 0.5);
    }

    &::-moz-placeholder {
      color: rgba(165, 178, 188, 0.5);
    }

    &:-ms-input-placeholder {
      color: rgba(165, 178, 188, 0.5);
    }

    &:-moz-placeholder {
      color: rgba(165, 178, 188, 0.5);
    }
  }
`;

const StyledWrapper = styled.div`
  position: relative;
  border-width: 1px;
  border-style: solid;
  cursor: text;
  display: flex;
  overflow: hidden;
  border-color: rgba(209, 209, 214, 1);
  border-radius: ${props => (props.size === 'small' ? '6px' : '8px')};
  transition: border-color 0.3s, border-width 0.3s;
  &._focused {
    border-color: var(--color-primary);
  }
  &._error {
    border-color: #d80606;
  }
`;
const StyledClearButton = styled.button`
  width: 20px;
  height: 20px;
  background: rgba(165, 178, 188, 0.25);
  flex-grow: 0;
  flex-shrink: 0;
  border: none;
  padding: 0;
  margin-right: ${props => (props.size === 'small' ? '8px' : '12px')};
  border-radius: 20px;
  font-size: 0;
  line-height: 0;
  position: relative;
  opacity: ${props => (props.value?.length ? 1 : 0)};
  visibility: ${props => (props.value?.length ? 'visible' : 'hidden')};
  transition: opacity 0.2s, visibility 0.2s, background-color 0.2s;
  &:hover {
    background-color: rgba(165, 178, 188, 0.5);
  }
  &::before,
  &::after {
    position: absolute;
    content: '';
    left: 50%;
    top: 50%;
    margin-left: -1px;
    margin-top: -6px;
    width: 2px;
    height: 12px;
    background-color: #fff;
  }
  &::before {
    transform: rotate(-45deg);
  }
  &::after {
    transform: rotate(45deg);
  }
`;

const StyledContainer = styled.div`
  position: absolute;
  top: 0;
  bottom: 0;
  width: 40px;
  display: flex;
  align-items: center;
`;

const StyledPrefixContainer = styled(StyledContainer)`
  left: 0;
  padding-left: ${props => (props.size === 'small' ? '8px' : '12px')};
  justify-content: flex-start;
`;

const StyledSuffixContainer = styled(StyledContainer)`
  right: 0;
  justify-content: flex-end;
`;

const TextInput = React.forwardRef((props, ref) => {
  const {
    prefix,
    suffix,
    suffixText,
    size,
    numeric,
    groupThousands = true,
    positiveOnly = false,
    thousandsDelimiter = ' ',
    decimalDelimiter = '.',
    precision = 2,
    type = 'text',
    clearable,
    isValid = true,
    hideArrows,
    disabled,
    onChangeValue,
    className,
    ...otherProps
  } = props;
  const [value, setValue] = useState(null);
  const [focused, setFocused] = useState(false);

  let inputNode = useRef();

  const controlled = 'value' in otherProps;

  useEffect(() => {
    setValue(otherProps.value);
  }, [otherProps.value]);

  const onFocus = e => {
    const { onFocus } = otherProps;
    setFocused(true);
    if (onFocus) {
      onFocus(e);
    }
  };

  const onBlur = e => {
    const { onBlur } = otherProps;
    setFocused(false);
    if (onBlur) {
      onBlur(e);
    }
  };

  const handleChange = e => {
    const { onChange } = otherProps;
    setValue(e.target.value);
    if (onChange) {
      onChange(e);
    }
  };

  const resolveOnChange = (target, e, onChange) => {
    if (onChange) {
      let event = e;
      if (e.type === 'click') {
        // click clear icon
        event = Object.create(e);
        event.target = target;
        event.currentTarget = target;
        const originalInputValue = target.value;
        // change target ref value cause e.target.value should be '' when clear input
        target.value = '';
        onChange(event);
        // reset target ref value
        target.value = originalInputValue;
        return;
      }
      onChange(event);
    }
  };

  const handleReset = e => {
    setValue('');
    inputNode.focus();
    resolveOnChange(inputNode, e, otherProps.onChange);
    if (!controlled) {
      inputNode.value = '';
      const changeEvent = document.createEvent('HTMLEvents');
      changeEvent.initEvent('input', false, true);
      inputNode.dispatchEvent(changeEvent);
    }
  };

  const fixControlledValue = value => {
    if (typeof value === 'undefined' || value === null) {
      return '';
    }
    return value;
  };

  return (
    <StyledWrapper
      clearable={clearable}
      size={size}
      isValid={isValid}
      className={cx(focused && '_focused', !isValid && '_error', className)}
    >
      <div className={clearableWrapper}>
        {!!prefix && (
          <StyledPrefixContainer size={size}>{prefix}</StyledPrefixContainer>
        )}
        {numeric ? (
          <Cleave
            {...otherProps}
            htmlRef={input => {
              inputNode = input;
              ref && ref(input);
            }}
            options={{
              numeral: true,
              delimiter: thousandsDelimiter,
              numeralThousandsGroupStyle: groupThousands ? 'thousand' : 'none',
              numeralDecimalScale: precision,
              numeralDecimalMark: decimalDelimiter,
              numeralPositiveOnly: positiveOnly,
            }}
            onFocus={onFocus}
            value={focused ? value : value}
            onBlur={onBlur}
            className='inner-input size-full'
            disabled={disabled}
            onChange={event => {
              otherProps.onChange?.(event);
              const value = event.target.rawValue;
              setValue(value);
              onChangeValue?.(isNumber(value) ? value : '0');
            }}
          />
        ) : (
          <StyledInput
            {...otherProps}
            ref={input => {
              inputNode = input;
              ref && ref(input);
            }}
            prefixed={!!prefix}
            suffixed={!!suffix}
            className={cx(otherProps.className, hideArrows && '_noarrows')}
            filled={!!value}
            onChange={handleChange}
            onFocus={onFocus}
            onBlur={onBlur}
            value={controlled ? fixControlledValue(value) : undefined}
            type={type}
            clearable={clearable}
            size={size}
          />
        )}
        {(!!clearable || !!suffix) && (
          <StyledSuffixContainer size={size}>
            {!!clearable && (
              <StyledClearButton
                type='button'
                value={value}
                onClick={handleReset}
              />
            )}
            {!!suffix && suffix}
          </StyledSuffixContainer>
        )}
      </div>
      {!!suffixText && (
        <StyledSuffixText size={size}>{suffixText}</StyledSuffixText>
      )}
    </StyledWrapper>
  );
});

export default TextInput;
