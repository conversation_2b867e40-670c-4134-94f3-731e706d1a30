import { StoreStatus } from './core';
import { ImportTaskType } from './tasks';
import { ApiDataFieldOperationLegend, ApiDataSourceLayer } from './server';

export type ExtraFieldsApi = {
  method: {
    values: string[];
  };
};

export type MetaAttributesApi = {
  name: string;
  is_hidden: boolean;
  units: string[];
  extra_fields: ExtraFieldsApi;
  match: boolean;
  allowed_types: string[];
  preview: null | {
    column: string;
    priority: number;
  };
};

export type MetaAttributesWithChildren = {
  name: string;
  is_hidden: boolean;
  units: string[];
  match: boolean;
  extra_fields: ExtraFieldsApi;
  children: MetaAttributesApi[];
  allowed_types: string[];
  preview: null | {
    column: string;
    priority: number;
  };
};

export type MetaPropertiesGroups<
  TProperty extends
    | MetaAttributesApi
    | MetaAttributesWithChildren = MetaAttributesApi,
> = {
  name: string | null;
  properties: TProperty[];
};

export type TasksByMachineryMeta<
  TProperty extends
    | MetaAttributesApi
    | MetaAttributesWithChildren = MetaAttributesApi,
> = {
  groups: MetaPropertiesGroups<TProperty>[];
};

export type TasksByMachineryState<
  TProperty extends
    | MetaAttributesApi
    | MetaAttributesWithChildren = MetaAttributesApi,
> = {
  tasks: ImportTaskType[];
  status: StoreStatus;
  preventRestoreFromServer: string[];
  meta: Nullable<TasksByMachineryMeta<TProperty>>;
  legends: Record<string, ApiDataFieldOperationLegend>;
  layerTasks: ApiDataSourceLayer[]; //?
};
