import { BBox } from '@turf/helpers';
import {
  ServerErrorType,
  UploadFileErrorType,
} from 'features/fields/UploadFileStep/types';
import { FieldsUsersFilesFeatures } from 'types/api/models/FieldsUsersFilesFeatures';
import { FieldsUsersFilesResponse } from 'types/api';
import { FileFormatsType, FileProcessingStatus } from 'utils/import-fields';
import { CropType } from 'types/crops';

export enum BulkUploadStep {
  FILE_SELECTION = 'FILE_SELECTION',
  PROCESSING = 'PROCESSING',
  CHECK_DATA = 'CHECK_DATA',
  UPLOADING = 'UPLOADING',
  COLUMN_MATCHING = 'COLUMN_MATCHING',
  CROP_MATCHING = 'CROP_MATCHING',
  SAVING = 'SAVING',
}

export enum FileBundleStatus {
  PROCESSED = 'PROCESSED',
  WAITING_FOR_UPLOAD = 'WAITING_FOR_UPLOAD',
  UPLOADING = 'UPLOADING',
  UPLOADED = 'UPLOADED',
}

export type FileBundleErrors = Record<
  FileFormatsType | 'bundle',
  UploadFileErrorType['status']
>;

export interface FieldsUsersFilesFeature {
  id: number;
  preview_url: string;
  title: string;
  file_id: number;
}

export interface FileBundle {
  name: string;
  status: FileBundleStatus;
  files: File[];
  errors: FileBundleErrors;
  uploadingResult?: FieldsUsersFilesResponse;
  uploadingError?: ServerErrorType | null;
}

export type BulkUploadState = {
  files: File[];
  currentStep: BulkUploadStep;
  reachedStep: BulkUploadStep;
  processingProgress: number;
  processingStatus: Record<string, FileProcessingStatus>;
  fileBundles: FileBundle[];
  importedFields: FieldsUsersFilesFeature[];
  fieldsFetchingPending: boolean;
  cropMatchingAvailable: boolean;
  checkedIDs: number[];
  bbox: BBox | null;
};

export interface BundleValidationResult {
  files: Record<FileFormatsType, File>;
  errors: Record<FileFormatsType | 'bundle', UploadFileErrorType['status']>;
}

export interface FieldsFromFilesResponse {
  bbox: BBox;
  features: FieldsUsersFilesFeatures[];
}

export interface BatchResponseItem<ResponseData> {
  request_id: string;
  response: {
    data: ResponseData;
    success: boolean;
  };
}

export interface BatchResponse<ResponseData> {
  has_errors: boolean;
  responses: BatchResponseItem<ResponseData>[];
}

export type LocationState = {
  from?: string;
};

export interface FieldFromFile {
  field_user_season_id: number;
  id: number;
  crops?: CropType[];
}

export interface FieldAttribute {
  id: string;
  label: string | React.ReactNode;
  season_id?: number;
  type?: string;
}

export type PropertyRecord = Record<string, string | number | undefined>;

export type SeasonIdToPropertiesMap = Record<string, PropertyRecord | string>;

export interface BulkUploadStepState {
  available: boolean;
  clickable: boolean;
  visible: boolean;
}

export interface ToastProps {
  titleKey: string;
  contentKey?: string;
  linkKey?: string;
  ttl?: number | undefined;
  showOnBulkUploadPage?: boolean;
}
