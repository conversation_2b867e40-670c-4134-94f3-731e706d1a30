import React, { type FC } from 'react';

import { styles } from './CheckmarkIcon.styles';

const CheckmarkIcon: FC = () => {
  return (
    <div className={styles.checkmarkContainer}>
      <svg
        className={styles.checkmarkSvg}
        xmlns='http://www.w3.org/2000/svg'
        viewBox='0 0 60 60'
      >
        <circle
          className={styles.checkmarkCircle}
          cx='30'
          cy='30'
          r='25'
          fill='none'
        />
        <path
          className={styles.checkmarkCheck}
          fill='none'
          d='M18.1 31.2l7.1 7.2 16.7-16.8'
        />
      </svg>
    </div>
  );
};

export default CheckmarkIcon;
