import { useState } from 'react';

import { Modals } from './types';

export type DeleteConsultantManager = {
  deleteConsultantUuid: string | null;
  showDeleteConsultantModal: (uuid: string) => void;
  closeDeleteConsultantModal: () => void;
};

export const useDeleteConsultant = (
  setVisibleModal: (type: Modals) => void,
): DeleteConsultantManager => {
  const [deleteConsultantUuid, setDeleteConsultantUuid] = useState<
    string | null
  >(null);

  const showDeleteConsultantModal = (uuid: string) => {
    setVisibleModal('delete-consultant');
    setDeleteConsultantUuid(uuid);
  };

  const closeDeleteConsultantModal = () => {
    setVisibleModal(null);
  };

  return {
    deleteConsultantUuid,
    showDeleteConsultantModal,
    closeDeleteConsultantModal,
  };
};
