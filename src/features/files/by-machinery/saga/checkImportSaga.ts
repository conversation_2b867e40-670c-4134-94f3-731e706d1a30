import { select, put, takeEvery, delay, call } from 'redux-saga/effects';

import importTaskModule from 'features/files/by-machinery/redux';
import { selectors } from 'features/files/by-machinery/redux/selectors';

import {
  ApiDataSourceLayer,
  StoreStatus,
  TaskSuccessType,
} from 'features/files/by-machinery/types';
// @ts-ignore
import { apiCall } from 'utils/effects';

import { processedImportUpdate } from './processedImportUpdate';
import { getMessageForPostprocessingSuccess } from './toasts/getMessageForPostprocessingSuccess';
import { isEntityHaveUpdate } from '../utils/isEntityHaveUpdate';
import { throttleSaga } from 'utils/decorators/throttleSaga';
import entitiesModule from 'modules/entities';

function* update() {
  yield put(importTaskModule.actions.updateTasksStatus());
}

const updateThrottled = throttleSaga(update, 3000);

function* handleUpdateTasksStatusResolved() {
  // Dispatch fetchLayerTasks action immediately after updateTasksStatus has resolved
  yield put(importTaskModule.actions.fetchLayerTasks());
}

function* fetchImportStatus() {
  let completed = false;

  while (!completed) {
    try {
      const layers: ApiDataSourceLayer[] = yield select(
        selectors.getLayerTasks,
      );
      const isProcessing = layers.some(layer => layer.status === 'processing');
      if (!isProcessing) {
        completed = true;
      } else {
        yield delay(2000);
        yield call(apiCall, importTaskModule.actions.fetchLayerTasks());
      }
    } catch (error) {
      console.error('Failed to fetch tasks');
      return;
    }
  }
}

export default function* checkImportSaga() {
  yield takeEvery(importTaskModule.actions.createTask.key, function* () {
    const updatingStatus: StoreStatus = yield select(
      selectors.getUpdatingStatus,
    );

    if (updatingStatus === 'finished') {
      yield updateThrottled();
    }
  });

  yield takeEvery(
    importTaskModule.actions.updateTasksStatus.resolved,
    function* () {
      const hasUnfinishedActiveTask: boolean = yield select(
        importTaskModule.selectors.hasUnfinishedActiveTask,
      );

      if (hasUnfinishedActiveTask) {
        yield updateThrottled();
      }
    },
  );

  yield takeEvery(
    importTaskModule.actions.updateTasksStatus.resolved,
    function* () {
      const allHistoryTasks: TaskSuccessType[] = yield select(
        importTaskModule.selectors.getHistoryTasks,
      );
      const allNewFinishedTasks = allHistoryTasks.filter(isEntityHaveUpdate);
      if (allNewFinishedTasks.length) {
        yield put(getMessageForPostprocessingSuccess(allNewFinishedTasks));
      }
    },
  );

  yield takeEvery(importTaskModule.actions.finished.resolved, function* () {
    yield updateThrottled();
  });

  yield takeEvery(
    importTaskModule.actions.updateTasksStatus.resolved,
    processedImportUpdate,
  );

  yield takeEvery(importTaskModule.actions.removeTask.resolved, function* () {
    yield put(
      entitiesModule.actions.request('useFieldDataOperationsLoader', [
        {
          type: 'fetchMany',
          entityType: 'fieldsDataOperation',
          query: {
            include: ['data_source.layer.properties'],
          },
        },
      ]),
    );
  });

  yield takeEvery(
    // @ts-ignore
    importTaskModule.actions.spatialMatching.resolved,
    function* (action: {
      payload: { task_id: string };
      meta: { requestAction: { payload: { data: { uuid: string } } } };
    }) {
      yield handleUpdateTasksStatusResolved();
    },
  );

  yield takeEvery(
    importTaskModule.actions.updateTasksStatus.resolved,
    handleUpdateTasksStatusResolved,
  );
  yield takeEvery(
    importTaskModule.actions.fetchLayerTasks.resolved,
    fetchImportStatus,
  );
}
