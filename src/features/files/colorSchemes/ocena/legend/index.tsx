import React, { VFC } from 'react';

import { useTranslate } from 'utils/use-translate';
import OverlapFileLegend from 'features/files/colorSchemes/soilAnalysis/legend/OverlapFileLegend';
import UndercoverFileLegend from 'features/files/colorSchemes/soilAnalysis/legend/UndercoverFileLegend';
import {
  LegendBox,
  LegendRow,
  LegendText,
  LegendTextBlock,
} from 'features/files/colorSchemes/soilAnalysis/legend/styles';

import { FileDataSourceType } from 'features/files/colorSchemes/types';

import { LegendGradientPalette, OcenaLegendBox } from './styles';

const OcenaFileLegend: VFC<FileDataSourceType> = dataSource => {
  const { t } = useTranslate();

  return (
    <LegendRow>
      <LegendBox>
        <OcenaLegendBox>
          <LegendTextBlock>
            <LegendText>{t('file.ocena.legend.min')}</LegendText>
            <LegendText>{t('file.ocena.legend.max')}</LegendText>
          </LegendTextBlock>
          <LegendGradientPalette />
        </OcenaLegendBox>

        <OverlapFileLegend {...dataSource} />
        <UndercoverFileLegend {...dataSource} />
      </LegendBox>
    </LegendRow>
  );
};

export default OcenaFileLegend;
