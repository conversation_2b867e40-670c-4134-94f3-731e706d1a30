import React from 'react';
import startCase from 'lodash/startCase';

import Icon from 'components/Icon';
import BodyClassName from 'components/BodyClassName';
import LogoWhite from 'components/vector/LogoWhite';
import AuthorizePage from './AuthorizePage';

import 'assets/styles/partner.css';
import { cx } from 'linaria';
import { useTranslate } from 'utils/use-translate';

const PartnerSignupPage = props => {
  const { t } = useTranslate();
  const { partner } = props.match.params;

  return (
    <>
      <BodyClassName
        className={cx(
          'page-partner',
          partner === 'welcome' && 'page-partner__welcome',
        )}
      />
      <div className='partner-about'>
        <div className='partner-about-header'>
          <div className='partner-about-header__onesoil' title='OneSoil'>
            <LogoWhite />
          </div>
          {partner !== 'welcome' && (
            <div className='partner-about-header__and'>
              <Icon name='x' />
            </div>
          )}
          <div
            className={`partner-about-header__${partner}`}
            title={startCase(partner)}
          />
        </div>
        <h1>{t(`partner.${partner}.title`)}</h1>
        {partner !== 'welcome' && <p>{t(`partner.${partner}.terms`)}</p>}

        {partner === 'welcome' && (
          <>
            <h4>{t('partner.welcome.terms')}</h4>
            <ul className='partner-about-list'>
              <li className='partner-about-list__item'>
                {t(`partner.welcome.list.item1`)}
              </li>
              <li className='partner-about-list__item'>
                {t(`partner.welcome.list.item2`)}
              </li>
              <li className='partner-about-list__item'>
                {t(`partner.welcome.list.item3`)}
              </li>
              <li className='partner-about-list__item'>
                {t(`partner.welcome.list.item4`)}
              </li>
            </ul>
          </>
        )}
      </div>

      <AuthorizePage partner={partner} expandTerms {...props} />
      {partner === 'cargill' && (
        <div className='onesoil-hello-disclaimer'>
          <div className='onesoil-hello-disclaimer__inner'>
            <p>{t(`partner.${partner}.disclaimer`)}</p>
          </div>
        </div>
      )}
    </>
  );
};

export default PartnerSignupPage;
