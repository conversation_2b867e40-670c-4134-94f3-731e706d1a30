import React, { ChangeEventHandler } from 'react';
import {
  StyledLabel,
  StyledValue,
  StyledInput,
  StyledInputEl,
} from './RadioButton.style';

interface RadioButtonProps extends React.PropsWithChildren<{}> {
  checked: boolean;
  disabled?: boolean;
  colorClassName?: string;
  value: string;
  name: string;
  onChange: ChangeEventHandler<HTMLInputElement>;
}

const RadioButton = ({
  children,
  colorClassName,
  checked,
  disabled,
  value,
  name,
  onChange,
}: RadioButtonProps) => (
  <StyledLabel data-legend={colorClassName} checked={checked}>
    <StyledInput
      type='radio'
      checked={checked}
      disabled={disabled}
      value={value}
      name={name}
      onChange={onChange}
    />
    <StyledInputEl />
    <StyledValue>{children}</StyledValue>
  </StyledLabel>
);

export default RadioButton;
