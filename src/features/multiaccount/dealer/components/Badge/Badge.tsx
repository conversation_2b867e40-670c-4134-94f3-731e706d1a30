import React, { type ReactNode } from 'react';
import cx from 'classnames';

import { useTranslate } from 'utils/use-translate';

import { styles } from './Badge.styles';

type TBadgeType = 'master' | 'overLimit' | 'expired';

type TBadgeProps = {
  type: TBadgeType;
  icon?: ReactNode;
  label?: string;
  customStyleClassName?: string;
};

function Badge({
  type,
  icon,
  label = '',
  customStyleClassName = '',
}: TBadgeProps) {
  const { t } = useTranslate();

  const getDefaultLabel = (): string => {
    switch (type) {
      case 'master':
        return t('multiaccount.dealer.badge.master');
      case 'overLimit':
        return t('multiaccount.dealer.badge.over-limit');
      case 'expired':
        return t('multiaccount.dealer.badge.expired');
      default:
        return '';
    }
  };

  return (
    <div className={cx(styles.badge, styles[type], customStyleClassName)}>
      {icon && <span className={styles.icon}>{icon}</span>}
      <span>{label || getDefaultLabel()}</span>
    </div>
  );
}

export default Badge;
