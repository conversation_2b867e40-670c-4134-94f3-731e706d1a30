import React, { ReactNode } from 'react';

import Alert from 'components/ui/Alert/Alert';

export default {
  title: 'ui/Alert',
  component: Alert,
  decorators: [(storyFn: () => ReactNode) => <div>{storyFn()}</div>],
};

export const Default = () => (
  <Alert
    show
    text='Are you sure you want to leave Strom account?'
    onCancel={() => {}}
    onDanger={() => {}}
    danger='Leave'
  />
);

export const Success = () => (
  <Alert
    show
    text='You <NAME_EMAIL> invitation and now able to collaborate'
    icon='modal-success'
    onSuccess={() => {}}
  />
);

export const ErrorModal = () => (
  <Alert
    show
    text='User <EMAIL> has left your account'
    icon='modal-error'
    onSuccess={() => {}}
  />
);
