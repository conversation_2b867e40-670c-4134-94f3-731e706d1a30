import { ImportTaskType } from '../types/tasks';
import { useDispatch, useSelector } from 'react-redux';
import { selectors } from '../redux/selectors';
import { actions } from '../redux/actions';
import { isEntityHaveUpdate } from '../utils/isEntityHaveUpdate';

const preventMarkAsShovedAgain: string[] = [];

export const useMarkAllActiveTaskAsShowed = () => {
  const dispatch = useDispatch();

  const activeTasks: ImportTaskType[] = useSelector(selectors.getAllTasks);

  const taskForMark = activeTasks
    .filter(task => isEntityHaveUpdate(task))
    .filter(task => task.uuid)
    // @ts-ignore
    .filter(task => !preventMarkAsShovedAgain.includes(task.uuid));

  if (taskForMark.length) {
    preventMarkAsShovedAgain.push(
      ...(taskForMark
        .map(i => i.uuid)
        .filter(v => v !== undefined) as string[]),
    );
    dispatch(actions.markTasksAsShowed(taskForMark));
  }
};
