import React from 'react';

import { ClientToggleField } from '../ClientToggleField';

export type ClientModeFieldProps = {
  control: TEMPORARY_ANY;
};

const ClientModeField = ({ control }: ClientModeFieldProps) => {
  return (
    <ClientToggleField
      control={control}
      name='mode'
      options={['single', 'bulk']}
      translationKeyPrefix='multiaccount.invite-client-modal.'
    />
  );
};

export default ClientModeField;
