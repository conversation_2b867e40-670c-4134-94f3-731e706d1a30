import bbox from '@turf/bbox';
import { Feature, featureCollection } from '@turf/helpers';

import { UploadFileErrorType } from 'features/fields/UploadFileStep/types';

import {
  MediaFormats,
  FileFormatsType,
  FormatValidators,
  FormatValidatorsKeysType,
  getExtension,
  hasDuplicates,
  hasSameNames,
  BulkUploadMaxFileSize,
  stripExtension,
} from 'utils/import-fields';

import {
  BatchResponse,
  BundleValidationResult,
  FieldAttribute,
  FieldsFromFilesResponse,
  PropertyRecord,
} from 'features/fields/BulkUpload/types';

import { FieldsUsersFilesFeatures } from 'types/api/models/FieldsUsersFilesFeatures';

import { SeasonIdToPropertiesMap } from 'features/fields/BulkUpload/types';

export function composeFileBundles(files: File[]): Map<string, File[]> {
  const bundles: Map<string, File[]> = new Map();

  for (const file of files) {
    // We cannot import xml files
    if (file.name.endsWith('.xml')) {
      continue;
    }

    const fileName = stripExtension(file.name);

    if (!bundles.has(fileName)) {
      bundles.set(fileName, [file]);
    } else {
      bundles.get(fileName)?.push(file);
    }
  }

  return bundles;
}

export function validateBundle(bundle: File[]): BundleValidationResult {
  const packFiles: Record<FileFormatsType, File> = {};
  const errors: Record<
    FileFormatsType | 'bundle',
    UploadFileErrorType['status']
  > = {};

  for (const file of bundle) {
    const ext = getExtension(file.name);

    if (MediaFormats.includes(ext)) {
      errors[ext] = 'unsupported_media_file';
      break;
    }

    if (packFiles[ext]) {
      errors[ext] = 'same_format';
      break;
    }

    if (file.size > BulkUploadMaxFileSize) {
      errors[ext] = 'too_big_file';
    }

    packFiles[ext] = file;
  }

  if (Object.keys(errors).length > 0) {
    return {
      files: packFiles,
      errors,
    };
  }

  const filesValues = Object.values(packFiles);
  const allFilesExts = filesValues.map(item => getExtension(item.name));

  const MAX_FILES_VALUES = 7;

  if (filesValues.length > MAX_FILES_VALUES) {
    errors.bundle = 'too_many_files';
  } else {
    if (hasDuplicates(allFilesExts)) {
      errors.bundle = 'same_format';
    } else {
      const validators: FormatValidatorsKeysType[] = Object.keys(
        FormatValidators,
      ) as FormatValidatorsKeysType[];

      let pickedValidator = null;
      validators.some(validator => {
        if (FormatValidators[validator](filesValues)) {
          pickedValidator = validator;
          return true;
        }
        return false;
      });

      if (!pickedValidator) {
        errors.bundle = 'mixed_format';
      } else if (filesValues.length > 1 && !hasSameNames(filesValues)) {
        errors.bundle = 'filenames_not_same';
      }
    }
  }

  if (packFiles.shp && !packFiles.dbf) {
    errors.bundle = 'dbf_missing';
  }

  if (
    !packFiles.shp &&
    (packFiles.dbf ||
      packFiles.prj ||
      packFiles.cpg ||
      packFiles.shx ||
      packFiles.sbn ||
      packFiles.sbx ||
      packFiles.qpj)
  ) {
    errors.bundle = 'shp_missing';
  }

  return {
    files: packFiles,
    errors,
  };
}

export function convertFilesFeaturesToGeoJsonFeature(
  feature: FieldsUsersFilesFeatures,
): Feature {
  return {
    type: 'Feature',
    geometry: feature.geom,
    id: feature.id,
    properties: feature.properties,
  };
}

export function extractFeaturesFromFilesBatchResponse(
  response: BatchResponse<FieldsFromFilesResponse>,
): Feature[] {
  return response.responses
    .reduce((acc: FieldsUsersFilesFeatures[], item) => {
      for (const feature of item.response.data.features) {
        acc.push(feature);
      }
      return acc;
    }, [])
    .map(convertFilesFeaturesToGeoJsonFeature);
}

export function getFeaturesBBox(features: Feature[]) {
  return bbox(featureCollection(features));
}

export function groupPropertiesBySeasonId(
  properties: Record<string, FieldAttribute>,
): SeasonIdToPropertiesMap {
  let groupBySeasonId: SeasonIdToPropertiesMap = {};

  Object.entries(properties).forEach(([key, attribute]) => {
    const seasonId = attribute.season_id;

    if (!seasonId) {
      groupBySeasonId[key] = attribute.id;
      return;
    }

    if (!groupBySeasonId[seasonId]) {
      groupBySeasonId[seasonId] = {
        [key]: attribute.type,
      };
      return;
    }

    const seasonGroup = groupBySeasonId[seasonId];
    if (typeof seasonGroup === 'object') {
      seasonGroup[key] = attribute.type;
    }

    // should be unreachable since every value in groupBySeasonId
    // is either a string or an object
  });

  return groupBySeasonId;
}

export function validateMatchedFields(
  matchedFields: SeasonIdToPropertiesMap,
): number[] {
  const invalidSeasons: number[] = [];

  Object.keys(matchedFields).forEach((key: string) => {
    if (matchedFields[key] && typeof matchedFields[key] === 'object') {
      const matched = matchedFields[key];

      if (matched && !Object.values(matched).some(i => i === 'crop')) {
        invalidSeasons.push(+key);
      }
    }
  });

  return invalidSeasons;
}

export function prepareFieldsFileUpdate(
  groupBySeasonId: SeasonIdToPropertiesMap,
): {
  preparedToSend: SeasonIdToPropertiesMap;
  noCrops: boolean;
} {
  let preparedToSend: SeasonIdToPropertiesMap = {};
  let noCrops = true;

  for (const [key, value] of Object.entries(groupBySeasonId)) {
    if (typeof value !== 'object') {
      preparedToSend[key] = value;
      continue;
    }

    for (const [property, attributeType] of Object.entries(value)) {
      if (attributeType !== 'crop') {
        continue;
      }

      noCrops = false;

      const otherProperties = Object.keys(value).reduce<PropertyRecord>(
        (otherProps, d) => {
          if (value[d] !== 'crop') {
            otherProps[d] = value[d];
          }
          return otherProps;
        },
        {},
      );

      preparedToSend[property] = {
        season_id: +key,
        type: 'crop',
        ...otherProperties,
      };
    }
  }

  return { preparedToSend, noCrops };
}

export function getCropsKeys(properties: Record<string, FieldAttribute>) {
  let cropsKeys: string[] = [];

  for (const [key, value] of Object.entries(properties)) {
    if (value.type && value.type === 'crop') {
      cropsKeys.push(key);
    }
  }

  return cropsKeys;
}
