import React, { useCallback, useState, useEffect, useMemo, memo } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useTranslate } from 'utils/use-translate';
import debounce from 'lodash/debounce';

// @ts-ignore
import ui from 'modules/ui';
import settings from 'modules/settings';
// @ts-ignore
import auth, { AuthInitialState } from 'modules/auth';

import logEvent from 'sagas/global/logEvent';

import Modal from 'components/ui/Modal/Modal';
import { UserSettingsStatusMap } from 'modules/settings/types';

import {
  getNewFeatures,
  NewFeature,
  NewFeatureComponent,
} from 'constants/settings';
import { filterFeatures } from 'utils/new-features/filterFeatures/filterFeatures';
import { cleanUpFeatures } from 'utils/new-features/cleanUpFeatures/cleanUpFeatures';
import { getLastSeenFeature } from 'utils/new-features/getLastSeenFeature/getLastSeenFeature';
import WhatsNewContent from './WhatsNewContent/WhatsNewContent';

const WHATS_NEW_MODAL_WIDTH = 870;

const formatDeviceLocale = (deviceLocale: string) => {
  if (deviceLocale && deviceLocale.includes('-')) {
    return deviceLocale.slice(0, deviceLocale.indexOf('-')).toLowerCase();
  }

  return deviceLocale;
};

const WhatsNew = () => {
  const isModalActive = useSelector(ui.selectors.isModalActive);
  const featuresUsed = useSelector(settings.selectors.getFeaturesUsed);
  const status = useSelector(settings.selectors.getStatus);
  const justSignedUp = useSelector<AuthInitialState, boolean | undefined>(
    auth.selectors.justSignedUp,
  );
  const dispatch = useDispatch();
  const { t, i18n } = useTranslate();

  const [showWhatsNew, setShowWhatsNew] = useState<boolean>(false);
  const [markedAsSeen, setMarkedAsSeen] = useState<boolean>(false);
  const [lastFeature, setLastFuture] = useState<string | null>(null);
  const [pages, setPages] = useState<NewFeatureComponent[]>([]);
  const language = i18n.resolvedLanguage;

  useEffect(() => {
    if (justSignedUp && !featuresUsed.id) {
      dispatch(
        settings.actions.update(
          //@ts-ignore
          {
            featuresUsed: {
              id: null,
              value: [],
            },
          },
          null,
        ),
      );
    }
  }, [featuresUsed.id, dispatch, justSignedUp]);

  const featuresFilters = useMemo(
    () => ({
      locale: language,
      deviceLocale: formatDeviceLocale(
        navigator.language || navigator.userLanguage,
      ),
    }),
    [language],
  );

  const getPages = useCallback(async () => {
    let featuresToShow: NewFeature[] = [];
    if (status === UserSettingsStatusMap.resolved) {
      const lastSeenFeature = await getLastSeenFeature(featuresUsed.value);
      const NewFeatures = await getNewFeatures();

      if (lastSeenFeature) {
        const lastSeenFeatureIndex = NewFeatures.findIndex(
          ({ key }) => key === lastSeenFeature,
        );
        featuresToShow = NewFeatures.slice(lastSeenFeatureIndex + 1);
      } else {
        featuresToShow = NewFeatures;
      }
    }

    // We want to show last added feature at first
    featuresToShow = [...featuresToShow].reverse();

    return filterFeatures(featuresToShow, featuresFilters);
  }, [featuresUsed.value, featuresFilters, status]);

  useEffect(() => {
    getNewFeatures().then(NewFeatures => {
      setLastFuture(NewFeatures[NewFeatures.length - 1]?.key ?? null);
    });

    getPages().then(pages => {
      setPages(pages);
    });
  }, [getPages]);

  const markAllAsSeen = useCallback(() => {
    // we can have keys from mob also such as basemap, whats-new-mob-crops-140, etc.
    const filteredFeatures = featuresUsed.value.filter(cleanUpFeatures);
    let updateActionSettings = {
      featuresUsed: {
        id: featuresUsed.id,
        value: [...filteredFeatures, lastFeature],
      },
    };
    dispatch(
      settings.actions.update(
        //@ts-ignore
        updateActionSettings,
        featuresUsed.id,
      ),
    );
    setMarkedAsSeen(true);
    /* eslint-disable-next-line react-hooks/exhaustive-deps */
  }, [featuresUsed, lastFeature]);

  const showModal = useMemo(
    () =>
      debounce(() => {
        if (!justSignedUp) {
          logEvent('whats_new_modal_open', { version: lastFeature });
          setShowWhatsNew(true);
        }
      }, 1500),
    [lastFeature, justSignedUp],
  );

  const closeModal = useCallback(() => {
    markAllAsSeen();
    logEvent('whats_new_modal_close', { version: lastFeature });
    setShowWhatsNew(false);
    dispatch(ui.actions.toggleModalActive(false));
    /* eslint-disable-next-line react-hooks/exhaustive-deps */
  }, [featuresUsed, lastFeature]);

  useEffect(() => {
    if (!featuresUsed.id || !lastFeature) {
      return;
    }

    if (justSignedUp && !markedAsSeen) {
      markAllAsSeen();
    } else if (pages.length > 0 && !markedAsSeen && !isModalActive) {
      dispatch(ui.actions.toggleModalActive(true));
      showModal();
    }
    /* eslint-disable-next-line react-hooks/exhaustive-deps */
  }, [pages, lastFeature, featuresUsed]);

  return (
    <Modal
      width={WHATS_NEW_MODAL_WIDTH}
      show={showWhatsNew}
      withCloseButton
      onClose={closeModal}
    >
      <WhatsNewContent
        onClose={closeModal}
        language={language}
        t={t}
        pages={pages}
      />
    </Modal>
  );
};

export default memo(WhatsNew);
