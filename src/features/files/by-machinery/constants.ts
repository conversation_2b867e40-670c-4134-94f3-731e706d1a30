import { useSelector } from 'react-redux';
import {
  StepRoute,
  StepStatus,
  StepValidationStatus,
} from './service/steps/types';
import { baseRoutes } from './service/steps/constants';

export const MAX_FILE_SIZE = 500 * 1024 * 1024;
export const FILE_TYPES = [
  'zip',
  'rar',
  'gz',
  'gzip',
  'tar',
  'kml',
  'kmz',
  'json',
  'geojson',
  'topojson',
  'gpkg',
  'csv',
  'txt',
  'xls',
  'xlsx',
  'xml',
];

export const TOAST_KEY_TO_IMPORT = 'import.by-machinery.toast';
export const TOAST_KEY_TO_FIELDS = 'import.by-machinery.toast';

export const BASE_ROUTE_MACHINERY_IMPORT = 'files-by-machinery';
export const BASE_ROUTE_FIELDS = 'fields';

export const useImportFilesSteps = (): StepRoute[] => {
  const allRoutes = useSelector(state =>
    baseRoutes.map(route => {
      let isValid = StepValidationStatus.valid;

      if (route.validation) {
        for (let i = 0; i < route.validation.length; i++) {
          const temp = route.validation[i]!(state);
          if (temp !== StepValidationStatus.valid) {
            isValid = temp;

            break;
          }
        }
      }

      return {
        ...route,
        status: route.check ? route.check(state) : StepStatus.available,
        valid: isValid,
      };
    }),
  );

  return allRoutes.filter(item => item.status !== StepStatus.hiding);
};
