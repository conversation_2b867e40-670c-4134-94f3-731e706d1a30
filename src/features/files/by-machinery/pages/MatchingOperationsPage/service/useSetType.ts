import { MutableRefObject, useCallback } from 'react';
import { useDispatch } from 'react-redux';

import entities from 'modules/entities';
import { EntityOperation } from 'modules/entities/types';

import { DataStoreType } from 'features/files/by-machinery/pages/MatchingOperationsPage/service/useDataStore';
import {
  ApiDataFieldOperations,
  ApiDataFieldOperationType,
} from '../../../types/server';

export type OnSetPropertyType = (
  operation: ApiDataFieldOperations,
  type: Nullable<ApiDataFieldOperationType>,
) => void;

export const useSetType = (
  dataStore: MutableRefObject<DataStoreType>,
): OnSetPropertyType => {
  const dispatch = useDispatch();

  return useCallback(
    (operation, type) => {
      const parentId = operation.parent_operation_uuid || operation.uuid;
      const baseParentOperation = dataStore.current.operations.filter(
        v => v.uuid === parentId,
      );

      const parentLayers = baseParentOperation.map(
        o => o.data_source.layer.uuid,
      );
      const parentsOperations = dataStore.current.operations.filter(item =>
        parentLayers.includes(item.data_source.layer.uuid),
      );

      const requests: EntityOperation[] = parentsOperations.map(item => ({
        type: 'patch',
        entityType: 'fieldsDataOperation',
        path: {
          id: item.uuid,
        },
        query: {
          include: ['data_source.layer.properties'],
        },
        body: {
          type: type,
          is_enabled: type !== null ? item.is_enabled : false,
        },
        children: dataStore.current.operations
          .filter(v => v.parent_operation_uuid === item.uuid)
          .map(childOperation => ({
            type: 'patch',
            entityType: 'fieldsDataOperation',
            path: {
              id: childOperation.uuid,
            },
            query: {
              include: ['data_source.layer.properties'],
            },
            body: {
              type: type,
              is_enabled: type !== null ? childOperation.is_enabled : false,
            },
          })),
      }));

      dispatch(entities.actions.request('setRequestId', requests));
    },
    [dataStore, dispatch],
  );
};
