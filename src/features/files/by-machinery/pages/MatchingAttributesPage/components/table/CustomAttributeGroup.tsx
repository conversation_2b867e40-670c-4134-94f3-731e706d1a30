import React, { VFC } from 'react';
import { useTranslate } from 'utils/use-translate';

import {
  ApiDataFieldLayer,
  TasksByMachineryMeta,
} from 'features/files/by-machinery/types';

import { TableCell } from './TableCell';

import { OnSetPropertyType } from 'features/files/by-machinery/pages/MatchingAttributesPage/service';
import { CustomAttributeKebabCell } from './CustomAttributeKebabCell';
import { CustomAttributeSelectBoxMemo } from './CustomAttributeSelectBox';

import {
  CustomAttributeGroupStyle,
  CustomRowTitleStyle,
  Delimiter,
} from './styles';

type AttributeSelectorProps = {
  dataLayers: ApiDataFieldLayer[];
  fields: TEMPORARY_ANY;
  onShowEditAttributeNameModal: (index: number) => void;
  onRemoveAttribute: (index: number) => void;
  onSetAttributeValue: OnSetPropertyType;
  onSetSelectValue: (index: number, value: string | null) => void;
  meta: TasksByMachineryMeta;
};

export const CustomAttributeGroup: VFC<AttributeSelectorProps> = ({
  dataLayers,
  fields,
  onRemoveAttribute,
  onShowEditAttributeNameModal,
  onSetAttributeValue,
  onSetSelectValue,
  meta,
}) => {
  const { t } = useTranslate();

  return (
    <React.Fragment>
      <Delimiter columns={dataLayers.length} />
      <CustomAttributeGroupStyle columns={dataLayers.length}>
        {t('import.matching.attribute_group.custom_elements')}
      </CustomAttributeGroupStyle>

      {fields.map(
        (field: { id: string; name: string; units: [] }, index: number) => (
          <React.Fragment key={field.id}>
            <TableCell>
              <CustomRowTitleStyle>{field.name}</CustomRowTitleStyle>
              <CustomAttributeKebabCell
                index={index}
                onShowEditAttributeNameModal={onShowEditAttributeNameModal}
                onRemoveAttribute={onRemoveAttribute}
              />
            </TableCell>
            {dataLayers.map(layer => {
              return (
                <TableCell key={layer?.uuid}>
                  <CustomAttributeSelectBoxMemo
                    key={layer?.uuid}
                    index={index}
                    attribute={fields[index]}
                    meta={meta}
                    layer={layer}
                    onSetAttributeValue={onSetAttributeValue}
                    onSetSelectValue={onSetSelectValue}
                  />
                </TableCell>
              );
            })}
          </React.Fragment>
        ),
      )}
    </React.Fragment>
  );
};
