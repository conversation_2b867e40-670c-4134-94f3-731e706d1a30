import React, { useEffect, useRef } from 'react';
import { fromUrl } from 'geotiff';

import { ApiDataFieldOperations } from 'features/files/by-machinery/types';

const MAX_SIZE = 300;

export type OperationsImageProps = {
  operations: ApiDataFieldOperations[];
};
export const OperationsImage = (props: OperationsImageProps) => {
  const ref = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const imagesDataPromises = props.operations.map(operation =>
      fromUrl(operation.image)
        .then(tiff => tiff.getImage())
        .then(image => image.readRasters()),
    );

    Promise.all(imagesDataPromises)
      // @ts-ignore
      .then(imagesData => {
        const firstImage = imagesData[0];
        if (!firstImage || !ref.current) {
          return null;
        }

        const factor =
          Math.max(
            Math.floor(Math.max(firstImage.width, firstImage.height) / 300),
            1,
          ) * 2;
        const factorStepStart = Math.floor((factor - 1) / 2);
        const factorStepEnd = factor - factorStepStart - 1;

        ref.current.width = firstImage.width;
        ref.current.height = firstImage.height;

        setTimeout(() => {
          const context = ref.current?.getContext('2d');
          if (!context || !firstImage) {
            return;
          }
          const contextImageData = context.createImageData(
            firstImage.width,
            firstImage.height,
          );

          imagesData.forEach(image => {
            for (let i = 0; i < contextImageData.data.length; i += 4) {
              // @ts-ignore
              if (image[0][i / 4] === 0) {
                contextImageData.data[i] = 255;
                contextImageData.data[i + 1] = 255;
                contextImageData.data[i + 2] = 255;
                contextImageData.data[i + 3] = 255;
              }
            }
          });

          /** borders */
          for (let x = 0; x <= firstImage.width; x++) {
            const point = x * 4;
            contextImageData.data[point] = 255;
            contextImageData.data[point + 1] = 0;
            contextImageData.data[point + 2] = 0;
            contextImageData.data[point + 3] = 0;
          }
          for (let x = 0; x <= firstImage.width; x++) {
            const point = (x + (firstImage.height - 1) * firstImage.width) * 4;
            contextImageData.data[point] = 255;
            contextImageData.data[point + 1] = 0;
            contextImageData.data[point + 2] = 0;
            contextImageData.data[point + 3] = 0;
          }
          for (let y = 0; y <= firstImage.height; y++) {
            const point = y * firstImage.width * 4;
            contextImageData.data[point] = 255;
            contextImageData.data[point + 1] = 0;
            contextImageData.data[point + 2] = 0;
            contextImageData.data[point + 3] = 0;
          }
          for (let y = 0; y <= firstImage.height; y++) {
            const point = (y * firstImage.width - 1) * 4;
            contextImageData.data[point] = 255;
            contextImageData.data[point + 1] = 0;
            contextImageData.data[point + 2] = 0;
            contextImageData.data[point + 3] = 0;
          }

          // @ts-ignore;
          const border = firstImage[1] as number[];
          for (let i = 0; i < border.length; i++) {
            if (border[i] === 0) {
              const currentX = i % firstImage.width;
              const currentY = Math.floor(i / firstImage.width);

              for (
                let x = currentX - factorStepStart;
                x <= currentX + factorStepEnd;
                x++
              ) {
                for (
                  let y = currentY - factorStepStart;
                  y <= currentY + factorStepEnd;
                  y++
                ) {
                  if (
                    x >= 0 &&
                    x < firstImage.width &&
                    y >= 0 &&
                    y < firstImage.height
                  ) {
                    const point = (x + y * firstImage.width) * 4;
                    contextImageData.data[point] = 255;
                    contextImageData.data[point + 1] = 255;
                    contextImageData.data[point + 2] = 0;
                    contextImageData.data[point + 3] = 255;
                  }
                }
              }
            }
          }

          context.putImageData(contextImageData, 0, 0);
        }, 0);
      });
  }, [props.operations]);

  return <canvas ref={ref} style={{ width: MAX_SIZE, maxHeight: MAX_SIZE }} />;
};
