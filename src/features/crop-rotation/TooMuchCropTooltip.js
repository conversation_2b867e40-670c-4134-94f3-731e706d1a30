import React, { useState } from 'react';

import { PopoverDeprecated } from 'components/ui/Popover';
import { SimpleTooltip } from 'components/ui/Tooltip/SimpleTooltip/SimpleTooltip';

const TooMuchCropTooltip = ({
  active,
  offset = [-18, 0],
  align = 'middle-left',
  animate = 'from-left',
  arrow = 'right',
  renderTrigger,
  renderTooltip,
  ...otherProps
}) => {
  const [hovered, setHovered] = useState(false);

  return (
    <PopoverDeprecated
      active={active && hovered}
      offset={offset}
      align={align}
      {...otherProps}
      renderTrigger={props =>
        renderTrigger({
          onMouseEnter: () => setHovered(true),
          onMouseLeave: () => setHovered(false),
          ...props,
        })
      }
      renderPopover={({ style, ...props }) => (
        <SimpleTooltip style={style} arrow={arrow} {...props}>
          {renderTooltip()}
        </SimpleTooltip>
      )}
    />
  );
};

export default TooMuchCropTooltip;
