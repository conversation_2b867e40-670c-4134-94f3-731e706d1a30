import React, { useCallback, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import transformScale from '@turf/transform-scale';
import calcBbox from '@turf/bbox';
import area from '@turf/area';
import cx from 'classnames';

import Checkbox from 'components/ui/Checkbox/Checkbox';
import LinkButton from 'components/LinkButton';
import SortedByLabel from './SortedByLabel';
import PredictedCropTooltip from 'features/fields/PredictedCropTooltip/PredictedCropTooltip';
import LinkedNotesWarningPopup from 'features/fields/LinkedNotesWarningPopup';
import EnterExitTransition from 'components/EnterExitTransition';

import map from 'modules/map';
import entities from 'modules/entities';
import fields from 'modules/fields';
import seasons from 'modules/seasons';
import settings from 'modules/settings';
import auth from 'modules/auth';

import getRootScoutingUrl, {
  getViewportHash,
} from 'utils/get-root-scouting-url';
import { formatCropList } from 'utils/fields-formatters';
import { CustomDropdown } from 'components/ui/CustomDropdown/CustomDropdown';
import logEvent from 'sagas/global/logEvent';
import getFieldArea from 'utils/get-field-area';
import { useTranslate } from 'utils/use-translate';
import { FEATURES } from 'features/permissions';

import {
  filterOperationsForField,
  getOperationsValue,
} from '../../files/by-machinery/redux/selectors';
import { isEntityHaveUpdate } from '../../files/by-machinery/utils/isEntityHaveUpdate';
import BackgroundImage from '../../../components/ui/BackgroundImage/BackgroundImage';
import { getUrlForPolygon } from '../../../utils/static-maps';
import fieldSeasonModule from '../../../modules/fieldSeason';

const FieldOptions = [
  {
    name: 'edit',
    feature: FEATURES.FIELD_EDIT,
  },
  {
    name: 'remove',
    feature: FEATURES.FIELD_DELETE,
  },
];

export const FieldListItem = ({
  fieldId,
  style,
  noZoom,
  history,
  selected,
  readOnly,
  sorted,
  index,
  isGroupedBy,
  checkedIDs,
  notesInSeason,
  handleCheck,
  onSelect = () => {},

  getHistoryStateAfterSelect = () => {},
  onClick,
  visibleNewData,
}) => {
  const { t, i18n } = useTranslate();
  const field = useSelector(state => fields.selectors.getByID(state, fieldId));
  const formatUnit = useSelector(settings.selectors.getUnitFormatter);
  const lang = i18n.resolvedLanguage;

  const fieldFillingType = useSelector(fields.selectors.getFieldFillingType);
  const userID = useSelector(auth.selectors.getUserID);
  const currentSeason = useSelector(seasons.selectors.getCurrent);

  const fieldBySeason = useSelector(state => {
    return fieldSeasonModule.selectors.getFieldBydSeason(
      state,
      currentSeason.id,
      field.originalId,
    );
  });

  const fieldSeason = useSelector(state =>
    entities.selectors.getByID(state, 'fieldSeason', field.fieldUserSeasonId),
  );

  const allOperations = useSelector(getOperationsValue);
  const hasUpdate = allOperations
    .filter(operation =>
      filterOperationsForField(operation, field.fieldUserSeasonId),
    )
    .some(isEntityHaveUpdate);

  const highlighted = useSelector(state =>
    fields.selectors.isHighlighted(state, fieldId),
  );

  const [showWarning, setShowWarning] = useState(null);

  const dispatch = useDispatch();

  const title =
    field.title ||
    t('fields.default_title', {
      index: field.index_number || index + 1,
    });

  const baseUrl = getRootScoutingUrl(history.location.pathname);

  const isChecked = checkedIDs && checkedIDs.includes(fieldId);

  const zoomToField = useCallback(() => {
    const SLICE_LENGTH = 4;

    if (!noZoom) {
      const bbox = calcBbox(transformScale(field.geometry, 2));
      dispatch(
        map.actions.addIntent('fly-to-bounds', {
          bounds: [
            bbox.slice(0, 2).reverse(),
            bbox.slice(2, SLICE_LENGTH).reverse(),
          ],
        }),
      );
    }
  }, [field.geometry, noZoom, dispatch]);

  const select = useCallback(() => {
    history.replace(`${baseUrl}/${fieldId}`, getHistoryStateAfterSelect());
    onSelect();
  }, [history, baseUrl, fieldId, onSelect, getHistoryStateAfterSelect]);

  const unselect = useCallback(() => {
    if (history.location?.pathname.endsWith('/edit/list')) {
      dispatch(fields.actions.removeCheck(fieldId));
    } else if (history.location?.pathname.endsWith('/fields')) {
    } else {
      history.replace(baseUrl);
    }
  }, [dispatch, history, baseUrl, fieldId]);

  const onMouseEnter = useCallback(() => {
    dispatch(fields.actions.setHoveredInList(fieldId));
  }, [dispatch, fieldId]);

  const onMouseLeave = useCallback(() => {
    dispatch(fields.actions.setHoveredInList(null));
  }, [dispatch]);

  const logClickEvent = useCallback(() => {
    logEvent('field_open_field_list', {
      ndvi_type: fieldFillingType,
      field_user_season_id: field?.fieldUserSeasonId,
      user_id: userID,
      season_id: currentSeason?.id ?? null,
    });
  }, [field, fieldFillingType, userID, currentSeason]);

  const onFieldListItemClick = useCallback(() => {
    if (readOnly) {
      return;
    }
    if (onClick) {
      logClickEvent();
      zoomToField();
      onClick();
      return;
    }
    if (selected) {
      unselect();
    } else {
      logClickEvent();
      select();
    }
    zoomToField();
  }, [
    readOnly,
    selected,
    zoomToField,
    onClick,
    unselect,
    select,
    logClickEvent,
  ]);

  const hasAttachedNotes = useCallback(
    fieldSeason => {
      return notesInSeason?.some(
        note => fieldSeason.uuid === note.field_user_season_uuid,
      );
    },
    [notesInSeason],
  );

  const removeField = useCallback(() => {
    if (hasAttachedNotes(fieldSeason)) {
      setShowWarning({ type: 'fieldWithNotes' });
    } else {
      setShowWarning({ type: 'field' });
    }
  }, [fieldSeason, hasAttachedNotes]);

  const onChange = useCallback(
    action => {
      switch (action) {
        case 'edit': {
          const viewportHash = getViewportHash(history.location.pathname);
          logEvent('field_edit_dialog_from_fields_list');
          logEvent('field_edit', {
            field_id: field.originalId,
            field_type: field.type,
            field_size: area(field.geometry),
            season: currentSeason.id,
          });
          history.push(`/${viewportHash}/fields/${fieldId}/edit`);
          break;
        }
        case 'remove': {
          removeField();
          break;
        }
        default: {
          console.warn('Unsupported action', action);
          break;
        }
      }
    },
    [history, currentSeason.id, field, fieldId, removeField],
  );

  const visibleIndicator = visibleNewData && hasUpdate;

  return (
    <>
      <EnterExitTransition variant='popup'>
        {showWarning && (
          <LinkedNotesWarningPopup
            t={t}
            title={t('fields.warning.title')}
            text={
              showWarning?.type === 'field'
                ? t('fields.delete-field.warning.text', {
                    count: 1,
                  })
                : t('fields.linked-notes.warning.text', {
                    count: 1,
                  })
            }
            onConfirm={() => {
              setShowWarning(false);
              dispatch(fields.actions.remove(field, currentSeason.id));
            }}
            onCancel={() => {
              setShowWarning(false);
            }}
          />
        )}
      </EnterExitTransition>
      <div
        style={style}
        data-scroll-to={`field-${fieldId}`}
        className={cx('soil-fields-list__list-item', {
          __updated: readOnly || highlighted,
        })}
      >
        {checkedIDs && (
          <div className='soil-fields-list__edit-chk'>
            <Checkbox
              checked={isChecked}
              onChange={event => {
                handleCheck(event, field.id);
              }}
            />
          </div>
        )}

        <LinkButton
          className={cx('soil-fields-list__item', {
            __selected: selected,
            __indicator: !selected && visibleIndicator,
          })}
          onMouseEnter={onMouseEnter}
          onMouseLeave={onMouseLeave}
          onClick={onFieldListItemClick}
        >
          <div className='soil-fields-list__wrapper'>
            <BackgroundImage
              className='soil-fields-list__pic'
              src={getUrlForPolygon(field.geometry, {
                lang,
              })}
            />
            {visibleIndicator && (
              <div className='soil-fields-list__indicator' />
            )}
          </div>

          <div className='soil-fields-list__content'>
            <h2 className='soil-fields-list__header'>
              {title} {getFieldArea({ field, formatUnit })}
            </h2>
            {field.type === 'own' && (
              <div className='soil-fields-list-meta'>
                <div className='soil-fields-list-meta__item soil-fields-list-meta__places'>
                  {field.crops.length !== 0 ? (
                    isGroupedBy && field.crops.length === 1 ? (
                      field.crops[0].variety ? (
                        field.crops[0].variety
                      ) : (
                        <span className='c-neutral'>
                          {t('fields.no_variety')}
                        </span>
                      )
                    ) : (
                      formatCropList(t, field)
                    )
                  ) : (
                    <em className='c-neutral'>
                      {t('fields.no_crop')}
                      &nbsp;
                      <PredictedCropTooltip
                        field={field}
                        history={history}
                        onZoomToField={zoomToField}
                      />
                    </em>
                  )}
                </div>
                {sorted && <SortedByLabel fieldId={fieldId} />}
              </div>
            )}
          </div>
        </LinkButton>
        {!readOnly && (
          <CustomDropdown
            className='soil-fields-list-actions'
            placeholder={t('notes.actions.placeholder')}
            renderValue={() => (
              <span className='soil-fields-list-actions__item' />
            )}
            options={FieldOptions.map(item => ({
              label: t(`notes.actions.${item.name}`),
              feature: item.feature,
              data: fieldBySeason,
              id: item.name,
            }))}
            onClick={event => {
              event.stopPropagation();
            }}
            onChange={onChange}
          />
        )}
      </div>
    </>
  );
};
