import React from 'react';
import { useSelector } from 'react-redux';

import { useTranslate } from 'utils/use-translate';

import RangeLegend from 'components/RangeLegend';

import { StyledBottomMapWidget } from 'components/ui/mapToolBar/styles';

import { BarsCount, LegendColorsMap } from 'constants/style';

import { ApiDataFieldOperations } from '../../files/by-machinery/types';
import { useLegendLoader } from '../../files/by-machinery/dataLoaders/useLegendLoader';
import { fieldsSelectors } from '../../../modules/fields/selectors';
import { getNormalizeValue } from '../../files/by-machinery/utils/getNormalizeValue';

export type OperationLegendProps = {
  operation: ApiDataFieldOperations;
};

export const OperationLegend = ({ operation }: OperationLegendProps) => {
  const { t } = useTranslate();
  const [legends] = useLegendLoader(operation);
  const currentPropertyName = useSelector(
    fieldsSelectors.getFieldFillingProperty,
  );
  if (!legends) {
    return null;
  }
  const legend = legends[currentPropertyName as string];

  if (
    !legend ||
    currentPropertyName === 'timestamp' ||
    legend.min === legend.max
  ) {
    return null;
  }

  let label = t(
    `import.visualisation.${currentPropertyName?.toLowerCase() || ''}.title`,
  ) as string;
  if (legend.unit) {
    label += (', ' + t('import.matching.units.' + legend.unit)) as string;
  }
  const labels: Array<string> = [];
  if (typeof legend.min === 'number' && typeof legend.max === 'number') {
    const precision = 2;
    let minValue = legend.min.toFixed(precision);
    if (currentPropertyName !== 'vr_air_temperature') {
      minValue = Math.abs(+legend.min.toFixed(precision)) + '';
    }
    labels.push(minValue);
    labels.push(legend.max.toFixed(precision));
  } else {
    labels.push(getNormalizeValue(legend.min));
    labels.push(getNormalizeValue(legend.max));
  }

  return (
    <StyledBottomMapWidget>
      <RangeLegend
        colorsRange={LegendColorsMap.operations.colors(BarsCount)}
        labels={[label]}
        bottomLabels={labels.filter(Boolean)}
        smooth={true}
      />
    </StyledBottomMapWidget>
  );
};
