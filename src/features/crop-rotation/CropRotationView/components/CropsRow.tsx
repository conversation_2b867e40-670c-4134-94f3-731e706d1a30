import React, { CSSProperties, VFC } from 'react';

import {
  StyledCropColorBar,
  StyledCropsTableCell,
  styles,
} from '../CropRotationView.style';
import { SeasonType } from 'types/rootState';
import { CropType } from 'types/crops';
import CropsHeaderCell from './CropsHeaderCell';
import CropsTableCell from './CropsTableCell';
import { TranslateFunction } from 'utils/use-translate';

type CropsRowProps = {
  seasons: SeasonType[];
  columnIndex: number;
  rowIndex: number;

  allCrops: Record<string, CropType>;
  allCropsCodes: string;
  t: TranslateFunction;
  style: CSSProperties;
};

const CropsRow: VFC<CropsRowProps> = ({
  seasons,
  columnIndex,
  rowIndex,
  style,
  t,
  allCrops,
  allCropsCodes,
}) => {
  if (rowIndex === 0) {
    return (
      <CropsHeaderCell
        season={seasons[columnIndex - 1] as SeasonType}
        columnIndex={columnIndex}
        rowIndex={rowIndex}
        style={style}
        t={t}
      />
    );
  }
  const cropCode: string = allCropsCodes[rowIndex - 1] as string;
  if (columnIndex === 0) {
    return (
      <StyledCropsTableCell
        columnIndex={columnIndex}
        rowIndex={rowIndex}
        style={style}
      >
        <StyledCropColorBar color={allCrops[cropCode]?.color || ''} />
        <span className={styles.cropName}>{allCrops[cropCode]?.name}</span>
      </StyledCropsTableCell>
    );
  }

  return (
    <CropsTableCell
      season={seasons[columnIndex - 1] as SeasonType}
      columnIndex={columnIndex}
      rowIndex={rowIndex}
      cropCode={cropCode}
      style={style}
    />
  );
};

export default CropsRow;
