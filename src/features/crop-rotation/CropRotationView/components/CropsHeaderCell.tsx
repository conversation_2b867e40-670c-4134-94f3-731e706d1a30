import React, { CSSProperties, VFC } from 'react';
import { useSelector } from 'react-redux';
import sumBy from 'lodash/sumBy';

import fieldSeason from 'modules/fieldSeason';
import { StyledCropsHeaderCell } from '../CropRotationView.style';
import { SeasonType } from 'types/rootState';
import settings from 'modules/settings';
import { FormatUnit } from 'utils/functions';
import { TranslateFunction } from 'utils/use-translate';

type CropsHeaderCellProps = {
  season: SeasonType,
  columnIndex: number,
  rowIndex: number,
  style: CSSProperties,
  t: TranslateFunction
};

const CropsHeaderCell: VFC<CropsHeaderCellProps> = (
  {
    season,
    columnIndex,
    rowIndex,
    style,
    t,
  }) => {
  const formatUnit: FormatUnit = useSelector(settings.selectors.getUnitFormatter);
  const crops = useSelector(state => fieldSeason.selectors.getCropStatistics(state, season?.id));
  const filledArea = sumBy(crops, 'area');
  return (
    <StyledCropsHeaderCell
      columnIndex={columnIndex}
      rowIndex={rowIndex}
      style={style}
    >
      {columnIndex === 0
        ? t('croprotation.total_used_crops')
        : formatUnit(filledArea, 'ha', 'area')}
    </StyledCropsHeaderCell>
  );
};

export default CropsHeaderCell;
