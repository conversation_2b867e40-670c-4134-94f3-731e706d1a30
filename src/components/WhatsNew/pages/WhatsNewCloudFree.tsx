import React, { VFC } from 'react';
import { Trans } from 'react-i18next';
import Button from 'components/ui/Button/Button';

import WhatsNewImg from 'assets/images/whatsnew/whatsnew_cloudfree.jpg';
import WhatsNewImg2x from 'assets/images/whatsnew/<EMAIL>';
import { WhatsNewProps } from '../types';
import { FeaturesKeyEnum } from '../consts';
import {
  StyledPage,
  styles,
} from 'components/WhatsNew/WhatsNewContent/WhatsNewContent.style';

const CloudFree: VFC<WhatsNewProps> = ({ style, onClick, isLast, t }) => {
  const link = (
    /* eslint-disable-next-line jsx-a11y/anchor-has-content */
    <a
      target='_blank'
      rel='noreferrer'
      href={t('whatsnew.cloudfree.text.link') as string}
    />
  );
  return (
    <StyledPage style={{ ...style }}>
      <div className={styles.semiPage}>
        <div className={styles.pageContent}>
          <div className={styles.textContent}>
            <h2 className={styles.title}>{t('whatsnew.cloudfree.title')}</h2>
            <p className={styles.text}>
              <Trans components={[link]}>
                {'whatsnew.cloudfree.text.withlink'}
              </Trans>
            </p>
          </div>
          <Button className='btn btn-success fit-content' onClick={onClick}>
            {t(`whatsnew.${isLast ? 'end' : 'next'}.action_text`)}
          </Button>
        </div>
      </div>
      <div className={styles.semiPage}>
        <picture
          className={styles.pagePicture}
          style={{ marginRight: 40, marginTop: 40 }}
        >
          <source srcSet={`${WhatsNewImg} 1x, ${WhatsNewImg2x} 2x`} />
          <img
            src={WhatsNewImg}
            alt={t('whatsnew.cloudfree.title') as string}
          />
        </picture>
      </div>
    </StyledPage>
  );
};

const WhatsNewCloudFree = {
  key: FeaturesKeyEnum.cloudFree,
  component: CloudFree,
  filter: {},
};

export default WhatsNewCloudFree;
