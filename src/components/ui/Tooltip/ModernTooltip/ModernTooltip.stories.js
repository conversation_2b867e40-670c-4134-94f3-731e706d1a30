import React from 'react';
import { linkTo } from '@storybook/addon-links';

import LinkButton from 'components/LinkButton';
import { ModernTooltip, ModernTooltipDirection } from './ModernTooltip';

export default {
  title: 'ui/Tooltip/ModernTooltip',
  component: ModernTooltip,
  decorators: [storyFn => <div style={{ padding: 16 }}>{storyFn()}</div>],
};

export const Simple = () => (
  <ModernTooltip
    active
    offset={[0, 0]}
    renderTrigger={props => <span {...props}>This is tooltip trigger</span>}
    renderTooltip={() => 'This is tooltip content'}
  />
);

export const AnimatedFromRight = () => (
  <ModernTooltip
    active
    offset={[0, 0]}
    animate={ModernTooltipDirection.FROM_RIGHT}
    renderTrigger={props => <span {...props}>This is tooltip trigger</span>}
    renderTooltip={() => 'This is tooltip content'}
  />
);

export const WithPopover = () => (
  <span>
    All other props are passed to{' '}
    <ModernTooltip
      active
      offset={[10, 0]}
      align='middle-right'
      renderTrigger={props => (
        <LinkButton {...props} onClick={linkTo('common/Popover')}>
          Popover
        </LinkButton>
      )}
      renderTooltip={() => 'We showcase align and offset there too'}
    />
  </span>
);
