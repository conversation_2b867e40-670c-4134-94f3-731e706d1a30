import { css } from 'linaria';

export const styles = {
  monthPickerDropdownContainer: css`
    position: relative;
    width: 100%;
  `,
  monthPickerDropdownButton: css`
    align-items: center !important;
    all: unset;
    background: #e5e5ea;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    margin: 13px 0 4px -1px;
    padding: 5px 4px;
    text-align: center;
    width: 100%;
  `,
  monthPickerDropdownButtonDisabled: css`
    background-color: #f5f5f5;
    border-color: #e0e0e0;
    color: #999;
    cursor: not-allowed;

    &:hover {
      border-color: #e0e0e0;
    }
  `,
  monthPickerDropdownLabel: css`
    color: #1c1c1e;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 130%;
  `,
};
