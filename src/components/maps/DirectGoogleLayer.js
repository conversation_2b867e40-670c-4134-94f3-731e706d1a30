import React, { memo } from 'react';
import { TileLayer } from 'react-leaflet';

import { useTranslate } from 'utils/use-translate';

const DirectGoogleLayer = () => {
  const { i18n } = useTranslate();
  const lang = i18n.resolvedLanguage;

  return (
    <TileLayer
      url={`https://{s}.google.com/vt/lyrs=s,h&hl=${lang}&x={x}&y={y}&z={z}`}
      attribution='Map data: Google, DigitalGlobe'
      subdomains={['mt0', 'mt1', 'mt2', 'mt3']}
    />
  );
};

export default memo(DirectGoogleLayer);
