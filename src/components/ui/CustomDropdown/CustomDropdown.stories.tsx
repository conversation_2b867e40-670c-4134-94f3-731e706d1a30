import React, { useState } from 'react';
import { CustomDropdown } from './CustomDropdown';
import { css } from 'linaria';

export default {
  title: 'ui/CustomDropdown',
  component: CustomDropdown,
  decorators: [
    (Story: React.FC) => (
      <div style={{ padding: 16 }}>
        <Story />
      </div>
    ),
  ],
};

const MOCK_OPTIONS = [
  { id: 'a', label: 'Option A' },
  { id: 'b', label: 'Option B' },
  { id: 'c', label: 'Option C' },
];

export const Simple = () => {
  const [value, setValue] = useState<string | null>(null);

  return (
    <CustomDropdown
      options={MOCK_OPTIONS}
      value={value}
      renderValue={props => {
        return (
          <span>
            {MOCK_OPTIONS.find(option => option.id === props?.value)?.label ||
              'Select...'}
          </span>
        );
      }}
      onChange={selectedValue => {
        setValue(selectedValue ?? null);
      }}
    />
  );
};

export const IsOpenValue = () => {
  const [value, setValue] = useState<string | null>(null);

  return (
    <CustomDropdown
      options={MOCK_OPTIONS}
      value={value}
      renderValue={props => {
        let { value, isOpen } = props ?? {};
        return (
          <span>
            {`${
              MOCK_OPTIONS.find(option => option.id === value)?.label ||
              'Select...'
            } - ${isOpen ? 'opened' : 'closed'}`}
          </span>
        );
      }}
      onChange={selectedValue => {
        setValue(selectedValue ?? null);
      }}
    />
  );
};

export const OptionsWithIcon = () => {
  const [value, setValue] = useState<string | null>(null);

  const MOCK_OPTIONS_WITH_ICONS = MOCK_OPTIONS.map(option => ({
    ...option,
    icon: 'marker',
  }));

  return (
    <CustomDropdown
      options={MOCK_OPTIONS_WITH_ICONS}
      value={value}
      renderValue={props => {
        let { value } = props ?? {};
        return (
          <span>
            {MOCK_OPTIONS.find(option => option.id === value)?.label ||
              'Select...'}
          </span>
        );
      }}
      onChange={selectedValue => {
        setValue(selectedValue ?? null);
      }}
    />
  );
};

export const Disabled = () => {
  const [value, setValue] = useState<string | null>(null);

  return (
    <CustomDropdown
      disabled
      options={MOCK_OPTIONS}
      value={value}
      renderValue={props => {
        let { value } = props ?? {};
        return (
          <span>
            {MOCK_OPTIONS.find(option => option.id === value)?.label ||
              'Select...'}
          </span>
        );
      }}
      onChange={selectedValue => {
        setValue(selectedValue ?? null);
      }}
    />
  );
};

export const WrapperClassName = () => {
  const [value, setValue] = useState<string | null>(null);
  const wrapperClassName = css`
    display: inline-block;
    border: 1px solid red;
    padding: 5px;
  `;

  return (
    <CustomDropdown
      wrapperClassName={wrapperClassName}
      options={MOCK_OPTIONS}
      value={value}
      renderValue={props => {
        let { value } = props ?? {};
        return (
          <span>
            {MOCK_OPTIONS.find(option => option.id === value)?.label ||
              'Select...'}
          </span>
        );
      }}
      onChange={selectedValue => {
        setValue(selectedValue ?? null);
      }}
    />
  );
};

//Story show different between wrapper and trigger classNames
export const TriggerClassName = () => {
  const [value, setValue] = useState<string | null>(null);
  const wrapperClassName = css`
    border: 1px solid green;
    padding: 5px;
  `;

  const triggerClassName = css`
    display: inline-block;
    border: 1px solid red;
    padding: 5px;
  `;

  return (
    <CustomDropdown
      wrapperClassName={wrapperClassName}
      className={triggerClassName}
      options={MOCK_OPTIONS}
      value={value}
      renderValue={props => {
        let { value } = props ?? {};
        return (
          <span>
            {MOCK_OPTIONS.find(option => option.id === value)?.label ||
              'Select...'}
          </span>
        );
      }}
      onChange={selectedValue => {
        setValue(selectedValue ?? null);
      }}
    />
  );
};

export const SelectBox = () => {
  const [value, setValue] = useState<string | null>(null);

  return (
    <CustomDropdown
      options={MOCK_OPTIONS}
      value={value}
      className='form-select'
      renderValue={props => {
        return (
          <span className='form-select__value'>
            {MOCK_OPTIONS.find(option => option.id === props?.value)?.label ||
              'Select...'}
          </span>
        );
      }}
      onChange={selectedValue => {
        setValue(selectedValue ?? null);
      }}
      withSearchBox
      searchBoxProps={{ placeholder: 'Search...' }}
    />
  );
};

export const SelectBoxWhite = () => {
  const [value, setValue] = useState<string | null>(null);

  return (
    <CustomDropdown
      options={MOCK_OPTIONS}
      value={value}
      className='form-select form-select-white'
      renderValue={props => {
        return (
          <span className='form-select__value'>
            {MOCK_OPTIONS.find(option => option.id === props?.value)?.label ||
              'Select...'}
          </span>
        );
      }}
      onChange={selectedValue => {
        setValue(selectedValue ?? null);
      }}
      withSearchBox
      searchBoxProps={{ placeholder: 'Search...' }}
    />
  );
};

export const WithSearchbox = () => {
  const [value, setValue] = useState<string | null>(null);

  return (
    <CustomDropdown
      options={MOCK_OPTIONS}
      value={value}
      renderValue={props => {
        let { value } = props ?? {};
        return (
          <span>
            {MOCK_OPTIONS.find(option => option.id === value)?.label ||
              'Select...'}
          </span>
        );
      }}
      onChange={selectedValue => {
        setValue(selectedValue ?? null);
      }}
      withSearchBox
      searchBoxProps={{ placeholder: 'Search...' }}
    />
  );
};

export const IgnoreIds = () => {
  const [value, setValue] = useState<string | null>(null);

  return (
    <CustomDropdown
      options={MOCK_OPTIONS}
      ignoreIDs={['a']}
      value={value}
      renderValue={props => {
        let { value } = props ?? {};
        return (
          <span>
            {MOCK_OPTIONS.find(option => option.id === value)?.label ||
              'Select ignored option A...'}
          </span>
        );
      }}
      onChange={selectedValue => {
        setValue(selectedValue ?? null);
      }}
    />
  );
};

export const DisabledIds = () => {
  const [value, setValue] = useState<string | null>(null);

  return (
    <CustomDropdown
      options={MOCK_OPTIONS}
      disabledIDs={['a']}
      value={value}
      renderValue={props => {
        let { value } = props ?? {};
        return (
          <span>
            {MOCK_OPTIONS.find(option => option.id === value)?.label ||
              'Select...'}
          </span>
        );
      }}
      onChange={selectedValue => {
        setValue(selectedValue ?? null);
      }}
    />
  );
};

export const OnToggle = () => {
  const [value, setValue] = useState<string | null>(null);
  const [toggleState, setToggleState] = useState<{
    count: number;
    isOpen: boolean;
  }>({ count: 0, isOpen: false });

  const onToggle = (isOpen: boolean) => {
    setToggleState({
      count: toggleState.count + 1,
      isOpen,
    });
  };

  return (
    <>
      <span>You've toggled dropdown {toggleState.count} times.</span>
      {toggleState.count > 0 ? (
        <span>
          On last Toggle it was {toggleState.isOpen ? 'opened' : 'closed'}
        </span>
      ) : null}
      <CustomDropdown
        options={MOCK_OPTIONS}
        value={value}
        renderValue={props => {
          let { value } = props ?? {};
          return (
            <span>
              {MOCK_OPTIONS.find(option => option.id === value)?.label ||
                'Select...'}
            </span>
          );
        }}
        onChange={selectedValue => {
          setValue(selectedValue ?? null);
        }}
        onToggle={onToggle}
      />
    </>
  );
};

export const OnClick = () => {
  const [value, setValue] = useState<string | null>(null);
  const [clicksCount, setClicksCount] = useState<number>(0);

  const onClick = () => setClicksCount(clicksCount + 1);

  return (
    <>
      <span>You've clicked on dropdown {clicksCount} times</span>
      <CustomDropdown
        options={MOCK_OPTIONS}
        value={value}
        renderValue={props => {
          let { value } = props ?? {};
          return (
            <span>
              {MOCK_OPTIONS.find(option => option.id === value)?.label ||
                'Select...'}
            </span>
          );
        }}
        onChange={selectedValue => {
          setValue(selectedValue ?? null);
        }}
        onClick={onClick}
      />
    </>
  );
};
