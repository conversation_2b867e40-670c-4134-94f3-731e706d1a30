import React from 'react';
import { useSelector } from 'react-redux';
import { RouteComponentProps } from 'react-router-dom';

import { fieldsSelectors } from 'modules/fields/selectors';
import { VegetationFieldToolbar } from './VegetationFieldToolbar';
import { MachineryFieldToolbar } from './MachineryFieldToolbar';

type FieldToolbarProps = {} & RouteComponentProps<{ fieldId: string }>;

export const FieldToolbar = ({ match }: FieldToolbarProps) => {
  const fieldFillingType = useSelector(fieldsSelectors.getFieldFillingType);

  if (fieldFillingType === 'operation') {
    return <MachineryFieldToolbar fieldId={match.params.fieldId} />;
  }

  return <VegetationFieldToolbar fieldId={match.params.fieldId} />;
};
