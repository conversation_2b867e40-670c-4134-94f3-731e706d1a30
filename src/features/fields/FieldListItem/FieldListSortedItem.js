import React, { memo } from 'react';
import { useSelector } from 'react-redux';

import settings from 'modules/settings';

import { FieldListItem } from './FieldListItem';

import { styles } from 'features/fields/styles/fieldList.styles';
import { styled } from 'linaria/react';

import { useTranslate } from 'utils/use-translate';

export const FantomFieldListItemId = 'fantom-field-list-item-id';

const StyledCollapsibleTrigger = styled.button`
  display: block;
  width: 100%;
  box-sizing: border-box;
  border: none;
  background: none;
  cursor: pointer;
  padding: 11px 20px 10px 30px;
  transition: box-shadow 0.3s;
  border-top: ${props =>
    props.index === 0 ? 'none' : '1px solid rgba(214, 220, 225, 0.5)'};
  &:before {
    content: '';
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 3.5px 0 3.5px 4px;
    border-color: transparent transparent transparent #222222;
    position: absolute;
    left: 16px;
    top: 50%;
    transform-origin: 50% 50%;
    transform: ${props =>
      props.isOpened
        ? 'translateY(-50%) rotate(90deg)'
        : 'translateY(-50%) rotate(0)'};
    transition: transform 0.3s, border-color 0.3s;
  }
  &:hover {
    cursor: pointer;
    &:before {
      border-color: transparent transparent transparent #27ae60;
    }
  }
`;

export const FieldListSortedItem = ({
  index,
  style,
  data,
  match,
  history,
  isGroupedBy,
  notesInSeason,
  onSelect,
  onExpand,
  ...otherProps
}) => {
  const { t } = useTranslate();
  const formatUnit = useSelector(settings.selectors.getUnitFormatter);

  const { id, expandable } = data[index];

  if (id === FantomFieldListItemId) {
    return <div style={style} />;
  }

  if (expandable) {
    const { crop, count, area, expanded } = data[index];
    return (
      <div style={style}>
        <StyledCollapsibleTrigger
          isOpened={expanded}
          onClick={() => onExpand(crop)}
          index={index}
        >
          <div className={styles.expandableTriggerContent}>
            <div className={styles.expandableTriggerTitle}>
              <span className={styles.fieldItemHeader}>
                {t(`crop.${crop}`)}
              </span>
              <span className={styles.fieldGroupTriggerCount}>{count}</span>
            </div>
            <div>
              <span className={styles.fieldGroupTriggerArea}>
                {formatUnit(area, 'm2', 'area')}
              </span>
            </div>
          </div>
        </StyledCollapsibleTrigger>
      </div>
    );
  }

  return (
    <FieldListItem
      style={style}
      fieldId={id}
      selected={id === match.params.fieldId}
      history={history}
      index={index}
      isGroupedBy={isGroupedBy}
      onSelect={onSelect}
      sorted
      notesInSeason={notesInSeason}
      visibleNewData
      {...otherProps}
    />
  );
};

export const FieldListSortedItemMemo = memo(FieldListSortedItem);
