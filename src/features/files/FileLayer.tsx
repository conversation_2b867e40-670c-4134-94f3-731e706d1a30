import React, { Fragment, VFC } from 'react';
import { <PERSON>eo<PERSON><PERSON><PERSON>, Pane } from 'react-leaflet';
import { connect } from 'react-redux';
import L from 'leaflet';
import { RouteComponentProps } from 'react-router-dom';

// @ts-ignore
import VectorGrid from 'components/maps/VectorGrid';

// @ts-ignore
import MapNavigationLock from 'features/scouting/MapNavigationLock';
// @ts-ignore
import MissingFileRedirector from './MissingFileRedirector';

import ZIndex from 'constants/zindex';
import { parseQuery } from 'utils/query';

// @ts-ignore
import files from 'modules/files';
// @ts-ignore
import fields from 'modules/fields';
import { colors } from 'constants/style';
import RootState from 'types/rootState';
import { FileFullData } from 'types/filesState';
import { getViewSchemeConfig } from './colorSchemes';

const PreviewStyle = {
  color: '#FFE767',
  fillOpacity: 0,
  weight: 1,
  radius: 2.5,
};

type FileLayerProps = {
  file: FileFullData;
  fieldLayer?: TEMPORARY_ANY;
} & RouteComponentProps;

export const FileLayerComponent: VFC<FileLayerProps> = ({
  location,
  match,
  file,
  fieldLayer,
}) => {
  if (!file || file.status !== 'resolved') {
    return null;
  }
  const query = parseQuery(location);
  const column = file.columns
    .filter(column => file.properties.includes(column.name))
    .find(column => column.name === (query.column || file.default_column));
  const preview = match.url.includes('/upload') || !column;

  let colorScheme: TEMPORARY_ANY;

  if (preview) {
    colorScheme = PreviewStyle;
  } else {
    const viewSchemeConfig = getViewSchemeConfig(column, file);
    colorScheme = viewSchemeConfig.colorSchemeGetter(column, file);
  }

  return (
    <Fragment key={file.id}>
      {preview && <MapNavigationLock />}
      {!preview && (
        <MissingFileRedirector location={location} fileId={file.id} />
      )}
      <Pane
        className='fields-pane-fields'
        style={{ zIndex: ZIndex.SelectionPane }}
      >
        {!preview && fieldLayer && (
          <GeoJSON
            key={fieldLayer.key}
            data={fieldLayer.geojson}
            zIndex={0}
            style={{
              fillColor: colors.undercoverColor,
              color: colors.undercoverColor,
              fillOpacity: 1,
              weight: 2,
            }}
          />
        )}
        <VectorGrid
          zIndex={1}
          key={column && column.name}
          data={file.geojson}
          interactive={!preview}
          tooltip={column ? column.name : null}
          tooltipClassName='tooltip-dark'
          style={colorScheme}
          onClick={(mapEvent: TEMPORARY_ANY) => {
            L.DomEvent.stopPropagation(mapEvent);
          }}
        />
        {!preview && (
          <VectorGrid
            zIndex={2}
            data={file.overlap.geojson}
            interactive={false}
            style={{
              color: 'gray',
              fillColor: colors.overlapColor,
              fillOpacity: 1,
              weight: 0.5,
            }}
          />
        )}
      </Pane>
    </Fragment>
  );
};

type MatchParams = {
  fileId: string;
};

const mapStateToProps = (
  state: RootState,
  ownProps: RouteComponentProps<MatchParams>,
) => {
  const file = files.selectors.getByID(state, ownProps.match.params.fileId);
  const field =
    file && fields.selectors.getByOriginalID(state, 'own', file.field_user_id);

  return {
    fieldLayer: field ? fields.selectors.getFieldLayer(state, field.id) : null,
    file,
  };
};

export default connect(mapStateToProps)(FileLayerComponent);
