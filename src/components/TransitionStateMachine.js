import { Component } from 'react';

class TransitionStateMachine extends Component {
  state = {
    state: this.props.active ? 'entered' : 'exited',
  };

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (this.props.active !== nextProps.active) {
      const { active: nextActive, enterTime, exitTime } = nextProps;
      this.setState({
        state: nextActive ? 'entering' : 'exiting',
        persistedChild: nextActive ? null : this.lastRenderedChild,
      });
      clearTimeout(this.timer);
      this.timer = setTimeout(
        () => {
          this.setState({
            state: nextActive ? 'entered' : 'exited',
            persistedChild: null,
          });
        },
        nextActive ? enterTime : exitTime,
      );
    }
  }

  componentWillUnmount() {
    clearTimeout(this.timer);
  }

  wrapActiveChild = child => {
    const { state, persistedChild } = this.state;
    if (state === 'exited') {
      return null;
    }
    this.lastRenderedChild = child;
    return persistedChild || child;
  };

  render() {
    const { render } = this.props;
    const { state } = this.state;
    return render({
      state,
      wrapActiveChild: this.wrapActiveChild,
    });
  }
}

export default TransitionStateMachine;
