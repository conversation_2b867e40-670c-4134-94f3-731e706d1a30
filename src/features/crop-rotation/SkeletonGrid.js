import { MultiGrid } from 'react-virtualized';
import {
  StyledAutoRota<PERSON><PERSON><PERSON>,
  Styled<PERSON><PERSON>s<PERSON><PERSON><PERSON>n<PERSON><PERSON>,
  Styled<PERSON><PERSON>s<PERSON><PERSON><PERSON><PERSON>,
  StyledFieldsHeader<PERSON><PERSON>,
  StyledHeader<PERSON>ell,
  StyledSkeletonCell,
  StyledTopLeftCell,
  styles,
} from './CropRotationView/CropRotationView.style';
import scrollbarSize from 'dom-helpers/scrollbarSize';
import React from 'react';
import {
  AUTOROTATE_ROW_HEIGHT,
  COLUMN_WIDTH,
  FIELDS_FIRST_ROW_HEIGHT,
  FIELDS_ROW_HEIGHT,
  FIRST_COLUMN_WIDTH,
  rowTypes,
  SEASONS_ROW_HEIGHT,
} from 'constants/crop-rotation';

export const SkeletonGrid = ({ width, height, t }) => {
  const skeletonRenderers = {
    [rowTypes.seasons]: ({ columnIndex, key, style }) => {
      if (columnIndex === 0) {
        return (
          <StyledTopLeftCell key={key} style={style}>
            <h1 className={styles.cropRotationTitle} key={key}>
              {t('croprotation.title')}
            </h1>
          </StyledTopLeftCell>
        );
      }
      return (
        <StyledHeaderCell columnIndex={columnIndex} key={key} style={style}>
          <div className={styles.headerCell}>
            <div className={styles.headerCellTitle}>
              <StyledSkeletonCell
                width={75}
                height={10}
                marginTop={3}
                marginBottom={12}
              />
            </div>
            <div className={styles.headerCellStats}>
              <StyledSkeletonCell width={70} height={8} marginBottom={4} />
              <div className={styles.colHeaderInfoProgress} />
            </div>
          </div>
        </StyledHeaderCell>
      );
    },
    [rowTypes.autorotate]: ({ columnIndex, key, rowIndex, style }) => {
      if (columnIndex === 0) return null;
      return (
        <StyledAutoRotateCell
          columnIndex={columnIndex}
          key={key}
          rowIndex={rowIndex}
          style={style}
        >
          <StyledSkeletonCell width={COLUMN_WIDTH} height={42} />
        </StyledAutoRotateCell>
      );
    },
    [rowTypes.fields]: ({ columnIndex, key, rowIndex, style }) => {
      if (rowIndex === 0 && columnIndex === 0) {
        return (
          <StyledFieldsHeaderCell
            rowIndex={0}
            columnIndex={0}
            style={style}
            key={key}
          >
            <StyledSkeletonCell height={18} width={118} />
            <StyledSkeletonCell height={18} width={77} />
          </StyledFieldsHeaderCell>
        );
      }
      if (rowIndex === 0) {
        return (
          <StyledFieldsHeaderCell
            rowIndex={0}
            columnIndex={columnIndex}
            style={style}
            key={key}
          >
            <StyledSkeletonCell height={18} width={77} />
          </StyledFieldsHeaderCell>
        );
      }
      if (columnIndex === 0) {
        return (
          <StyledFieldsColumnCell rowIndex={rowIndex} style={style} key={key}>
            <div className={styles.fieldMeta}>
              <StyledSkeletonCell height={48} width={48} />
              <StyledSkeletonCell
                height={14}
                width={77}
                marginTop={5}
                marginLeft={10}
              />
            </div>
            <div className={styles.fieldArea}>
              <StyledSkeletonCell height={14} width={40} />
            </div>
          </StyledFieldsColumnCell>
        );
      }

      return (
        <StyledFieldsCropsCell
          columnIndex={columnIndex}
          rowIndex={rowIndex}
          key={key}
          style={style}
        >
          <StyledSkeletonCell height={18} width={COLUMN_WIDTH} />
        </StyledFieldsCropsCell>
      );
    },
  };

  return (
    <MultiGrid
      className={styles.cropRotationGrid}
      classNameBottomLeftGrid={styles.bottomLeft}
      rowCount={10}
      rowHeight={({ index }) => {
        switch (index) {
          case 0:
            return SEASONS_ROW_HEIGHT;
          case 1:
            return AUTOROTATE_ROW_HEIGHT;
          case 2:
            return FIELDS_FIRST_ROW_HEIGHT;
          default:
            return FIELDS_ROW_HEIGHT;
        }
      }}
      columnCount={4}
      columnWidth={({ index }) => (index ? COLUMN_WIDTH : FIRST_COLUMN_WIDTH)}
      fixedColumnCount={1}
      height={height}
      width={width - scrollbarSize()}
      cellRenderer={({ columnIndex, key, rowIndex, style }) => {
        let type = '';
        switch (rowIndex) {
          case 0:
            type = rowTypes.seasons;
            break;
          case 1:
            type = rowTypes.autorotate;
            break;
          default:
            type = rowTypes.fields;
            break;
        }
        return skeletonRenderers[type]({
          columnIndex,
          key,
          rowIndex: rowIndex - 2,
          style,
        });
      }}
    />
  );
};
