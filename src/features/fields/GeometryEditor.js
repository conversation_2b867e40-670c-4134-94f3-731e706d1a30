import React, { useRef, useEffect, useState, useImperativeHandle } from 'react';
import { GeoJSON, withLeaflet } from 'react-leaflet';

import DocumentKeyHandler from 'components/DocumentKeyHandler';

import useUnmountEffect from 'utils/use-unmount-effect';
import randomID from 'utils/random-id';

const Options = {
  draggable: false,
  tooltips: false,
  allowSelfIntersection: false,
  hintlineStyle: {
    color: '#FFE767',
    dashArray: [5, 5],
    weight: 2,
  },
  templineStyle: {
    color: '#FFE767',
    weight: 2,
  },
};

const CutOptions = {
  draggable: false,
  tooltips: false,
  snapDistance: 5,
  hintlineStyle: {
    color: 'white',
    dashArray: [5, 5],
    weight: 2,
  },
  templineStyle: {
    color: 'white',
    weight: 2,
  },
};

const SelectOptions = {
  draggable: false,
  tooltips: false,
  snappable: false,
  cursorMarker: false,
  pathOptions: {
    color: 'white',
    fillOpacity: 0.4,
    weight: 2,
  },
};

const ensureMultipolygon = geometry => {
  if (geometry.type === 'MultiPolygon') {
    return geometry;
  }
  if (geometry.type === 'Polygon') {
    return {
      type: 'MultiPolygon',
      coordinates: [geometry.coordinates],
    };
  }
  return null;
};

const GeometryEditor = (
  {
    style,
    leaflet,
    allowCreate,
    editMode,
    data,
    noKeyDeleteHandle,
    onCreate,
    onUpdate,
    onSelectionChange,
  },
  ref,
) => {
  const { map } = leaflet;
  const layer = useRef(null);
  const skipNextUpdate = useRef(false);

  const [updateKey, setUpdateKey] = useState('default');

  // Recreate leaflet layer on geometry change
  useEffect(() => {
    if (skipNextUpdate.current) {
      skipNextUpdate.current = false;
      return;
    }
    setUpdateKey(randomID());
  }, [data]);

  // Start drawing mode on each map click with allowCreate
  useEffect(() => {
    const handleMapClick = event => {
      if (
        !allowCreate ||
        map.pm.Draw.Polygon.enabled() ||
        map.pm.Draw.Cut.enabled()
      ) {
        return;
      }
      map.pm.enableDraw('Polygon', Options);
      map.pm.Draw.Polygon._createVertex(event);
    };

    map.on('click', handleMapClick);
    return () => map.off('click', handleMapClick);
  }, [map, allowCreate]);

  // Handle geometry creation
  useEffect(() => {
    const handleCreate = event => {
      const points = event.layer.getLatLngs();
      if (points[0].length < 3) {
        event.layer.remove();
        return;
      }

      const feature = event.layer.toGeoJSON();
      onCreate(ensureMultipolygon(feature.geometry), event);
      event.layer.remove();
    };

    map.on('pm:create', handleCreate);
    return () => map.off('pm:create', handleCreate);
  }, [map, onCreate]);

  // Disable all drawing modes when leaving
  useUnmountEffect(() => {
    map.pm.Draw.disable();
  });

  // Process some events on existing layer, like editing and cutting
  useEffect(() => {
    const events = {
      'pm:vertexselection': event => {
        onSelectionChange(event.count);
      },
      'pm:vertexclicked': event => {
        if (editMode !== 'select') {
          return;
        }
        const { marker, sourceTarget } = event;
        sourceTarget.pm.toggleMarkerSelection(marker);
      },
      'pm:edit': event => {
        // Last vertex was removed
        if (event.removed) {
          onUpdate(null);
          return;
        }

        skipNextUpdate.current = true;
        const feature = event.sourceTarget.toGeoJSON();
        onUpdate(ensureMultipolygon(feature.geometry), event);
      },
      'pm:cut': event => {
        const geojson = event.layer.toGeoJSON();

        // We don't set skipNextUpdate here, as leaflet-geoman already removed
        // original layer that we had all the events set up, so we just re-create
        // entire thing after update as though the update was external
        event.layer.remove();

        // Everything was removed during cut
        if (geojson.features.length === 0) {
          onUpdate(null);
          return;
        }

        onUpdate(ensureMultipolygon(geojson.features[0].geometry), event);
      },
    };

    const { leafletElement } = layer.current;
    leafletElement.on(events);
    return () => leafletElement.off(events);
  }, [updateKey, onUpdate, onSelectionChange, editMode]);

  // Handle point selection ux
  useEffect(() => {
    if (editMode !== 'select') {
      return;
    }

    const handleSelect = () => {
      setImmediate(() => {
        // Restart selection after each selection
        map.pm.enableDraw('Selection', SelectOptions);
      });
    };

    // Allow dragging while shift is pressed
    const handleKeyDown = event => {
      if (event.key !== 'Shift') {
        return;
      }
      map.pm.disableDraw('Selection');
      map.dragging.enable();
    };
    const handleKeyUp = event => {
      if (event.key !== 'Shift') {
        return;
      }
      map.pm.enableDraw('Selection', SelectOptions);
      map.dragging.disable();
    };

    map.on('pm:select', handleSelect);
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);
    return () => {
      map.off('pm:select', handleSelect);
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, [editMode, map]);

  useEffect(() => {
    if (editMode === 'cut') {
      map.pm.enableDraw('Cut', CutOptions);
      map.getPane('overlayPane').style.zIndex = 600;
    } else if (editMode === 'select') {
      map.pm.enableDraw('Selection', SelectOptions);
      map.getPane('overlayPane').style.zIndex = 600;
    } else {
      map.pm.disableDraw();
    }
    if (editMode === 'select') {
      map.dragging.disable();
    } else {
      map.dragging.enable();
    }
    const { leafletElement } = layer.current;
    leafletElement.pm.enable();
  }, [editMode, updateKey, map]);

  useImperativeHandle(ref, () => ({
    removeSelectedMarkers: () => {
      const { leafletElement } = layer.current;
      leafletElement.pm.removeSelectedMarkers();
    },
  }));

  return (
    <>
      <DocumentKeyHandler
        hotkey='Escape'
        onPress={() => {
          if (editMode === 'cut') {
            // Just resets currenly cut shape
            map.pm.disableDraw('Cut');
            map.pm.enableDraw('Cut', CutOptions);
          }
          if (editMode === 'select') {
            map.pm.disableDraw('Selection');
            map.pm.enableDraw('Selection', SelectOptions);
          }
          if (allowCreate) {
            map.pm.disableDraw('Polygon');
          }
        }}
      />
      {!noKeyDeleteHandle && (
        <DocumentKeyHandler
          hotkeys={['delete', 'backspace']}
          onPress={() => {
            const { leafletElement } = layer.current;
            leafletElement.pm.removeSelectedMarkers();
          }}
        />
      )}
      <GeoJSON ref={layer} key={updateKey} style={style} data={data} />
    </>
  );
};

export default withLeaflet(React.forwardRef(GeometryEditor));
