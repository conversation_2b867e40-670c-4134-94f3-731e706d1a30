import { styled } from 'linaria/react';
import { css } from 'linaria';

export const styles = {
  valuesListItem: css`
    padding-left: 17px;
    font-family: <PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
    font-size: 10px;
    line-height: 12px;
    position: relative;
    margin-right: 14px;
    margin-bottom: 10px;

    &.__vertical {
      margin-bottom: 10px;
    }

    &:before {
      content: '';
      width: 12px;
      height: 12px;
      background-color: var(--marker-color);
      position: absolute;
      box-sizing: border-box;
      border: 1px solid var(--border-color);
      left: 0;
      top: 50%;
      border-radius: 50%;
      transform: translateY(-50%);
    }
  `,
  showMoreTrigger: css`
    position: absolute;
    right: -32px;
    width: 32px;
    top: -10px;
    bottom: 0;
    color: #ebebeb;
    background: #222222;
    border: none;
    padding: 0;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;

    &:after {
      content: '';
      position: absolute;
      width: 5px;
      height: 5px;
      left: 50%;
      bottom: 14px;
      border-left: 1px solid currentColor;
      border-bottom: 1px solid currentColor;
      transform: translateX(-50%) rotate(-45deg);
      transition: transform 0.1s;
    }

    &:hover {
      color: #fff;
    }

    &.__opened {
      &:after {
        transform: translateX(-50%) rotate(135deg);
      }
    }
  `,
};

type StyledValuesListProps = {
  expanded: boolean;
};
export const StyledValuesList = styled.ul<StyledValuesListProps>`
  list-style-type: none;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  padding: 0;
  margin: 0;
  overflow: hidden;
  transition: max-height 0.3s;
  max-height: ${props => (props.expanded ? '180px' : '22px')};

  &.__vertical {
    padding-left: 16px;
    padding-right: 16px;
    flex-direction: column;
    align-items: flex-start;

    .${styles.valuesListItem} {
      margin-right: 0;
    }
  }
`;
