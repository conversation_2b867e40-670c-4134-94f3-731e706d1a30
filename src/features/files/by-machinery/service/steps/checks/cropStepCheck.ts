import RootState from 'types/rootState';

import {
  getFieldsDataLayerValue,
  selectCropsProperties,
} from 'features/files/by-machinery/redux/selectors';

import { StepStatus } from '../types';
import { defaultCheck } from './defaultCheck';

export const cropStepCheck = (state: RootState): StepStatus => {
  if (defaultCheck(state) === StepStatus.hiding) {
    return StepStatus.hiding;
  }

  const cropsProperty = selectCropsProperties(state);
  const dataValues = getFieldsDataLayerValue(state);
  if (cropsProperty.some(v => !v.is_values_imported)) {
    return StepStatus.loading;
  }

  if (cropsProperty.length === 0 || dataValues.length === 0) {
    return StepStatus.hiding;
  }

  return StepStatus.available;
};
