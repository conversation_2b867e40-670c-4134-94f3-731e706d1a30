import { channel } from 'redux-saga';
import { select, put, takeEvery } from 'redux-saga/effects';

import { actions } from 'features/fields/BulkUpload/redux/actions';
import { selectors } from 'features/fields/BulkUpload/redux/selectors';

import {
  BulkUploadStep,
  FileBundle,
  FileBundleStatus,
} from 'features/fields/BulkUpload/types';

import { unpackArchives, UnpackingProgressData } from 'utils/import-fields';
import { Action } from 'redux';
import { composeFileBundles, validateBundle } from './utils';
import { getMessageForSuccess } from './toasts/getMessageForSuccess';
import { getMessageForErrors } from './toasts/getMessageForErrors';

const progressChannel = channel();

const handleProcessingProgress = ({
  progress,
  status,
}: UnpackingProgressData) => {
  progressChannel.put(actions.setProcessingProgress(progress));

  if (status) {
    progressChannel.put(actions.updateProcessingStatus(status));
  }
};

export function* watchProgressChannel() {
  yield takeEvery(
    progressChannel,
    function* (action: Action<{ progress: number }>) {
      yield put(action);
    },
  );
}

export function* processFilesSaga() {
  yield takeEvery(actions.processFiles.key, function* () {
    yield put(actions.setCurrentStep(BulkUploadStep.PROCESSING));

    const selectedFiles: File[] = yield select(selectors.getValidFiles);

    let unpackedFiles: File[] = [];

    try {
      unpackedFiles = yield unpackArchives(
        selectedFiles,
        handleProcessingProgress,
      );

      const message = getMessageForSuccess({
        titleKey: 'fields.bulk_upload.toasts.processing.finished',
        linkKey: 'fields.bulk_upload.toasts.processing.continue',
        ttl: Infinity,
      });

      if (message) {
        yield put(message);
      }
    } catch (error) {
      handleProcessingProgress({ progress: 100 });

      const message = getMessageForErrors({
        titleKey: 'fields.bulk_upload.toasts.processing.failed.title',
        contentKey: 'fields.bulk_upload.toasts.processing.failed.description',
        ttl: Infinity,
        showOnBulkUploadPage: true,
      });

      if (message) {
        yield put(message);
      }

      yield put(actions.setCurrentStep(BulkUploadStep.FILE_SELECTION));

      return;
    }

    const bundles = composeFileBundles(unpackedFiles);

    const fileBundles: FileBundle[] = Array.from(bundles.entries()).map(
      ([name, files]) => {
        const errors = validateBundle(files).errors;
        const status = FileBundleStatus.PROCESSED;

        return {
          name,
          status,
          files,
          errors,
        };
      },
    );

    yield put(actions.setFileBundles(fileBundles));

    const validBundles: FileBundle[] = yield select(
      selectors.getValidFileBundles,
    );

    if (validBundles.length === 0) {
      const message = getMessageForErrors({
        titleKey: 'fields.bulk_upload.toasts.processing.failed.title',
        contentKey: 'fields.bulk_upload.toasts.processing.failed.description',
        ttl: Infinity,
      });

      if (message) {
        yield put(message);
      }
    }

    yield put(actions.setCurrentStep(BulkUploadStep.CHECK_DATA));
  });
}
