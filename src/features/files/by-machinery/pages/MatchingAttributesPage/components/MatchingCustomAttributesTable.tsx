import React, { useEffect, useMemo, useState, FC, useCallback } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';

import { useTranslate } from 'utils/use-translate';

import {
  ApiDataFieldLayer,
  DataTypesKey,
  TasksByMachineryMeta,
} from 'features/files/by-machinery/types';
import { CustomAttributeGroup } from './table/CustomAttributeGroup';
import { AddAttributeModal } from './table/AddAttributeModal';
import { createLayersLinks } from 'features/files/by-machinery/pages/MatchingAttributesPage/utils/createLayersLinks';
import LinkButton from 'components/LinkButton';

import {
  useDataStore,
  useSetAttribute,
} from 'features/files/by-machinery/pages/MatchingAttributesPage/service';
import logEvent from 'sagas/global/logEvent';

import { MatchingTableStyle, addAttributeBtn } from './table/styles';

export type MatchingCustomAttributesTableProps = {
  dataLayers: ApiDataFieldLayer[];
  meta: TasksByMachineryMeta;
  dataType: DataTypesKey;
};

export type CustomAttributeType = {
  name: string;
  value: string;
  units: string[];
};

type CunstomAttributesFormData = {
  attributes: CustomAttributeType[];
};

export const MatchingCustomAttributesTable: FC<
  MatchingCustomAttributesTableProps
> = ({ dataLayers, meta, dataType }) => {
  const { t } = useTranslate();
  const [modal, setModal] = useState<string | null>(null);
  const [index, setIndex] = useState<number | null>(null);

  const layersLinks = createLayersLinks(dataLayers);

  // Soil-sampling data-layers has only one layer
  const customProperties = useMemo(() => {
    return dataLayers[0]?.properties.filter(
      property => property.is_custom_target_name,
    );
  }, [dataLayers]);

  const dataStore = useDataStore({
    layersLinks,
    dataLayers,
    meta,
  });

  const onSetAttributeValue = useSetAttribute(dataStore);

  const { control, setValue, watch } = useForm<CunstomAttributesFormData>({
    mode: 'onBlur',
    defaultValues: {
      attributes: [],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'attributes',
  });

  const watchedFields = watch('attributes');

  const onAddAttribute = (name: string) => {
    append({ name, value: null });
  };

  const onEditAttribute = useCallback(
    (name: string, index: number) => {
      const newAttrs = watchedFields.map((item, i) => {
        if (i === index) {
          if (item.value) {
            onSetAttributeValue(item.value, name, dataLayers[0]!.uuid);
          }

          return { ...item, name };
        }
        return item;
      });

      setValue(`attributes`, newAttrs);
    },
    [watchedFields, setValue, dataLayers, onSetAttributeValue],
  );

  const onSetSelectValue = (index: number, value: string | null) => {
    const newAttrs = watchedFields.map((item, i) => {
      if (i === index) {
        return { ...item, value };
      }
      return item;
    });

    setValue(`attributes`, newAttrs);
  };

  const onRemoveAttribute = useCallback(
    (index: number) => {
      const item = watchedFields[index];
      const customPropertyToRemove = customProperties?.find(
        property => property.target_name === item?.name,
      );

      if (customPropertyToRemove) {
        onSetAttributeValue(
          null,
          customPropertyToRemove.target_name!,
          dataLayers[0]!.uuid,
        );
      }
      remove(index);
    },
    [watchedFields, customProperties, dataLayers, onSetAttributeValue, remove],
  );

  useEffect(() => {
    if (customProperties && customProperties.length > 0) {
      const newAttrs = customProperties.map(property => ({
        name: property.target_name,
        value: property.name || '',
      }));
      setValue(`attributes`, newAttrs);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      {fields.length > 0 && (
        <MatchingTableStyle
          equalColumns={dataType !== DataTypesKey.soilSampling}
          columns={dataLayers.length}
        >
          <CustomAttributeGroup
            fields={watchedFields}
            onShowEditAttributeNameModal={(index: number) => {
              setModal('edit-attribute');
              setIndex(index);
            }}
            meta={meta}
            onSetSelectValue={onSetSelectValue}
            onRemoveAttribute={onRemoveAttribute}
            onSetAttributeValue={onSetAttributeValue}
            dataLayers={dataLayers}
          />
        </MatchingTableStyle>
      )}

      <LinkButton
        className={addAttributeBtn}
        onClick={() => {
          logEvent('web_soil_sampling_vis_custom');
          setModal('add-attribute');
        }}
      >
        {t('import.by-machinery.modal.attribute.add')}
      </LinkButton>

      <AddAttributeModal
        isVisible={modal === 'add-attribute'}
        mode='add'
        attributes={watchedFields}
        defaultValue={''}
        onAddAttribute={onAddAttribute}
        onClose={() => setModal(null)}
      />

      <AddAttributeModal
        isVisible={modal === 'edit-attribute'}
        mode='edit'
        index={index!}
        attributes={watchedFields}
        defaultValue={
          index !== null && watchedFields.length > 0
            ? watchedFields[index]?.name
            : ''
        }
        onEditAttribute={onEditAttribute}
        onClose={() => setModal(null)}
      />
    </>
  );
};
