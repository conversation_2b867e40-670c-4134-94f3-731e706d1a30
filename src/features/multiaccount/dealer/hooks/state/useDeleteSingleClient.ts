import { useState } from 'react';
import { Modals } from './types';
import { MultiAccountUser } from 'types/multiaccount';

export type DeleteSingleClientManager = {
  deleteSingleClientId: MultiAccountUser['uuid'] | null;
  showDeleteSingleModal: (uuid: MultiAccountUser['uuid']) => void;
  closeDeleteSingleModal: () => void;
};

export const useDeleteSingleClient = (
  setVisibleModal: (type: Modals) => void,
): DeleteSingleClientManager => {
  const [deleteSingleClientId, setDeleteSingleClientId] = useState<
    MultiAccountUser['uuid'] | null
  >(null);

  const showDeleteSingleModal = (userId: MultiAccountUser['uuid']) => {
    setVisibleModal('delete-one-client');
    setDeleteSingleClientId(userId);
  };

  const closeDeleteSingleModal = () => {
    setVisibleModal(null);
  };

  return {
    deleteSingleClientId,
    showDeleteSingleModal,
    closeDeleteSingleModal,
  };
};
