import React, { CSSProperties, VFC } from 'react';

import {
  StyledCollapseTrigger,
  StyledTopLeftCell,
  styles,
} from '../CropRotationView.style';
import { SeasonType } from 'types/rootState';
import { SeasonData } from '../types';
import Icon from 'components/Icon';
import SeasonsHeaderCell from './SeasonsHeaderCell';
import { TranslateFunction } from 'utils/use-translate';

type SeasonsRowProps = {
  seasons: SeasonType[];
  seasonsData: SeasonData[];
  columnIndex: number;
  rowIndex: number;
  cropsHidden: boolean;

  setShowSeasonPopup: () => void;
  toggleCrops: () => void;
  style: CSSProperties;
  t: TranslateFunction;
};

const SeasonsRow: VFC<SeasonsRowProps> = ({
  seasons,
  seasonsData,
  columnIndex,
  rowIndex,
  style,
  t,
  cropsHidden,
  toggleCrops,
  setShowSeasonPopup,
}) => {
  const season: SeasonType = seasons[columnIndex - 1] as SeasonType;
  const seasonData: SeasonData = seasonsData[columnIndex - 1] as SeasonData;

  if (columnIndex === 0 && rowIndex === 0) {
    return (
      <StyledTopLeftCell style={style}>
        <h1 className={styles.cropRotationTitle}>{t('croprotation.title')}</h1>
        <div className={styles.collapseTrigger}>
          <StyledCollapseTrigger
            state={cropsHidden ? 'collapsed' : 'expanded'}
            onClick={toggleCrops}
          >
            <span className={'collapse-icon'}>
              <Icon name={'arrow-down-small'} />
            </span>
            {cropsHidden
              ? t('croprotation.show_crops')
              : t('croprotation.hide_crops')}
          </StyledCollapseTrigger>
        </div>
      </StyledTopLeftCell>
    );
  }
  return (
    <SeasonsHeaderCell
      style={style}
      columnIndex={columnIndex}
      season={season}
      seasonData={seasonData}
      t={t}
      setShowSeasonPopup={setShowSeasonPopup}
    />
  );
};

export default SeasonsRow;
