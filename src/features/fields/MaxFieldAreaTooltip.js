import React, { useState, useRef } from 'react';
import { css } from 'linaria';

import Icon from 'components/Icon';
import { PopoverDeprecated } from 'components/ui/Popover';

import {
  SimpleTooltip,
  SimpleTooltipArrow,
} from 'components/ui/Tooltip/SimpleTooltip/SimpleTooltip';

import { MaxFieldArea } from 'sagas/local/edit-field-geometry';

const CloseDelay = 50;

const icon = css`
  display: inline-block;
  vertical-align: top;
  margin-top: 2px;
  margin-bottom: -2px;
  margin-left: 5px;
  cursor: pointer;
`;

const tooltip = css`
  padding-bottom: 10px;
`;

const MaxFieldAreaTooltip = ({ formatUnit, t, history }) => {
  const [hovered, setHovered] = useState(false);
  const timer = useRef(null);

  const onMouseEnter = () => {
    clearTimeout(timer.current);
    setHovered(true);
  };
  const onMouseLeave = () => {
    clearTimeout(timer.current);
    timer.current = setTimeout(setHovered, CloseDelay, false);
  };

  return (
    <PopoverDeprecated
      active={hovered}
      offset={[10, 0]}
      align={'middle-right'}
      transitionVariant='legacy'
      renderTrigger={({ ref, ...otherProps }) => (
        <span
          className={icon}
          onMouseEnter={onMouseEnter}
          onMouseLeave={onMouseLeave}
          {...otherProps}
        >
          <Icon innerRef={ref} name='attention' />
        </span>
      )}
      renderPopover={({ style, ...props }) => (
        <SimpleTooltip
          className={tooltip}
          style={{ maxWidth: 270, ...style }}
          {...props}
          onMouseEnter={onMouseEnter}
          onMouseLeave={onMouseLeave}
          arrow={SimpleTooltipArrow.LEFT}
          onClick={event => {
            // Don't let clicks propagate to FieldListItem
            event.stopPropagation();
          }}
        >
          <p>
            {t('toasts.fields.too_large', {
              area: formatUnit(MaxFieldArea, 'm2', 'area'),
            })}
          </p>
        </SimpleTooltip>
      )}
    />
  );
};

export default MaxFieldAreaTooltip;
