import { cx } from 'linaria';
import React from 'react';
import {
  tableCell,
  tableCellActive,
  tableCellGroupStartHeader,
  tableCellFixedLast,
  tableCellFixedHeader,
  tableCellGroupStart,
  tableHeaderCell,
  tableHeaderCellContent,
  tableHeaderCellSortIcon,
  tableHeaderCellSortIconDesc,
} from '../TableRow.style';
import Icon from 'components/Icon';
import { SortDirections } from 'constants/sortDirections';
import { ColumnInfo } from '../types';

type TableHeaderCellProps = {
  onCellClick?: (key: string) => void;
  columnInfo: ColumnInfo;
  children: React.ReactNode;
};

export const TableHeaderCell = ({
  columnInfo,
  onCellClick,
  children,
}: TableHeaderCellProps) => {
  const width = columnInfo.styledInfo?.width;
  const rightBorder = columnInfo.styledInfo?.rightBorder;
  const columnsGroupName = columnInfo.columnsGroupName;

  return (
    <td
      key={columnInfo.key}
      className={cx(
        tableCell,
        tableHeaderCell,
        width && tableCellFixedHeader,
        rightBorder && tableCellFixedLast,
        columnsGroupName && tableCellGroupStartHeader,
        columnsGroupName && tableCellGroupStart,
        onCellClick && tableCellActive,
      )}
      onClick={() => onCellClick?.(columnInfo.key)}
      style={{
        ...(width
          ? {
              width: `${width}px`,
              minWidth: `${width}px`,
              maxWidth: `${width}px`,
            }
          : {}),
      }}
    >
      <div className={tableHeaderCellContent}>
        {children}
        {columnInfo.sorted && (
          <Icon
            name='sortArrow'
            className={cx(
              tableHeaderCellSortIcon,
              columnInfo.sorted === SortDirections.DESCENDING &&
                tableHeaderCellSortIconDesc,
            )}
          />
        )}
      </div>
    </td>
  );
};
