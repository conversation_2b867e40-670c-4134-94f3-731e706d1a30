import React from 'react';
import { connect } from 'react-redux';

import Button from 'components/ui/Button/Button';
import MissingFieldRedirector from './MissingFieldRedirector';
import DashboardWeatherSection from './DashboardWeatherSection';

import fields from 'modules/fields';

import { getViewportHash } from 'utils/get-root-scouting-url';
import DashboardNotesSection from 'features/fields/DashboardNotesSection';
import DashboardFieldSection from 'features/fields/DashboardFieldSection';

const DashboardPanel = ({ field, match, history, location }) => {
  if (!field) {
    return null;
  }
  return (
    <div className='map-dashboard'>
      <MissingFieldRedirector fieldId={field.id} />
      <Button
        className='map-dashboard__close'
        onClick={() => {
          const viewport = getViewportHash(history.location.pathname);
          history.replace(`/${viewport}/fields`);
        }}
      />
      <div className='map-dashboard__main'>
        <DashboardWeatherSection
          fieldId={match.params.fieldId}
          location={location}
        />
        <div className='map-dashboard__hr' />
        <DashboardNotesSection
          fieldId={match.params.fieldId}
          location={location}
        />
        <div className='map-dashboard__hr' />
      </div>
      <DashboardFieldSection
        fieldId={match.params.fieldId}
        location={location}
        history={history}
      />
    </div>
  );
};

const mapStateToProps = (state, ownProps) => ({
  field: fields.selectors.getByID(state, ownProps.match.params.fieldId),
});

export default connect(mapStateToProps)(DashboardPanel);
