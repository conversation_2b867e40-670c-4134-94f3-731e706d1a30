import React from 'react';
import {
  SimpleTooltip,
  SimpleTooltipArrow,
  SimpleTooltipTheme,
} from './SimpleTooltip';

const TOOLTIP_POSITION = 32;

const getTooltipStyle = (top: number, left: number): React.CSSProperties => {
  return {
    clip: 'auto',
    position: 'fixed',
    top,
    left,
  };
};

const TOOLTIP_STYLE: React.CSSProperties = getTooltipStyle(
  TOOLTIP_POSITION,
  TOOLTIP_POSITION,
);

export default {
  title: 'ui/Tooltip/SimpleTooltip',
  component: SimpleTooltip,
};

export const Simple = () => {
  return <SimpleTooltip style={TOOLTIP_STYLE}>Tooltip Title</SimpleTooltip>;
};

export const Initial = () => {
  return (
    <SimpleTooltip style={TOOLTIP_STYLE} className={'__initial'}>
      Tooltip Title
    </SimpleTooltip>
  );
};

export const Normal = () => {
  return (
    <SimpleTooltip style={TOOLTIP_STYLE} className={'__normal'}>
      Tooltip Title
    </SimpleTooltip>
  );
};

export const ArrowTop = () => {
  return (
    <SimpleTooltip style={TOOLTIP_STYLE} arrow={SimpleTooltipArrow.TOP}>
      Tooltip Title
    </SimpleTooltip>
  );
};

export const ArrowBottom = () => {
  return (
    <SimpleTooltip style={TOOLTIP_STYLE} arrow={SimpleTooltipArrow.BOTTOM}>
      Tooltip Title
    </SimpleTooltip>
  );
};

export const ArrowLeft = () => {
  return (
    <SimpleTooltip style={TOOLTIP_STYLE} arrow={SimpleTooltipArrow.LEFT}>
      Tooltip Title
    </SimpleTooltip>
  );
};

export const ArrowRight = () => {
  return (
    <SimpleTooltip style={TOOLTIP_STYLE} arrow={SimpleTooltipArrow.RIGHT}>
      Tooltip Title
    </SimpleTooltip>
  );
};

//Dark theme
export const DarkTheme = () => {
  return (
    <SimpleTooltip style={TOOLTIP_STYLE} theme={SimpleTooltipTheme.DARK}>
      Tooltip Title
    </SimpleTooltip>
  );
};

export const DarkThemeArrowTop = () => {
  return (
    <SimpleTooltip
      style={TOOLTIP_STYLE}
      theme={SimpleTooltipTheme.DARK}
      arrow={SimpleTooltipArrow.TOP}
    >
      Tooltip Title
    </SimpleTooltip>
  );
};

export const DarkThemeArrowBottom = () => {
  return (
    <SimpleTooltip
      style={TOOLTIP_STYLE}
      theme={SimpleTooltipTheme.DARK}
      arrow={SimpleTooltipArrow.BOTTOM}
    >
      Tooltip Title
    </SimpleTooltip>
  );
};

export const DarkThemeArrowLeft = () => {
  return (
    <SimpleTooltip
      style={TOOLTIP_STYLE}
      theme={SimpleTooltipTheme.DARK}
      arrow={SimpleTooltipArrow.LEFT}
    >
      Tooltip Title
    </SimpleTooltip>
  );
};

export const DarkThemeArrowRight = () => {
  return (
    <SimpleTooltip
      style={TOOLTIP_STYLE}
      theme={SimpleTooltipTheme.DARK}
      arrow={SimpleTooltipArrow.RIGHT}
    >
      Tooltip Title
    </SimpleTooltip>
  );
};

//Onboarding theme
export const OnboardingTheme = () => {
  return (
    <SimpleTooltip style={TOOLTIP_STYLE} theme={SimpleTooltipTheme.ONBOARDING}>
      Tooltip Title
    </SimpleTooltip>
  );
};

export const OnboardingThemeArrowTop = () => {
  return (
    <SimpleTooltip
      style={TOOLTIP_STYLE}
      theme={SimpleTooltipTheme.ONBOARDING}
      arrow={SimpleTooltipArrow.TOP}
    >
      Tooltip Title
    </SimpleTooltip>
  );
};

export const OnboardingThemeArrowBottom = () => {
  return (
    <SimpleTooltip
      style={TOOLTIP_STYLE}
      theme={SimpleTooltipTheme.ONBOARDING}
      arrow={SimpleTooltipArrow.BOTTOM}
    >
      Tooltip Title
    </SimpleTooltip>
  );
};

export const OnboardingThemeArrowLeft = () => {
  return (
    <SimpleTooltip
      style={TOOLTIP_STYLE}
      theme={SimpleTooltipTheme.ONBOARDING}
      arrow={SimpleTooltipArrow.LEFT}
    >
      Tooltip Title
    </SimpleTooltip>
  );
};

export const OnboardingThemeArrowRight = () => {
  return (
    <SimpleTooltip
      style={TOOLTIP_STYLE}
      theme={SimpleTooltipTheme.ONBOARDING}
      arrow={SimpleTooltipArrow.RIGHT}
    >
      Tooltip Title
    </SimpleTooltip>
  );
};

export const Small = () => {
  return (
    <SimpleTooltip small style={TOOLTIP_STYLE}>
      Tooltip Title
    </SimpleTooltip>
  );
};

export const SmallArrowTop = () => {
  return (
    <SimpleTooltip small style={TOOLTIP_STYLE} arrow={SimpleTooltipArrow.TOP}>
      Tooltip Title
    </SimpleTooltip>
  );
};

export const SmallArrowBottom = () => {
  return (
    <SimpleTooltip
      small
      style={TOOLTIP_STYLE}
      arrow={SimpleTooltipArrow.BOTTOM}
    >
      Tooltip Title
    </SimpleTooltip>
  );
};

export const SmallArrowLeft = () => {
  return (
    <SimpleTooltip small style={TOOLTIP_STYLE} arrow={SimpleTooltipArrow.LEFT}>
      Tooltip Title
    </SimpleTooltip>
  );
};

export const SmallArrowRight = () => {
  return (
    <SimpleTooltip small style={TOOLTIP_STYLE} arrow={SimpleTooltipArrow.RIGHT}>
      Tooltip Title
    </SimpleTooltip>
  );
};

//Small Dark
export const SmallDark = () => {
  return (
    <SimpleTooltip small style={TOOLTIP_STYLE} theme={SimpleTooltipTheme.DARK}>
      Tooltip Title
    </SimpleTooltip>
  );
};

export const SmallDarkArrowTop = () => {
  return (
    <SimpleTooltip
      small
      style={TOOLTIP_STYLE}
      arrow={SimpleTooltipArrow.TOP}
      theme={SimpleTooltipTheme.DARK}
    >
      Tooltip Title
    </SimpleTooltip>
  );
};

export const SmallDarkArrowBottom = () => {
  return (
    <SimpleTooltip
      small
      style={TOOLTIP_STYLE}
      arrow={SimpleTooltipArrow.BOTTOM}
      theme={SimpleTooltipTheme.DARK}
    >
      Tooltip Title
    </SimpleTooltip>
  );
};

export const SmallDarkArrowLeft = () => {
  return (
    <SimpleTooltip
      small
      style={TOOLTIP_STYLE}
      arrow={SimpleTooltipArrow.LEFT}
      theme={SimpleTooltipTheme.DARK}
    >
      Tooltip Title
    </SimpleTooltip>
  );
};

export const SmallDarkArrowRight = () => {
  return (
    <SimpleTooltip
      small
      style={TOOLTIP_STYLE}
      arrow={SimpleTooltipArrow.RIGHT}
      theme={SimpleTooltipTheme.DARK}
    >
      Tooltip Title
    </SimpleTooltip>
  );
};

//Small Onboarding
export const SmallOnboarding = () => {
  return (
    <SimpleTooltip
      small
      style={TOOLTIP_STYLE}
      theme={SimpleTooltipTheme.ONBOARDING}
    >
      Tooltip Title
    </SimpleTooltip>
  );
};

export const SmallOnboardingArrowTop = () => {
  return (
    <SimpleTooltip
      small
      style={TOOLTIP_STYLE}
      arrow={SimpleTooltipArrow.TOP}
      theme={SimpleTooltipTheme.ONBOARDING}
    >
      Tooltip Title
    </SimpleTooltip>
  );
};

export const SmallOnboardingArrowBottom = () => {
  return (
    <SimpleTooltip
      small
      style={TOOLTIP_STYLE}
      arrow={SimpleTooltipArrow.BOTTOM}
      theme={SimpleTooltipTheme.ONBOARDING}
    >
      Tooltip Title
    </SimpleTooltip>
  );
};

export const SmallOnboardingArrowLeft = () => {
  return (
    <SimpleTooltip
      small
      style={TOOLTIP_STYLE}
      arrow={SimpleTooltipArrow.LEFT}
      theme={SimpleTooltipTheme.ONBOARDING}
    >
      Tooltip Title
    </SimpleTooltip>
  );
};

export const SmallOnboardingArrowRight = () => {
  return (
    <SimpleTooltip
      small
      style={TOOLTIP_STYLE}
      arrow={SimpleTooltipArrow.RIGHT}
      theme={SimpleTooltipTheme.ONBOARDING}
    >
      Tooltip Title
    </SimpleTooltip>
  );
};

export const SimpleDarkOnBoarding = () => {
  return (
    <>
      <SimpleTooltip style={getTooltipStyle(32, 32)}>
        Tooltip Title
      </SimpleTooltip>
      <SimpleTooltip
        style={getTooltipStyle(32 * 3, 32)}
        theme={SimpleTooltipTheme.DARK}
      >
        Tooltip Title
      </SimpleTooltip>
      <SimpleTooltip
        style={getTooltipStyle(32 * 5, 32)}
        theme={SimpleTooltipTheme.ONBOARDING}
      >
        Tooltip Title
      </SimpleTooltip>
    </>
  );
};

export const SmallDarkOnBoarding = () => {
  return (
    <>
      <SimpleTooltip small style={getTooltipStyle(32, 32)}>
        Tooltip Title
      </SimpleTooltip>
      <SimpleTooltip
        small
        style={getTooltipStyle(32 * 3, 32)}
        theme={SimpleTooltipTheme.DARK}
      >
        Tooltip Title
      </SimpleTooltip>
      <SimpleTooltip
        small
        style={getTooltipStyle(32 * 5, 32)}
        theme={SimpleTooltipTheme.ONBOARDING}
      >
        Tooltip Title
      </SimpleTooltip>
    </>
  );
};
