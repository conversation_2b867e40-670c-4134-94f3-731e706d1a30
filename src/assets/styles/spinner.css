.b-spinner > g {
  display: inline-block;
  vertical-align: middle;

  animation-duration: 0.7s;
  animation-fill-mode: both;
  animation-timing-function: linear;
  animation-iteration-count: infinite;

  animation-name: rotateSpinner;
}

@keyframes rotateSpinner {
  from {
    transform-origin: center;
    transform: rotate(0deg);
  }

  to {
    transform-origin: center;
    transform: rotate(-360deg);
  }
}
