import { UnitSystemsType } from '../types/units';

export const UnitSystems: UnitSystemsType = {
  metric: {
    area: {
      unit: 'ha',
      unitPrecision: 1,
    },
    luminance: {
      unit: 'lx',
    },
    voltage: {
      unit: 'm',
      hideUnit: true,
      unitPrecision: 2,
    },
    humidity: {
      unit: 'm',
      postfix: '%',
      hideUnit: true,
    },
    temp: {
      unit: 'C',
      postfix: '°',
      plusSign: true,
      hideUnit: true,
    },
    speed: {
      unit: 'm/s',
    },
    pressure: {
      // @ts-ignore
      unit: 'mmHg',
    },
    precip: {
      unit: 'mm',
    },
    mass: {
      unit: 'kg',
    },
    yieldMass: {
      unit: 'mt',
    },
    yield: [{ unit: 't/ha' }, { unit: 'c/ha' }, { unit: 'kg/ha' }],
    ndvi: {
      unit: 'm',
      unitPrecision: 2,
      hideUnit: true,
    },
    distance: {
      unit: 'km',
    },
    ruler: {
      unit: 'm',
    },
    sensorPrecip: {
      unit: 'mm',
      unitPrecision: 1,
    },
    sensorDepth: {
      unit: 'cm',
    },
    unknown: {
      unit: 'm',
      hideUnit: true,
    },
  },
  imperial: {
    area: {
      unit: 'ac',
      unitPrecision: 1,
    },
    luminance: {
      unit: 'lx',
    },
    humidity: {
      unit: 'm',
      postfix: '%',
      hideUnit: true,
    },
    temp: {
      unit: 'F',
      postfix: '°',
      plusSign: true,
      hideUnit: true,
    },
    speed: {
      unit: 'mph',
    },
    pressure: {
      // @ts-ignore
      unit: 'mbar',
    },
    precip: {
      unit: 'in',
    },
    mass: {
      unit: 'lb',
    },
    yieldMass: {
      unit: 'lb',
    },
    yield: [{ unit: 'lb/ac' }, { unit: 'bu/ac' }, { unit: 't/ac' }],
    ndvi: {
      unit: 'm',
      unitPrecision: 2,
      hideUnit: true,
    },
    distance: {
      unit: 'mi',
    },
    ruler: {
      // @ts-ignore
      unit: 'yd',
    },
    voltage: {
      unit: 'm',
      hideUnit: true,
      unitPrecision: 2,
    },
    sensorPrecip: {
      unit: 'in',
      unitPrecision: 1,
    },
    sensorDepth: {
      unit: 'in',
    },
    unknown: {
      unit: 'm',
      hideUnit: true,
    },
  },
  hybrid: {
    area: {
      unit: 'ac',
      unitPrecision: 1,
    },
    luminance: {
      unit: 'lx',
    },
    humidity: {
      unit: 'm',
      postfix: '%',
      hideUnit: true,
    },
    temp: {
      unit: 'C',
      postfix: '°',
      plusSign: true,
      hideUnit: true,
    },
    speed: {
      unit: 'km/h',
    },
    pressure: {
      unit: 'mbar',
    },
    precip: {
      unit: 'mm',
    },
    mass: {
      unit: 'lb',
    },
    yieldMass: {
      unit: 'lb',
    },
    yield: [{ unit: 'lb/ac' }, { unit: 'bu/ac' }, { unit: 't/ac' }],
    ndvi: {
      unit: 'm',
      unitPrecision: 2,
      hideUnit: true,
    },
    distance: {
      unit: 'mi',
    },
    ruler: {
      unit: 'ft',
    },
    voltage: {
      unit: 'm',
      hideUnit: true,
      unitPrecision: 2,
    },
    sensorPrecip: {
      unit: 'in',
      unitPrecision: 1,
    },
    sensorDepth: {
      unit: 'in',
    },
    unknown: {
      unit: 'm',
      hideUnit: true,
    },
  },
};
