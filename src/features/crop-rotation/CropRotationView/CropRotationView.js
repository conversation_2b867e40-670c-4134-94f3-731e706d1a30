import React, {
  useMemo,
  useState,
  useCallback,
  useEffect,
  useRef,
} from 'react';
import { AutoSizer, MultiGrid, ScrollSync } from 'react-virtualized';
import { MemoryRouter } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import sortBy from 'lodash/sortBy';
import orderBy from 'lodash/orderBy';
import fill from 'lodash/fill';
import qs from 'qs';

// @ts-ignore
import { cropRotationSaga } from 'sagas/local/crop-rotation';
import logEvent from 'sagas/global/logEvent';

import config from 'config';

import auth from 'modules/auth';
import fields from 'modules/fields';
import settings from 'modules/settings';
import seasonsModule from 'modules/seasons';
import entities from 'modules/entities';
import multiaccount from 'modules/multiaccount';

import { getViewportHash } from 'utils/get-root-scouting-url';
import { formatTitle as formatFieldTitle } from 'utils/fields-formatters';
import useUnmountEffect from 'utils/use-unmount-effect';
import { round } from 'utils/format-number';
import { useTranslate } from 'utils/use-translate';

import EditFieldGeometryPopupV2 from 'features/fields/EditFieldGeometryPopupV2';
import CreateFieldDialogDropDown from 'features/fields/CreateFieldDialogDropDown/CreateFieldDialogDropDown';
import Icon from 'components/Icon';
import Button from 'components/ui/Button/Button';
import Saga from 'components/saga/Saga';
import AnimatedRoute from 'components/router/AnimatedRoute';
import EnterExitTransition from 'components/EnterExitTransition';
import AutoFillPopup from 'features/crop-rotation/AutoFillPopup';
import EditSeasonPopup from 'features/seasons/EditSeasonPopup';
import FieldImage from 'features/crop-rotation/FieldImage';
import RemoveEntityPopup from '../RemoveEntityPopup';
import LinkedNotesWarningPopup from 'features/fields/LinkedNotesWarningPopup';

import {
  StyledFieldsColumnCell,
  StyledCropsTableCell,
  StyledCropColorBar,
  StyledFieldsHeaderCell,
  StyledAutoRotateCell,
  StyledSortButton,
  styles,
} from './CropRotationView.style';

import BodyClassName from 'components/BodyClassName';
import { SkeletonGrid } from '../SkeletonGrid';
import {
  AUTOROTATE_ROW_HEIGHT,
  COLUMN_WIDTH,
  CROPS_FIRST_ROW_HEIGHT,
  CROPS_ROW_HEIGHT,
  FIELDS_FIRST_ROW_HEIGHT,
  FIELDS_ROW_HEIGHT,
  FIRST_COLUMN_WIDTH,
  rowTypes,
  SEASONS_ROW_HEIGHT,
} from 'constants/crop-rotation';
import FieldsCropCell from './components/FieldsCropCell';
import fieldSeason from 'modules/fieldSeason';
import SeasonsHeaderCell from './components/SeasonsHeaderCell';
import SeasonsRow from './components/SeasonsRow';
import CropsHeaderCell from './components/CropsHeaderCell';
import CropsTableCell from './components/CropsTableCell';
import { SortDirections } from 'constants/sortDirections';
import { FEATURES, FeatureDisabledTooltip } from 'features/permissions';
import { PopoverDeprecatedAlign } from 'components/ui/Popover';
import { CropRotationStickyHeader } from './components/CropRotationStickyHeader';

const getKeysArray = (keys, counts) => {
  let arr = [];
  counts.reduce((acc, cur, index) => {
    arr.push(...fill(Array(cur), { key: keys[index], startOffset: acc }));
    acc += cur;
    return acc;
  }, 0);
  return arr;
};

const collator = new Intl.Collator(undefined, {
  numeric: true,
  sensitivity: 'base',
});

const CropRotationView = ({ match, history }) => {
  const dispatch = useDispatch();
  const [showSeasonPopup, setShowSeasonPopup] = useState(false);
  const [keysArray, setKeysArray] = useState([]);
  const [cropsHidden, setCropsHidden] = useState(true);
  const [showAutoFillPopup, setShowAutoFillPopup] = useState(false);
  const [showRemoveEntityPopup, setShowRemoveEntityPopup] = useState(false);
  const [showRemoveFieldWithNotesPopup, setShowRemoveFieldWithNotesPopup] =
    useState(false);
  const [sortConfig, setSortConfig] = useState(null);

  const fieldUsers = useSelector(state =>
    entities.selectors.getAll(state, 'field'),
  );

  const fieldSeasons = useSelector(state =>
    entities.selectors.getAll(state, 'fieldSeason'),
  );

  const fieldCrops = useSelector(state =>
    entities.selectors.getAll(state, 'fieldCrop'),
  );
  const allSeasons = useSelector(state =>
    entities.selectors.getAll(state, 'season'),
  );
  const recommendations = useSelector(state =>
    entities.selectors.getAll(state, 'recommendation'),
  );

  const notes = useSelector(state => entities.selectors.getAll(state, 'note'));

  const currentSeason = useSelector(seasonsModule.selectors.getCurrentSeasonV2);
  const cropColors = useSelector(fieldSeason.selectors.getCropColors);
  const fetchRequest = useSelector(state =>
    entities.selectors.getRequestStatus(state, 'fetch-crop-rotation'),
  );
  const formatUnit = useSelector(settings.selectors.getUnitFormatter);
  const token = useSelector(auth.selectors.getToken);
  const currentMembership = useSelector(
    multiaccount.selectors.getCurrentMembership,
  );
  const workspaceUuid = currentMembership?.workspace_id;

  const gridRef = useRef();

  const { t } = useTranslate();

  const viewport = getViewportHash(history.location.pathname);

  const openEditGeometry = id => history.push(`${match.url}/edit-field/${id}`);

  const onChange = useCallback(
    option => {
      if (option === 'upload') {
        dispatch(fields.actions.setImportStatus({ state: 'resolved' }));
      }

      history.push({
        pathname: `/${viewport}/fields/add/${option}`,
        state: { from: `/${viewport}/crop-rotation` },
      });
    },
    [viewport, history, dispatch],
  );

  useEffect(() => {
    const { Intercom = () => {} } = window;
    Intercom('update', { hide_default_launcher: true });
    return () => {
      Intercom('update', { hide_default_launcher: false });
    };
  }, []);
  useUnmountEffect(() => {
    dispatch(fields.actions.refresh());
  });

  const seasons = sortBy(allSeasons, ['end_date', 'created_at']);

  const seasonsData = seasons.map(season => {
    const fieldSeasonsInSeason = fieldSeasons.filter(
      fs => fs.season_id === season.id,
    );

    const crops = fieldCrops.filter(crop =>
      fieldSeasonsInSeason.some(fs => fs.id === crop.field_user_season_id),
    );
    const cropsIds = crops.map(crop => crop.id);

    const totalArea = fieldSeasonsInSeason.reduce((result, fs) => {
      return result + fs.area;
    }, 0);
    const filledArea = fieldSeasonsInSeason.reduce((result, fs) => {
      const crops = fieldCrops.filter(
        crop => crop.field_user_season_id === fs.id,
      );
      if (crops.length > 0) {
        return result + fs.area;
      }
      return result;
    }, 0);

    return {
      cropsIds: cropsIds,
      totalArea: round(totalArea, 2),
      filledBy: round((filledArea / totalArea) * 100, 0),
    };
  });

  const rows = useMemo(() => {
    return fieldUsers
      .map(field => ({
        title: formatFieldTitle(t, field),
        fieldId: field.id,
        area: fieldSeasons.find(fs => fs.field_user_id === field.id)?.area,
        columns: seasons.map(season => {
          const fieldSeason = fieldSeasons.find(
            fs => fs.field_user_id === field.id && fs.season_id === season.id,
          );
          const crops = fieldCrops.filter(
            crop => crop.field_user_season_id === fieldSeason?.id,
          );
          const comms = recommendations.filter(
            r => r.field_user_season_id === fieldSeason?.id,
          );

          return {
            fieldSeasonId: fieldSeason?.id,
            fieldSeasonUuid: fieldSeason?.uuid,
            area: fieldSeasons.find(fs => fs.field_user_id === field.id)?.area,
            crops: crops.map(crop => ({
              name: t(`crop.${crop.crop}`),
              code: crop.crop,
              color: cropColors[crop.crop],
              variety: crop.variety,
              sowing_date: crop.sowing_date,
              harvest_date: crop.harvest_date,
              yield_value: crop.yield_value,
              yield_value_units: crop.yield_value_units,
              recommendation: comms.find(comm => comm.crop === crop.crop),
              warn: true,
            })),
          };
        }),
      }))
      .filter(row => row.columns.some(column => column.fieldSeasonId));
  }, [
    fieldUsers,
    fieldSeasons,
    fieldCrops,
    seasons,
    cropColors,
    t,
    recommendations,
  ]);
  const fieldsCount = rows?.length;

  const columnCount = seasons.length > 0 ? seasons.length + 1 : 5;

  const allCrops = useMemo(
    () =>
      rows.reduce((acc, row) => {
        row.columns.forEach(column => {
          column.crops.forEach(
            crop => (acc[crop.code] = { color: crop.color, name: crop.name }),
          );
        });
        return acc;
      }, {}),
    [rows],
  );

  useEffect(() => {
    gridRef.current?.measureAllCells();
    gridRef.current?.recomputeGridSize();
  }, [keysArray, rows, allCrops]);

  const sortedRows = useMemo(() => {
    let sortableRows = [...rows];

    if (sortConfig !== null) {
      const { key } = sortConfig;
      if (key.startsWith('crop')) {
        const columnIndex = key.substr(key.indexOf('.') + 1) - 1;

        const rowsWithCrops = [];
        const rowsWithoutCrops = [];

        for (const row of sortableRows) {
          if (row.columns[columnIndex]?.crops[0]) {
            rowsWithCrops.push(row);
          } else {
            rowsWithoutCrops.push(row);
          }
        }

        const sortedRowsWithCrops = orderBy(
          rowsWithCrops,
          [`columns[${columnIndex}.crops[0].name`],
          [sortConfig.direction],
        );
        const sortedRowsWithoutCrops = orderBy(
          rowsWithoutCrops,
          [`columns[${columnIndex}.fieldSeasonId`],
          [SortDirections.ASCENDING],
        );
        sortableRows = [...sortedRowsWithCrops, ...sortedRowsWithoutCrops];
      } else if (key === 'title') {
        sortableRows.sort((a, b) => {
          return sortConfig.direction === SortDirections.ASCENDING
            ? collator.compare(a[key], b[key])
            : collator.compare(b[key], a[key]);
        });
      } else {
        sortableRows.sort((a, b) => {
          const aKey =
            typeof a[key] === 'string' ? a[key].toUpperCase() : a[key];
          const bKey =
            typeof b[key] === 'string' ? b[key].toUpperCase() : b[key];

          if (aKey < bKey) {
            return sortConfig.direction === SortDirections.ASCENDING ? -1 : 1;
          }
          if (aKey > bKey) {
            return sortConfig.direction === SortDirections.ASCENDING ? 1 : -1;
          }
          return 0;
        });
      }
    }
    return sortableRows;
  }, [rows, sortConfig]);

  const requestSort = key => {
    let direction = SortDirections.ASCENDING;
    if (
      sortConfig &&
      sortConfig.key === key &&
      sortConfig.direction === SortDirections.ASCENDING
    ) {
      direction = SortDirections.DESCENDING;
    }
    setSortConfig({ key, direction });
  };

  const allCropsCodes = Object.keys(allCrops);
  const cropsCount = allCropsCodes.length;

  const fillRowsToDraw = useCallback(() => {
    if (!cropsHidden) {
      setKeysArray(
        getKeysArray(
          [
            rowTypes.seasons,
            rowTypes.crops,
            rowTypes.autorotate,
            rowTypes.fields,
          ],
          [1, cropsCount + 1, 1, fieldsCount + 2],
        ),
      );
    } else {
      setKeysArray(
        getKeysArray(
          [rowTypes.seasons, rowTypes.autorotate, rowTypes.fields],
          [1, 1, fieldsCount + 2],
        ),
      );
    }
  }, [fieldsCount, cropsHidden, cropsCount]);

  const toggleCrops = useCallback(() => {
    fillRowsToDraw();
    logEvent('crop_rotation_toggle_crops_click');
    setCropsHidden(!cropsHidden);
  }, [cropsHidden, fillRowsToDraw]);

  useEffect(() => {
    fillRowsToDraw();
  }, [fillRowsToDraw]);

  const renderers = {
    [rowTypes.seasons]: ({ columnIndex, key, rowIndex, style }) => {
      return (
        <SeasonsRow
          key={key}
          seasons={seasons}
          seasonsData={seasonsData}
          columnIndex={columnIndex}
          rowIndex={rowIndex}
          setShowSeasonPopup={setShowSeasonPopup}
          toggleCrops={toggleCrops}
          style={style}
          t={t}
          cropsHidden={cropsHidden}
        />
      );
    },
    [rowTypes.crops]: ({ columnIndex, key, rowIndex, style }) => {
      if (rowIndex === 0) {
        return (
          <CropsHeaderCell
            season={seasons[columnIndex - 1]}
            columnIndex={columnIndex}
            rowIndex={rowIndex}
            key={key}
            style={style}
            t={t}
          />
        );
      }
      if (columnIndex === 0) {
        return (
          <StyledCropsTableCell
            columnIndex={columnIndex}
            rowIndex={rowIndex}
            key={key}
            style={style}
          >
            <StyledCropColorBar
              color={allCrops[allCropsCodes[rowIndex - 1]]?.color}
            />
            <span className={styles.cropName}>
              {allCrops[allCropsCodes[rowIndex - 1]]?.name}
            </span>
          </StyledCropsTableCell>
        );
      }

      return (
        <CropsTableCell
          season={seasons[columnIndex - 1]}
          columnIndex={columnIndex}
          rowIndex={rowIndex}
          cropCode={allCropsCodes[rowIndex - 1]}
          key={key}
          style={style}
        />
      );
    },
    [rowTypes.autorotate]: ({ columnIndex, key, rowIndex, style }) => {
      if (columnIndex === 0) return null;
      const seasonId = seasons[columnIndex - 1].id;
      return (
        <StyledAutoRotateCell
          columnIndex={columnIndex}
          key={key}
          rowIndex={rowIndex}
          style={style}
        >
          <FeatureDisabledTooltip
            feature={FEATURES.CROP_EDIT}
            direction={PopoverDeprecatedAlign.BottomMiddle}
          >
            <Button
              className='btn btn-default'
              onClick={() => {
                logEvent('crop_rotation_autofill_open', {
                  season_id: seasonId,
                });
                setShowAutoFillPopup(seasonId);
              }}
            >
              {t('croprotation.season.autofill_short')}
            </Button>
          </FeatureDisabledTooltip>
        </StyledAutoRotateCell>
      );
    },
    [rowTypes.fields]: ({ columnIndex, key, rowIndex, style }) => {
      if (fetchRequest.status !== 'resolved') {
        return null;
      }

      if (rowIndex === 0 && columnIndex === 0) {
        return (
          <StyledFieldsHeaderCell
            rowIndex={0}
            columnIndex={0}
            style={style}
            key={key}
          >
            <StyledSortButton
              onClick={() => requestSort('title')}
              sort={
                sortConfig?.key === 'title' ? sortConfig.direction : undefined
              }
            >
              {t('croprotation.sort.name')}
            </StyledSortButton>
            <StyledSortButton
              onClick={() => requestSort('area')}
              sort={
                sortConfig?.key === 'area' ? sortConfig.direction : undefined
              }
            >
              {t('croprotation.sort.square')}
            </StyledSortButton>
          </StyledFieldsHeaderCell>
        );
      }

      if (rowIndex === 0) {
        return (
          <StyledFieldsHeaderCell
            rowIndex={0}
            columnIndex={columnIndex}
            style={style}
            key={key}
          >
            <StyledSortButton
              onClick={() => requestSort(`crop.${columnIndex}`)}
              sort={
                sortConfig?.key === `crop.${columnIndex}`
                  ? sortConfig.direction
                  : undefined
              }
            >
              {t('croprotation.sort.crop')}
            </StyledSortButton>
          </StyledFieldsHeaderCell>
        );
      }

      if (rowIndex === fieldsCount + 1) {
        if (columnIndex === 0) {
          return (
            <StyledFieldsColumnCell rowIndex={rowIndex} style={style} key={key}>
              <CreateFieldDialogDropDown
                renderValue={() => (
                  <FeatureDisabledTooltip
                    feature={FEATURES.FIELD_ADD}
                    direction={PopoverDeprecatedAlign.MiddleTop}
                  >
                    <button className={styles.addField}>
                      <div className={styles.fieldPreview}>
                        <span className={styles.addFieldIcon}>+</span>
                      </div>
                      <span className={styles.addFieldText}>
                        {t('croprotation.button.addfield')}
                      </span>
                    </button>
                  </FeatureDisabledTooltip>
                )}
                onChange={onChange}
              />
            </StyledFieldsColumnCell>
          );
        } else return null;
      }

      const season = seasons[columnIndex - 1];

      const row = sortedRows[rowIndex - 1];
      if (!row) return;

      if (columnIndex === 0) {
        const currentFieldSeason = fieldSeasons.find(
          fs =>
            fs.field_user_id === row.fieldId &&
            fs.season_id === currentSeason.id,
        );
        const anyFieldSeason = fieldSeasons.find(
          fs => fs.field_user_id === row.fieldId,
        );
        const fieldSeason = currentFieldSeason || anyFieldSeason;

        const previewURL = `${config.apiHost}/v2/fields-users-seasons/${
          fieldSeason.id
        }/preview?${qs.stringify({
          token: token,
          workspace_uuid: workspaceUuid,
          width: 156,
          height: 156,
          key: fieldSeason.geom_updated_at,
        })}`;
        return (
          <StyledFieldsColumnCell rowIndex={rowIndex} style={style} key={key}>
            <div className={styles.fieldMeta}>
              <FieldImage
                src={previewURL}
                title={row.title}
                fieldBySeason={fieldSeason}
              />
              <span>{row.title}</span>
            </div>
            <div className={styles.fieldArea}>
              {formatUnit(fieldSeason.area, 'ha', 'area')}
            </div>
          </StyledFieldsColumnCell>
        );
      }

      const cell = row.columns[columnIndex - 1];

      return (
        <FieldsCropCell
          key={key}
          style={style}
          seasonId={season.id}
          currentSeasonId={currentSeason?.id}
          notes={notes}
          fieldId={row.fieldId}
          rowIndex={rowIndex}
          columnIndex={columnIndex}
          t={t}
          cell={cell}
          match={match}
          history={history}
          rowsCount={rows.length}
          openEditGeometry={openEditGeometry}
          setShowRemoveEntityPopup={setShowRemoveEntityPopup}
          setShowRemoveFieldWithNotesPopup={setShowRemoveFieldWithNotesPopup}
        />
      );
    },
    stickyHeader: ({ columnIndex, key, rowIndex, style }) => {
      const season = seasons[columnIndex - 1];
      const seasonData = seasonsData[columnIndex - 1];

      if (columnIndex === 0 && rowIndex === 0) {
        return (
          <div style={style} key={key}>
            <span className={styles.cropRotationTitleSticky}>
              {t('croprotation.title')}
            </span>
          </div>
        );
      } else if (rowIndex === 0) {
        return (
          <SeasonsHeaderCell
            style={style}
            key={key}
            columnIndex={columnIndex}
            match={match}
            season={season}
            seasonData={seasonData}
            t={t}
            setShowSeasonPopup={setShowSeasonPopup}
            isSticky={true}
          />
        );
      }
      if (columnIndex === 0) {
        return (
          <StyledFieldsHeaderCell
            key={key}
            rowIndex={0}
            columnIndex={0}
            style={style}
          >
            <StyledSortButton
              onClick={() => requestSort('title')}
              sort={
                sortConfig?.key === 'title' ? sortConfig.direction : undefined
              }
            >
              {t('croprotation.sort.name')}
            </StyledSortButton>
            <StyledSortButton
              onClick={() => requestSort('area')}
              sort={
                sortConfig?.key === 'area' ? sortConfig.direction : undefined
              }
            >
              {t('croprotation.sort.square')}
            </StyledSortButton>
          </StyledFieldsHeaderCell>
        );
      }
      return (
        <StyledFieldsHeaderCell
          rowIndex={0}
          columnIndex={columnIndex}
          style={style}
          key={key}
        >
          <StyledSortButton
            onClick={() => requestSort(`crop.${columnIndex}`)}
            sort={
              sortConfig?.key === `crop.${columnIndex}`
                ? sortConfig.direction
                : undefined
            }
          >
            {t('croprotation.sort.crop')}
          </StyledSortButton>
        </StyledFieldsHeaderCell>
      );
    },
  };

  const rowHeights = {
    [rowTypes.seasons]: () => SEASONS_ROW_HEIGHT,
    [rowTypes.crops]: index =>
      index ? CROPS_ROW_HEIGHT : CROPS_FIRST_ROW_HEIGHT,
    [rowTypes.autorotate]: () => AUTOROTATE_ROW_HEIGHT,
    [rowTypes.fields]: index =>
      index ? FIELDS_ROW_HEIGHT : FIELDS_FIRST_ROW_HEIGHT,
  };

  return (
    <>
      <MemoryRouter browserHistory={history}>
        <BodyClassName className={'crop-rotation'} />
        <EnterExitTransition variant='popup'>
          {showSeasonPopup && (
            <EditSeasonPopup
              mode={showSeasonPopup === 'create' ? 'create' : 'edit'}
              seasonId={showSeasonPopup}
              onConfirm={() => {
                setShowSeasonPopup(false);
              }}
              onCancel={() => {
                setShowSeasonPopup(false);
              }}
            />
          )}
        </EnterExitTransition>
        <EnterExitTransition variant='popup'>
          {showAutoFillPopup && (
            <AutoFillPopup
              seasonId={showAutoFillPopup}
              cropColors={cropColors}
              t={t}
              getCropStatistics={fieldSeason.selectors.getCropStatistics}
              onClose={() => setShowAutoFillPopup(false)}
            />
          )}
        </EnterExitTransition>
        <EnterExitTransition variant='popup'>
          {showRemoveEntityPopup && (
            <RemoveEntityPopup
              entityType='field'
              t={t}
              onConfirm={() => {
                dispatch(
                  entities.actions.request('delete-field-season-with-crops', [
                    ...fieldCrops
                      .filter(
                        crop =>
                          crop.field_user_season_id === showRemoveEntityPopup,
                      )
                      .map(crop => ({
                        entityType: 'fieldCrop',
                        path: { id: crop.id + '' },
                        type: 'delete',
                      })),
                    {
                      type: 'delete',
                      path: {
                        id: showRemoveEntityPopup + '',
                      },
                      entityType: 'fieldSeason',
                    },
                  ]),
                );
                setShowRemoveEntityPopup(false);
              }}
              onCancel={() => setShowRemoveEntityPopup(false)}
            />
          )}
        </EnterExitTransition>
        <EnterExitTransition variant='popup'>
          {showRemoveFieldWithNotesPopup && (
            <LinkedNotesWarningPopup
              t={t}
              title={t('fields.linked-notes.warning.title')}
              text={t('fields.linked-notes.warning.text', {
                count: 1,
              })}
              onConfirm={() => {
                dispatch(
                  entities.actions.request('delete-field-season-with-crops', [
                    ...fieldCrops
                      .filter(
                        crop =>
                          crop.field_user_season_id ===
                          showRemoveFieldWithNotesPopup,
                      )
                      .map(crop => ({
                        entityType: 'fieldCrop',
                        path: { id: crop.id + '' },
                        type: 'delete',
                      })),
                    {
                      type: 'delete',
                      path: {
                        id: showRemoveFieldWithNotesPopup,
                      },
                      entityType: 'fieldSeason',
                    },
                  ]),
                );
                setShowRemoveFieldWithNotesPopup(false);
              }}
              onCancel={() => {
                setShowRemoveFieldWithNotesPopup(false);
              }}
            />
          )}
        </EnterExitTransition>
        <Saga saga={cropRotationSaga} history={history} />
        <div className={styles.cropRotationContainer}>
          <ScrollSync>
            {({ onScroll, scrollLeft, scrollTop }) => (
              <section className={styles.cropRotationContent}>
                <header className={styles.cropRotationHeader}>
                  {fetchRequest.status === 'resolved' && (
                    <CropRotationStickyHeader
                      scrollLeft={scrollLeft}
                      scrollTop={scrollTop}
                      cellRenderer={renderers.stickyHeader}
                      columnCount={columnCount}
                    />
                  )}
                  <div className={styles.cropRotationHeaderContent}>
                    <div className={styles.cropRotationHeaderRight}>
                      <FeatureDisabledTooltip
                        feature={FEATURES.FIELD_ADD}
                        direction={PopoverDeprecatedAlign.BottomMiddle}
                      >
                        <div>
                          <CreateFieldDialogDropDown
                            className='btn size-full btn-primary'
                            renderValue={() => (
                              <>
                                {t('fields.list.add')}{' '}
                                <span className='btn__arrow' />
                              </>
                            )}
                            onChange={onChange}
                          />
                        </div>
                      </FeatureDisabledTooltip>
                      {!config.whiteLabel?.hideSeasons && (
                        <FeatureDisabledTooltip
                          feature={FEATURES.SEASON_ADD}
                          direction={PopoverDeprecatedAlign.BottomMiddle}
                        >
                          <Button
                            className='btn btn-success'
                            onClick={() => {
                              setShowSeasonPopup('create');
                            }}
                          >
                            <span className='btn__ico'>
                              <Icon className='ico-plus-os' name='plus-btn' />
                            </span>
                            {t('fields.edit.create_season')}
                          </Button>
                        </FeatureDisabledTooltip>
                      )}
                    </div>
                  </div>
                </header>
                <div className={styles.cropRotationBody}>
                  <AutoSizer>
                    {({ width, height }) => {
                      if (fetchRequest.status !== 'resolved') {
                        return (
                          <SkeletonGrid width={width} height={height} t={t} />
                        );
                      }
                      return (
                        <MultiGrid
                          ref={gridRef}
                          className={styles.cropRotationGrid}
                          classNameBottomLeftGrid={styles.bottomLeft}
                          rowCount={keysArray.length}
                          rowHeight={({ index }) => {
                            return rowHeights[keysArray[index].key](
                              index - keysArray[index].startOffset,
                            );
                          }}
                          columnCount={columnCount}
                          columnWidth={({ index }) =>
                            index ? COLUMN_WIDTH : FIRST_COLUMN_WIDTH
                          }
                          fixedColumnCount={1}
                          enableFixedColumnScroll
                          height={height}
                          width={width}
                          cellRenderer={({
                            columnIndex,
                            key,
                            rowIndex,
                            style,
                          }) => {
                            return renderers[keysArray[rowIndex].key]({
                              columnIndex,
                              key,
                              rowIndex:
                                rowIndex - keysArray[rowIndex].startOffset,
                              style,
                            });
                          }}
                          onScroll={onScroll}
                        />
                      );
                    }}
                  </AutoSizer>
                </div>
              </section>
            )}
          </ScrollSync>
        </div>
      </MemoryRouter>
      {fetchRequest.status === 'resolved' && (
        <AnimatedRoute
          path={`*/edit-field/:fieldSeasonId`}
          component={EditFieldGeometryPopupV2}
          timeout={{
            appear: 0,
            enter: 0,
            exit: 300,
          }}
        />
      )}
    </>
  );
};

export default CropRotationView;
