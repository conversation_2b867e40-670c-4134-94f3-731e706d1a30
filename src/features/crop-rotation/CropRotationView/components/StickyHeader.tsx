import React, { CSSProperties, VFC } from 'react';

import { StyledFieldsHeader<PERSON>ell, StyledSortButton, styles } from '../CropRotationView.style';
import SeasonsHeaderCell from './SeasonsHeaderCell';
import { SeasonType } from 'types/rootState';
import { SeasonData, SortConfigType } from '../types';
import { TranslateFunction } from 'utils/use-translate';

type StickyHeaderProps = {
  columnIndex: number
  rowIndex: number
  style: CSSProperties
  setShowSeasonPopup: (id?: number | boolean) => void,
  seasons: SeasonType[],
  seasonsData: SeasonData[],
  t: TranslateFunction,
  sortConfig: SortConfigType
  requestSort: (key: string) => void,
};

const StickyHeader: VFC<StickyHeaderProps> = (
  {
    sortConfig,
    requestSort,
    setShowSeasonPopup,
    t,
    seasons,
    seasonsData,
    columnIndex,
    rowIndex,
    style
  }) => {
  const season: SeasonType = seasons[columnIndex - 1] as SeasonType;
  const seasonData: SeasonData = seasonsData[columnIndex - 1] as SeasonData;

  if (columnIndex === 0 && rowIndex === 0) {
    return (
      <div style={style}>
        <span className={styles.cropRotationTitleSticky}>
          {t('croprotation.title')}
        </span>
      </div>
    );
  } else if (rowIndex === 0) {
    return (
      <SeasonsHeaderCell
        style={style}

        columnIndex={columnIndex}
        season={season}
        seasonData={seasonData}
        t={t}
        setShowSeasonPopup={setShowSeasonPopup}
        isSticky={true}
      />
    );
  }
  if (columnIndex === 0) {
    return (
      <StyledFieldsHeaderCell
        columnIndex={0}
        style={style}
      >
        <StyledSortButton
          onClick={() => requestSort('title')}
          sort={
            sortConfig?.key === 'title' ? sortConfig.direction : undefined
          }
        >
          {t('croprotation.sort.name')}
        </StyledSortButton>
        <StyledSortButton
          onClick={() => requestSort('area')}
          sort={
            sortConfig?.key === 'area' ? sortConfig.direction : undefined
          }
        >
          {t('croprotation.sort.square')}
        </StyledSortButton>
      </StyledFieldsHeaderCell>
    );
  }
  return (
    <StyledFieldsHeaderCell
      columnIndex={columnIndex}
      style={style}

    >
      <StyledSortButton
        onClick={() => requestSort(`crop.${columnIndex}`)}
        sort={
          sortConfig?.key === `crop.${columnIndex}`
            ? sortConfig.direction
            : undefined
        }
      >
        {t('croprotation.sort.crop')}
      </StyledSortButton>
    </StyledFieldsHeaderCell>
  );
}

export default StickyHeader;
