import React, { VFC } from 'react';

import { colors } from 'constants/style';

import { useOverlapPercentage } from 'features/files/colorSchemes/hooks/useOverlapPercentage';
import { FileDataSourceType } from 'features/files/colorSchemes/types';
import LegendBar from 'features/files/colorSchemes/default/legend/LegendBar';

const OverlapFileLegend: VFC<FileDataSourceType> = dataSource => {
  const overlap = useOverlapPercentage(dataSource);

  return (
    <div className='map-legend__item'>
      <LegendBar from={overlap} colors={[colors.overlapColor]} />
    </div>
  );
};

export default OverlapFileLegend;
