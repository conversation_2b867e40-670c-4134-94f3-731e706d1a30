import React, { VFC } from 'react';

import { colors } from 'constants/style';
import { FileDataSourceType } from 'features/files/colorSchemes/types';
import { useOverlapPercentage } from 'features/files/colorSchemes/hooks/useOverlapPercentage';
import {
  LegendPalette,
  LegendText,
  LegendBlock,
  SoilAnalysisCoverageLegend,
} from './styles';

const OverlapFileLegend: VFC<FileDataSourceType> = dataSource => {
  const overlap = useOverlapPercentage(dataSource);

  return (
    <SoilAnalysisCoverageLegend>
      <LegendBlock>
        <LegendText>{overlap}</LegendText>
        <LegendPalette color={colors.overlapColor} />
      </LegendBlock>
    </SoilAnalysisCoverageLegend>
  );
};

export default OverlapFileLegend;
