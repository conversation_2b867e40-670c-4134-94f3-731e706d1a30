import { EntityRequest } from '../../../../modules/entities/types';

export enum StoreStatus {
  init = 'init',
  updating = 'updating',
  finished = 'finished',
  reUpdating = 'reUpdating',
}

export enum FileImportError {
  download = 'download',

  /** validation error */
  mime_type_is_denied = 'mime_type_is_denied ',
  invalid_extension = 'invalid_extension',
  too_big_file = 'too_big_file ',

  /** parsing error */
  too_large = 'too_large',
  unsupported_format = 'unsupported_format',
  cannot_open = 'cannot_open',
  empty = 'empty',
  no_matched_fields = 'no_matched_fields',
  unknown = 'unknown',

  /** problem with server codes 500-600 */
  server = 'server',
}

export type DataLoader<DataType> = [DataType, EntityRequest, VoidFunction];
