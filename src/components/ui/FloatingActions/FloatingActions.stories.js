import React from 'react';
/* eslint-disable */
import {
  FloatingActionsDescriptionStyle,
  FloatingActionsNameStyle,
  FloatingActionsStyle,
} from 'components/ui/FloatingActions/styles';
import SmartButton from 'components/ui/Button/SmartButton';
import Icon from 'components/Icon';

export default {
  title: 'common/FloatingActions',
  component: FloatingActionsStyle,
  decorators: [storyFn => <div style={{ padding: 16 }}>{storyFn()}</div>],
};

export const Styled = () => (
  <>
    <FloatingActionsStyle>
      <FloatingActionsDescriptionStyle>
        <small>long long title</small>
        <br />
        <FloatingActionsNameStyle>subTitle</FloatingActionsNameStyle>
      </FloatingActionsDescriptionStyle>
      <SmartButton className='btn btn-dark btn-lg'>cancelLabel</SmartButton>
      <SmartButton className='btn btn-success btn-lg'>
        <span className='btn__ico'>
          <Icon className='ico-check-os' name='check' />
        </span>
        submitLabel
      </SmartButton>
    </FloatingActionsStyle>
  </>
);
