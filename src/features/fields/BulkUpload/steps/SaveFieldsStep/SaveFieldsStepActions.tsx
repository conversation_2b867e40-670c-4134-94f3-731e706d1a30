import React from 'react';

import Button from 'components/ui/Button/Button';
import Icon from 'components/Icon';

import { useTranslate } from 'utils/use-translate';
import { FloatingActionsStyle } from 'components/ui/FloatingActions/styles';

interface SaveFieldsStepActionsProps {
  disabled: boolean;
  isPending: boolean;
  onCancel: VoidCallBack;
  onSave: VoidCallBack;
}

export const SaveFieldsStepActions = ({
  disabled,
  isPending,
  onCancel,
  onSave,
}: SaveFieldsStepActionsProps) => {
  const { t } = useTranslate();

  return (
    <FloatingActionsStyle>
      <Button className='btn btn-dark btn-lg' onClick={onCancel}>
        {t('forms.cancel')}
      </Button>
      <Button
        className='btn btn-success btn-lg'
        type='submit'
        disabled={disabled}
        onClick={onSave}
        pending={isPending}
      >
        <span className='btn__ico'>
          <Icon className='ico-check-os' name='check' />
        </span>
        {t('forms.save')}
      </Button>
    </FloatingActionsStyle>
  );
};
