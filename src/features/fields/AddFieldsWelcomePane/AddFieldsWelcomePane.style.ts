import { css } from 'linaria';
import { styled } from 'linaria/react';

export const SoilSidebarEnters = styled.div`
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style: -ms-autohiding-scrollbar;
  min-height: 0;

  & h2 {
    margin-top: 0;
    margin-bottom: 5px;
    font-size: 19px;
    line-height: 21px;
    font-weight: bold;
  }
  & p {
    margin-top: 0;
    margin-bottom: 10px;
  }
`;

export const SoilSidebarEntersListItem = styled.div`
  flex: 1.5 0 auto;
  border-top: 0.5px solid rgba(165, 178, 188, 0.45);
  display: flex;
  font-size: 14px;
  line-height: 1.36;

  &:first-child {
    border-top-width: 0;
  }
`;

export const SoilSidebarEntersItem = css`
  padding: 15px 20px 15px 65px;
  width: 100%;
  display: flex;
  color: #222;
  align-items: center;

  &:hover {
    color: #222;
  }

  &:hover .btn-success {
    background-color: var(--color-secondary);
  }
`;

export const SoilSidebarEntersInner = styled.div`
  max-width: 175px;
  width: 100%;
`;

export const SoilSidebarEntersLabel = styled.p`
  margin-top: 0;
  margin-bottom: 8px;
`;

export const SoilSidebarEntersLabelInner = styled.span`
  display: inline-block;
  font-size: 10px;
  line-height: 1;
  letter-spacing: 0.75px;
  font-weight: bold;
  text-transform: uppercase;
  border-radius: 4px;
  padding: 5px;

  &[data-type='primary'] {
    color: #fff;
    background-color: #007aff;
  }
`;

export const SoilSidebarEntersIco = styled.span`
  position: absolute;
  margin-left: -45px;
  font-size: 1px;
  width: 25px;
  height: 42px;
  text-align: center;
  display: flex;

  & svg {
    margin: auto;
  }
`;
