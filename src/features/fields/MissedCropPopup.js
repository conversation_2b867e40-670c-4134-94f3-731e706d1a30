import React from 'react';
import { Trans } from 'react-i18next';

import Button from 'components/ui/Button/Button';
import RenderAboveEverything from 'components/RenderAboveEverything';

import { formatTitle as formatSeasonTitle } from 'utils/seasons-formatters';
import { useTranslate } from 'utils/use-translate';

const MissedCropPopup = React.memo(
  ({ seasonIds, allSeasons, onConfirm, onCancel }) => {
    const { t } = useTranslate();
    const missedSeasons = allSeasons.filter(s => seasonIds.includes(s.id));
    return (
      <RenderAboveEverything>
        <div className='modal modal-editseasons'>
          <div className='modal-outer'>
            <div className='modal-editseasons-content'>
              <div className='modal-editseasons-content__header'>
                {t('fields.upload.missed_crops.title')}
              </div>
              <div className='modal-editseasons-content__body'>
                <div className='form-group'>
                  <Trans>
                    {t('fields.upload.missed_crops.description', {
                      seasons: missedSeasons.map(
                        s => `<b> ${formatSeasonTitle(t, s)}</b>`,
                      ),
                    })}
                  </Trans>
                </div>
              </div>
              <div className='modal-editseasons-content__actions'>
                <Button
                  className='btn btn-primary btn-lg'
                  onClick={() => {
                    onCancel();
                  }}
                >
                  {t('forms.back')}
                </Button>
                <Button
                  className='btn btn-success btn-lg __no-visual-disable'
                  type='submit'
                  onClick={() => {
                    onConfirm();
                  }}
                >
                  {t(`forms.proceed`)}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </RenderAboveEverything>
    );
  },
);

export default MissedCropPopup;
