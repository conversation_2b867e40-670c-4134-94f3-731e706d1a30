import React, { forwardRef } from 'react';
import type { Control } from 'react-hook-form';
import type { FieldErrors } from 'react-hook-form/dist/types/errors';

import { ClientField } from 'features/multiaccount/dealer/modals/ClientForm/components/ClientField';
import {
  ClientForm,
  ClientFormType,
} from 'features/multiaccount/dealer/modals/ClientForm/types';

import { ErrorData } from 'modules/entities/types';
import { EMAIL_REGEX } from 'utils/forms/regexp';
import { useTranslate } from 'utils/use-translate';

type ClientEmailFieldProps = {
  control: Control<ClientForm>;
  formErrors: FieldErrors<ClientForm>;
  isPending: boolean;
  requestErrors: Nullable<ErrorData>;
  type: ClientFormType;
  name?: string;
};

const ClientEmailField = forwardRef<HTMLInputElement, ClientEmailFieldProps>(
  (props, ref) => {
    const { t } = useTranslate();

    return (
      <ClientField
        {...props}
        ref={ref}
        name={props.name || 'email'}
        inputType='email'
        autoFocus
        disabled={
          props.isPending ||
          props.type === ClientFormType.edit ||
          props.type === ClientFormType.editSubscriptionClient
        }
        columnTranslationKey='multiaccount.invite-client-modal.column.email'
        placeholderTranslationKey={`multiaccount.${props.type}-modal.placeholder.email`}
        validationRules={{
          required: t('multiaccount.dealer.error.required') as string,
          pattern: {
            value: EMAIL_REGEX,
            message: t('multiaccount.dealer.error.email') as string,
          },
        }}
      />
    );
  },
);

export default ClientEmailField;
