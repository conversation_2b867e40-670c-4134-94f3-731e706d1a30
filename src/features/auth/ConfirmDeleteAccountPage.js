import { useSelector } from 'react-redux';
import { call, put } from 'redux-saga/effects';

import auth from 'modules/auth';

import { apiCall } from 'utils/effects';
import usePersistentSaga from 'utils/use-persistent-saga';
import logEvent from 'sagas/global/logEvent';

const ConfirmDeleteAccountPage = ({ history, match }) => {
  const userID = useSelector(auth.selectors.getUserID);
  const isAuthorized = useSelector(auth.selectors.isAuthorized);

  usePersistentSaga(function*() {
    try {
      yield apiCall(
        auth.actions.confirmDeleteAccount(userID, match.params.token),
      );
      yield call(logEvent, 'delete_account_confirm');
      yield put(auth.actions.logout());

      yield call(
        [history, history.replace],
        '/internal/delete-account/success',
      );
    } catch (error) {
      const serverError = error.response?.error_details.find(
        e => e.field === 'confirmation_token',
      );

      if (isAuthorized && serverError) {
        yield call([history, history.replace], {
          pathname: '/',
          state: {
            delete_account_error: serverError,
          },
        });
      } else {
        yield call([history, history.replace], '/');
      }
    }
  });

  return null;
};

export default ConfirmDeleteAccountPage;
