import React, { useEffect, VFC } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { useDispatch, useSelector } from 'react-redux';

import toasts from 'modules/toasts';
import { useTranslate } from 'utils/use-translate';
import { UploadingTasksArea } from './styles';
import { DropArea } from 'components/ui/DropArea';

import {
  ImportTaskType,
  TaskSuccessType,
  DataTypesKey,
} from 'features/files/by-machinery/types';
import {
  FILE_TYPES,
  MAX_FILE_SIZE,
  TOAST_KEY_TO_IMPORT,
} from 'features/files/by-machinery/constants';
import { actions } from 'features/files/by-machinery/redux/actions';
import { selectors } from 'features/files/by-machinery/redux/selectors';
import { useMarkAllActiveTaskAsShowed } from 'features/files/by-machinery/hooks/useMarkAllActiveTaskAsShowed';

import ActiveTasksList from './components/ActiveTasksList';
import HistoryTasksList from './components/HistoryTasksList';
import ImportFilesDescription from './components/ImportFilesDescription';
// @ts-ignore
import useQuery from 'utils/use-query';

import { CentralSection, ImportTaskTitle } from '../styles';
import { failedToastIcon } from '../../styles';

const IMPORT_LIMIT = 10;

const ImportFilesSection: VFC = () => {
  const { t } = useTranslate();
  const dispatch = useDispatch();
  const activeTasks: ImportTaskType[] = useSelector(selectors.getActiveTasks);
  const historyTasks: TaskSuccessType[] = useSelector(
    selectors.getHistoryTasks,
  );

  const [query] = useQuery();
  const dataType = query['data-type'] || DataTypesKey.machinery;

  useEffect(() => {
    dispatch(toasts.actions.removeGroup(TOAST_KEY_TO_IMPORT));
  }, [dispatch]);

  /** mark all task as showed */
  useMarkAllActiveTaskAsShowed();

  const handleDropTasks = (files: File[]) => {
    const taskForUpload = files.slice(0, IMPORT_LIMIT - activeTasks.length);
    if (files.length > taskForUpload.length) {
      dispatch(
        toasts.actions.push(
          TOAST_KEY_TO_IMPORT,
          'import.by-machinery.failed.limit',
          {
            getLabel: ({ t, label }) => t(label) as string,
            position: 'bottom',
            icon: 'alert-circle',
            iconClassName: failedToastIcon,
            ttl: 10000,
          },
        ),
      );
    }

    for (let file of taskForUpload) {
      if (file.size < MAX_FILE_SIZE) {
        dispatch(actions.createTask(uuidv4(), file, dataType));
      } else {
        dispatch(actions.createTaskWithError(uuidv4(), file));
      }
    }
  };

  return (
    <form onSubmit={() => {}}>
      <CentralSection>
        <ImportTaskTitle>
          {t(`import.by-machinery.${dataType}.title`)}
        </ImportTaskTitle>
        <ImportFilesDescription dataType={dataType} />
        <UploadingTasksArea>
          <DropArea
            accept={FILE_TYPES.map(s => `.${s}`).join(',')}
            onLoadFiles={handleDropTasks}
            title={t('import.by-machinery.drop.title')}
            description={t(`import.by-machinery.drop.${dataType}.file_size`)}
            buttonLabel={t('import.by-machinery.drop.button')}
          />
          <ActiveTasksList tasks={activeTasks} />
        </UploadingTasksArea>
        <HistoryTasksList tasks={historyTasks} />
      </CentralSection>
    </form>
  );
};

export default ImportFilesSection;
