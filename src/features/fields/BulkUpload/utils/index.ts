import { BulkUploadStep, FileBundle } from 'features/fields/BulkUpload/types';

export function isBundleValid(bundle: FileBundle) {
  return Object.keys(bundle.errors).length === 0;
}

const bulkUploadUrlReg = /@[\d,.-]+z\/fields\/add\/upload/;
export const isBulkUploadPage = () => {
  return bulkUploadUrlReg.test(window.location.pathname);
};

export function isError(error: unknown): error is Error {
  return (
    error !== undefined &&
    error !== null &&
    typeof error === 'object' &&
    error instanceof Error
  );
}

export function getErrorMessage(error: unknown): string | undefined {
  if (typeof error === 'string') {
    return error;
  }

  if (isError(error)) {
    return error.message;
  }

  return undefined;
}

export const BulkUploadStepOrder: Record<BulkUploadStep, number> = {
  [BulkUploadStep.FILE_SELECTION]: 0,
  [BulkUploadStep.PROCESSING]: 1,
  [BulkUploadStep.CHECK_DATA]: 2,
  [BulkUploadStep.UPLOADING]: 3,
  [BulkUploadStep.COLUMN_MATCHING]: 4,
  [BulkUploadStep.CROP_MATCHING]: 5,
  [BulkUploadStep.SAVING]: 6,
};

export function isStepReached(
  step: BulkUploadStep,
  reachedStep: BulkUploadStep,
) {
  return BulkUploadStepOrder[step] <= BulkUploadStepOrder[reachedStep];
}
