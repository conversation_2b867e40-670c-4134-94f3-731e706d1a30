import React, { FC, useMemo } from 'react';

import {
  columnGroupHeaderCell,
  columnGroupHeaderLabel,
} from './TableRow.style';
import { ColumnInfo } from './types';
import { cx } from 'linaria';

type GroupsInfoType = {
  count: number;
  name: Nullable<string>;
};

type TableGroupsRowProps = {
  columnsInfo: ColumnInfo[];
};

export const TableGroupsRow: FC<TableGroupsRowProps> = ({ columnsInfo }) => {
  const groupsInfo = useMemo(() => {
    return columnsInfo.reduce<GroupsInfoType[]>(
      (acc, column) => {
        if (column.columnsGroupName) {
          acc.push({
            name: column.columnsGroupName,
            count: 0,
          });
        }

        const last = acc[acc.length - 1];
        if (last) {
          last.count++;
        }

        return acc;
      },
      [
        {
          name: null,
          count: 1,
        },
      ],
    );
  }, [columnsInfo]);

  return (
    <tr>
      {groupsInfo?.map((groupInfo, index) => {
        return (
          <td
            key={groupInfo.name}
            colSpan={groupInfo.count}
            className={cx(groupInfo.name && columnGroupHeaderCell)}
          >
            {groupInfo.name && (
              <div className={columnGroupHeaderLabel}>{groupInfo.name}</div>
            )}
          </td>
        );
      })}
    </tr>
  );
};
