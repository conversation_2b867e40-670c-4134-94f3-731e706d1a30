import React, { useMemo, useRef } from 'react';
import { AutoSizer, List } from 'react-virtualized';
import UploadFieldListItem from './UploadFieldListItem';

export const FieldItemWrapper = React.memo(({ index, style, data }) => {
  return (
    <UploadFieldListItem
      style={style}
      fieldId={data.importedFields[index].id}
      previewUrl={data.importedFields[index].preview_url}
      fieldTitle={
        data.importedFields[index].title
        // `Field ${data.importedFields[index].id}`
      }
      history={data.history}
      index={index}
      checkedIDs={data.checkedIDs}
      highlighted={data.highlighted}
      handleCheck={data.handleCheck}
    />
  );
});

const UploadFieldsSideBar = ({
  importedFields,
  checkedIDs,
  highlighted,
  handleCheck,
}) => {
  const list = useRef(null);

  const listData = useMemo(
    () => ({
      checkedIDs,
      importedFields,
      highlighted,
      handleCheck,
    }),
    [checkedIDs, highlighted, importedFields, handleCheck],
  );

  return (
    <div className='soil-sidebar-body'>
      <AutoSizer>
        {({ width, height }) => (
          <List
            ref={list}
            className='soil-fields-list'
            containerProps={{
              'data-mode': 'edit',
              'data-size': 'compact',
            }}
            width={width}
            height={height}
            rowCount={importedFields.length}
            rowHeight={60}
            rowRenderer={props => (
              <FieldItemWrapper data={listData} {...props} />
            )}
          />
        )}
      </AutoSizer>
    </div>
  );
};

export default UploadFieldsSideBar;
