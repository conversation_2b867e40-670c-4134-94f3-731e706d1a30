import { StringMap } from 'i18next';
import { useTranslate } from 'utils/use-translate';

import { clamp, formatPercentage } from 'features/files/utils/formats';
import { FileDataSourceType } from 'features/files/colorSchemes/types';

export const useUndercoverPercentage = ({ file }: FileDataSourceType) => {
  const { t } = useTranslate();
  const undercover = file.undercover && clamp(file.undercover.ratio, 0, 1);

  if (!Number.isFinite(undercover)) {
    return null;
  }

  return t('files.legend.undercover', {
    ratio: formatPercentage(undercover),
  } as StringMap) as string;
};
