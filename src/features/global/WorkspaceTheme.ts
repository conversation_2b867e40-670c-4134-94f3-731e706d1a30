import { useEffect } from 'react';

import useWorkspaceColors from 'utils/hooks/use-workspace-colors';

function WorkspaceTheme() {
  const [primaryColor, secondaryColor] = useWorkspaceColors();

  useEffect(() => {
    document.documentElement.style.setProperty(
      '--color-primary',
      `${primaryColor}`,
    );
    document.documentElement.style.setProperty(
      '--color-secondary',
      `${secondaryColor}`,
    );
  }, [primaryColor, secondaryColor]);

  return null;
}

export default WorkspaceTheme;
