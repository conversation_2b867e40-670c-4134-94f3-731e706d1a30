import { MutableRefObject, useCallback } from 'react';
import { useDispatch } from 'react-redux';

import entities from 'modules/entities';

import { DataStoreType } from 'features/files/by-machinery/pages/MatchingOperationsPage/service/useDataStore';
import { ApiDataFieldOperations } from '../../../types/server';

export type OnSetPropertyType = (
  operation: ApiDataFieldOperations,
  is_enabled: boolean,
) => void;

export const useSetChecked = (
  dataStore: MutableRefObject<DataStoreType>,
): OnSetPropertyType => {
  const dispatch = useDispatch();

  return useCallback(
    (operation, is_enabled) => {
      dispatch(
        entities.actions.request('setRequestId', [
          {
            type: 'patch',
            entityType: 'fieldsDataOperation',
            path: {
              id: operation.uuid,
            },
            query: {
              include: ['data_source.layer.properties'],
            },
            body: {
              is_enabled: is_enabled,
            },
          },
        ]),
      );
    },
    [dispatch],
  );
};
