import React, { FC, VFC } from 'react';
import { styles } from '../Modal.style';

export type ModalContentProps = {
  ModalHeader?: VFC;
  ModalBody?: VFC;
  ModalActions?: VFC;
  children?: TEMPORARY_ANY;
  onClick: (e: React.MouseEvent<HTMLDivElement>) => TEMPORARY_ANY;
  maxHeight?: number;
};

const ModalContent: FC<ModalContentProps> = ({
  children,
  ModalHeader,
  ModalBody,
  ModalActions,
  onClick,
  maxHeight,
}) => {
  if (children) {
    return (
      <div
        className={styles.modalContent}
        style={{ maxHeight: maxHeight || 'auto' }}
        onClick={onClick}
      >
        {children}
      </div>
    );
  } else {
    return (
      <div
        className={styles.modalContent}
        style={{ maxHeight: maxHeight || 'auto' }}
        onClick={onClick}
      >
        {!!ModalHeader && (
          <div className={styles.modalHeader}>
            <ModalHeader />
          </div>
        )}
        {!!ModalBody && (
          <div className={styles.modalBody}>
            <ModalBody />
          </div>
        )}
        {!!ModalActions && (
          <div className={styles.modalActions}>
            <ModalActions />
          </div>
        )}
      </div>
    );
  }
};

export default ModalContent;
