import React from 'react';

export type Props = {
  progress: number | null;
  size?: number;
};

const clamp = (v: number, min: number, max: number): number => {
  if (v < min) {
    return min;
  }
  if (v > max) {
    return max;
  }
  return v;
};

function CircularProgressBar({ progress, size = 20 }: Props) {
  const isDefinedProgress = progress !== null;

  return (
    <svg width={size} height={size}>
      <circle
        stroke='#D5DBDF'
        strokeWidth={2}
        fill='none'
        r={size / 2 - 1}
        cx={size / 2}
        cy={size / 2}
      />
      <circle
        stroke='var(--color-primary)'
        strokeLinecap='round'
        strokeWidth={2}
        pathLength='1'
        style={{
          strokeDasharray: 1,
          strokeDashoffset: clamp(
            1 - (isDefinedProgress ? progress : 0.6),
            0,
            1,
          ),
          transform: isDefinedProgress ? 'rotate(-90deg)' : '',
          transformOrigin: 'center',
        }}
        fill='none'
        r={size / 2 - 1}
        cx={size / 2}
        cy={size / 2}
      >
        {!isDefinedProgress && (
          <animateTransform
            attributeName='transform'
            attributeType='XML'
            type='rotate'
            from='90'
            to='450'
            dur='1s'
            repeatCount='indefinite'
          />
        )}
      </circle>
    </svg>
  );
}

export default CircularProgressBar;
