import React, { VFC } from 'react';
import { Trans } from 'react-i18next';
import {
  StyledPage,
  styles,
} from 'components/WhatsNew/WhatsNewContent/WhatsNewContent.style';
import Button from 'components/ui/Button/Button';

import WhatsNewImg from 'assets/images/whatsnew/whatsnew_hungarian.jpg';
import WhatsNewImg2x from 'assets/images/whatsnew/<EMAIL>';
import { WhatsNewProps } from '../types';
import { FeaturesKeyEnum } from '../consts';

const Hungarian: VFC<WhatsNewProps> = ({ style, onClick, isLast, t }) => (
  <StyledPage style={{ ...style }}>
    <div className={styles.semiPage}>
      <div className={styles.pageContent}>
        <div className={styles.textContent}>
          <h2 className={styles.title}>{t('whatsnew.hungarian.title')}</h2>
          <p className={styles.text}>
            <Trans>{t('whatsnew.hungarian.text')}</Trans>
          </p>
        </div>
        <Button className='btn btn-success fit-content' onClick={onClick}>
          {t(`whatsnew.${isLast ? 'end' : 'next'}.action_text`)}
        </Button>
      </div>
    </div>
    <div className={styles.semiPage}>
      <picture className={styles.pagePicture} style={{ marginRight: 40 }}>
        <source srcSet={`${WhatsNewImg} 1x, ${WhatsNewImg2x} 2x`} />
        <img src={WhatsNewImg} alt={t('whatsnew.hungarian.title') as string} />
      </picture>
    </div>
  </StyledPage>
);

const WhatsNewHungarian = {
  key: FeaturesKeyEnum.hungarian,
  component: Hungarian,
  filter: {
    locale: ['en'],
  },
};

export default WhatsNewHungarian;
