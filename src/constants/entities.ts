export const MethodByOperation = {
  fetchOne: 'get',
  fetchMany: 'get',
  update: 'put',
  create: 'post',
  createMany: 'post',
  patch: 'patch',
  delete: 'delete',
};

export const EntityUrls: EntityUrlsType = {
  user: 'users',
  season: 'seasons',
  field: 'fields-users',
  fieldsDataLayer: 'fields-data-layers',
  fieldsDataLayerProperty: 'fields-data-layers-properties',
  fieldsDataOperation: 'fields-data-operations',
  fieldsDataElectroconductivity: 'electroconductivity-analysis-results',
  fieldsDataLayerValue: 'fields-data-layers-values',

  // http://127.0.0.1:8080/v2/?include%5B%5D=layer.properties&filter%7Blayer.source.upload%7D=828ae079-e7df-4eec-86dd-1df2dec80d98
  fieldSeason: 'fields-users-seasons',
  workspace: 'workspaces',
  workspaceAccount: 'workspace-account',
  membership: 'workspaces-memberships',
  member: ({ urlId }) => `workspaces/${urlId}/memberships`,
  memberInvitation: path => {
    if (path?.operation) {
      return `workspaces/${path.urlId}/invitations/${path.operation}`;
    }
    return `workspaces/${path.urlId}/invitations`;
  },
  dealerWorkspace: 'vendor/client-relationships',

  dealerInvitation: path => {
    if (path?.operation) {
      return `vendor/client-invitations/${path.urlId}/${path.operation}`;
    }

    return 'vendor/client-invitations';
  },
  consultant: 'vendor/consultants',
  consultantInvitation: path => {
    if (path?.operation) {
      return `vendor/consultant-invitations/${path.urlId}/${path.operation}`;
    }
    return 'vendor/consultant-invitations';
  },
  vendor: 'vendor',

  paInfo: 'fields-users-seasons-pa-infos',

  sharingCode: 'sharing-codes',
  fieldCrop: 'fields-users-seasons-crops',
  note: 'notes',
  noteIssue: 'notes-issues',
  noteImage: 'notes-images',
  phenophase: 'fields-users-seasons-crops-phenophases',
  phenophaseImage: 'fields-users-seasons-crops-phenophases-images',
  ndvi: 'fields-users-seasons-ndvi',
  recommendation: 'crop-rotation/recommendations',
  crop: 'crops',
  country: 'countries',
};

export type Path = Record<string, string> & { urlId: string };
export type EntityUrlsCallback = (path: Path) => string;

export type EntityUrlsType = Record<string, string | EntityUrlsCallback>;

export type EntityType = keyof typeof EntityUrls;
export type EntityRelations = {
  entityType: EntityType;
  field: string;
};

export type EntityEmbeds = {
  entityType: EntityType;
  field: string;
  multiple?: boolean;
};

export type EntityChild = {
  fieldInChild: string;
  field: string;
  entityType: EntityType;
};

export type EntityGraphType = {
  [key in EntityType]: {
    plural: string;

    parent?: EntityRelations;
    children?: EntityChild[];

    relations: EntityRelations[];
    embeds: EntityEmbeds[];

    embedLink?: EntityEmbeds[];
    fieldsToExclude?: string[];

    embedded?: {
      entity: EntityType;
      field: string;
    };
  };
};

export const EntityGraph: EntityGraphType = {
  user: {
    plural: 'users',
    relations: [],
    embeds: [],
  },
  fieldsDataLayerValue: {
    plural: 'fieldsDataLayerValues',
    relations: [],
    embeds: [],
  },
  fieldsDataLayer: {
    plural: 'fieldsDataLayers',
    relations: [],
    embeds: [],
    embedLink: [
      {
        entityType: 'fieldsDataLayerProperty',
        field: 'properties',
        multiple: true,
      },
    ],
  },
  fieldsDataLayerProperty: {
    plural: 'fieldsDataLayerProperties',
    relations: [],
    embeds: [],
  },
  fieldsDataOperation: {
    plural: 'fieldsDataOperations',
    relations: [],
    embeds: [],
  },
  fieldsDataElectroconductivity: {
    plural: 'fieldsDataElectroconductivity',
    relations: [],
    embeds: [],
  },
  workspace: {
    plural: 'workspaces',
    relations: [],
    embeds: [
      {
        entityType: 'workspaceAccount',
        field: 'workspace_account',
      },
    ],
  },
  workspaceAccount: {
    plural: 'workspaceAccount',
    relations: [],
    embeds: [],
  },
  membership: {
    plural: 'memberships',
    relations: [
      {
        entityType: 'workspace',
        field: 'workspace_id',
      },
    ],
    embeds: [],
  },
  member: {
    plural: 'members',
    relations: [
      {
        entityType: 'workspace',
        field: 'workspace_id',
      },
    ],
    embeds: [],
  },
  memberInvitation: {
    plural: 'memberInvitations',
    relations: [
      {
        entityType: 'workspace',
        field: 'workspace_id',
      },
    ],
    embeds: [],
  },
  dealerInvitation: {
    plural: 'dealerInvitations',
    relations: [
      {
        entityType: 'consultant',
        field: 'assigned_consultant_uuid',
      },
    ],
    embeds: [],
  },
  dealerWorkspace: {
    plural: 'dealerWorkspaces',
    relations: [
      {
        entityType: 'workspace',
        field: 'workspace_uuid',
      },
      {
        entityType: 'consultant',
        field: 'assigned_consultant_uuid',
      },
    ],
    embeds: [],
  },
  consultant: {
    plural: 'consultants',
    relations: [],
    embeds: [],
  },
  consultantInvitation: {
    plural: 'consultantInvitations',
    relations: [],
    embeds: [],
  },
  vendor: {
    plural: 'vendors',
    relations: [],
    embeds: [],
  },
  paInfo: {
    plural: 'paInfos',
    relations: [
      {
        entityType: 'fieldSeason',
        field: 'field_user_season_uuid',
      },
    ],
    embeds: [],
  },
  season: {
    plural: 'seasons',
    relations: [],
    embeds: [],
  },
  sharingCode: {
    plural: 'sharingCode',
    relations: [
      {
        entityType: 'note',
        field: 'note_id',
      },
      {
        entityType: 'fieldSeason',
        field: 'field_user_season_id',
      },
    ],
    embeds: [],
  },
  field: {
    plural: 'fields',
    relations: [],
    embeds: [],
  },
  fieldSeason: {
    plural: 'fieldSeasons',
    relations: [
      {
        entityType: 'field',
        field: 'field_user_id',
      },
      {
        entityType: 'season',
        field: 'season_id',
      },
    ],
    embeds: [
      {
        entityType: 'field',
        field: 'field_user',
      },
      {
        entityType: 'season',
        field: 'season',
      },
      {
        entityType: 'fieldCrop',
        field: 'crops',
      },
      {
        entityType: 'paInfo',
        field: 'active_pa_info',
      },
    ],
  },
  fieldCrop: {
    plural: 'fieldCrops',
    parent: {
      entityType: 'fieldSeason',
      field: 'field_user_season_id',
    },
    relations: [
      {
        entityType: 'fieldSeason',
        field: 'field_user_season_id',
      },
    ],
    embeds: [],
  },
  note: {
    plural: 'notes',
    relations: [
      {
        entityType: 'season',
        field: 'season_id',
      },
    ],
    embeds: [
      {
        entityType: 'noteIssue',
        field: 'issues',
        multiple: true,
      },
      {
        entityType: 'noteImage',
        field: 'images',
        multiple: true,
      },
    ],
  },
  noteIssue: {
    plural: 'issues',
    relations: [
      {
        entityType: 'note',
        field: 'note_id',
      },
    ],
    embeds: [],
  },
  noteImage: {
    plural: 'images',
    relations: [
      {
        entityType: 'note',
        field: 'note_id',
      },
    ],
    embeds: [],
  },
  phenophase: {
    plural: 'phenophases',
    relations: [
      {
        entityType: 'fieldCrop',
        field: 'crop_id',
      },
    ],
    embeds: [],
  },
  phenophaseImage: {
    plural: 'images',
    relations: [
      {
        entityType: 'phenophase',
        field: 'phenophase_id',
      },
    ],
    embeds: [],
  },
  ndvi: {
    plural: 'ndvis',
    relations: [
      {
        entityType: 'fieldSeason',
        field: 'field_user_season_id',
      },
    ],
    embeds: [],
  },
  /**
   predictedNDVI: {
    plural: 'predictedNDVIs',
    relations: [
      {
        entityType: 'predictedField',
        field: 'predicted_field_id',
      },
    ],
    embeds: [],
  },
   predictedField: {
    plural: 'predictedFields',
    relations: [],
    embeds: [],
  },
   reverseGeocode: {
    relations: [],
    embeds: [],
  },
   */
  crop: {
    plural: 'crops',
    relations: [],
    embeds: [],
  },

  country: {
    plural: 'countries',
    relations: [],
    embeds: [],
  },

  recommendation: {
    plural: 'recommendations',
    relations: [
      {
        entityType: 'fieldSeason',
        field: 'field_user_season_id',
      },
    ],
    embeds: [],
  },
};

// Add relation links
for (const entityType of Object.keys(EntityGraph)) {
  const entity = EntityGraph[entityType];

  if (entity) {
    const { plural, relations } = entity;
    for (const related of relations) {
      const relatedEntity = EntityGraph[related.entityType];

      if (relatedEntity) {
        if (!relatedEntity.children) {
          relatedEntity.children = [];
        }
        relatedEntity.children.push({
          fieldInChild: related.field,
          field: plural,
          entityType: entityType as EntityType,
        });
      }
    }

    const { embeds } = entity;
    entity.fieldsToExclude = embeds.map(v => v.field);

    if (entity.embedLink) {
      for (const embed of entity.embedLink) {
        const relatedEntity = EntityGraph[embed.entityType];

        if (relatedEntity) {
          relatedEntity.embedded = {
            entity: entityType,
            field: embed.field,
          };
        }
      }
    }
  }
}
