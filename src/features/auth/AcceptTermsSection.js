import React, { useState } from 'react';
import { Control } from 'react-redux-form';
import cx from 'classnames';
import { Trans } from 'react-i18next';
import { useTranslate } from 'utils/use-translate';

import Button from 'components/ui/Button/Button';
import Checkbox from 'components/ui/Checkbox/Checkbox';

const AcceptTermsSection = ({ validators, forceExpand }) => {
  const { t } = useTranslate();

  const link = (
    /* eslint-disable-next-line jsx-a11y/anchor-has-content */
    <a
      href={t('urls.privacy_policy')}
      target='_blank'
      rel='noopener noreferrer'
    />
  );
  const link2 = (
    /* eslint-disable-next-line jsx-a11y/anchor-has-content */
    <a
      href={t('urls.terms_of_use')}
      target='_blank'
      rel='noopener noreferrer'
    />
  );
  const [opened, setOpened] = useState(forceExpand);
  return (
    <div
      className={cx('onesoil-hello-rules', {
        __active: opened,
      })}
    >
      <div className='onesoil-hello-rules__content'>
        <Control.checkbox
          model='.accept_terms'
          component={Checkbox}
          validators={validators}
        >
          <Trans components={[link, link2]}>
            {'auth.terms.label.withlinks'}
          </Trans>
        </Control.checkbox>
      </div>
      {!forceExpand && (
        <Button
          className='onesoil-hello-rules__showmore'
          onClick={() => setOpened(!opened)}
        >
          {t(opened ? 'auth.terms.collapse' : 'auth.terms.expand')}
        </Button>
      )}
    </div>
  );
};

export default AcceptTermsSection;
