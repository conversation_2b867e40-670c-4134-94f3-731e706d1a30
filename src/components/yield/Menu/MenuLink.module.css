.link {
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 7px 8px;
  font-size: 14px;
  line-height: 18px;
  font-weight: 400;
  transition: background-color 0.3s;
  white-space: nowrap;
  margin: 0 8px;
  border-radius: 8px;
}

.link[disabled] {
  cursor: not-allowed;
  opacity: 0.5;
}

.theme__default {
  color: var(--color-black);
  background: white;
}

.theme__default:hover,
.theme__default.link__active {
  background-color: var(--color-grey-10);
  color: var(--color-black);
}

.theme__noHover {
  color: var(--color-black);
  background: white;
}

.theme__danger {
  color: var(--color-red);
  background: white;
}

.theme__danger:hover,
.theme__danger.link__active {
  background-color: var(--color-grey-10);
  color: var(--color-red);
}

.theme__dark {
  color: white;
  background: var(--color-black);
}

.theme__dark:hover,
.theme__dark.link__active {
  color: white;
  background-color: black;
}

