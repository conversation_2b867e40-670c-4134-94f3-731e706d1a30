import DefaultFileLegend from './default/legend';
import { getDefaultColorScheme } from './default/getColorScheme';

import OcenaFileLegend from './ocena/legend';
import { isOcenaColorScheme } from './ocena/checker';
import { getOcenaColorScheme } from './ocena/getColorScheme';

import SoilAnalysisFileLegend from './soilAnalysis/legend';
import { isSoilAnalysisColorScheme } from './soilAnalysis/checker';
import { getSoilAnalysisColorScheme } from './soilAnalysis/getColorScheme';
import { ColorSchemeEnum, ViewSchemeConfig } from './types';
import { FileColumn, FileFullData } from 'types/filesState';

const viewSchemeMap = {
  ocena: {
    type: ColorSchemeEnum.ocena,
    checker: isOcenaColorScheme,
    legend: OcenaFileLegend,
    colorSchemeGetter: getOcenaColorScheme,
  },
  analysis: {
    type: ColorSchemeEnum.soilAnalysis,
    checker: isSoilAnalysisColorScheme,
    legend: SoilAnalysisFileLegend,
    colorSchemeGetter: getSoilAnalysisColorScheme,
  },
  default: {
    type: ColorSchemeEnum.default,
    checker: () => true,
    legend: DefaultFileLegend,
    colorSchemeGetter: getDefaultColorScheme,
  },
};

export const getViewSchemeConfig = (
  column: FileColumn,
  file: FileFullData,
): ViewSchemeConfig => {
  const customScheme =
    column &&
    Object.values(viewSchemeMap).find(({ checker }) =>
      checker({
        column,
        file,
      }),
    );

  return customScheme || viewSchemeMap.default;
};
