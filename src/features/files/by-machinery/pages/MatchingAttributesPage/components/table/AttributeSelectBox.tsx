import React, { VFC } from 'react';
import { useSelector } from 'react-redux';

import entities from 'modules/entities';

import { CustomDropdownOption } from 'components/ui/CustomDropdown/types';
import { SelectBox } from 'components/ui/CustomDropdown/SelectBox';

import {
  Api<PERSON><PERSON><PERSON><PERSON><PERSON>ayer,
  DataTypesKey,
  MetaAttributesApi,
} from 'features/files/by-machinery/types';

import {
  getSetPropertyRequestName,
  OnSetPropertyType,
} from 'features/files/by-machinery/pages/MatchingAttributesPage/service/useSetAttribute';
import { OnSetUnitType } from 'features/files/by-machinery/pages/MatchingAttributesPage/service/useSetUnit';

import { MethodSelectBox } from './MethodSelectBox';
import { UnitSelectBox } from './UnitSelectBox';

import { attributeSelectBoxWrapper, Skeleton } from './styles';
import { useSingletonSelector } from 'utils/optimization/useSingletonSelector';
import { deepEqualDecorator } from 'utils/optimization/deepEqualDecorator';
import { useDelayedState } from 'utils/optimization/useDelayedState';
import { OnSetMethodType } from '../../service';
import { useTranslate } from 'utils/use-translate';
import { EcUnitAttribute } from '../../consts';

type AttributeSelectorProps = {
  attribute: MetaAttributesApi;
  layer: ApiDataFieldLayer;
  isCustom?: boolean;
  dataType: string;
  onSetAttributeValue: OnSetPropertyType;
  onSetUnit: OnSetUnitType;
  onSetMethod: OnSetMethodType;
};

export const optionsCalculation = deepEqualDecorator(
  ([properties, allowed_types]: [
    ApiDataFieldLayer['properties'],
    MetaAttributesApi['allowed_types'],
  ]) => {
    return Object.values(properties)
      .filter(v => allowed_types.includes(v.type))
      .map<CustomDropdownOption>(property => ({
        id: property.name,
        label: property.name,
        subLabelItems: (property.sample_values || []).filter(
          s => typeof s === 'string',
        ) as string[],
      }))
      .sort((a, b) => a.id?.localeCompare(b.id));
  },
);

export const AttributeSelectBox: VFC<AttributeSelectorProps> = ({
  attribute,
  layer,
  isCustom,
  dataType,
  onSetAttributeValue,
  onSetUnit,
  onSetMethod,
}) => {
  const { t } = useTranslate();
  const properties = !isCustom
    ? layer.properties
    : layer.properties.filter(property => property.is_custom_target_name);
  const currentProperty = properties.find(
    property => property.target_name === attribute.name,
  );

  const value = currentProperty?.name;

  const options: CustomDropdownOption[] = useSingletonSelector(
    optionsCalculation,
    [layer.properties, attribute.allowed_types],
  );

  const status = useSelector(state =>
    entities.selectors.getEntityStatus(
      state,
      'fieldsDataLayerProperty',
      currentProperty?.uuid,
      'target_name',
    ),
  );

  const setRequestId = getSetPropertyRequestName(attribute.name, layer.uuid);
  const setRequest = useSelector(state =>
    entities.selectors.getRequestStatus(state, setRequestId),
  );

  const isElectroconductivity = dataType === DataTypesKey.electroconductivity;
  const isLoading = useDelayedState(
    setRequest.status === 'pending' || status === 'pending',
  );

  if (isLoading) {
    return <Skeleton />;
  }

  return (
    <>
      <div style={{ width: '100%' }}>
        <SelectBox
          options={options}
          value={value}
          wrapperClassName={attributeSelectBoxWrapper}
          onChange={value => {
            onSetAttributeValue(value, attribute.name, layer.uuid);
          }}
          unClearable={isElectroconductivity}
          rightText={
            isElectroconductivity ? EcUnitAttribute(attribute.name, t) : null
          }
          noFoundedText='import.matching.attributes.select.empty'
          withSearchBox
          placeholder='import.matching.attributes.select'
          searchPlaceholder='import.matching.attributes.select_search'
        />
      </div>
      {Boolean(value) && attribute.extra_fields?.method?.values.length > 0 && (
        <MethodSelectBox
          layer={layer}
          attribute={attribute}
          onSetMethod={onSetMethod}
        />
      )}
      {!isElectroconductivity && (
        <UnitSelectBox
          layer={layer}
          attribute={attribute}
          onSetUnit={onSetUnit}
          disabled={Boolean(value) && attribute.units.length === 0}
        />
      )}
    </>
  );
};

export const AttributeSelectBoxMemo = React.memo(AttributeSelectBox);
