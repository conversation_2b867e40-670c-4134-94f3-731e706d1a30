import React, { VFC } from 'react';

import Icon from 'components/Icon';

import Loader, { StyledLoaderSize } from 'components/progress/Loader';

import { FileProcessingStatus } from 'utils/import-fields';
import { useTranslate } from 'utils/use-translate';

import {
  BulkUploadItemRowStyle,
  BulkUploadItemStatusWrapper,
  BulkUploadItemDescriptionStyle,
  BulkUploadItemTitleStyle,
  BulkUploadItemErrorStyle,
  successButtonIconClass,
  failedButtonIconClass,
} from 'features/fields/BulkUpload/styles';

interface FileProcessingViewProps {
  name: string;
  status: FileProcessingStatus;
}

export const FileProcessingView: VFC<FileProcessingViewProps> = ({
  name,
  status,
}) => {
  const { t } = useTranslate();

  function renderIcon() {
    switch (status) {
      case FileProcessingStatus.WAITING_FOR_PROCESSING:
        return <Icon name='green-circle' className={successButtonIconClass} />;
      case FileProcessingStatus.PROCESSING:
        return <Loader size={StyledLoaderSize.S} />;
      case FileProcessingStatus.PROCESSED:
        return <Icon name='check' className={successButtonIconClass} />;
      case FileProcessingStatus.FAILED:
        return <Icon name='alert-circle' className={failedButtonIconClass} />;
      default:
        return <Icon name='green-circle' className={successButtonIconClass} />;
    }
  }

  function renderDescription() {
    switch (status) {
      case FileProcessingStatus.WAITING_FOR_PROCESSING:
        return t('fields.bulk_upload.processing.waiting');
      case FileProcessingStatus.PROCESSING:
        return t('fields.bulk_upload.processing.in_progress');
      case FileProcessingStatus.PROCESSED:
        return t('fields.bulk_upload.processing.success');
      case FileProcessingStatus.FAILED:
        return (
          <BulkUploadItemErrorStyle>
            {t(`fields.bulk_upload.processing.failed`)}
          </BulkUploadItemErrorStyle>
        );
      default:
        return t('fields.bulk_upload.processing.waiting');
    }
  }

  return (
    <BulkUploadItemRowStyle key={name}>
      <BulkUploadItemStatusWrapper>{renderIcon()}</BulkUploadItemStatusWrapper>
      <div>
        <BulkUploadItemTitleStyle>{name}</BulkUploadItemTitleStyle>
        <div>
          <BulkUploadItemDescriptionStyle>
            {renderDescription()}
          </BulkUploadItemDescriptionStyle>
        </div>
      </div>
    </BulkUploadItemRowStyle>
  );
};
