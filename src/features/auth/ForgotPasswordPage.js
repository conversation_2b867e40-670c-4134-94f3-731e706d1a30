import React, { useEffect } from 'react';
import { connect, useSelector } from 'react-redux';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';

import cx from 'classnames';

import Button from 'components/ui/Button/Button';
import LinkButton from 'components/LinkButton';
import BodyClassName from 'components/BodyClassName';
import Logo from 'components/Logo';

import auth from 'modules/auth';
import TextInput from 'components/forms/TextInput';
import { styled } from 'linaria/react';
import api from 'modules/api';
import useSaga from 'utils/use-saga';
import { useTranslate } from 'utils/use-translate';
import { call, take } from 'redux-saga/effects';
import { resolved } from 'sagas/global/api';

const ErrorLabel = styled.div`
  margin-top: 2px;
  font-size: 12px;
  color: #ff3b30;
`;

const ForgotPasswordPage = ({ history, onForgot }) => {
  const { t, i18n } = useTranslate();
  const schema = yup.object().shape({
    login: yup
      .string()
      .email(t('change_email.new_email.error.invalid'))
      .required(t('change_email.new_email.error.blank')),
  });

  const submitRequest = useSelector(state =>
    api.selectors.getRequestStatus(state, 'forgot-password'),
  );

  const { register, handleSubmit, errors, getValues, formState, trigger } =
    useForm({
      mode: 'onSubmit',
      resolver: yupResolver(schema),
    });

  useEffect(() => {
    if (formState.errors.login) {
      trigger();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [i18n.resolvedLanguage]);

  useSaga(function* () {
    yield take(resolved(auth.actions.forgot));

    yield call(
      [history, history.push],
      { pathname: '/auth/reset', search: history.location.search },
      { login: getValues('login') },
    );
  }, []);

  return (
    <div className={cx('onesoil-hello', { 'shake-error': !!errors.login })}>
      <BodyClassName className='onesoil-hello-page' />

      <div className='onesoil-hello__header'>
        <Logo />
      </div>
      <form onSubmit={handleSubmit(onForgot)}>
        <div className='form-group'>
          <label className='form-label' htmlFor='login'>
            {t('auth.email.label')}
          </label>
          <TextInput
            tabIndex={1}
            id='login'
            name='login'
            autoComplete='off'
            type='text'
            ref={register({ required: true })}
            clearable
            isValid={!errors.login}
            placeholder={t('auth.email.placeholder')}
          />
          {!!errors.login && <ErrorLabel>{errors.login.message}</ErrorLabel>}
        </div>

        <div className='form-group'>
          <Button
            className='btn btn-success btn-lg size-full'
            pending={submitRequest.status === 'pending'}
            type='submit'
          >
            {t(`auth.confirm.forgot`)}
          </Button>
        </div>

        <div className='onesoil-hello-backto'>
          <LinkButton
            onClick={() => {
              history.goBack();
            }}
          >
            {t('auth.back')}
          </LinkButton>
        </div>
      </form>
    </div>
  );
};

const mapDispatchToProps = {
  onForgot: auth.actions.forgot,
};

export default connect(null, mapDispatchToProps)(ForgotPasswordPage);
