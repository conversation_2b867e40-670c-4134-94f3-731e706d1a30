import React, { useCallback, useEffect, useRef, useState } from 'react';

import * as H from 'history';
import { useSelector, useDispatch } from 'react-redux';
import { FormProvider, useForm } from 'react-hook-form';

import saveFieldsSaga from 'features/fields/BulkUpload/saga/saveFieldsSaga';

import Saga from 'components/saga/Saga';

import Checkbox from 'components/ui/Checkbox/Checkbox';

// @ts-ignore
import ControlledMap from 'components/maps/ControlledMap';
import ZoomControls, {
  MinZoom,
  // @ts-ignore
} from 'features/platform/ZoomControls/ZoomControls';
// @ts-ignore
import DirectGoogleLayer from 'components/maps/DirectGoogleLayer';
// @ts-ignore
import RulerLayer from 'features/platform/RulerLayer/RulerEditor/RulerEditor';
// @ts-ignore
import UploadFieldsSideBar from 'features/fields/UploadFieldsSideBar';

import ModernSpinner from 'components/ui/ModernSpinner/ModernSpinner';

import { editOptions } from 'constants/map';

// @ts-ignore
import fields from 'modules/fields';

import { useTranslate } from 'utils/use-translate';

import { actions } from 'features/fields/BulkUpload/redux/actions';
import { selectors } from 'features/fields/BulkUpload/redux/selectors';
import {
  FieldsUsersFilesFeature,
  LocationState,
} from 'features/fields/BulkUpload/types';

import logEvent from 'sagas/global/logEvent';

import { FileFieldVectorGrid } from './FileFieldVectorGrid';
import { SaveFieldsStepActions } from './SaveFieldsStepActions';

interface SaveFieldsStepProps {
  history: H.History<LocationState>;
}

export const SaveFieldsStep = ({ history }: SaveFieldsStepProps) => {
  const { t } = useTranslate();
  const dispatch = useDispatch();

  const importedFields = useSelector(selectors.getImportedFields);

  const fileIds = useSelector(selectors.getUploadedFilesIds);

  const checkedIDs = useSelector(selectors.getCheckedIDs);
  const [highlighted, setHighlighted] = useState<number | null>(null);

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [isPending, setIsPending] = useState(false);

  const formMethods = useForm({
    defaultValues: importedFields,
    shouldUnregister: false,
  });

  const formFieldsReady = useRef(false);

  useEffect(() => {
    if (
      !formFieldsReady.current &&
      importedFields &&
      importedFields.length > 0
    ) {
      formMethods.reset(importedFields);
      formFieldsReady.current = true;
    }
  }, [importedFields, formMethods]);

  const handleCheck = useCallback(
    (event, id) => {
      const newIds = checkedIDs.includes(id)
        ? checkedIDs.filter(item => item !== id)
        : [...checkedIDs, id];

      dispatch(actions.setCheckedIDs(newIds));
    },
    [checkedIDs, dispatch],
  );

  const handleCheckAll = useCallback(
    (checked: boolean) => {
      const newIds = checked
        ? [...importedFields.map(fields => fields.id)]
        : [];

      dispatch(actions.setCheckedIDs(newIds));
    },
    [importedFields, dispatch],
  );

  const onSubmit = (allFields: FieldsUsersFilesFeature[]) => {
    logEvent('fields_import_save_fields', { fieldsCount: checkedIDs.length });
    dispatch(fields.actions.createFields(checkedIDs, allFields));
  };

  return (
    <>
      <Saga
        saga={saveFieldsSaga}
        history={history}
        onSetPending={setIsPending}
      />
      <div className='main-section__body' data-layout='horizontal'>
        <div className='soil-sidebar-create'>
          <div className='soil-sidebar-stickycontrols'>
            <div className='main-uploader-header'>
              <h2 className='main-uploader-header__title'>
                {t('fields.upload.save_fields.title')}
              </h2>
              <p className={'main-uploader-header__description'}>
                {t('fields.upload.save_fields.sub_title')}
              </p>
            </div>
            <div
              className='soil-sidebar-stickycontrols__group'
              data-extraspace='true'
            >
              <Checkbox
                checked={checkedIDs.length === importedFields.length}
                // @ts-ignore
                onChange={event => {
                  handleCheckAll(event.target.checked);
                }}
              >
                {t('fields.list.edit.select_all')}
              </Checkbox>
            </div>
          </div>
          {!importedFields.length && (
            <div className='soil-sidebar-empty'>
              <div className='soil-sidebar-empty__ico with_loader'>
                <ModernSpinner large />
              </div>
              <h2 className='soil-sidebar-empty__title'>
                {t('fields.loading.title')}
              </h2>
            </div>
          )}
          {!!importedFields.length && (
            <FormProvider {...formMethods}>
              <UploadFieldsSideBar
                importedFields={importedFields}
                checkedIDs={checkedIDs}
                highlighted={highlighted}
                handleCheck={handleCheck}
              />
            </FormProvider>
          )}
        </div>
        <div className='map-container'>
          <ControlledMap
            id='imported-fields'
            viewportHash='default'
            onViewportHashChange={() => {}}
            mapProps={{
              className: 'map-viewer',
              boxZoom: false,
              zoomControl: false,
              minZoom: MinZoom,
              editable: true,
              editOptions,
            }}
          >
            <DirectGoogleLayer />
            <RulerLayer />
            {fileIds.map(fileId => {
              return (
                <FileFieldVectorGrid
                  key={fileId}
                  fileId={fileId}
                  checkedIds={checkedIDs}
                  setHighlighted={id => setHighlighted(id)}
                />
              );
            })}
          </ControlledMap>
          <ZoomControls id='imported-fields' showLocation showRuler />
        </div>
      </div>
      <SaveFieldsStepActions
        disabled={!checkedIDs.length || !formFieldsReady.current}
        isPending={isPending}
        onCancel={() => {
          dispatch(actions.resetBulkUploadState());
        }}
        onSave={formMethods.handleSubmit(onSubmit)}
      />
    </>
  );
};
