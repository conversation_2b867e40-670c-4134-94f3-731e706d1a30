import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Pane } from 'react-leaflet';
import area from '@turf/area';

import Saga from 'components/saga/Saga';

import FloatingActions from 'components/ui/FloatingActions/FloatingActions';
import RenderAboveEverything from 'components/RenderAboveEverything';
import ToastContainer from 'features/platform/ToastContainer/ToastContainer';
import EmbeddedMap from 'features/platform/EmbeddedMap';
import EditFieldGeometryToolbar from './EditFieldGeometryToolbar';
import EditFieldLayerV2 from './EditFieldLayerV2';

import entities from 'modules/entities';
import settings from 'modules/settings';
import toasts from 'modules/toasts';

import forms from 'constants/forms';
import { formatTitle } from 'utils/fields-formatters';
import { formatTitle as formatSeasonTitle } from 'utils/seasons-formatters';
import { getViewportHash } from 'utils/get-root-scouting-url';
import { useTranslate } from 'utils/use-translate';
import { hasIntersections } from 'utils/field-has-intersection';
import editFieldGeometrySaga, {
  MaxFieldArea,
} from 'sagas/local/edit-field-geometry-v2';
import { VegetationFieldToolbar } from './ToolBar/VegetationFieldToolbar';
import SelectedFieldScouting from '../scouting/SelectedFieldScouting/SelectedFieldScouting';
import ZIndex from '../../constants/zindex';

const EditFieldGeometryPopupV2 = ({ match, history }) => {
  const { t } = useTranslate();
  const formatUnit = useSelector(settings.selectors.getUnitFormatter);
  const dispatch = useDispatch();

  const fieldSeason = useSelector(state =>
    entities.selectors.getByID(
      state,
      'fieldSeason',
      match.params.fieldSeasonId,
    ),
  );

  const field = useSelector(state =>
    entities.selectors.getByID(state, 'field', fieldSeason.field_user_id),
  );
  const season = useSelector(state =>
    entities.selectors.getByID(state, 'season', fieldSeason.season_id),
  );
  const updateRequest = useSelector(state =>
    entities.selectors.getRequestStatus(state, 'update-field-geometry'),
  );

  const [geometry, setGeometry] = useState(false);

  const hasSelfIntersection = hasIntersections(geometry);
  const fieldArea = geometry && area(geometry);
  const hasBigArea = fieldArea >= MaxFieldArea;

  const [mode, setMode] = useState(null);
  const [hasSelected, setHasSelected] = useState(false);
  const [changes, setChanges] = useState([]);

  const geometryEditor = useRef();

  let title = formatTitle(t, field);
  if (geometry) {
    title += `, ≈ ${formatUnit(fieldArea, 'm2', 'area')}`;
  }

  const onClose = useCallback(() => {
    history.goBack();
  }, [history]);

  // Display validation and mode toasts
  useEffect(() => {
    if (hasBigArea) {
      dispatch(
        toasts.actions.push(
          'field-editor',
          t('toasts.fields.too_large', {
            area: formatUnit(MaxFieldArea, 'm2', 'area'),
          }),
          {
            type: 'error',
            ttl: Infinity,
          },
        ),
      );
    } else if (hasSelfIntersection) {
      dispatch(
        toasts.actions.push(
          'field-editor',
          t(`fields.edit.geometry.has_kinks`),
          {
            type: 'error',
            ttl: Infinity,
          },
        ),
      );
    } else if (mode) {
      dispatch(
        toasts.actions.push(
          'field-editor',
          t(`fields.edit.geometry.${mode}.toast`),
          {
            type: 'success',
            ttl: Infinity,
          },
        ),
      );
    }
    if (geometry === null) {
      dispatch(
        toasts.actions.push(
          'field-editor',
          t(`fields.edit.geometry.no_geometry_toast`),
          {
            type: 'error',
            ttl: Infinity,
          },
        ),
      );
    }

    return () => {
      dispatch(toasts.actions.removeGroup('field-editor'));
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [mode, !!geometry, hasBigArea, hasSelfIntersection, dispatch]);

  return (
    <RenderAboveEverything>
      <Saga
        saga={editFieldGeometrySaga}
        fieldSeason={fieldSeason}
        geometry={geometry}
        onDone={onClose}
        onSetGeometry={setGeometry}
      />

      <div className='popup-edit-bound __opened'>
        <ToastContainer id='global' />
        <div className='popup-edit-bound__content'>
          <div className='popup-edit-bound-inner-wrapper'>
            <div className='popup-edit-bound-inner'>
              <VegetationFieldToolbar
                fieldId={'o' + field.id}
                className='popup-edit-bound-container__content'
              />
            </div>
            <div className='field-geometry-toolbar-wrapper'>
              <EditFieldGeometryToolbar
                cutMode={mode === 'cut'}
                selectMode={mode === 'select'}
                hasSelected={mode === 'select' && hasSelected}
                canUndo={changes.length !== 0}
                onCutModeChange={cutMode => {
                  setMode(cutMode ? 'cut' : null);
                }}
                onSelectModeChange={selectMode => {
                  setMode(selectMode ? 'select' : null);
                }}
                onRemoveSelected={() => {
                  geometryEditor.current.removeSelectedMarkers();
                }}
                onUndo={() => {
                  const prevGeometry = changes[changes.length - 1];
                  setChanges(changes.slice(0, -1));
                  setGeometry(prevGeometry);
                }}
              />
            </div>
          </div>
          <div className='popup-edit-bound__map'>
            <EmbeddedMap
              showControls
              id='edit-field'
              initialViewport={getViewportHash(history.location.pathname)}
            >
              <SelectedFieldScouting fieldId={'o' + field.id} hideBorders />
              <Pane
                className='ndvi-pane'
                style={{ zIndex: ZIndex.SelectionPane + 100 }}
              >
                <EditFieldLayerV2
                  ref={geometryEditor}
                  editMode={mode}
                  geometry={geometry}
                  hasErrors={hasBigArea || hasSelfIntersection}
                  onSelectionChange={count => {
                    setHasSelected(count !== 0);
                  }}
                  onChange={nextGeometry => {
                    setChanges([...changes, geometry]);
                    setGeometry(nextGeometry);
                  }}
                />
              </Pane>
            </EmbeddedMap>
          </div>
        </div>
        <FloatingActions
          model={forms.fieldGeometry}
          title={t('fields.edit.geometry.action', {
            season: formatSeasonTitle(t, season),
          })}
          entityName={title}
          submitLabel={t('forms.save')}
          cancelLabel={t('forms.cancel')}
          submitDisabled={!geometry || hasBigArea || hasSelfIntersection}
          pending={updateRequest.status === 'pending'}
          onCancel={onClose}
          onSubmit={() => {
            dispatch(
              entities.actions.request('update-field-geometry', [
                {
                  type: 'patch',
                  entityType: 'fieldSeason',
                  path: { id: fieldSeason.id },
                  body: { geom: geometry },
                },
              ]),
            );
          }}
        />
      </div>
    </RenderAboveEverything>
  );
};

export default EditFieldGeometryPopupV2;
