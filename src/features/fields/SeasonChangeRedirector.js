import React from 'react';
import { Redirect, useHistory } from 'react-router-dom';
import { useSelector, useStore } from 'react-redux';

import seasons from 'modules/seasons';
import entities from 'modules/entities';

import getRootScoutingUrl from 'utils/get-root-scouting-url';

/*
  Render this component anywhere to automatically redirect to matching fieldSeason
  when user switches to another season
*/

const SeasonChangeRedirector = ({ fieldSeason }) => {
  const history = useHistory();
  const store = useStore();

  const currentSeason = useSelector(seasons.selectors.getCurrent);

  if (currentSeason && fieldSeason.season_id !== currentSeason.id) {
    const fieldSeasons = entities.selectors.getIndexed(
      store.getState(),
      'fieldSeason',
      'field_user_id',
      fieldSeason.field_user_id,
    );
    const targetFieldSeason = fieldSeasons.find(
      fs => fs.season_id === currentSeason.id,
    );

    const baseUrl = getRootScoutingUrl(history.location.pathname);

    return (
      <Redirect
        to={
          targetFieldSeason
            ? {
                ...history.location,
                pathname: history.location.pathname.replace(
                  `${fieldSeason.id}`,
                  `${targetFieldSeason.id}`,
                ),
              }
            : baseUrl
        }
      />
    );
  }

  return null;
};

export default SeasonChangeRedirector;
