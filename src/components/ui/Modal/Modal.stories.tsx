import React, { ReactNode } from 'react';

import Modal from 'components/ui/Modal/Modal';
import Button from 'components/ui/Button/Button';

export default {
  title: 'ui/Modal',
  component: Modal,
  decorators: [(storyFn: () => ReactNode) => <div>{storyFn()}</div>],
};

export const Default = () => (
  <Modal
    show
    ModalHeader={() => <div>Header</div>}
    ModalBody={() => <div>Body</div>}
    ModalActions={() => (
      <>
        <Button className='btn btn-primary btn-lg' onClick={() => {}}>
          Cancel
        </Button>
        <Button className='btn btn-success btn-lg' onClick={() => {}}>
          Confirm
        </Button>
      </>
    )}
    width={400}
    onClose={() => {}}
    withCloseButton
  />
);

export const Old = () => (
  <div className='modal modal-editseasons __opened'>
    <div className='modal-outer'>
      <div className='modal-editseasons-content'>
        <div className='modal-editseasons-content__header'>Title</div>
        <div className='modal-editseasons-content__body'>
          <div className='form-group'>Email</div>
        </div>
        <div className='modal-editseasons-content__actions'>
          <Button className='btn btn-primary btn-lg' onClick={() => {}}>
            Cancel
          </Button>
          <Button className='btn btn-success btn-lg' onClick={() => {}}>
            Confirm
          </Button>
        </div>
      </div>
    </div>
  </div>
);
