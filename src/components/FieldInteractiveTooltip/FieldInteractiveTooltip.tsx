import React from 'react';
import cx from 'classnames';

import RenderAboveEverything from 'components/RenderAboveEverything';

import { TooltipContainer, TooltipDot } from './FieldInteractiveTooltip.style';

interface FieldInteractiveTooltipProps extends React.PropsWithChildren<{}> {
  isVisible: boolean;
  position: [number, number];
  withDot?: boolean;
  aboveEverything?: boolean;
}

export const FieldInteractiveTooltip = ({
  isVisible,
  position,
  withDot,
  aboveEverything,
  children,
}: FieldInteractiveTooltipProps) => {
  if (!isVisible || children === null) {
    return null;
  }

  const [translateX, translateY] = position;

  const Tooltip = (
    <TooltipContainer
      translateX={translateX}
      translateY={translateY}
      style={{
        pointerEvents: 'none',
      }}
    >
      {withDot ? <TooltipDot /> : null}
      <div
        className={cx(
          'leaflet-tooltip',
          'tooltip-dark',
          'leaflet-tooltip-top',
          'leaflet-interactive-tooltip',
        )}
      >
        {children}
      </div>
    </TooltipContainer>
  );

  return aboveEverything ? (
    <RenderAboveEverything>{Tooltip}</RenderAboveEverything>
  ) : (
    Tooltip
  );
};
