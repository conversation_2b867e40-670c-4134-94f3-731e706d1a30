import React, { useEffect, memo } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Controller, useWatch } from 'react-hook-form';
import { css } from 'linaria';
import area from '@turf/area';
import cx from 'classnames';

import VarietyInputRow from './VarietyInputRow';
import ClearableInput from 'components/ClearableInput';
import Button from 'components/ui/Button/Button';
import Icon from 'components/Icon';
import MaxFieldAreaTooltip from './MaxFieldAreaTooltip';

import fields from 'modules/fields';
import settings from 'modules/settings';

import { MaxFieldArea } from 'sagas/local/edit-field-geometry';

import { useTranslate } from 'utils/use-translate';
import { CustomDropdown } from 'components/ui/CustomDropdown/CustomDropdown';
import { FieldImage } from './FieldImage';

const fieldListContent = css`
  flex: 1 1 auto;
  min-width: 0;
  font-size: 14px;
  padding: 5px 0 0;
`;

const removeButton = css`
  position: absolute;
  top: 0;
  right: 0;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border-width: 0;
  font-size: 0;
  vertical-align: middle;
  color: #fff;
  padding: 0;
  background-color: #a5b2bc;
  transition: background-color 0.1s linear;
  & + & {
    margin-left: 10px;
  }
  &:hover {
    background-color: #5e5e5e;
  }
`;

const row = css`
  width: 100%;

  & + & {
    margin-top: 10px;
  }
`;

const EditableFieldListItem = ({
  index,
  fieldId,
  history,
  readOnly,
  control,
  nextIndex,
  setShowPopup,
  setValue,
  getValues,
}) => {
  const field = useSelector(state => fields.selectors.getByID(state, fieldId));
  const { t } = useTranslate();
  const formatUnit = useSelector(settings.selectors.getUnitFormatter);
  const sortedCropTypes = useSelector(fields.selectors.getSortedCropTypes);
  const cropTypes = ['propose', ...sortedCropTypes];

  const dispatch = useDispatch();

  const watchedTitle = useWatch({
    control,
    name: `selectedFields[${index}].title`,
    defaultValue: t('fields.default_title', {
      index: nextIndex + index,
    }),
  });

  const watchedCrop = useWatch({
    control,
    name: `selectedFields[${index}].crops[0].crop`,
    defaultValue: null,
  });

  useEffect(() => {
    if (!getValues(`selectedFields[${index}].titleChanged`)) {
      setValue(
        `selectedFields[${index}].title`,
        t('fields.default_title', {
          index: nextIndex + index,
        }),
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [index]);

  const fieldArea = area(field.geometry);

  return (
    <div
      data-scroll-to={`field-${fieldId}`}
      className={cx('soil-fields-list__list-item', '__nohover')}
    >
      <div
        className={cx('soil-fields-list__item soil-fields-list__column', {
          __selected: false,
        })}
      >
        <div className='soil-fields-list__row'>
          <FieldImage className='soil-fields-list__pic' field={field} />
          <div className={fieldListContent}>
            <h2 className='soil-fields-list__header'>{watchedTitle}</h2>
            <span
              className={
                fieldArea >= MaxFieldArea ? 'c-exclamation' : 'c-neutral'
              }
            >
              ≈ {formatUnit(fieldArea, 'm2', 'area')}
              {fieldArea >= MaxFieldArea && (
                <MaxFieldAreaTooltip t={t} formatUnit={formatUnit} />
              )}
            </span>
          </div>
          <Button
            className={removeButton}
            onClick={() => {
              dispatch(fields.actions.removeSelectedId(fieldId));
            }}
          >
            <Icon width={10} height={12} name='trashcan' />
          </Button>
        </div>
        <div className={row}>
          <Controller
            control={control}
            name={`selectedFields[${index}].title`}
            defaultValue={t('fields.default_title', {
              index: nextIndex + index,
            })}
            render={({ value, onChange }) => (
              <ClearableInput
                className='form-input size-full'
                type='text'
                value={value}
                onChange={title => {
                  onChange(title);
                }}
                onBlur={() => {
                  if (
                    getValues(`selectedFields[${index}].title`) !==
                    t('fields.default_title', {
                      index: nextIndex + index,
                    })
                  ) {
                    setValue(`selectedFields[${index}].titleChanged`, true);
                  }
                }}
              />
            )}
          />
        </div>
        <div className={row}>
          <Controller
            control={control}
            name={`selectedFields[${index}].crops[0].crop`}
            defaultValue={null}
            render={({ value, onChange }) => (
              <CustomDropdown
                className='form-select'
                withSearchBox={cropTypes.length >= 5}
                searchBoxProps={{
                  placeholder: t('suggest.crops_type.search.placeholder'),
                }}
                placeholder={t('fields.select')}
                options={cropTypes.map(type => ({
                  label: t(`crop.${type}`),
                  id: type,
                }))}
                ignoreIDs={['propose']}
                renderValue={() => {
                  let label = 'fields.select';
                  if (value && value !== 'propose') {
                    label = `crop.${value}`;
                  }
                  return (
                    <div className='form-select__value u-select__value'>
                      {t(label)}
                    </div>
                  );
                }}
                onChange={value => {
                  if (value === 'propose') {
                    setShowPopup(true);
                  } else {
                    onChange(value);
                  }
                }}
              />
            )}
          />
        </div>
        <div className={row}>
          <Controller
            control={control}
            name={`selectedFields[${index}].crops[0].variety`}
            defaultValue={null}
            render={({ value, onChange }) => (
              <VarietyInputRow
                index={index}
                crop={watchedCrop}
                setValue={setValue}
                value={value}
                placeholder={t('fields.edit.crops.variety_hybrid')}
                onChange={onChange}
              />
            )}
          />
        </div>
      </div>
    </div>
  );
};

export default memo(EditableFieldListItem);
