import React, { VFC } from 'react';

import { TableRow } from 'components/ui/DragAndDropTable/TableRow/TableRow';
import { TableRowDragPreview } from 'components/ui/DragAndDropTable/TableRow/TableRowDragPreview/TableRowDragPreview';
import { DragAndDropTableProps } from 'components/ui/DragAndDropTable/DragAndDropTable';
import {
  ColumnInfo,
  TableRowProps,
} from 'components/ui/DragAndDropTable/TableRow/types';

import { ApiDataFieldOperations } from 'features/files/by-machinery/types/server';
import { FieldEntityType } from 'types/fields';

import { CellOperation, CellOperationProps } from './Cells/CellOperation';
import { CellText } from './Cells/CellText';
import { CellArea } from './Cells/CellArea';
import { CellCalendar } from './Cells/CellCalendar';
import { OnSeSetDateType } from '../../service/useSetDate';

export type RowOperationProps = {
  operation: ApiDataFieldOperations;
  field: FieldEntityType;
  columnsInfo: DragAndDropTableProps['columnsInfo'];
  childrenOperations?: ApiDataFieldOperations[];
  dateFormat: TEMPORARY_ANY;
  isVisible: boolean;
  setParent: TableRowProps['onChildrenDrop'];
  setType?: CellOperationProps['setType'];
  setDate: OnSeSetDateType;
  setChecked?: CellOperationProps['setChecked'];
} & Omit<TableRowProps, 'id'>;

export const RowOperation: VFC<RowOperationProps> = ({
  childrenOperations = [],
  operation,
  columnsInfo,
  field,
  isVisible,
  setParent,
  setDate,
  setType,
  setChecked,
  ...otherProps
}) => {
  if (!isVisible) {
    return null;
  }
  const groupOperations =
    childrenOperations?.length > 0
      ? [operation].concat(childrenOperations)
      : [operation];

  return (
    <TableRow
      columnsInfo={columnsInfo}
      id={operation.uuid}
      onChildrenDrop={setParent}
      isParent={childrenOperations.length > 0}
      dragPreview={
        <TableRowDragPreview>
          <CellOperation
            hovered={false}
            operation={operation}
            setType={setType}
          />
        </TableRowDragPreview>
      }
      {...otherProps}
    >
      {({ hovered }) =>
        Object.values(columnsInfo).map((info: ColumnInfo) => {
          if (info.key === 'area') {
            return (
              <CellArea
                field={field}
                operations={groupOperations}
                hovered={hovered}
              />
            );
          }

          if (info.key === 'date') {
            return (
              <CellCalendar
                setDate={setDate}
                hovered={hovered}
                operations={groupOperations}
              />
            );
          }

          if (info.key === 'operation') {
            return (
              <div
                style={{
                  marginLeft: otherProps.isChild ? '28px' : 0,
                }}
              >
                <CellOperation
                  hovered={hovered}
                  operation={operation}
                  setType={setType}
                  setChecked={setChecked}
                  childrenOperations={childrenOperations}
                />
              </div>
            );
          }

          return (
            <CellText
              key={info.key}
              hovered={hovered}
              operations={groupOperations}
              propertyName={info.key}
            />
          );
        })
      }
    </TableRow>
  );
};
