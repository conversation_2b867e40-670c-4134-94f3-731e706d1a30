import React from 'react';
import { css } from 'linaria';
import { styled } from 'linaria/react';
import { animated } from 'react-spring';

export const styles = {
  modalContent: css`
    display: flex;
    flex-direction: column;
  `,
  modalHeader: css`
    position: relative;
    flex: 0 0 auto;
    font-size: 24px;
    font-weight: 700;
    line-height: 1.25;
    padding: 20px 25px 14px;
  `,
  defaultModalHeader: css`
    padding: 20px 25px 14px;
  `,
  newModalHeader: css`
    padding: 24px;
  `,
  modalBody: css`
    position: relative;
    flex: 1 1 auto;
    overflow-y: auto;
  `,
  defaultModalBody: css`
    padding: 0 25px 0;
  `,
  newModalBody: css`
    padding: 0 24px 0;
  `,
  modalActions: css`
    position: relative;
    flex: 0 0 auto;
    display: flex;

    .btn {
      flex: 1.5 1;
      margin-left: 10px;
    }
  `,
  defaultModalActions: css`
    padding: 10px 10px 10px 0;
  `,
  newModalActions: css`
    padding: 0 24px 24px 24px;
  `,
  closeButton: css`
    position: absolute;
    right: 25px;
    top: 25px;
    background: none;
    padding: 4px;
    border: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  `,
};

export const StyledOverlay = styled(animated.div)`
  position: fixed;
  z-index: 999;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
`;

type StyledModalProps = {
  withContentPadding?: boolean;
  width: Nullable<number>;
};

export const StyledModal = styled(({ withContentPadding, ...rest }) => (
  <animated.div {...rest} />
))<StyledModalProps>`
  position: relative;
  padding: ${props => (props.withContentPadding ? '25px' : 0)};
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.25);
  border-radius: 8px;
  overflow: hidden;
  background-color: #fff;
  width: ${props => (props.width ? '100%' : 'auto')};
  max-width: ${props => (props.width ? `${props.width}px` : 'auto')};
`;
