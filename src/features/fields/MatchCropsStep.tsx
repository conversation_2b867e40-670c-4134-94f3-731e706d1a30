import React, { useState, VFC } from 'react';
import { RouteComponentProps } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';

import { CropsMatchMapType, MatchCrops } from 'components/matching/MatchCrops';
import logEvent from 'sagas/global/logEvent';
// @ts-ignore
import fields from 'modules/fields';
// @ts-ignore
import matchCropsSaga from 'sagas/local/match-crops';

import getRootScoutingUrl from 'utils/get-root-scouting-url';
import Button from 'components/ui/Button/Button';
import SmartButton from 'components/ui/Button/SmartButton';
import { FloatingActionsStyle } from 'components/ui/FloatingActions/styles';
import Saga from 'components/saga/Saga';
import { useTranslate } from 'utils/use-translate';
import api from 'modules/api';

export type MatchCropsStepProps = {} & RouteComponentProps<{
  fileId: string;
}>;

const MatchCropsStep: VFC<MatchCropsStepProps> = ({ match, history }) => {
  const { t } = useTranslate();
  const dispatch = useDispatch();
  const [cropsForMatch, setCropsForMatch] = useState<string[]>([]);
  const [cropsMatchMap, setCropsMatchMap] = useState<CropsMatchMapType>({});

  const { fileId = 10750 } = match.params;
  const baseUrl = getRootScoutingUrl(history.location.pathname);

  const saveRequest = useSelector(state =>
    api.selectors.getRequestStatus(state, 'update-fields-file'),
  );

  const handleSubmit = () => {
    logEvent('fields_import_match_crops', cropsForMatch);
    dispatch(fields.actions.setMatchedProperties(fileId, cropsForMatch));
  };

  return (
    <>
      <Saga
        saga={matchCropsSaga}
        fileId={fileId}
        history={history}
        onSetData={setCropsForMatch}
      />
      <MatchCrops
        cropsForMatch={cropsForMatch}
        cropsMatchMap={cropsMatchMap}
        setCropsMatchMap={setCropsMatchMap}
      />
      <FloatingActionsStyle>
        <Button
          className='btn btn-dark btn-lg'
          onClick={() => {
            const {
              location: { state },
            } = history;
            history.replace({ pathname: `${baseUrl}/add/upload`, state });
          }}
        >
          {t('forms.cancel')}
        </Button>
        <SmartButton
          className='btn btn-success btn-lg'
          type='submit'
          pending={saveRequest.status === 'pending'}
          onClick={handleSubmit}
        >
          {t('forms.next')}
        </SmartButton>
      </FloatingActionsStyle>
    </>
  );
};

export default MatchCropsStep;
