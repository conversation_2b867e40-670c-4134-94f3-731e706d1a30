import { css } from 'linaria';
import { styled } from 'linaria/react';

export const legend = css`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

export const stackbar = css`
  margin-top: 5px;
  margin-bottom: 5px;
  height: 16px;
  border-radius: 2px;
  overflow: hidden;
`;

type StyledStackbarBaseProps = {
  baseColor: string;
};
export const StyledStackbarBase = styled.div<StyledStackbarBaseProps>`
  background-color: ${props => props.baseColor};
  height: 100%;
  position: relative;
  display: flex;
`;

export const StyledLegendLeft = styled.span`
  font-family: Graphik, sans-serif;
  font-weight: 500;
  font-size: 14px;
  color: #222222;
  line-height: 19px;
`;

export const StyledLegendRight = styled.span`
  font-family: Graphik, sans-serif;
  font-size: 14px;
  color: #a5b2bc;
  line-height: 19px;
`;

export const StyledLegendBottomLeft = styled.span`
  font-size: 12px;
  line-height: 16px;
  color: #5e5e5e;
`;

type StyledStackbarCellProps = {
  last: boolean;
  flex: number;
  color: string;
};
export const StyledStackbarCell = styled.div<StyledStackbarCellProps>`
  border-right: 1px solid white;
  flex: ${props => props.flex};
  background-color: ${props => props.color};
  &:hover {
    cursor: pointer;
  }
`;

type StyledStackbarCropBarProps = {
  color: string;
};
export const StyledStackbarCropBar = styled.span<StyledStackbarCropBarProps>`
  display: inline-block;
  background-color: ${props => props.color};
  width: 7px;
  height: 10px;
  margin-right: 6px;
`;

export const TooltipTitle = styled.div`
  font-weight: 500;
`;
