import React from 'react';

import { ClientToggleField } from 'features/multiaccount/dealer/modals/ClientForm/components/ClientToggleField';

export type ClientLimitsTabFieldProps = {
  control: TEMPORARY_ANY;
};

const ClientLimitsTabField = ({ control }: ClientLimitsTabFieldProps) => {
  return (
    <ClientToggleField
      control={control}
      name='limitsTab'
      options={['commercial', 'test']}
      translationKeyPrefix='multiaccount.invite-client-modal.limits-'
    />
  );
};

export default ClientLimitsTabField;
