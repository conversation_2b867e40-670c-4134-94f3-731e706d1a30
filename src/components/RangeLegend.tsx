import React, { VFC } from 'react';
import { css } from 'linaria';
import { useTranslate } from 'utils/use-translate';

import { styles } from '../features/fields/styles/fieldsLegend.styles';
import Button from './ui/Button/Button';
import Icon from './Icon';
import { styled } from 'linaria/react';

const rangeLabels = css`
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
`;

const bottomRangeLabels = css`
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
`;

const rangeLabel = css`
  font-size: 10px;
  font-family: Roboto, Arial, sans-serif;
  .map-topbar & {
    font-size: 12px;
  }
`;

const cloudBar = css`
  width: 54px;
  margin-left: 10px;
  height: 4px;
  border-radius: 2px;
  background-color: #d4d4d4;
`;

const StyledRangeBars = styled.ul`
  padding: 0;
  margin: 0;
  list-style-type: none;
  display: flex;
`;

type StyledRangeItemProps = {
  barsCount: number;
  bgColor: string;
};
const StyledRangeItem = styled.li<StyledRangeItemProps>`
  width: ${props => `calc(100% / ${props.barsCount})`};
  height: 4px;
  background-color: ${props => props.bgColor};
`;

type StyledRangeGradientItemProps = {
  colorsRange: string[];
};
const StyledRangeGradientItem = styled.li<StyledRangeGradientItemProps>`
  width: 100%;
  height: 4px;
  border-radius: 2px;
  background: ${props =>
    `linear-gradient(90deg, ${props.colorsRange
      .map(
        (color, index) =>
          `${color} ${(index / props.colorsRange.length) * 100}%`,
      )
      .join(', ')})`};
`;

export type RangeLegendProps = {
  colorsRange: string[];
  smooth?: boolean;
  withClouds?: boolean;
  labels?: string[];
  bottomLabels?: string[];
  onExpand?: () => void;
};

const RangeLegend: VFC<RangeLegendProps> = ({
  colorsRange,
  smooth,
  labels = [],
  bottomLabels = [],
  onExpand,
  withClouds = false,
}) => {
  const { t } = useTranslate();
  return (
    <div className={styles.widgetWrapper}>
      <div className={styles.widgetItem}>
        <div className={rangeLabels}>
          {labels.map((label, index) => (
            <span key={`${label}-${index}`} className={rangeLabel}>
              {label}
            </span>
          ))}
        </div>
        <StyledRangeBars>
          {smooth ? (
            <StyledRangeGradientItem colorsRange={colorsRange} />
          ) : (
            colorsRange.map((color, index) => (
              <StyledRangeItem
                key={`${color}-${index}`}
                barsCount={colorsRange.length}
                bgColor={color}
              />
            ))
          )}
        </StyledRangeBars>
        {bottomLabels.length > 0 && (
          <div className={bottomRangeLabels}>
            {bottomLabels.map((label, index) => (
              <span key={`${label}-${index}`} className={rangeLabel}>
                {label}
              </span>
            ))}
          </div>
        )}
      </div>
      {withClouds && (
        <div className={styles.widgetItem}>
          <div className={rangeLabels}>
            <span className={rangeLabel}>&nbsp;</span>
            <span className={rangeLabel}>{t('fields.ndvi.legend.clouds')}</span>
          </div>
          <div className={cloudBar} />
        </div>
      )}
      {!!onExpand && (
        <div className={styles.actions}>
          <Button className={styles.actionBtn} onClick={onExpand}>
            <Icon name='barchart' />
          </Button>
        </div>
      )}
    </div>
  );
};

export default RangeLegend;
