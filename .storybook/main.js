module.exports = {
  stories: ['../src/**/*.stories.@(js|jsx|ts|tsx)'],
  addons: [
    '@storybook/preset-create-react-app',
    '@storybook/addon-essentials',
    '@storybook/addon-actions',
    '@storybook/addon-links',
    '@storybook/addon-interactions',
  ],
  framework: '@storybook/react',
  core: {
    builder: {
      name: 'webpack5',
      options: {
        fsCache: false,
      },
    },
  },
  typescript: {
    check: false,
    checkOptions: {},
  },
  babel: config => {
    config.presets.push(require.resolve('linaria/babel'));
    return config;
  },
  webpackFinal: async (config, { configType }) => {
    const linariaLoaderConfig = {
      loader: 'linaria/loader',
      options: {
        sourceMap: process.env.NODE_ENV !== 'production',
      },
    };

    const jsRegex = /\.(js|mjs|jsx|ts|tsx)$/;
    const modules = config.module.rules.find(rule => !!rule.oneOf);

    const jsLoaderIndex = modules.oneOf.findIndex(({ test }) => {
      return test.toString() === jsRegex.toString();
    });

    if (jsLoaderIndex === -1) {
      return config;
    }

    const jsLoader = modules.oneOf.find(({ test }) => {
      return test.toString() === jsRegex.toString();
    });

    const { test, include, ...rest } = jsLoader;

    modules.oneOf[jsLoaderIndex] = {
      test,
      include,
      use: [{ ...rest }, linariaLoaderConfig],
    };

    return config;
  },
};
