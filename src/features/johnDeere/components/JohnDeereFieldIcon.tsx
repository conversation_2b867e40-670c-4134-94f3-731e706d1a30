import React from 'react';
import { cx } from 'linaria';

import { FieldBySeason } from 'types/fields';
import Icon from 'components/Icon';

import { isArchivedJohnDeere } from '../utils';
import { styles } from './styles';

type JohnDeereFieldIconProps = {
  fieldBySeason: Nullable<FieldBySeason>;
};

export const JohnDeereFieldIcon = ({
  fieldBySeason,
}: JohnDeereFieldIconProps) => {
  if (!fieldBySeason || fieldBySeason.source !== 'john_deere') {
    return null;
  }

  const isArchived = isArchivedJohnDeere(fieldBySeason);

  return (
    <Icon
      className={cx(styles.jdIcon, isArchived && styles.archived)}
      name='johnDeer'
    />
  );
};
