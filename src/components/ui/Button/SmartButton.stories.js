import React from 'react';
import { action } from '@storybook/addon-actions';
/* eslint-disable */
import SmartButton from 'components/ui/Button/SmartButton';
import Icon from 'components/Icon';

export default {
  title: 'common/SmartButton',
  component: SmartButton,
  decorators: [storyFn => <div style={{ padding: 16 }}>{storyFn()}</div>],
};

const Section = ({ title, children }) => (
  <div style={{ marginBottom: 10 }}>
    {title && <p style={{ marginBottom: 0 }}>{title}</p>}
    {children}
  </div>
);

const SmartButtonStates = ({ className }) => (
  <>
    <SmartButton className={`${className} size-full`}>full-width</SmartButton>
    <br />
    <br />
    {['btn-sm', '', 'btn-lg'].map(size => (
      <div style={{ marginBottom: 16 }}>
        <SmartButton className={`${className} ${size}`}>
          {size || 'normal'}
        </SmartButton>
        &nbsp;
        <SmartButton className={`${className} ${size}`} data-shadow='true'>
          {size} with shadow
        </SmartButton>
        &nbsp;
        <SmartButton className={`${className} ${size}`} disabled>
          {size} disabled
        </SmartButton>
        &nbsp;
        <SmartButton
          className={`${className} ${size}`}
          pending
          title='Pending'
        />
      </div>
    ))}
  </>
);

export const AllStates = () => (
  <>
    <Section title='Just a generic SmartButton'>
      <SmartButton onClick={action('clicked')}>Hello SmartButton</SmartButton>
    </Section>

    <Section title="With pending state (it's also disabled)">
      <SmartButton pending onClick={action('clicked')}>
        Hello SmartButton
      </SmartButton>
    </Section>
  </>
);

export const Styled = () => (
  <>
    <Section title='Default'>
      <SmartButtonStates className='btn btn-default' />
    </Section>

    <Section title='Primary'>
      <SmartButtonStates className='btn btn-primary' />
    </Section>

    <Section title='Success'>
      <SmartButtonStates className='btn btn-success' />
    </Section>

    <Section title='Danger'>
      <SmartButtonStates className='btn btn-danger' />
    </Section>

    <Section title='Dark'>
      <SmartButtonStates className='btn btn-dark' />
    </Section>

    <Section title='Link'>
      <SmartButton className='btn btn-link'>
        Looks like link, but it's a SmartButton!
      </SmartButton>
      <br />
      <SmartButton className='btn btn-link btn-lg'>Large works too</SmartButton>
    </Section>
  </>
);

export const Icons = () => (
  <>
    <Section title='Default'>
      <SmartButton className='btn btn-default'>
        <span className='btn__ico'>
          <Icon className='ico-check-os' name='check' />
        </span>
        button with icon
      </SmartButton>

      <SmartButton className='btn btn-default'>
        button with icon
        <span className='btn__ico btn__ico-right'>
          <Icon className='ico-check-os' name='check' />
        </span>
      </SmartButton>
    </Section>
  </>
);
