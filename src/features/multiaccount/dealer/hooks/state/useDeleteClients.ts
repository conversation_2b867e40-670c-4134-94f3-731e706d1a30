import { useState } from 'react';
import { Modals } from './types';

export type DeleteClientsManager = {
  deleteClientIds: Record<string, boolean>;
  setDeleteClientId: React.Dispatch<
    React.SetStateAction<Record<string, boolean>>
  >;

  showDeleteModal: () => void;
  closeDeleteModal: () => void;
  closeDeleteModalWithReset: () => void;
};

export const useDeleteClients = (
  setVisibleModal: (type: Modals) => void,
): DeleteClientsManager => {
  const [deleteClientIds, setDeleteClientId] = useState<
    Record<string, boolean>
  >({});

  const showDeleteModal = () => {
    setVisibleModal('delete-client');
  };
  const closeDeleteModalWithReset = () => {
    setDeleteClientId({});
    setVisibleModal(null);
  };
  const closeDeleteModal = () => {
    setVisibleModal(null);
  };

  return {
    deleteClientIds,
    setDeleteClientId,

    showDeleteModal,
    closeDeleteModalWithReset,
    closeDeleteModal,
  };
};
