import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Controller, useWatch } from 'react-hook-form';
import { css } from 'linaria';
import cx from 'classnames';
import moment from 'moment';

import fields from 'modules/fields';
import settings from 'modules/settings';

import { formatDate } from 'utils/format-date';

import Icon from 'components/Icon';
import Button from 'components/ui/Button/Button';
import DateInput from 'components/ui/DateInput/DateInput';
import { CustomDropdown } from 'components/ui/CustomDropdown/CustomDropdown';
import SelectCropInput from 'features/fields/SelectCropInput';
import VarietyInputRow from 'features/fields/VarietyInputRow';

import { formatYieldUnit } from 'utils/units';

import logEvent from 'sagas/global/logEvent';
import {
  FEATURES,
  FeatureDisabledTooltip,
  FEATURE_PERMISSIONS,
  useFeaturePermission,
} from 'features/permissions';
import { PopoverDeprecatedAlign } from 'components/ui/Popover';

const formRow = css`
  padding: 0 20px;
  label {
    display: block;
    font-size: 14px;
    line-height: 19px;
    font-weight: 500;
  }
  & + & {
    margin-top: 15px;
  }
`;

const formGroup = css`
  display: flex;
`;

const formGroupCol = css`
  width: 50%;
  & + & {
    margin-left: 10px;
  }
`;

const warning = css`
  font-weight: 500;
  font-size: 12px;
  color: #ff3b30;
  line-height: 16px;
  margin-top: 5px;
`;

const buttonDelete = css`
  font-weight: 500;
  font-size: 12px;
  color: #ff3b30;
  line-height: 16px;
`;

const RecommendationWarning = ({ crop, recommendations, t }) => {
  const recommendation = recommendations.find(comm => comm.crop === crop);

  if (!recommendation) {
    return null;
  }
  if (!recommendation.is_rotation_ok) {
    const recomendationText = t('croprotation.recommendation.crop');
    const prevCrop = t(`crop.${recommendation.crop_prev}`).toLowerCase();
    return <div className={warning}>{`${recomendationText} ${prevCrop}.`}</div>;
  }
  return null;
};

const EditFieldCropRow = ({
  control,
  setValue,
  getValues,
  fieldSeason,
  recommendations = [],
  t,
  noDelete,
  onRemove,
}) => {
  const units = useSelector(settings.selectors.getUnits);
  const yieldUnitsByCrop = useSelector(settings.selectors.getYieldUnitsByCrop);

  const isAvailableForEdit =
    useFeaturePermission(FEATURES.CROP_EDIT) === FEATURE_PERMISSIONS.ALLOWED;

  const dispatch = useDispatch();

  const sowingDate = useWatch({ control, name: 'sowing_date' });
  const harvestDate = useWatch({ control, name: 'harvest_date' });
  const watchedCrop = useWatch({ control, name: 'crop' });
  const { field_user_season_id } = getValues(['id', 'field_user_season_id']);

  return (
    <>
      <div className={formRow}>
        <label>{t('fields.edit.crops.crop')}</label>
        <Controller
          name='crop'
          control={control}
          render={props => (
            <>
              <SelectCropInput
                disabled={!isAvailableForEdit}
                value={props.value}
                onChange={crop => {
                  if (crop === 'remove') {
                    /* onRemove(); */
                    return;
                  }
                  setValue('yield_value_units', yieldUnitsByCrop[crop] || null);
                  dispatch(
                    fields.actions.fetchRecommendations(
                      [crop],
                      field_user_season_id,
                    ),
                  );
                  props.onChange(crop);
                }}
              />
              <RecommendationWarning
                crop={props.value}
                recommendations={recommendations}
                t={t}
              />
            </>
          )}
        />
      </div>
      <div className={formRow}>
        <label>{t('fields.edit.crops.variety_hybrid')}</label>
        <Controller
          name='variety'
          control={control}
          render={props => (
            <VarietyInputRow
              disabled={!isAvailableForEdit}
              className='form-input size-full'
              placeholder={t('fields.edit.crops.placeholder.variety_hybrid')}
              type='text'
              crop={watchedCrop}
              value={props.value || ''}
              onChange={props.onChange}
            />
          )}
        />
      </div>
      <div className={formRow}>
        <div className={formGroup}>
          <div className={formGroupCol}>
            <label>{t('fields.edit.crops.sowing_date')}</label>
            <Controller
              name='sowing_date'
              control={control}
              render={props => (
                <DateInput
                  noIcon
                  disabled={!isAvailableForEdit}
                  placeholder={t('fields.edit.crops.select_date')}
                  calendarProps={{
                    openToDate: moment(),
                    maxDate: harvestDate,
                  }}
                  renderValue={value =>
                    formatDate(value, t('seasons.edit.date_format'))
                  }
                  value={props.value}
                  onChange={props.onChange}
                />
              )}
            />
          </div>

          <div className={formGroupCol}>
            <label>{t('fields.edit.crops.harvest_date')}</label>
            <Controller
              name='harvest_date'
              control={control}
              render={props => (
                <DateInput
                  noIcon
                  disabled={!isAvailableForEdit}
                  placeholder={t('fields.edit.crops.select_date')}
                  calendarProps={{
                    openToDate: sowingDate || moment(),
                    minDate: sowingDate,
                  }}
                  renderValue={value =>
                    formatDate(value, t('seasons.edit.date_format'))
                  }
                  value={props.value}
                  onChange={props.onChange}
                />
              )}
            />
          </div>
        </div>
      </div>
      <div className={formRow}>
        <label>{t('fields.edit.crops.yield_value')}</label>
        <div className='unit-append'>
          <Controller
            name='yield_value'
            control={control}
            rules={{ min: 0, valueAsNumber: true }}
            render={({ onChange, value }, { invalid }) => {
              return (
                <input
                  disabled={!isAvailableForEdit}
                  className={cx('form-input size-full', {
                    __error: invalid,
                  })}
                  placeholder='0'
                  type='number'
                  step='any'
                  min={0}
                  value={value || ''}
                  onChange={event => {
                    const { value } = event.target;
                    onChange(+value || null);
                  }}
                />
              );
            }}
          />
          <Controller
            name='yield_value_units'
            control={control}
            render={({ value, onChange }) => (
              <CustomDropdown
                disabled={!isAvailableForEdit}
                wrapperClassName='unit-append__wrapper'
                className='unit-append__select'
                options={units.yield.map(({ unit }) => ({
                  label: formatYieldUnit(
                    unit,
                    units,
                    yieldUnitsByCrop[watchedCrop],
                    t,
                  ),
                  id: unit,
                }))}
                renderValue={() => (
                  <>
                    <div className='form-select__value u-select__value'>
                      {formatYieldUnit(
                        value,
                        units,
                        yieldUnitsByCrop[watchedCrop],
                        t,
                      )}
                    </div>
                    <Icon
                      className='ico-chevron-down-small-os'
                      name='arrow-down-small'
                    />
                  </>
                )}
                value={value}
                onChange={unit => {
                  onChange(unit);

                  logEvent('change_yield_unit', {
                    crop: watchedCrop,
                    unit,
                    field_user_season_id: `${field_user_season_id}`,
                  });
                }}
              />
            )}
          />
        </div>
      </div>
      {!noDelete && (
        <FeatureDisabledTooltip
          feature={FEATURES.CROP_DELETE}
          data={fieldSeason}
          direction={PopoverDeprecatedAlign.MiddleLeft}
        >
          <div className={formRow}>
            <Button
              className={cx('edit-action-delete', buttonDelete)}
              onClick={() => {
                const id = getValues('id');
                onRemove(id);
              }}
            >
              <span className='edit-action-delete__ico'>
                <Icon className='ico-trash-n-os' name='trashcan' />
              </span>
              {t('croprotation.field.remove_crop')}
            </Button>
          </div>
        </FeatureDisabledTooltip>
      )}
    </>
  );
};

export default EditFieldCropRow;
