import React from 'react';
import socialLogin, { Provider } from 'react-social-login';
import { useTranslate } from 'utils/use-translate';

import Button from 'components/ui/Button/Button';
import ModernSpinner from 'components/ui/ModernSpinner/ModernSpinner';

interface Props {
  network: Provider;
  pending: boolean;
  triggerLogin: () => void;
}

const SocialLoginButton = ({ network, pending, triggerLogin }: Props) => {
  const { t } = useTranslate();

  return (
    <Button
      className='btn size-full'
      data-social={network}
      onClick={triggerLogin}
    >
      <span className='onesoil-hello-socials__socico' />
      {pending ? <ModernSpinner /> : t(`auth.provider.${network}`)}
    </Button>
  );
};

export default socialLogin(SocialLoginButton);
