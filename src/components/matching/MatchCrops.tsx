import React, { useState, VFC } from 'react';
import { useSelector } from 'react-redux';

import EnterExitTransition from 'components/EnterExitTransition';
import { CustomDropdown } from 'components/ui/CustomDropdown/CustomDropdown';
import Icon from 'components/Icon';
import ModernSpinner from 'components/ui/ModernSpinner/ModernSpinner';
import ProposeCulturePopup from 'features/fields/ProposeCulturePopup';

// @ts-ignore
import fields from 'modules/fields';

import { useTranslate } from 'utils/use-translate';

export type CropsMatchMapType = Record<string, string>;

export type cropsMatchMapStepProps = {
  cropsForMatch: string[];
  cropsMatchMap: CropsMatchMapType;
  setCropsMatchMap: (cropsMatchMap: CropsMatchMapType) => void;
};

export const MatchCrops: VFC<cropsMatchMapStepProps> = ({
  cropsForMatch,
  cropsMatchMap,
  setCropsMatchMap,
}) => {
  const { t } = useTranslate();

  const [showPopup, setShowPopup] = useState(false);

  const sortedCropTypes: string[] = useSelector(
    fields.selectors.getSortedCropTypes,
  );
  const cropTypes = ['no_import', 'propose', ...sortedCropTypes];

  return (
    <>
      <EnterExitTransition variant='popup'>
        {showPopup && (
          <ProposeCulturePopup
            onCancel={() => {
              setShowPopup(false);
            }}
          />
        )}
      </EnterExitTransition>
      <div className='main-section__body'>
        <form onSubmit={() => {}}>
          <div className='main-uploader' data-type='columns'>
            <div className='main-uploader-header'>
              <h2 className='main-uploader-header__title'>
                {t('fields.upload.match_crops.title')}
              </h2>
              <p className={'main-uploader-header__description'}>
                {t('fields.upload.match_crops.sub_title')}
              </p>
            </div>
            <div className='main-uploader-naming' data-type='compact'>
              <div className='main-uploader-naming__header'>
                <div className='main-uploader-naming__col'>
                  {t('fields.upload.match_columns.from_file')}
                </div>
                <div className='main-uploader-naming__col'>
                  {t('fields.upload.match_columns.to_platform')}
                </div>
              </div>
              {!cropsForMatch.length && (
                <div className='main-uploader-spinner'>
                  <ModernSpinner large />
                </div>
              )}
              {cropsForMatch.map(key => (
                <div
                  key={key}
                  className='main-uploader-naming__row'
                  data-column={cropsMatchMap[`${key}`] ? 'selected' : 'passed'}
                >
                  <div className='main-uploader-naming__arr'>
                    <Icon
                      className='ico-arrow-circle-right-os'
                      name='arrow-circle-right'
                    />
                  </div>
                  <div className='main-uploader-naming__col'>
                    <h3 className='main-uploader-naming__title'>{[key]}</h3>
                  </div>
                  <div className='main-uploader-naming__col'>
                    <CustomDropdown
                      placeholder={t('files.actions.placeholder') as string}
                      className='form-select form-select-sm'
                      value={cropsMatchMap[key]}
                      renderValue={item => {
                        return (
                          <div className='form-select__value'>
                            {item?.value
                              ? t(`crop.${item.value}`)
                              : t('fields.upload.match_crops.no_import')}
                          </div>
                        );
                      }}
                      options={cropTypes.map(type => ({
                        label: t(`crop.${type}`) as string,
                        id: type,
                      }))}
                      withSearchBox={cropTypes.length >= 5}
                      searchBoxProps={{
                        placeholder: t(
                          'suggest.crops_type.search.placeholder',
                        ) as string,
                      }}
                      onClick={event => event.stopPropagation()}
                      onChange={(action: string) => {
                        let state = { ...cropsMatchMap };
                        if (action === 'no_import') {
                          delete state[key];
                        } else if (action === 'propose') {
                          delete state[key];
                          setShowPopup(true);
                        } else {
                          state = { ...state, [key]: action };
                        }
                        setCropsMatchMap(state);
                      }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </form>
      </div>
    </>
  );
};
