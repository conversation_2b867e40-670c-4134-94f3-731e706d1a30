import RootState from 'types/rootState';
import { TaskMatchingType } from 'features/files/by-machinery/types';
import { filters } from 'features/files/by-machinery/redux/filters';
import { selectors } from 'features/files/by-machinery/redux/selectors';

import { StepStatus } from '../types';

export const defaultCheck = (state: RootState): StepStatus => {
  const hasUnfinishedActiveTask: boolean =
    selectors.hasUnfinishedActiveTask(state);
  if (hasUnfinishedActiveTask) {
    return StepStatus.hiding;
  }
  const matchingTasks: TaskMatchingType[] = selectors
    .getAllTasks(state)
    .filter(filters.isMatchingTask);
  if (matchingTasks.length === 0) {
    return StepStatus.hiding;
  }

  return StepStatus.available;
};
