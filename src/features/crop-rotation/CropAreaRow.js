import React, { useState } from 'react';
import { css } from 'linaria';
import { useController } from 'react-hook-form';
import { useSelector } from 'react-redux';

import LinkButton from 'components/LinkButton';
import TextInput from 'components/forms/TextInput';
import Icon from 'components/Icon';

import settings from 'modules/settings';

import { roundDown } from 'utils/format-number';
import { useTranslate } from 'utils/use-translate';

const cropAreaContainer = css`
  display: flex;
  flex-direction: column;
`;

const cropAreaRow = css`
  display: flex;
  align-items: center;
  margin-top: 20px;
  justify-content: space-between;
`;

const cropAreaRowLeft = css`
  display: flex;
  align-items: center;
`;

const cropAreaRowRight = css`
  display: flex;
  flex-shrink: 0;
  align-items: center;
`;

const errorLabel = css`
  font-size: 12px;
  line-height: 16px;
  align-self: flex-end;
  margin-top: 5px;
`;

const cropName = css`
  font-size: 16px;
  line-height: 22px;
`;

const cropAreaTrashButton = css`
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e4e7ea;
  width: 24px;
  height: 24px;
  margin-left: 10px;
  border-radius: 50%;
  border: 0;
  transition: background-color 0.3s;

  &:hover {
    background-color: rgba(165, 178, 188, 0.5);
  }

  svg {
    margin-top: -1px;
  }
`;

const inputWrap = css`
  width: 120px;
`;

const CropAreaRow = ({
  text,
  index,
  control,
  totalArea,
  filledArea,
  formState,
  onRemove,
}) => {
  const { t } = useTranslate();
  const units = useSelector(settings.selectors.getUnits);

  const [focused, setFocused] = useState(false);

  const { field } = useController({
    name: `crops[${index}].area`,
    rules: { valueAsNumber: true },
    control,
    defaultValue: 0,
  });

  const maxArea = totalArea - (filledArea - field.value);

  const valid = field.value <= maxArea && maxArea !== 0;
  const maxAreaToFill = roundDown(maxArea, 1);

  const validate = () => {
    setFocused(false);
    if (!valid || !+field.value) {
      field.onChange('');
    }
  };

  return (
    <div className={cropAreaContainer}>
      <div className={cropAreaRow}>
        <div className={cropAreaRowLeft}>
          <span className={cropName}>{text}</span>
        </div>
        <div className={cropAreaRowRight}>
          <div className={inputWrap}>
            <TextInput
              suffix={t(`units.${units.area.unit}.short`)}
              placeholder='0'
              type='number'
              step='any'
              size='small'
              hideArrows
              // Only show errors when focused
              isValid={focused ? valid : true}
              // We hide 0 value, so that we see placeholder instead
              value={field.value === 0 ? '' : field.value}
              onChange={field.onChange}
              onFocus={() => setFocused(true)}
              onBlur={validate}
            />
          </div>
          <button
            type='button'
            className={cropAreaTrashButton}
            onClick={event => {
              onRemove();
            }}
          >
            <Icon className='ico-trash-n-os' name='trashcan' />
          </button>
        </div>
      </div>

      {focused && !valid && (
        <span className={errorLabel}>
          {maxAreaToFill === 0 && t('croprotation.autofill.error.no_area_left')}
          {maxAreaToFill > 0 && (
            <>
              {t('croprotation.autofill.error.area_too_large')}{' '}
              <LinkButton
                onMouseDown={() => {
                  field.onChange(maxAreaToFill);
                }}
              >
                {maxAreaToFill} {t(`units.${units.area.unit}.short`)}
              </LinkButton>
            </>
          )}
        </span>
      )}
    </div>
  );
};

export default CropAreaRow;
