import RootState from 'types/rootState';

import { TaskMatchingType } from 'features/files/by-machinery/types';
import { selectors } from 'features/files/by-machinery/redux/selectors';
import { filters } from 'features/files/by-machinery/redux/filters';

import { StepStatus } from '../types';
import { defaultCheck } from './defaultCheck';

export const attributeStepCheck = (state: RootState): StepStatus => {
  if (defaultCheck(state) === StepStatus.hiding) {
    return StepStatus.hiding;
  }

  const matchingTasks: TaskMatchingType[] = selectors
    .getAllTasks(state)
    .filter(filters.isMatchingTask);

  if (
    matchingTasks.every(task =>
      task.sources.every(source => source.driver === 'ADAPT'),
    )
  ) {
    return StepStatus.hiding;
  }

  return StepStatus.available;
};
