import React, { useRef } from 'react';
import { useDispatch } from 'react-redux';
import cx from 'classnames';
import { v4 as uuidv4 } from 'uuid';

import Icon from 'components/Icon';
import { CustomDropdown } from 'components/ui/CustomDropdown/CustomDropdown';

import modems from 'modules/modems';
import logEvent from 'sagas/global/logEvent';

import { useTranslate } from 'utils/use-translate';

const ImportFilesButton = ({ modemId, directory }) => {
  const dispatch = useDispatch();
  const { t } = useTranslate();
  const filesInput = useRef(null);
  const folderInput = useRef(null);

  return (
    <div className='form-upload' style={{ overflow: 'visible' }}>
      <input
        ref={filesInput}
        className='form-upload__input modems-files-input'
        type='file'
        multiple
        onChange={async event => {
          const { target } = event;
          const maxSize = Math.max(
            ...Array.from(target.files).map(file => file.size),
          );
          logEvent('modems_files_upload', {
            count: Array.from(target.files).length,
            maxSize,
          });
          for (const file of Array.from(target.files)) {
            const uuid = uuidv4();
            const filename = `${directory}/${file.name}`;
            const data = new FormData();
            data.append('file', file);
            data.append('filename', filename);
            data.append('modem_uuid', modemId);
            dispatch(
              modems.actions.upload(data, modemId, uuid, filename, file.size),
            );
          }
          // Without resetting the 'value' input will not
          // trigger change event for the same file again
          target.value = null;
        }}
      />
      <input
        ref={folderInput}
        className='form-upload__input modems-files-input'
        type='file'
        webkitdirectory=''
        directory=''
        onChange={async event => {
          const { target } = event;
          const maxSize = Math.max(
            ...Array.from(target.files).map(file => file.size),
          );
          logEvent('modems_files_upload', {
            isFolder: true,
            count: Array.from(target.files).length,
            maxSize,
          });
          for (const file of Array.from(target.files)) {
            const uuid = uuidv4();
            const filename = `${directory}/${file.webkitRelativePath}`;
            const data = new FormData();
            data.append('file', file);
            data.append('filename', filename);
            data.append('modem_uuid', modemId);
            dispatch(
              modems.actions.upload(data, modemId, uuid, filename, file.size),
            );
          }
          // Without resetting the 'value' input will not
          // trigger change event for the same file again
          target.value = null;
        }}
      />
      <CustomDropdown
        options={['folder', 'files'].map(id => ({
          label: t(`modems.files.upload.${id}`),
          icon: `${id}-upload`,
          id,
        }))}
        renderValue={({ isOpen }) => (
          <span className={cx('btn btn-default', { 'btn--active': isOpen })}>
            <span className='btn__ico'>
              <Icon name='upload' />
            </span>
            {t('modems.files.upload')} <span className='btn__arrow' />
          </span>
        )}
        onChange={type => {
          if (type === 'files') {
            filesInput.current.click();
          } else if (type === 'folder') {
            folderInput.current.click();
          }
        }}
      />
    </div>
  );
};

export default ImportFilesButton;
