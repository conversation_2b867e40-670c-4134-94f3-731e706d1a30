import { CropType } from 'types/crops';
import { SortDirections } from 'constants/sortDirections';

export type ExpandedKeys = 'expanded' | 'collapsed';

export type CropStatisticType = {
  id: number;
  crop: string;
  color: string;
  area: number;
};

export type CropCellType = {
  fieldSeasonUuid?: string;
  fieldSeasonId?: number;
  area: string;
  crops: CropType[];
};

export type SeasonData = {
  cropsIds: number[];
  filledBy: number;
  totalArea: number;
};

export type SortConfigType = {
  key: string;
  direction: SortDirections;
};
