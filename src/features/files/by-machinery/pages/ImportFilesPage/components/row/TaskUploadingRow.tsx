import React, { useState, VFC } from 'react';
import { useDispatch } from 'react-redux';

import { useTranslate } from 'utils/use-translate';
import { TaskUploadingType } from 'features/files/by-machinery/types';
import { actions } from 'features/files/by-machinery/redux/actions';

import Icon from 'components/Icon';
import CircularProgressBar from 'components/progress/CircularProgressBar';

import {
  cancelButtonIconClass,
  CancelButtonStyle,
  ActiveTaskInfoStyle,
  ActiveTaskSubTitleStyle,
  ActiveTaskTitleStyle,
  ActiveTaskStatusWrapper,
  ActiveTaskRowStyle,
  ActiveTaskRowContainer,
} from '../styles';
import Alert from 'components/ui/Alert/Alert';

type UploadingTaskRowProps = {
  task: TaskUploadingType;
};

const TaskUploadingRow: VFC<UploadingTaskRowProps> = ({ task }) => {
  const { t } = useTranslate();
  const dispatch = useDispatch();
  const [isShowRemoveAlert, setIsShowRemoveAlert] = useState(false);

  return (
    <ActiveTaskRowStyle>
      <ActiveTaskRowContainer>
        <CancelButtonStyle
          onClick={e => {
            e.preventDefault();
            setIsShowRemoveAlert(true);
          }}
        >
          <ActiveTaskStatusWrapper>
            <Icon name='close' className={cancelButtonIconClass} />
            <CircularProgressBar progress={task.progress} size={32} />
          </ActiveTaskStatusWrapper>
        </CancelButtonStyle>
        <div>
          <ActiveTaskTitleStyle>{task.name}</ActiveTaskTitleStyle>
          <div>
            <ActiveTaskSubTitleStyle>
              {t('import.by-machinery.uploading.title')}
            </ActiveTaskSubTitleStyle>{' '}
            <ActiveTaskInfoStyle>
              {Math.ceil(task.progress * 100)}%
            </ActiveTaskInfoStyle>
          </div>
        </div>
        {isShowRemoveAlert && (
          <Alert
            show={true}
            text={t(`import.by-machinery.alert.remove.text`)}
            onDanger={() => {
              dispatch(actions.removeTask(task));
              setIsShowRemoveAlert(false);
            }}
            onCancel={() => setIsShowRemoveAlert(false)}
            danger={t('import.by-machinery.alert.remove.confirm') as string}
            success={t('import.by-machinery.alert.remove.cancel') as string}
          />
        )}
      </ActiveTaskRowContainer>
    </ActiveTaskRowStyle>
  );
};

export default TaskUploadingRow;
