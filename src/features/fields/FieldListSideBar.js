import React, {
  useEffect,
  useCallback,
  useRef,
  useMemo,
  useState,
} from 'react';
import { AutoSizer, List } from 'react-virtualized';
import { Link } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { css } from 'linaria';
import { styled } from 'linaria/react';
import firebase from 'firebase/app';
import 'firebase/remote-config';

import Icon from 'components/Icon';
import Button from 'components/ui/Button/Button';
import Saga from 'components/saga/Saga';
import StackBar from 'components/ui/StackBar/StackBar';
import ModernSpinner from 'components/ui/ModernSpinner/ModernSpinner';

import CreateFieldDialogDropDown from 'features/fields/CreateFieldDialogDropDown/CreateFieldDialogDropDown';
import AddFieldsWelcomePane from './AddFieldsWelcomePane/AddFieldsWelcomePane';
import FieldUploadOverlay from './FieldUploadOverlay';
import {
  FieldListSortedItemMemo,
  FantomFieldListItemId,
} from './FieldListItem';

import fields from 'modules/fields';
import notes from 'modules/notes';
import seasons from 'modules/seasons';
import settings from 'modules/settings';

import getRootScoutingUrl, {
  getViewportHash,
} from 'utils/get-root-scouting-url';
import { useTranslate } from 'utils/use-translate';

import fieldListSaga from 'sagas/local/field-list';
import logEvent from 'sagas/global/logEvent';
import {
  FEATURES,
  FeatureDisabledTooltip,
  ProtectedRouteHOC,
} from 'features/permissions';
import { PopoverDeprecatedAlign } from 'components/ui/Popover';

export const FieldRowHeight = 70;
export const ExpandableRowHeight = 40;

const header = css`
  display: flex;
  align-items: center;
  min-height: 50px;
`;

const stackBarHolder = css`
  padding: 16px 20px;
  border-bottom: 1px solid rgba(214, 220, 225, 0.5);
  transition: background-color 0.3s;
  position: relative;
  display: block;
  &:hover {
    cursor: pointer;
    background-color: #f5f7f9;
  }
`;

const stackBarAction = css``;

const stackBarLink = css`
  font-family: Graphik, sans-serif;
  font-weight: 500;
  font-size: 12px;
  color: #007aff;
  line-height: 16px;
`;

const titleAction = css`
  width: 20px;
  height: 20px;
  border-width: 0;
  font-size: 0;
  vertical-align: middle;
  color: #222;
  padding: 0;
  background: none;
  & + & {
    margin-left: 10px;
  }
`;

const StyledBtnGroup = styled.div`
  display: flex;
  align-items: center;
`;

const SidebarBtnGroup = styled(StyledBtnGroup)`
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 10px;
`;

const disableAutoScroll = () => ({
  autoScrollDisable: true,
});

const FieldListSideBar = ({ match, history, location }) => {
  const status = useSelector(fields.selectors.getOwnStatus);
  const fieldIDs = useSelector(fields.selectors.getAllSortedIDs);
  const formatUnit = useSelector(settings.selectors.getUnitFormatter);
  const cropsStatistics = useSelector(fields.selectors.getCropsStatistics);
  const fieldListSettings = useSelector(settings.selectors.getFieldList);
  const currentSeason = useSelector(seasons.selectors.getCurrent);
  const notesInSeason = useSelector(state =>
    notes.selectors.getInSeason(state, currentSeason?.id),
  );

  const { t, i18n } = useTranslate();
  const dispatch = useDispatch();

  const code = i18n.resolvedLanguage;
  const list = useRef(null);

  const [buttonClass, setButtonClass] = useState('btn-success');

  // history is mutable hence baseUrl doesn't change when map position changes
  const baseUrl = useMemo(
    () => getRootScoutingUrl(history.location.pathname),
    [history.location.pathname],
  );

  useEffect(() => {
    if (list.current) {
      list.current.recomputeRowHeights();
    }
  }, [code, location.pathname, cropsStatistics, fieldListSettings, fieldIDs]);

  const scrollIndex = useMemo(() => {
    if (!location.state?.autoScrollDisable) {
      let index = fieldIDs.findIndex(({ id }) => id === match.params.fieldId);
      // hack to beat scroll issue
      if (index === fieldIDs.length - 1) {
        index += 1;
      }
      return {
        scrollToIndex: index,
      };
    }
    return {};
  }, [match.params?.fieldId, location.state, fieldIDs]);

  useEffect(() => {
    if (list.current && match.params?.fieldId) {
      if (fieldIDs.every(({ id }) => id !== match.params.fieldId)) {
        const cropGroupes = fieldIDs.filter(
          ({ expandable, expanded }) => expandable && !expanded,
        );

        const { crop } =
          cropGroupes.find(({ items }) =>
            items.includes(match.params.fieldId),
          ) || {};

        if (!crop) {
          if (status === 'resolved') {
            // remove fieldId from url because the crop doesn't belong to the user
            history.push(baseUrl);
          }
          // prevent undefined crop expanding while pending crops fetch
          return;
        }

        dispatch(fields.actions.expandCrop(crop));
        logEvent('fields_list_grouped_crop_expand', {
          crop,
        });
      }
    }
  }, [
    match.params?.fieldId,
    fieldIDs,
    dispatch,
    location.state,
    status,
    baseUrl,
    history,
  ]);

  useEffect(() => {
    const remoteConfig = firebase.remoteConfig();
    remoteConfig.fetch().then(() => {
      const addFieldClass = remoteConfig.getValue('add_field_class').asString();
      setButtonClass(addFieldClass);
    });
  }, []);

  const viewport = useMemo(
    () => getViewportHash(history.location.pathname),
    [history.location.pathname],
  );

  const onExpand = useCallback(
    crop => {
      dispatch(fields.actions.expandCrop(crop));
      logEvent('fields_list_grouped_crop_expand', {
        crop,
      });
    },
    [dispatch],
  );

  const onChange = useCallback(
    option => {
      if (option === 'upload') {
        dispatch(fields.actions.setImportStatus({ state: 'resolved' }));
      }
      history.push(`${baseUrl}/add/${option}`);
    },
    [baseUrl, history, dispatch],
  );

  const calculateRowHeight = useCallback(
    ({ index }) =>
      fieldIDs[index]?.expandable ? ExpandableRowHeight : FieldRowHeight,
    [fieldIDs],
  );

  return (
    <div className='soil-sidebar-viewer'>
      <Saga saga={fieldListSaga} history={history} />
      <FieldUploadOverlay history={history} />
      <div className='soil-sidebar-header'>
        <div className={header}>
          <h1 className='soil-sidebar-header__main'>
            {t('fields.list.title')}
          </h1>
        </div>
        {fieldIDs.length !== 0 && (
          <SidebarBtnGroup>
            <Button
              className={titleAction}
              onClick={() => {
                history.push(`${baseUrl}/list/settings`);
              }}
            >
              <Icon width={12} height={12} name='filters' />
            </Button>
            <Button
              className={titleAction}
              onClick={() => {
                history.push(`${baseUrl}/edit/list`);
              }}
            >
              <Icon width={13} height={13} name='pencil' />
            </Button>
          </SidebarBtnGroup>
        )}
      </div>
      {status === 'pending' && (
        <div className='soil-sidebar-empty'>
          <div className='soil-sidebar-empty__ico with_loader'>
            <ModernSpinner large />
          </div>
          <h2 className='soil-sidebar-empty__title'>
            {t('fields.loading.title')}
          </h2>
        </div>
      )}
      {status === 'resolved' && fieldIDs.length === 0 && (
        <AddFieldsWelcomePane match={match} />
      )}
      {status === 'resolved' && fieldIDs.length > 0 && (
        <Link to={`/${viewport}/crop-rotation`} className={stackBarHolder}>
          <StackBar
            data={cropsStatistics.crops}
            total={Math.max(
              cropsStatistics.filledArea,
              cropsStatistics.totalArea,
            )}
            unitsType={'ha'}
            valueKey={'area'}
            legendLeftText={t('croprotation.stats.title')}
            legendRightText={t('croprotation.stats.title.label', {
              filledArea: formatUnit(cropsStatistics.filledArea, 'ha', 'area'),
              totalArea: formatUnit(cropsStatistics.totalArea, 'ha', 'area'),
            })}
            t={t}
          />
          <div className={stackBarAction}>
            <span className={stackBarLink}>
              {t('croprotation.link.produce')}
            </span>
          </div>
        </Link>
      )}
      {status === 'resolved' && fieldIDs.length !== 0 && (
        <>
          <div className='soil-sidebar-body'>
            <AutoSizer>
              {({ width, height }) => (
                <List
                  ref={list}
                  className='soil-fields-list __with-actions'
                  width={width}
                  height={height}
                  rowCount={fieldIDs.length + 1}
                  rowHeight={calculateRowHeight}
                  scrollToAlignment='start'
                  {...scrollIndex}
                  rowRenderer={props => (
                    <FieldListSortedItemMemo
                      data={[...fieldIDs, { id: FantomFieldListItemId }]}
                      notesInSeason={notesInSeason}
                      match={match}
                      history={history}
                      isGroupedBy={fieldListSettings.groupBy}
                      onExpand={onExpand}
                      getHistoryStateAfterSelect={disableAutoScroll}
                      {...props}
                    />
                  )}
                />
              )}
            </AutoSizer>
          </div>
          <div className='soil-sidebar-addnew'>
            <FeatureDisabledTooltip
              feature={FEATURES.FIELD_ADD}
              direction={PopoverDeprecatedAlign.MiddleTop}
            >
              <div data-testid='#button_bottom_add_fields'>
                <CreateFieldDialogDropDown
                  className={`btn size-full btn-lg ${buttonClass}`}
                  renderValue={() => (
                    <>
                      <span className='btn__ico'>
                        <Icon className='ico-plus-os' name='plus-btn' />
                      </span>
                      {t('fields.list.add')} <span className='btn__arrow' />
                    </>
                  )}
                  onChange={onChange}
                />
              </div>
            </FeatureDisabledTooltip>
          </div>
        </>
      )}
    </div>
  );
};

export default ProtectedRouteHOC({
  Component: FieldListSideBar,
  feature: FEATURES.FIELD_LIST,
});
