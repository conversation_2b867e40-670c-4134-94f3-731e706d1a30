import React, { VFC } from 'react';

import { RowTitleStyle, RowSubTitleStyle } from './styles';

import { useTranslate } from 'utils/use-translate';
import { MetaAttributesApi } from 'features/files/by-machinery/types';
import { TableCell } from './TableCell';

type AttributeSelectorProps = {
  attribute: MetaAttributesApi;
  invisible: boolean;
  showSubTitle: boolean;
};

export const AttributeHeadCell: VFC<AttributeSelectorProps> = ({
  attribute,
  invisible,
  showSubTitle,
}) => {
  const { t } = useTranslate();

  return (
    <TableCell invisible={invisible}>
      <div>
        <RowTitleStyle>
          {t('import.matching_attributes.' + attribute.name + '.title')}
        </RowTitleStyle>
        {showSubTitle && (
          <RowSubTitleStyle>
            {t('import.matching_attributes.' + attribute.name + '.description')}
          </RowSubTitleStyle>
        )}
      </div>
    </TableCell>
  );
};
