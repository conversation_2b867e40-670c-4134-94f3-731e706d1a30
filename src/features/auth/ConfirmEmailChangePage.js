import { useSelector } from 'react-redux';
import { Redirect } from 'react-router-dom';
// import config from 'config';

import auth from 'modules/auth';

import { apiCall } from 'utils/effects';
import usePersistentSaga from 'utils/use-persistent-saga';
// import { getYieldHost } from 'utils/yield';
import logEvent from 'sagas/global/logEvent';

const ConfirmEmailChangePage = ({ history, match }) => {
  const userID = useSelector(auth.selectors.getUserID);

  usePersistentSaga(function* () {
    try {
      yield apiCall(
        auth.actions.confirmEmailChange(userID, match.params.token),
      );
      history.replace({
        ...history.location,
        state: {
          email_change_success: true,
        },
      });
      logEvent('change_email_confirm');
    } catch (error) {
      const serverError = error.response?.error_details.find(
        e => e.field === 'confirmation_token',
      );
      if (serverError) {
        history.replace({
          ...history.location,
          state: {
            email_change_error: serverError,
          },
        });
      }
    }
  });

  // add this logic after painted doors in yield app will be released, remove <Redirect to='/' />
  // window.location.replace(`${getYieldHost()}/`);
  // return null;

  return <Redirect to='/' />;
};

export default ConfirmEmailChangePage;
