import React from 'react';
import { connect } from 'react-redux';
import { actions as formActions } from 'react-redux-form';
import forms from 'constants/forms';

import { FEATURES, FeatureDisabledTooltip } from 'features/permissions';
import { PopoverDeprecatedAlign } from 'components/ui/Popover';
import files from 'modules/files';

const ImportFilesButton = ({ children, onImportFiles, onReset }) => (
  <FeatureDisabledTooltip
    feature={FEATURES.UPLOAD_DATA_UPLOAD}
    direction={PopoverDeprecatedAlign.MiddleTop}
  >
    <div className='form-upload'>
      <input
        className='form-upload__input'
        type='file'
        multiple
        onChange={event => {
          onReset();
          onImportFiles(Array.from(event.target.files));
        }}
      />
      {children}
    </div>
  </FeatureDisabledTooltip>
);

const mapDispatchToProps = {
  onImportFiles: files.actions.import,
  onReset: () => formActions.reset(`${forms.addFiles}.files`),
};

export default connect(null, mapDispatchToProps)(ImportFilesButton);
