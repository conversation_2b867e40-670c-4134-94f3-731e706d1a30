import React, { useState, VFC } from 'react';

import { useTranslate } from 'utils/use-translate';

import {
  MetaPropertiesGroups,
  ApiDataFieldLayer,
  DataTypesKey,
} from 'features/files/by-machinery/types';

import { AttributeSelectBoxMemo } from './AttributeSelectBox';
import { AttributeHeadCell } from './AttributeHeadCell';
import { ExpandButton } from './ExpandButton';
import { TableCell } from './TableCell';

import {
  OnSetPropertyType,
  OnSetUnitType,
  OnSetMethodType,
} from 'features/files/by-machinery/pages/MatchingAttributesPage/service';

import { AttributeGroupStyle, CellStyle, Delimiter } from './styles';

type AttributeSelectorProps = {
  group: MetaPropertiesGroups;
  dataLayers: ApiDataFieldLayer[];
  dataType: string;
  onSetAttributeValue: OnSetPropertyType;
  onSetUnit: OnSetUnitType;
  onSetMethod: OnSetMethodType;
};

export const AttributeGroup: VFC<AttributeSelectorProps> = ({
  dataLayers,
  group,
  dataType,
  onSetAttributeValue,
  onSetUnit,
  onSetMethod,
}) => {
  const { t } = useTranslate();
  const [open, setOpen] = useState(false);
  const hasHidden = group.properties.some(property => property.is_hidden);
  const properties = group.properties.filter(v => v.match);

  return (
    <React.Fragment>
      <Delimiter top='8' columns={dataLayers.length} />
      {group.name && (
        <AttributeGroupStyle columns={dataLayers.length}>
          {t('import.matching.attribute_group.' + group.name)}
        </AttributeGroupStyle>
      )}
      {properties
        .filter(property => property.name !== 'custom')
        .map(property => {
          const hasEditedFields = dataLayers.some(layer =>
            layer.properties.some(item => item.target_name === property.name),
          );
          const invisible = !open && property.is_hidden && !hasEditedFields;
          return (
            <React.Fragment key={property.name}>
              <AttributeHeadCell
                attribute={property}
                invisible={invisible}
                showSubTitle={
                  dataType === DataTypesKey.soilSampling ||
                  dataType === DataTypesKey.electroconductivity
                }
              />

              {dataLayers.map(layer => (
                <TableCell key={layer.uuid} invisible={invisible}>
                  <AttributeSelectBoxMemo
                    key={layer.uuid}
                    attribute={property}
                    layer={layer}
                    dataType={dataType}
                    onSetUnit={onSetUnit}
                    onSetMethod={onSetMethod}
                    onSetAttributeValue={onSetAttributeValue}
                  />
                </TableCell>
              ))}
            </React.Fragment>
          );
        })}
      {hasHidden && (
        <CellStyle>
          <ExpandButton dataType={dataType} open={open} setOpen={setOpen} />
        </CellStyle>
      )}
    </React.Fragment>
  );
};
