import React, { Component, Fragment } from 'react';
import { GeoJSON } from 'react-leaflet';
import { connect } from 'react-redux';

import RenderAboveEverything from 'components/RenderAboveEverything';

import fields from 'modules/fields';

import randomID from 'utils/random-id';

class SupportedCountriesLayer extends Component {
  state = {};

  componentWillMount() {
    this.id = randomID();
    import('assets/data/borders.json').then(borders => {
      this.setState({ borders });
    });
  }

  componentDidMount() {
    const { countries, onFetchCountries } = this.props;
    if (!countries) {
      onFetchCountries();
    }
  }

  render() {
    const { countries } = this.props;
    const { borders } = this.state;
    return (
      <Fragment>
        <RenderAboveEverything className='hidden-overlay'>
          <svg width='100%' height='100%'>
            <pattern
              id={this.id}
              x={0}
              y={0}
              width={8}
              height={8}
              patternUnits='userSpaceOnUse'
            >
              <rect x={0} y={0} width={8} height={8} fill='rgba(0,0,0,0.3)' />
              <circle cx={2} cy={2} r={1} fill='white' />
            </pattern>
          </svg>
        </RenderAboveEverything>

        {borders && countries && (
          <GeoJSON
            data={{
              ...borders,
              features: borders.features.filter(
                f => !countries[f.properties.name],
              ),
            }}
            style={{
              weight: 0,
              fillColor: `url(#${this.id})`,
              fillOpacity: 0.6,
            }}
          />
        )}
      </Fragment>
    );
  }
}

const mapStateToProps = state => ({
  countries: fields.selectors.getPredictionCountries(state),
});

const mapDispatchToProps = {
  onFetchCountries: fields.actions.fetchPredictionCountries,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(SupportedCountriesLayer);
