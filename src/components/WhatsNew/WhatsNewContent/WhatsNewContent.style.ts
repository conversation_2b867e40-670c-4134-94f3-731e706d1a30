import { css } from 'linaria';
import { styled } from 'linaria/react';
import { animated } from 'react-spring';

export const styles = {
  semiPage: css`
    position: relative;
    box-sizing: border-box;
    padding: 25px 0 50px 25px;
    display: flex;
    flex-direction: column;
    &:nth-child(2) {
      justify-content: center;
      padding-right: 25px;
      padding-left: 0;
    }
  `,
  pageHeader: css`
    display: flex;
    position: absolute;
    left: 25px;
    top: 25px;
  `,
  pageHeaderIcon: css`
    font-size: 36px;
    line-height: 47px;
    margin-right: 14px;
  `,
  pageHeaderTitle: css`
    font-family: Graphik, sans-serif;
    font-weight: bold;
    font-size: 20px;
    line-height: 24px;
  `,
  pageHeaderDate: css`
    font-family: Graphik, sans-serif;
    font-size: 12px;
    color: #a5b2bc;
    line-height: 16px;
  `,
  pageContent: css`
    margin-top: 60px;
    padding-bottom: 10px;
    width: 100%;
    height: calc(100% - 60px);
    box-sizing: border-box;
    overflow-y: hidden;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  `,
  textContent: css``,
  title: css`
    font-family: Graphik, sans-serif;
    font-weight: bold;
    font-size: 36px;
    line-height: 45px;
    margin-bottom: 10px;
    margin-top: 0;
  `,
  text: css`
    font-size: 16px;
    line-height: 22px;
    margin-bottom: 22px;
  `,
  bullets: css`
    display: flex;
    position: absolute;
    z-index: 0;
    bottom: 25px;
    left: 25px;
    right: 25px;
    justify-content: center;
    align-items: center;
  `,
  pagePicture: css`
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    & img {
      display: block;
      width: 100%;
    }
  `,
  pagePictureCentered: css`
    & img {
      display: block;
      width: 100%;
      border-radius: 8%;
    }
  `,
};

export const StyledContainer = styled.div`
  position: relative;
  height: 550px;
  width: 100%;
  overflow: hidden;
`;

export const StyledPage = styled(animated.div)`
  position: absolute;
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: minmax(50%, 60%) minmax(40%, 50%);
`;

type StyledBulletProps = {
  isActive: boolean;
};

export const StyledBullet = styled(animated.button)<StyledBulletProps>`
  width: 8px;
  height: 8px;
  padding: 4px;
  margin-left: 2px;
  margin-right: 2px;
  border-radius: 50%;
  background-color: ${props => (props.isActive ? '#27AE60' : '#D5DBDF')};
  transition: background-color 0.3s;
  border: none;
`;

export const StyledPrevLink = styled(animated.a)`
  position: absolute;
  display: flex;
  align-items: center;
  left: 25px;
  bottom: 25px;
  font-weight: 500;
  font-size: 12px;
  line-height: 16px;
  color: #007aff;
  svg {
    fill: currentColor;
    margin-right: 8px;
  }
`;
