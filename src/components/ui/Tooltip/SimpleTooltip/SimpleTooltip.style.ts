import { css } from 'linaria';
import { styled } from 'linaria/react';

export const Small = css``;

export const SimpleTooltipContainer = css`
  position: absolute;
  z-index: 999;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  min-width: 145px;
  padding: 10px 15px 15px;
  font-size: 13px;
  line-height: 1.32;
  border: 1px solid rgba(214, 220, 225, 0.5);

  &.${Small} {
    border-radius: 6px;
    min-width: 0;
    padding: 5px 10px;
    font-size: 12px;
    line-height: 1.45;
  }

  &.__initial,
  &.__state-initial {
    opacity: 0;
  }

  &.__state-initial[data-direction='from-top'] {
    -webkit-transform: translate(0, -10px);
    transform: translate(0, -10px);
  }
  &.__state-initial[data-direction='from-bottom'] {
    -webkit-transform: translate(0, 10px);
    transform: translate(0, 10px);
  }
  &.__state-initial[data-direction='from-left'] {
    -webkit-transform: translate(-10px, 0);
    transform: translate(-10px, 0);
  }
  &.__state-initial[data-direction='from-right'] {
    -webkit-transform: translate(10px, 0);
    transform: translate(10px, 0);
  }

  &.__initial.__arrow-left,
  &.__state-initial.__arrow-left {
    -webkit-transform: translate(20px, 0);
    transform: translate(20px, 0);
  }
  &.__initial.__arrow-right,
  &.__state-initial.__arrow-right {
    -webkit-transform: translate(-20px, 0);
    transform: translate(-20px, 0);
  }
  &.__initial.__arrow-bottom,
  &.__state-initial.__arrow-bottom {
    -webkit-transform: translate(0, 20px);
    transform: translate(0, 20px);
  }
  &.__initial.__arrow-top,
  &.__state-initial.__arrow-top {
    -webkit-transform: translate(0, -20px);
    transform: translate(0, -20px);
  }
  &.__normal,
  &.__state-normal {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
    transition: -webkit-transform 0.15s;
    transition: transform 0.15s;
    transition: transform 0.15s, -webkit-transform 0.15s;
  }

  &[data-theme='dark'] {
    color: #fff;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.25);
    background-color: #222;
    border: 0;
  }
  &[data-theme='dark'] h2 {
    font-size: 16px;
    line-height: 22px;
    margin-top: 0;
    margin-bottom: 5px;
  }

  &[data-theme='onboarding'] {
    color: #fff;
    box-shadow: 0 5px 10px rgba(18, 51, 82, 0.37);
    border-radius: 7px;
    background-color: #007aff;
    padding: 15px 10px;
    border: 0;
  }
`;

export const SimpleTooltipArrowContainer = css`
  position: absolute;
  z-index: 2;
  overflow: hidden;

  .${SimpleTooltipContainer}.__arrow-bottom & {
    bottom: -14px;
    left: 50%;
    margin-left: -12px;
    width: 26px;
    height: 14px;
  }

  .${SimpleTooltipContainer}[data-theme='dark'].${Small}.__arrow-bottom & {
    bottom: -10px;
  }

  .${SimpleTooltipContainer}.__arrow-top & {
    top: -14px;
    left: 50%;
    margin-left: -12px;
    width: 26px;
    height: 14px;
  }
  .${SimpleTooltipContainer}[data-theme='dark'].${Small}.__arrow-top & {
    top: -10px;
  }

  .${SimpleTooltipContainer}.__arrow-left & {
    left: -14px;
    top: 50%;
    margin-top: -13px;
    height: 26px;
    width: 14px;
  }

  .${SimpleTooltipContainer}.__arrow-right & {
    right: -14px;
    top: 50%;
    margin-top: -13px;
    height: 26px;
    width: 14px;
  }
`;

export const SimpleTooltipArrowContainerInner = css`
  position: absolute;
  width: 11px;
  height: 11px;

  &:before {
    position: absolute;
    content: '';
    background-color: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(214, 220, 225, 0.5);
    width: 11px;
    height: 11px;
    border-top-right-radius: 2px;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.1);
  }

  .${SimpleTooltipContainer}[data-theme='dark'] &:before {
    background-color: #222;
    border: none;
  }

  .${SimpleTooltipContainer}[data-theme='onboarding'] &:before {
    background-color: #007aff;
    border: none;
  }

  .${SimpleTooltipContainer}.__arrow-bottom & {
    top: 50%;
    left: 50%;
    -webkit-transform: rotate(-90deg);
    transform: rotate(-90deg);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
  }

  .${SimpleTooltipContainer}.__arrow-top & {
    top: 50%;
    left: 50%;
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
  }

  .${SimpleTooltipContainer}.__arrow-left & {
    top: 50%;
    margin-top: -3px;
    right: -11px;
    -webkit-transform: rotate(-90deg);
    transform: rotate(-90deg);
    -webkit-transform-origin: center;
    transform-origin: center;
  }
  .${SimpleTooltipContainer}.${Small}.__arrow-left & {
    right: -13px;
  }

  .${SimpleTooltipContainer}.__arrow-right & {
    top: 50%;
    margin-top: -7px;
    left: -11px;
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
    -webkit-transform-origin: center;
    transform-origin: center;
  }
  .${SimpleTooltipContainer}.${Small}.__arrow-right & {
    left: -13px;
  }
`;

export const SimpleTooltipContent = css`
  .${SimpleTooltipContainer}.${Small} & {
    position: relative;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .${SimpleTooltipContainer}[data-theme='onboarding'] & {
    font-weight: 500;
    text-align: center;
  }

  & p {
    margin-top: 0;
    margin-bottom: 0;
  }
`;

export const TooltipTitleStyle = styled.div`
  font-weight: 700;
  font-size: 16px;
  line-height: 110%;
  padding-bottom: 5px;
  color: #ffffff;
`;

export const TooltipTextStyle = styled.div`
  font-weight: 500;
  font-size: 12px;
  line-height: 100%;
  color: #ffffff;
`;

export const TooltipDescriptionStyle = styled.div`
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  color: #999999;
`;

export const TooltipDescriptionListStyle = styled.ul`
  padding-left: 20px;
  margin-top: 5px;
`;
