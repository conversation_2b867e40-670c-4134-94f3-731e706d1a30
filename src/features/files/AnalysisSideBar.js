import React from 'react';
import { Route, matchPath } from 'react-router-dom';
import cx from 'classnames';

import TransitionStateMachine from 'components/TransitionStateMachine';

import UploadFilesSideBar from './sideBar/UploadFilesSideBar';
import FileListSideBar from './sideBar/FileListSideBar';
import EditFileSideBar from './sideBar/EditFileSideBar';
import AnalysisFieldSideBar from './sideBar/AnalysisFieldSideBar';

import { FEATURES, ProtectedRouteHOC } from 'features/permissions';

const AnalysisSideBar = ({ match, location, history }) => {
  const uploadFilesMatch = matchPath(location.pathname, {
    path: `${match.url}/upload/:fileId?`,
  });
  const filterByFieldMatch = matchPath(location.pathname, {
    path: `${match.url}/by-field/:fieldId/:fileId?`,
    exact: true,
  });

  const editFileMatch = matchPath(location.pathname, {
    path: `${match.url}/:fileId/edit`,
  });

  const editFileByFieldMatch = matchPath(location.pathname, {
    path: `${match.url}/by-field/:fieldId/:fileId/edit`,
  });

  const analyzeFieldMatch = matchPath(location.pathname, {
    path: `${match.url}/fields/:fieldId/:type`,
    exact: true,
  });

  return (
    <TransitionStateMachine
      enterTime={0}
      exitTime={300}
      active={[
        uploadFilesMatch,
        filterByFieldMatch,
        editFileMatch,
        editFileByFieldMatch,
        analyzeFieldMatch,
      ].some(Boolean)}
      render={({ state, wrapActiveChild }) => (
        <div className={cx('soil-sidebar', { __addnew: state === 'entered' })}>
          <Route path={`${match.url}/:fileId?`} component={FileListSideBar} />
          {uploadFilesMatch &&
            wrapActiveChild(
              <UploadFilesSideBar
                history={history}
                location={location}
                basePath={match.path}
                selectedFileId={
                  uploadFilesMatch && uploadFilesMatch.params.fileId
                }
              />,
            )}
          {filterByFieldMatch &&
            wrapActiveChild(
              <FileListSideBar
                history={history}
                location={location}
                match={filterByFieldMatch}
              />,
            )}
          {(editFileMatch || editFileByFieldMatch) &&
            wrapActiveChild(
              <EditFileSideBar
                history={history}
                location={location}
                match={editFileMatch || editFileByFieldMatch}
              />,
            )}
          {analyzeFieldMatch &&
            wrapActiveChild(
              <AnalysisFieldSideBar
                history={history}
                location={location}
                match={analyzeFieldMatch}
              />,
            )}
        </div>
      )}
    />
  );
};

export default ProtectedRouteHOC({
  Component: AnalysisSideBar,
  feature: FEATURES.FIELD_CALC_PARAM,
  redirect: '/fields',
});
