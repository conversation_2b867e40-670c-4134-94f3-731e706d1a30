import React, { type FC } from 'react';

import { styles } from './CrossIcon.styles';

const CrossIcon: FC = () => {
  return (
    <div className={styles.crossContainer}>
      <svg
        className={styles.crossSvg}
        xmlns='http://www.w3.org/2000/svg'
        viewBox='0 0 60 60'
      >
        <circle
          className={styles.crossCircle}
          cx='30'
          cy='30'
          r='25'
          fill='none'
        />
        <path className={styles.firstCrossLine} fill='none' d='M20 20L40 40' />
        <path className={styles.secondCrossLine} fill='none' d='M40 20L20 40' />
      </svg>
    </div>
  );
};

export default CrossIcon;
