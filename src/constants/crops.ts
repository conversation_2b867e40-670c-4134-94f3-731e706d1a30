import memoize from 'lodash/memoize';
import range from 'lodash/range';
import xor from 'lodash/xor';
import invert from 'lodash/invert';
import countBy from 'lodash/countBy';
import sortBy from 'lodash/sortBy';
import uniqBy from 'lodash/uniqBy';

import RemovalRate from './phosphor-potassium-removal';

import { colors } from 'constants/style';
import { TranslateStringFunction } from '../utils/use-translate';
import { CropType } from '../types/crops';

export const DefaultCrops = {
  2020: 'unknown',
  2019: 'unknown',
  2018: 'unknown',
  2017: 'unknown',
  2016: 'unknown',
};

export const getSortedTargetCropTypes = (t: TranslateStringFunction) =>
  Object.keys(RemovalRate).sort((a, b) => t(a).localeCompare(t(b)));

export const getCropCategories = memoize(translations => {
  const categoryKeys = Object.keys(translations).filter(
    key =>
      key.startsWith('bbch.') && key.endsWith('.groups') && translations[key],
  );
  const categoryByCrop: Record<string, string> = {};
  for (const key of categoryKeys) {
    for (const crop of translations[key].split(',')) {
      categoryByCrop[crop] = key.slice('bbch.'.length, -'.groups'.length);
    }
  }
  return categoryByCrop;
});

export const getCropColors = (
  crops: { crop: string }[],
  cropColors: Record<string, string>,
) => {
  const cropsUniq = uniqBy(crops, 'crop').map(({ crop }) => crop);

  const updatedCropColors = Object.keys(cropColors)
    .filter(key => cropsUniq.includes(key))
    .reduce<Record<string, string>>((newColors, crop) => {
      newColors[crop] = cropColors[crop] as string;
      return newColors;
    }, {});

  return cropsUniq.reduce(
    (newCropColors, crop) => {
      if (!newCropColors[crop]) {
        const [color] = xor(Object.values(newCropColors), colors.cropColors);
        if (color) {
          newCropColors[crop] = color;
        } else {
          const cropColorsData = Object.values(newCropColors);

          // sort by design crop colors
          const sortedByUsedColors = sortBy(cropColorsData, item =>
            colors.cropColors.indexOf(item),
          );

          // get the most unused colors first
          sortedByUsedColors.reverse();

          // combine it into object with duplicates count
          const duplicatesValues = countBy(sortedByUsedColors);

          // get inverted values
          const uniqValues = invert(duplicatesValues);

          const [color] = Object.values(uniqValues);

          newCropColors[crop] = color as string;
        }
      }
      return newCropColors;
    },
    { ...updatedCropColors },
  );
};

export const sortCropsColorsByFieldCount = (
  ownFields: { crops: CropType[] }[],
  cropColors: Record<string, string>,
  noCrops: boolean,
  multipleCrops: boolean,
) => {
  const cropsCount = countBy(
    ownFields.filter(({ crops }) => crops.length === 1),
    'crops[0].crop',
  );

  const sortedCropColorsByFieldsCount = Object.keys(cropsCount)
    // @ts-ignore
    .sort((crop1, crop2) => cropsCount[crop2] - cropsCount[crop1])
    .reduce<{ crop: string; color?: string }[]>((colors, crop) => {
      colors.push({ crop, color: cropColors[crop] });
      return colors;
    }, []);

  if (noCrops) {
    sortedCropColorsByFieldsCount.push({
      crop: 'no_crops',
      color: colors.noValues,
    });
  }

  if (multipleCrops) {
    sortedCropColorsByFieldsCount.push({
      crop: 'multiple_crops',
      color: colors.multipleValues,
    });
  }

  return sortedCropColorsByFieldsCount;
};

export const getCropColor = (
  label: string,
  currentCropColors: Record<string, string>,
) => {
  if (label === 'no_crops') {
    return '#FFFFFF';
  }

  if (label === 'multiple_crops') {
    return '#222222';
  }

  if (currentCropColors[label]) {
    return currentCropColors[label];
  }

  const currentColors = Object.values(currentCropColors);

  const [color] = xor(currentColors, colors.cropColors);
  if (color) {
    return color;
  } else {
    const sortedByUsedColors = sortBy(currentColors, item =>
      colors.cropColors.indexOf(item),
    );

    sortedByUsedColors.reverse();

    const duplicatesValues = countBy(sortedByUsedColors);

    const uniqValues = invert(duplicatesValues);

    const [color] = Object.values(uniqValues);
    return color;
  }
};

export const getKnownStages = (
  translations: Record<string, string>,
  crop: string,
  min = 0,
  max = 99,
) => {
  const category = getCropCategories(translations)[crop];
  if (!category) {
    return range(min, max + 1);
  }
  const prefix = `bbch.${category}.`;
  return Object.keys(translations)
    .filter(key => key.startsWith(prefix) && !key.endsWith('.groups'))
    .filter(key => translations[key])
    .map(key => +key.slice(prefix.length))
    .filter(v => v >= min && v <= max);
};

export const CropSource = {
  newField: 'when_add_field',
  field: 'field_card',
  rotation: 'rotated',
  autofill: 'crop_autofill',
  upload: 'upload',
};
