import React from 'react';
import classnames from 'classnames';

import { formatNumberToIntegers } from 'features/multiaccount/dealer/utils';

import { useTranslate } from 'utils/use-translate';

import { styles } from './FieldsHeaderInfo.styles';

export type FieldsHeaderRowDataType = {
  rowName: string;
  allocated: string;
  used: string;
};

type TFieldsHeaderInfoProps = {
  firstColumnName?: string;
  secondColumnName?: string;
  rowData: FieldsHeaderRowDataType[];
};

function FieldsHeaderInfo({
  firstColumnName,
  secondColumnName,
  rowData,
}: TFieldsHeaderInfoProps) {
  const { t } = useTranslate();

  if (!rowData.length) {
    return <div className={classnames('td', styles.emptyContainer)} />;
  }

  return (
    <>
      <div className={classnames(styles.dataRowNamesColumn)}>
        {firstColumnName || secondColumnName ? (
          <span className={styles.columnEmpty}>
            {t('multiaccount.dealer.fields-header.row-name')}
          </span>
        ) : null}
        {rowData.map(({ rowName }) => (
          <div key={`name-${rowName}`} className={styles.rowNameContainer}>
            <span className={styles.rowName}>{t(rowName)}</span>
            <span className={styles.dataRowNamesDotted} />
          </div>
        ))}
      </div>

      <div className={classnames(styles.allocatedColumn)}>
        {firstColumnName ? (
          <span className={styles.columnName}>{firstColumnName}</span>
        ) : null}
        {rowData.map(({ allocated, rowName }) => (
          <div
            key={`allocated-${rowName}-${allocated}`}
            className={styles.allocatedContainer}
          >
            <span className={styles.rowData}>
              {formatNumberToIntegers(allocated)}
            </span>
            <span className={styles.allocatedDotted} />
          </div>
        ))}
      </div>

      <div className={classnames(styles.usedColumn)}>
        {secondColumnName ? (
          <span className={styles.columnName}>{secondColumnName}</span>
        ) : null}
        {rowData.map(({ used, rowName }) => (
          <div key={`used-${rowName}-${used}`} className={styles.usedContainer}>
            <span className={styles.rowData}>
              {formatNumberToIntegers(used)}
            </span>
          </div>
        ))}
      </div>
    </>
  );
}

export default FieldsHeaderInfo;
