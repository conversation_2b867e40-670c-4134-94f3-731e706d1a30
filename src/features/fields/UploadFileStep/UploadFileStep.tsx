import React, {
  useEffect,
  useState,
  use<PERSON><PERSON><PERSON>,
  VFC,
  DragEventHandler,
} from 'react';
import cx from 'classnames';
import { useSelector, useDispatch } from 'react-redux';
import JSZip from 'jszip';

import Saga from 'components/saga/Saga';
import Button from 'components/ui/Button/Button';
import Icon from 'components/Icon';

import * as H from 'history';
import api from 'modules/api';
// @ts-ignore
import fields from 'modules/fields';

import uploadFilesSaga from 'sagas/local/upload-files';
import logEvent from 'sagas/global/logEvent';
import getRootScoutingUrl from 'utils/get-root-scouting-url';
import { parseQuery } from 'utils/query';
import { useTranslate } from 'utils/use-translate';

import { readFile } from 'utils/files';
import {
  MaxFileSize,
  FileFormats,
  getExtension,
  hasDuplicates,
  hasSameNames,
  FileFormatsType,
} from 'utils/import-fields';
import DropArea from 'components/inputs/DropArea';
import { FloatingActionsStyle } from 'components/ui/FloatingActions/styles';
import RootState from 'types/rootState';
import { FieldsUsersFilesValidationError } from 'types/api';
import { ServerErrorType, UploadFileErrorType } from './types';
import UploadFileErrorPopup from './UploadFileErrorPopup';

type LocationState = {
  files?: Iterable<File> | ArrayLike<File>;
  from?: string;
};

type UploadFileStepProps = {
  history: H.History<LocationState>;
  location: H.Location<LocationState>;
};

const UploadFileStep: VFC<UploadFileStepProps> = ({ history, location }) => {
  const { t } = useTranslate();
  const saveRequest = useSelector((state: RootState) =>
    api.selectors.getRequestStatus(state, 'create-fields-file'),
  );
  const [files, setFiles] = useState<Record<FileFormatsType, File>>({});
  const [error, setError] = useState<UploadFileErrorType | null>(null);
  const [serverError, setServerError] = useState<ServerErrorType | null>(null);
  const [isValid, setIsValid] = useState<boolean>(false);

  const baseUrl = getRootScoutingUrl(history.location.pathname);

  const dispatch = useDispatch();

  const handleServerError = (error: FieldsUsersFilesValidationError) => {
    const errorDetails = error.error_details || [];

    setServerError(error);

    const errorCode =
      errorDetails.length > 0 ? errorDetails[0]?.code : error.error_code;

    logEvent('fields_import_files_error', { error: errorCode });
  };

  const setAndLogError = (e: UploadFileErrorType | null) => {
    setError(e);

    if (e !== null && e.status) {
      logEvent('fields_import_files_error', { error: e.status });
    }
  };

  const handleDrop: DragEventHandler<Element> = event => {
    event.preventDefault();

    const filesArray = Array.from(event.dataTransfer.files);
    logEvent('fields_import_files', { via: 'drag' });
    handleFiles(filesArray); // eslint-disable-line
  };

  const validateSize = useCallback(files => {
    let valid = true;
    for (const file of files) {
      if (file.size > MaxFileSize) {
        valid = false;
        setAndLogError({ status: 'too_big_file' });
      }
    }
    return valid;
  }, []);

  const unpackAllArchives = async (files: File[]) => {
    const allFiles = [];
    for (const file of files) {
      if (file.name.endsWith('.zip')) {
        const data = await readFile(file);
        const zip = await JSZip.loadAsync(data);

        zip.forEach((path, file) => {
          // Remove folders
          if (file.dir) return;
          // This is mac internal metadata folder
          if (
            file.name.includes('__MACOSX') ||
            file.name.includes('.DS_Store')
          ) {
            return;
          }
          file.name = file.name.split('/').pop() as string;
          allFiles.push(file);
        });
      } else {
        allFiles.push(file);
      }
    }
    return allFiles;
  };

  const handleFiles = useCallback(
    async (filesArray: File[]) => {
      const packFiles: Record<FileFormatsType, File> = { ...files };

      const validSize = validateSize(filesArray);
      if (!validSize) return;

      let error: UploadFileErrorType['status'] | null = null;
      let valid = false;

      const allFiles = await unpackAllArchives(filesArray);

      for (const file of allFiles) {
        const ext = getExtension(file.name);
        if (!FileFormats.includes(ext)) {
          error = 'unsupported_file';
          break;
        }

        if (packFiles[ext]) {
          error = 'same_format';
          break;
        } else {
          packFiles[ext] = file;
        }
      }

      if (error) {
        setAndLogError({ status: error });
        return;
      }

      const filesValues = Object.values(packFiles);
      const allFilesExts = filesValues.map(item => getExtension(item.name));

      const MAX_FILES_VALUES = 7;

      if (filesValues.length > MAX_FILES_VALUES) {
        error = 'too_many_files';
      } else {
        if (hasDuplicates(allFilesExts)) {
          error = 'same_format';
        } else {
          if (filesValues.length > 1) {
            if (!hasSameNames(filesValues)) {
              error = 'filenames_not_same';
            } else {
              valid = true;
            }
          } else {
            valid = true;
          }
        }
      }

      if (valid) {
        setFiles(packFiles);

        if (packFiles.shp && packFiles.dbf) {
          setError(null);
          setIsValid(valid);
        } else if (packFiles.shp) {
          setAndLogError({ status: 'dbf_missing' });
        } else if (
          packFiles.dbf ||
          packFiles.prj ||
          packFiles.cpg ||
          packFiles.shx ||
          packFiles.sbn ||
          packFiles.sbx ||
          packFiles.qpj
        ) {
          setAndLogError({ status: 'shp_missing' });
        } else {
          setError(null);
          setIsValid(valid);
        }
      } else {
        setAndLogError({ status: error });
      }
    },
    [files, validateSize],
  );

  const revalidateForm = (files: Record<FileFormatsType, File>) => {
    setAndLogError(null);
    const filesLength = Object.keys(files).length;

    if (filesLength) {
      if (files.shp && files.dbf) {
        setIsValid(true);
      } else if (files.shp) {
        setAndLogError({ status: 'dbf_missing' });
        setIsValid(false);
      } else if (
        files.dbf ||
        files.prj ||
        files.cpj ||
        files.shx ||
        files.sbn ||
        files.sbx ||
        files.qpj
      ) {
        setAndLogError({ status: 'shp_missing' });
        setIsValid(false);
      }
    } else {
      setIsValid(false);
    }
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    logEvent('fields_import_files', { via: 'input' });

    handleFiles(Array.from(event.target.files as Iterable<File>));
    // @ts-ignore
    event.target.value = null;
  };

  const handleRemoveFile = (fileType: FileFormatsType) => {
    const newFiles: Record<FileFormatsType, File> = { ...files };
    delete newFiles[fileType];
    setFiles(newFiles);

    revalidateForm(newFiles);
  };

  const handleSubmit = async () => {
    logEvent('fields_import_send_files', { files: Object.keys(files) });
    dispatch(fields.actions.sendFiles(Object.values(files)));
  };

  useEffect(() => {
    if (location?.state?.files) {
      let state = { ...history.location.state };

      const filesArray = Array.from(state.files || []);
      handleFiles(filesArray);

      delete state.files;
      history.replace({ ...history.location, state });
    }
  }, [history, location, handleFiles]);

  return (
    <>
      <Saga
        saga={uploadFilesSaga}
        history={history}
        handleError={handleServerError}
      />
      {serverError && (
        <UploadFileErrorPopup
          onClose={() => setServerError(null)}
          error={serverError}
        />
      )}
      <div className='main-section__body'>
        {saveRequest.status === 'pending' && (
          <div className='main-section__overlay' />
        )}
        <form onSubmit={() => handleSubmit()}>
          <div className='main-uploader' data-type='compact'>
            <div className='main-uploader-header'>
              <h2 className='main-uploader-header__title'>
                {t('fields.upload.upload_file.title')}
              </h2>
              <p
                className={cx('main-uploader-header__description', {
                  'c-exclamation': error,
                })}
              >
                {!error ? (
                  t('fields.upload.upload_file.sub_title')
                ) : (
                  <>
                    <Icon
                      className='ico-exclamation-mask-os'
                      name='exclamation-mask'
                    />
                    {t(`fields.upload_error.${error.status}`)}
                  </>
                )}
              </p>
            </div>
            <div className='main-uploader-ways'>
              <div className='main-uploader-ways__longway'>
                <div className='main-uploader-types'>
                  <div className='main-uploader-types__list-item'>
                    <div className='main-uploader-types__ext'>.shp</div>
                    <div className='main-uploader-types__md'>
                      {!files.shp ? (
                        t('fields.upload.upload_file.option.mandatory')
                      ) : (
                        <div className='main-uploader-types-files'>
                          <div className='main-uploader-types-files__name'>
                            {files.shp.name}
                          </div>
                          <button
                            type='button'
                            className='main-uploader-types-files__remove'
                            onClick={() => handleRemoveFile('shp')}
                          >
                            <Icon className='ico-close-os' name='close' />
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className='main-uploader-types__list-item'>
                    <div className='main-uploader-types__ext'>.dbf</div>
                    <div className='main-uploader-types__md'>
                      {!files.dbf ? (
                        t('fields.upload.upload_file.option.mandatory')
                      ) : (
                        <div className='main-uploader-types-files'>
                          <div className='main-uploader-types-files__name'>
                            {files.dbf.name}
                          </div>
                          <button
                            type='button'
                            className='main-uploader-types-files__remove'
                            onClick={() => handleRemoveFile('dbf')}
                          >
                            <Icon className='ico-close-os' name='close' />
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className='main-uploader-types__list-item'>
                    <div className='main-uploader-types__ext'>.prj</div>
                    <div className='main-uploader-types__md'>
                      {!files.prj ? (
                        t('fields.upload.upload_file.option.optional')
                      ) : (
                        <div className='main-uploader-types-files'>
                          <div className='main-uploader-types-files__name'>
                            {files.prj.name}
                          </div>
                          <button
                            type='button'
                            className='main-uploader-types-files__remove'
                            onClick={() => handleRemoveFile('prj')}
                          >
                            <Icon className='ico-close-os' name='close' />
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                <div className='main-uploader-ways__desc'>
                  {t('fields.upload.upload_file.main_description')}
                </div>
              </div>

              <div className='main-uploader-ways__or'>
                {t('fields.upload.upload_file.or')}
              </div>
              <div className='main-uploader-ways__shortway'>
                <div className='main-uploader-types'>
                  <div className='main-uploader-types__list-item'>
                    <div className='main-uploader-types__ext'>
                      {(!files.kml && !files.kmz) || files.kml
                        ? '.kml'
                        : '.kmz'}
                    </div>
                    <div className='main-uploader-types__md'>
                      {!files.kml && !files.kmz ? (
                        t('fields.upload.upload_file.option.or')
                      ) : (
                        <div className='main-uploader-types-files'>
                          <div className='main-uploader-types-files__name'>
                            {files.kml ? files.kml.name : files.kmz?.name}
                          </div>
                          <button
                            type='button'
                            className='main-uploader-types-files__remove'
                            onClick={() => {
                              const fileType = files.kml ? 'kml' : 'kmz';
                              handleRemoveFile(fileType);
                            }}
                          >
                            <Icon className='ico-close-os' name='close' />
                          </button>
                        </div>
                      )}
                    </div>
                    {!files.kml && !files.kmz && (
                      <div className='main-uploader-types__ext'>.kmz</div>
                    )}
                  </div>
                </div>
              </div>
            </div>
            <div className='main-uploader-ways__other'>
              {files.cpg && (
                <div className='main-uploader-types-files'>
                  <div className='main-uploader-types-files__name'>
                    {files.cpg.name}
                  </div>
                  <button
                    type='button'
                    className='main-uploader-types-files__remove'
                    onClick={() => handleRemoveFile('cpg')}
                  >
                    <Icon className='ico-close-os' name='close' />
                  </button>
                </div>
              )}
              {files.shx && (
                <div className='main-uploader-types-files'>
                  <div className='main-uploader-types-files__name'>
                    {files.shx.name}
                  </div>
                  <button
                    type='button'
                    className='main-uploader-types-files__remove'
                    onClick={() => handleRemoveFile('shx')}
                  >
                    <Icon className='ico-close-os' name='close' />
                  </button>
                </div>
              )}
              {files.sbx && (
                <div className='main-uploader-types-files'>
                  <div className='main-uploader-types-files__name'>
                    {files.sbx.name}
                  </div>
                  <button
                    type='button'
                    className='main-uploader-types-files__remove'
                    onClick={() => handleRemoveFile('sbx')}
                  >
                    <Icon className='ico-close-os' name='close' />
                  </button>
                </div>
              )}
              {files.sbn && (
                <div className='main-uploader-types-files'>
                  <div className='main-uploader-types-files__name'>
                    {files.sbn.name}
                  </div>
                  <button
                    type='button'
                    className='main-uploader-types-files__remove'
                    onClick={() => handleRemoveFile('sbn')}
                  >
                    <Icon className='ico-close-os' name='close' />
                  </button>
                </div>
              )}
              {files.qpj && (
                <div className='main-uploader-types-files'>
                  <div className='main-uploader-types-files__name'>
                    {files.qpj.name}
                  </div>
                  <button
                    type='button'
                    className='main-uploader-types-files__remove'
                    onClick={() => handleRemoveFile('qpj')}
                  >
                    <Icon className='ico-close-os' name='close' />
                  </button>
                </div>
              )}
              {['gpkg', 'gdb', 'json', 'geojson', 'gmt', 'jml'].includes(
                Object.keys(files)[0] as FileFormatsType,
              ) ? (
                <div className='main-uploader-types-files'>
                  <div className='main-uploader-types-files__name'>
                    {files[Object.keys(files)[0] as FileFormatsType]?.name}
                  </div>
                  <button
                    type='button'
                    className='main-uploader-types-files__remove'
                    onClick={() =>
                      handleRemoveFile(Object.keys(files)[0] as FileFormatsType)
                    }
                  >
                    <Icon className='ico-close-os' name='close' />
                  </button>
                </div>
              ) : (
                !files.shx &&
                !files.cpg &&
                !files.sbx &&
                !files.sbn &&
                !files.qpj &&
                t('fields.upload.upload_file.other_formats')
              )}
            </div>
            <DropArea onDrop={handleDrop} onInputChange={handleInputChange} />
          </div>
        </form>
      </div>
      <FloatingActionsStyle>
        <Button
          className='btn btn-dark btn-lg'
          onClick={() => {
            // @ts-ignore
            const query = parseQuery(history.location);
            if (query.next) {
              // @ts-ignore
              window.location = decodeURIComponent(query.next);
              return;
            }

            const { state } = history.location;
            history.replace(state?.from ? state.from : `${baseUrl}`);
          }}
        >
          {t('forms.cancel')}
        </Button>
        <Button
          className='btn btn-success btn-lg'
          type='submit'
          disabled={!isValid}
          pending={saveRequest.status === 'pending'}
          onClick={() => handleSubmit()}
        >
          {t('forms.next')}
        </Button>
      </FloatingActionsStyle>
    </>
  );
};

export default UploadFileStep;
