import React from 'react';
import { Redirect, withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import auth from 'modules/auth';

const RequireAuthorization = ({ history, location, isAuthorized }) => {
  if (isAuthorized) {
    return null;
  }
  return (
    <Redirect
      to={{
        pathname: '/auth/login',
        search: `?next=${location.pathname}&internal=true`,
      }}
    />
  );
};

const mapStateToProps = state => ({
  isAuthorized: auth.selectors.isAuthorized(state),
});

export default withRouter(connect(mapStateToProps)(RequireAuthorization));
