import React, { useState, useRef, useEffect } from 'react';
import cx from 'classnames';
import DocumentKeyHandler from 'components/DocumentKeyHandler';

const EditFieldTitle = ({ placeholder, value, onChange }) => {
  const [editing, setEditing] = useState(false);
  const [tempValue, setTempValue] = useState('');
  const inputRef = useRef();
  useEffect(() => {
    if (editing) {
      inputRef.current.focus();
    }
  }, [editing]);
  return (
    <div
      className={cx('field-edit-maintitle', {
        '__edit-field': editing,
      })}
      data-placeholder={editing && !tempValue ? '' : placeholder}
      onClick={() => {
        if (editing) {
          return;
        }
        setTempValue(value);
        setEditing(true);
      }}
    >
      <DocumentKeyHandler
        hotkey='Escape'
        onPress={() => {
          if (!editing) {
            return;
          }
          setEditing(false);
          onChange(tempValue);
        }}
      />
      {editing ? (
        <input
          ref={inputRef}
          className='field-edit-maintitle__value'
          placeholder={placeholder}
          value={tempValue}
          onChange={event => {
            setTempValue(event.target.value);
          }}
          onBlur={() => {
            setEditing(false);
            onChange(tempValue);
          }}
          onKeyPress={event => {
            if (event.key === 'Enter') {
              event.preventDefault();
              setEditing(false);
              onChange(tempValue);
            }
          }}
        />
      ) : (
        <div className='field-edit-maintitle__value'>{value}</div>
      )}
    </div>
  );
};

export default EditFieldTitle;
