import { css } from 'linaria';

export const styles = {
  emptyContainer: css`
    width: 650px;
    flex-grow: 615;
  `,
  dataRowNamesColumn: css`
    width: 260px;
    flex-grow: 260;
    display: flex;
    flex-direction: column;
  `,
  columnEmpty: css`
    color: transparent;
    font-size: 12px;
    font-weight: 500;
  `,
  rowNameContainer: css`
    display: flex;
    margin: 3px 0 0 0;
  `,
  allocatedColumn: css`
    width: 122px;
    flex-grow: 122;
    display: flex;
    flex-direction: column;
  `,
  columnName: css`
    color: #636366;
    font-size: 12px;
    font-weight: 500;
  `,
  allocatedContainer: css`
    display: flex;
  `,
  usedColumn: css`
    width: 128px;
    flex-grow: 128;
    display: flex;
    flex-direction: column;
  `,
  usedContainer: css`
    display: flex;
  `,
  rowName: css`
    color: #636366;
    font-size: 12px;
    font-weight: 500;
  `,
  rowData: css`
    font-size: 14px;
  `,
  dataRowNamesDotted: css`
    display: block;
    flex: 1;
    height: 12px;
    margin: 0 3px 0 5px;
    min-width: 10px;
    position: relative;

    &:before {
      border-bottom: 2px dotted #636366;
      bottom: 0;
      content: '';
      left: 0;
      position: absolute;
      right: 0;
    }
  `,
  allocatedDotted: css`
    display: block;
    flex: 1;
    height: 12px;
    margin: 3px 3px 0 5px;
    min-width: 10px;
    position: relative;

    &:before {
      border-bottom: 2px dotted #636366;
      bottom: 0;
      content: '';
      left: 0;
      position: absolute;
      right: 0;
    }
  `,
};
