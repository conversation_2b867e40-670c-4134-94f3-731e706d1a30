import cx from 'classnames';
import React, {
  Change<PERSON>vent,
  ChangeEventHandler,
  DragEvent,
  ReactNode,
  useState,
  VFC,
  useRef,
} from 'react';

import {
  DropAreaIcon,
  DropAreaDescription,
  DropAreaTitle,
  fileInput,
} from './styles';
import Icon from 'components/Icon';

type DropAreaProps = {
  onLoadFiles?: (files: File[]) => void;
  onDrop?: (e: DragEvent) => void;
  onInputChange?: ChangeEventHandler;
  buttonLabel?: ReactNode | string;
  title: ReactNode | string;
  description: ReactNode | string;
  accept?: string;
};

export const DropArea: VFC<DropAreaProps> = props => {
  const {
    accept = '*',
    onDrop,
    onInputChange,
    onLoadFiles,
    buttonLabel,
    title,
    description,
  } = props;
  const inputFile = useRef<HTMLInputElement | null>(null);

  const [dragging, setDragging] = useState(false);

  const handleInputChange = (event: ChangeEvent<HTMLInputElement>) => {
    onInputChange && onInputChange(event);
    if (onLoadFiles) {
      onLoadFiles(Array.from(event.target.files || []));
    }
    // @ts-ignore
    event.target.value = null;
  };

  const handleDrop = (event: DragEvent) => {
    event.preventDefault();
    setDragging(false);

    if (onDrop) {
      onDrop(event);
    }
    if (onLoadFiles) {
      onLoadFiles(Array.from(event.dataTransfer.files));
    }
  };

  const handleDragOver = (event: DragEvent) => {
    event.preventDefault();
    setDragging(true);
  };

  const handleDragLeave = (event: DragEvent) => {
    event.preventDefault();
    setDragging(false);
  };

  return (
    <div
      onDrop={e => handleDrop(e)}
      onDragOver={e => handleDragOver(e)}
      onDragLeave={e => handleDragLeave(e)}
      className={cx('main-uploader-area', {
        __highlight: dragging,
      })}
    >
      <div className='main-uploader-area__inner'>
        <DropAreaTitle>{title}</DropAreaTitle>
        <DropAreaDescription>{description}</DropAreaDescription>
        <DropAreaIcon>
          <Icon name='archive-file' />
        </DropAreaIcon>
        <label className='btn btn-success fit-content'>
          {buttonLabel}
          <input
            id='drop-area-file-input'
            accept={accept}
            ref={inputFile}
            className={fileInput}
            type='file'
            multiple
            onChange={handleInputChange}
          />
        </label>
      </div>
    </div>
  );
};
