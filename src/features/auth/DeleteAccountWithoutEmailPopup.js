import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { call, take } from 'redux-saga/effects';

import Icon from 'components/Icon';
import Button from 'components/ui/Button/Button';
import useHistory from 'utils/use-history';
import RenderAboveEverything from 'components/RenderAboveEverything';
import { resolved } from 'sagas/global/api';
import logEvent from 'sagas/global/logEvent';

import auth from 'modules/auth';

import useSaga from 'utils/use-saga';
import { useTranslate } from 'utils/use-translate';

const DeleteAccountWithoutEmailPopup = ({ onClose }) => {
  const { t } = useTranslate();
  const userID = useSelector(auth.selectors.getUserID);
  const history = useHistory();
  const dispatch = useDispatch();

  useSaga(function*() {
    const action = yield take(resolved(auth.actions.deleteAccount));
    const { confirmation_token } = action.payload;

    yield call(
      [history, history.replace],
      `/internal/delete/${confirmation_token}`,
    );

    yield call(logEvent, 'delete_account_request');
    yield call(onClose);
  }, []);

  const onSubmit = () => {
    dispatch(auth.actions.deleteAccount(userID));
  };

  return (
    <RenderAboveEverything>
      <div className='modal modal-editseasons'>
        <div className='modal-outer'>
          <div className='modal-editseasons-content'>
            <div className='modal-editseasons-content__header'>
              {t('delete_account.confirm.title')}
            </div>
            <div className='modal-editseasons-content__body'>
              <div className='form-group'>
                {t('delete_account.confirm.notification')}
              </div>
            </div>
            <div className='modal-editseasons-content__actions'>
              <Button
                className='btn btn-primary btn-lg'
                onClick={() => {
                  onClose();
                }}
              >
                {t('forms.cancel')}
              </Button>
              <Button className='btn btn-danger btn-lg' onClick={onSubmit}>
                <span className='btn__ico'>
                  <Icon className='ico-trash-n-os' name='trashcan' />
                </span>
                {t('delete_account.button.remove')}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </RenderAboveEverything>
  );
};

export default DeleteAccountWithoutEmailPopup;
