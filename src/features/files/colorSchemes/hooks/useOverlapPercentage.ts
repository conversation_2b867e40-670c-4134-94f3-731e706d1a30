import { StringMap } from 'i18next';
import { useTranslate } from 'utils/use-translate';

import { clamp, formatPercentage } from 'features/files/utils/formats';
import { FileDataSourceType } from 'features/files/colorSchemes/types';

export const useOverlapPercentage = ({ file }: FileDataSourceType) => {
  const { t } = useTranslate();
  const overlap = file.overlap && clamp(file.overlap.ratio, 0, 1);

  if (!Number.isFinite(overlap)) {
    return null;
  }

  return t('files.legend.overlap', {
    ratio: formatPercentage(overlap),
  } as StringMap) as string;
};
