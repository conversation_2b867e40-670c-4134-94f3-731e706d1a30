import React, { FC, useRef } from 'react';
import { useSpring } from 'react-spring';

import { CellStyle } from './styles';

export type TableCellProps = {
  invisible?: boolean;
};

export const TableCell: FC<TableCellProps> = ({ children, invisible }) => {
  const ref = useRef<HTMLDivElement>(null);
  const animation = useSpring({
    from: { maxHeight: invisible ? 45 : 0 },
    to: { maxHeight: invisible ? 0 : 48 },
    immediate: true,
    onRest: (style: TEMPORARY_ANY) => {
      if (ref.current && style?.maxHeight === 0) {
        ref.current.style.display = 'none';
      }
    },
    onFrame: (style: TEMPORARY_ANY) => {
      if (ref.current && style?.maxHeight > 0) {
        ref.current.style.display = 'flex';
      }
    },
    config: {
      duration: 300,
      delay: 500,
    },
  });

  return (
    <CellStyle
      ref={ref}
      // @ts-ignore
      style={animation}
    >
      {children}
    </CellStyle>
  );
};
