import React from 'react';
import { Route } from 'react-router-dom';
import cx from 'classnames';

import TransitionStateMachine from 'components/TransitionStateMachine';
import ModemListSideBar from './ModemListSideBar';

const ModemsSideBar = ({ match, location, history }) => {
  return (
    <TransitionStateMachine
      enterTime={0}
      exitTime={300}
      active={false}
      render={({ state, wrapActiveChild }) => (
        <div className={cx('soil-sidebar', { __addnew: state === 'entered' })}>
          <Route path={`${match.url}/:modemId?`} component={ModemListSideBar} />
        </div>
      )}
    />
  );
};

export default ModemsSideBar;
