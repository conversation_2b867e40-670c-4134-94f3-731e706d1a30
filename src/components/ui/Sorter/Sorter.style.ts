import { styled } from 'linaria/react';

export const StyledSorterButton = styled.button<{ isHidden?: boolean }>`
  font-family: Graphik, sans-serif;
  font-weight: 500;
  font-size: 12px;
  line-height: 16px;
  flex-shrink: 0;
  flex-grow: 0;
  margin-left: 10px;
  border: none;
  background: none;
  padding: 0;
  opacity: ${props => (props.isHidden ? 0 : 1)};
  visibility: ${props => (props.isHidden ? 'hidden' : 'visible')};
  transition: opacity 0.3s, visibility 0.3s;
  cursor: pointer;
  color: #007aff;
`;
