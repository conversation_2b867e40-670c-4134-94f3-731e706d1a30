import React from 'react';

import Icon, { IconName } from 'components/Icon';
import { CustomDropdown } from 'components/ui/CustomDropdown/CustomDropdown';
import { Popover } from 'components/ui/Popover';

import i18n from 'utils/i18n';
import logEvent from 'sagas/global/logEvent';

import { maKebab } from './styles';

export type CustomAttributeOption = 'edit-name' | 'remove';

const optionIcon: { [K in CustomAttributeOption]: IconName } = {
  'edit-name': 'yield-edit',
  remove: 'yield-trash',
};

type CustomAttributeKebabCellProps = {
  index: number;
  onShowEditAttributeNameModal: (id: number) => void;
  onRemoveAttribute: (index: number) => void;
};

export function CustomAttributeKebabCell({
  index,
  onShowEditAttributeNameModal,
  onRemoveAttribute,
}: CustomAttributeKebabCellProps) {
  const { t } = i18n;

  const attributeOptions: CustomAttributeOption[] = ['edit-name', 'remove'];

  return (
    <CustomDropdown
      placement='bottom'
      PopoverComponent={Popover}
      renderValue={() => (
        <div className={maKebab}>
          <Icon name='kebab' color='#5E5E5E' style={{ zIndex: 1 }} />
        </div>
      )}
      options={attributeOptions.map(optionId => ({
        label: t(`multiaccount.vendors.actions.${optionId}`),
        id: optionId,
        icon: optionIcon[optionId],
      }))}
      onClick={(event: React.MouseEvent) => event.stopPropagation()}
      onChange={action => {
        if (action === 'edit-name') {
          onShowEditAttributeNameModal(index);
          logEvent('web_soil_sampling_vis_rename_custom');
        } else if (action === 'remove') {
          onRemoveAttribute(index);
          logEvent('web_soil_sampling_vis_delete_custom');
        }
      }}
    />
  );
}
