import React, { Component } from 'react';
import { connect } from 'react-redux';
import { withTranslation } from 'react-i18next';
import cx from 'classnames';

import Saga from 'components/saga/Saga';
import RenderAboveEverything from 'components/RenderAboveEverything';

import files from 'modules/files';

import importFilesSaga from 'sagas/local/import-files';

const hasFiles = event => event.dataTransfer.types.includes('Files');

class FieldUploadOverlay extends Component {
  state = {
    hovered: false,
  };

  componentDidMount() {
    document.addEventListener('dragover', this.handleDragOver);
    document.addEventListener('dragleave', this.handleDragLeave);
    document.addEventListener('drop', this.handleDrop);
  }

  componentWillUnmount() {
    document.removeEventListener('dragover', this.handleDragOver);
    document.removeEventListener('dragleave', this.handleDragLeave);
    document.removeEventListener('drop', this.handleDrop);
  }

  handleDragOver = event => {
    event.preventDefault();
    this.setState({ hovered: hasFiles(event) });
  };

  handleDragLeave = event => {
    event.preventDefault();
    this.setState({ hovered: false });
  };

  handleDrop = event => {
    const { onImport } = this.props;
    event.preventDefault();
    this.setState({ hovered: false });
    if (!hasFiles(event)) {
      return;
    }
    onImport(Array.from(event.dataTransfer.files));
  };

  render() {
    const { history, t } = this.props;
    const { hovered } = this.state;
    return (
      <RenderAboveEverything>
        <Saga saga={importFilesSaga} history={history} />
        <div className={cx('upload-container', { __opened: hovered })}>
          <div className='upload-container__outer'>
            <div className='upload-container__inner'>
              <h2 className='upload-container__title'>
                {t('files.upload_overlay.title')}
              </h2>
              <p>{t('files.upload_overlay.description')}</p>
            </div>
          </div>
        </div>
      </RenderAboveEverything>
    );
  }
}

const mapDispatchToProps = {
  onImport: files.actions.import,
};

export default connect(
  null,
  mapDispatchToProps,
)(withTranslation()(FieldUploadOverlay));
