import React, { useState } from 'react';
import { call, put, take, takeEvery } from 'redux-saga/effects';
import { useSelector, useDispatch } from 'react-redux';
import { useForm } from 'react-hook-form';
import { styled } from 'linaria/react';
import groupBy from 'lodash/groupBy';

import Icon from 'components/Icon';
import Button from 'components/ui/Button/Button';
import LinkButton from 'components/LinkButton';
import { ModernTooltip } from 'components/ui/Tooltip/ModernTooltip/ModernTooltip';
import { SimpleTooltipTheme } from 'components/ui/Tooltip/SimpleTooltip/SimpleTooltip';
import VisiblePasswordInput from 'components/VisiblePasswordInput';
import RenderAboveEverything from 'components/RenderAboveEverything';

import api from 'modules/api';
import auth from 'modules/auth';
import toasts from 'modules/toasts';

import { resolved, rejected } from 'sagas/global/api';
import logEvent from 'sagas/global/logEvent';
import useQuery from 'utils/use-query';
import useSaga from 'utils/use-saga';
import { useTranslate } from 'utils/use-translate';

const ErrorLabel = styled.div`
  margin-top: 2px;
  font-size: 12px;
  color: #ff3b30;
`;

const ChangePasswordPopup = ({ onClose }) => {
  const { t } = useTranslate();
  const userID = useSelector(auth.selectors.getUserID);
  const dispatch = useDispatch();

  // eslint-disable-next-line no-unused-vars
  const [query, mergeQuery] = useQuery();
  const [errors, setErrors] = useState([]);
  const errorsByField = groupBy(errors, 'field');

  const { register, formState, handleSubmit } = useForm({
    mode: 'onChange',
  });

  const submitRequest = useSelector(state =>
    api.selectors.getRequestStatus(state, 'change-password'),
  );

  const onSubmit = form => {
    dispatch(auth.actions.changePassword(userID, form));
  };

  useSaga(function* () {
    yield takeEvery(rejected(auth.actions.changePassword), action => {
      const { error_details = [] } = action.payload;
      setErrors(error_details);
    });

    yield take(resolved(auth.actions.changePassword));

    yield put(
      toasts.actions.push('change-password', 'toasts.password_changed', {
        getLabel: ({ t, label }) => t(label),
        target: 'map-pane',
        position: 'bottom',
        icon: 'modem-done',
        ttl: Infinity,
      }),
    );

    yield call(logEvent, 'change_password');
    yield call(onClose);
  }, []);

  return (
    <RenderAboveEverything>
      <div className='modal modal-editseasons'>
        <div className='modal-outer'>
          <div className='modal-editseasons-content'>
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className='modal-editseasons-content__header'>
                {t('change_password.title')}
              </div>
              <div className='modal-editseasons-content__body'>
                <div className='form-group'>
                  <label className='form-label' htmlFor='old_password'>
                    {t('change_password.old_password.label')}
                    <div className='form-label__side'>
                      <LinkButton
                        onClick={() => {
                          mergeQuery({ settings: 'reset' }, 'push');
                          logEvent('forgot_password_click');
                        }}
                      >
                        {t('auth.forgot')}
                      </LinkButton>
                    </div>
                  </label>
                  <VisiblePasswordInput
                    id='old_password'
                    name='old_password'
                    autoComplete='current-password'
                    ref={register({ required: true })}
                    isValid={!errorsByField.old_password}
                  />
                  {errorsByField.old_password?.map(({ code }) => (
                    <ErrorLabel key={code}>
                      {t(`change_password.old_password.error.${code}`)}
                    </ErrorLabel>
                  ))}
                </div>

                <div className='form-group'>
                  <label className='form-label' htmlFor='new_password'>
                    {t('change_password.new_password.label')}{' '}
                    <ModernTooltip
                      active
                      offset={[5, 0]}
                      small
                      theme={SimpleTooltipTheme.DARK}
                      renderTooltip={() => (
                        <div className='text-center' style={{ maxWidth: 180 }}>
                          {t('change_password.new_password.hint')}
                        </div>
                      )}
                      className={'form-hint'}
                      renderTrigger={({ ref, ...otherProps }) => (
                        <Icon
                          innerRef={ref}
                          name='question-circle'
                          style={{ color: '#A5B2BC', opacity: 0.5 }}
                          width={12.25}
                          height={12.25}
                          {...otherProps}
                        />
                      )}
                    />
                  </label>
                  <VisiblePasswordInput
                    id='new_password'
                    name='new_password'
                    autoComplete='new-password'
                    ref={register({ required: true })}
                    isValid={!errorsByField.new_password}
                  />
                  {errorsByField.new_password?.map(({ code }) => (
                    <ErrorLabel key={code}>
                      {t(`change_password.new_password.error.${code}`)}
                    </ErrorLabel>
                  ))}
                </div>

                <div className='form-group'>
                  <label className='form-label' htmlFor='new_password_repeat'>
                    {t('change_password.new_password_repeat.label')}
                  </label>
                  <VisiblePasswordInput
                    id='new_password_repeat'
                    name='new_password_repeat'
                    autoComplete='new-password'
                    ref={register({ required: true })}
                    isValid={!errorsByField.new_password_repeat}
                  />
                  {errorsByField.new_password_repeat?.map(({ code }) => (
                    <ErrorLabel key={code}>
                      {t(`change_password.new_password_repeat.error.${code}`)}
                    </ErrorLabel>
                  ))}
                </div>
              </div>
              <div className='modal-editseasons-content__actions'>
                <Button
                  className='btn btn-primary btn-lg'
                  onClick={() => {
                    onClose();
                  }}
                >
                  {t('forms.cancel')}
                </Button>
                <Button
                  className='btn btn-success btn-lg'
                  pending={submitRequest.status === 'pending'}
                  disabled={!formState.isValid}
                  type='submit'
                >
                  <span className='btn__ico'>
                    <Icon className='ico-check-os' name='check' />
                  </span>
                  {t(`change_password.submit`)}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </RenderAboveEverything>
  );
};

export default ChangePasswordPopup;
