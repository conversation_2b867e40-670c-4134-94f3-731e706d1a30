import { CSSProperties, VFC } from 'react';
import { TranslateFunction } from 'utils/use-translate';
import { FeaturesKeyEnum } from './consts';

export type WhatsNewProps = {
  language: string;
  style: CSSProperties;
  onClick: () => void;
  t: TranslateFunction;
  isLast: boolean;
};

export type WhatsNewConfigType = {
  key: FeaturesKeyEnum;
  component: VFC<WhatsNewProps>;
  filter: Record<string, string | string[]>;
};

export type WhatsNewImage = [string, string];

export type ImageLanguageMap = {
  [key: string]: WhatsNewImage;
  default: WhatsNewImage;
};
