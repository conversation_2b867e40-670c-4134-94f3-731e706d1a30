import { Component } from 'react';
import isHotkey from 'is-hotkey';

type DocumentKeyHandlerProps = {
  onPress: (event: KeyboardEvent) => void;
};

type DocumentKeyHandlerKeyProps = {
  hotkey: string;
  hotkeys?: undefined;
} & DocumentKeyHandlerProps;

type DocumentKeyHandlerKeysProps = {
  hotkeys: string[];
  hotkey?: undefined;
} & DocumentKeyHandlerProps;

class DocumentKeyHandler extends Component<
  DocumentKeyHandlerKeyProps | DocumentKeyHandlerKeysProps
> {
  static defaultProps = {
    modifiers: [],
  };

  componentDidMount() {
    document.addEventListener('keydown', this.handleKeyDown);
  }

  componentWillUnmount() {
    document.removeEventListener('keydown', this.handleKeyDown);
  }

  handleKeyDown = (event: KeyboardEvent) => {
    const { hotkey, hotkeys, onPress } = this.props;
    const hotkeysToCheck = hotkeys || [hotkey];
    if (hotkeysToCheck.every(hotkey => !isHotkey(hotkey, event))) {
      return;
    }
    event.stopPropagation();
    event.preventDefault();
    onPress(event);
  };

  render() {
    return null;
  }
}

export default DocumentKeyHandler;
