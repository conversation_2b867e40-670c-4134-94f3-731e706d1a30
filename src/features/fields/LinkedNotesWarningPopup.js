import React from 'react';
import { css } from 'linaria';

import Modal from 'components/ui/Modal/Modal';
import Button from 'components/ui/Button/Button';

const popupText = css`
  font-size: 16px;
  line-height: 22px;
  text-align: center;
`;

const LinkedNotesWarningPopup = ({ t, title, text, onConfirm, onCancel }) => {
  return (
    <Modal
      show
      width={400}
      onClose={onCancel}
      ModalHeader={() => <div style={{ textAlign: 'center' }}>{title}</div>}
      ModalBody={() => <div className={popupText}>{text}</div>}
      ModalActions={() => (
        <>
          <Button className='btn btn-primary btn-lg' onClick={onCancel}>
            {t('forms.cancel')}
          </Button>
          <Button className='btn btn-danger btn-lg' onClick={onConfirm}>
            {t('forms.remove')}
          </Button>
        </>
      )}
    />
  );
};

export default LinkedNotesWarningPopup;
