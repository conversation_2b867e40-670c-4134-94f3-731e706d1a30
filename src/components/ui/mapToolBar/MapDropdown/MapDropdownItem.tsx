import React, { FC } from 'react';
import { cx } from 'linaria';

import LinkButton from 'components/LinkButton';

import {
  MapDropdownItemIndicatorStyle,
  mapDropdownItemLabelStyle,
  MapDropdownItemValueStyle,
  MapDropdownListItemStyle,
} from './styles';

export type MapDropdownOption = {
  label: string;
  id: string;
  indicator?: boolean;
};

export type MapDropdownItemProps = {
  option: MapDropdownOption;
  onClick: () => void;
  value: string | null;
};

export const MapDropdownItem: FC<MapDropdownItemProps> = ({
  option,
  onClick,
  value,
}) => {
  return (
    <MapDropdownListItemStyle>
      <LinkButton
        className={cx(
          mapDropdownItemLabelStyle,
          option.id === value && 'selected',
        )}
        onClick={onClick}
      >
        <MapDropdownItemValueStyle>
          {option.label}
          {option.indicator && <MapDropdownItemIndicatorStyle />}
        </MapDropdownItemValueStyle>
      </LinkButton>
    </MapDropdownListItemStyle>
  );
};
