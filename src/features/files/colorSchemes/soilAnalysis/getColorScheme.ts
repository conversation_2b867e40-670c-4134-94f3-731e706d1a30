import { ColorScale } from 'constants/style';
import { FileColumn } from 'types/filesState';
import { ColorSchemeGetter } from '../types';
import { getSoilElementParameter } from './utils';

export const getSoilAnalysisColorScheme: ColorSchemeGetter = (
  column: FileColumn,
) => {
  const parameter = getSoilElementParameter(column.name);

  return (properties: TEMPORARY_ANY, zoom: number, type: number) => {
    const baseColor = ColorScale(
      (properties[column.name] - column.min) / (column.max - column.min),
    ).hex();

    const level = parameter.breakPoints.findIndex((breakPoint, index) => {
      const max = parameter.breakPoints[index + 1] || Infinity;

      return properties[column.name] < max;
    });

    return {
      color: type === 1 ? baseColor : 'gray',
      fillColor: parameter.colorsPattern[level] as string,
      fillOpacity: 1,
      weight: 0.5,
      radius: 3.75,
    };
  };
};
