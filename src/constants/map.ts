import L from 'leaflet';

// @ts-ignore
const VertexMarker = L.Editable.VertexMarker.extend({
  options: {
    className: 'marker-icon',
  },
});
// @ts-ignore
const MiddleMarker = L.Editable.MiddleMarker.extend({
  options: {
    className: 'marker-icon marker-icon-middle',
    opacity: 0.25,
  },
});

export const editOptions = {
  lineGuideOptions: {
    color: '#FFF',
    dashArray: [5, 5],
    weight: 2,
    pane: 'rulerPane',
  },
  middleMarkerClass: MiddleMarker,
  vertexMarkerClass: VertexMarker,
};

export const ToolbarFillingOptions = [
  'last_ndvi_default',
  'last_ndvi_contrasted',
  'crop',
  'sowing_date',
  'harvest_date',
  'yield_value',
  'avg_ndvi',
  'std_ndvi',
  'no_option',
];

export const ToolbarTooltipOptions = [
  'last_ndvi_date',
  'avg_ndvi',
  'crop',
  'sowing_date',
  'harvest_date',
  'yield_value',
  'std_ndvi',
  'area',
  'field_title',
  'no_option',
];
