import React from 'react';
import { Route } from 'react-router-dom';
import { TransitionGroup, CSSTransition } from 'react-transition-group';

const AnimatedRoute = ({ path, component, timeout = 1000 }) => (
  <Route path={path}>
    {params => (
      <TransitionGroup>
        {params.match && (
          <CSSTransition
            appear
            key={path}
            timeout={timeout}
            classNames={{
              enterActive: '__normal',
              enterDone: '__normal',
              enter: '__initial',
              exitActive: '__initial',
            }}
          >
            {React.createElement(component, params)}
          </CSSTransition>
        )}
      </TransitionGroup>
    )}
  </Route>
);

export default AnimatedRoute;
