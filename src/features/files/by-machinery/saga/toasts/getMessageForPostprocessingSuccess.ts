import { BASE_ROUTE_FIELDS, TOAST_KEY_TO_IMPORT } from '../../constants';
import { isFieldsPage } from '../../redux/utils';
import { actions as toastActions } from 'features/platform/ToastModule/redux/actions';
import { TaskSuccessType } from '../../types';
import importTaskModule from '../../redux';
import { ToastOptions } from 'features/platform/ToastModule/redux/types';

export const getMessageForPostprocessingSuccess = (
  tasks: TaskSuccessType[],
) => {
  const buttons: ToastOptions['actions'] = [];

  if (!isFieldsPage()) {
    const viewport = window.location.pathname.split('/')[1];
    buttons.push({
      label: 'import.by-machinery.toast.processing.success.to',
      to: `/${viewport}/${BASE_ROUTE_FIELDS}`,
      onClick: dispatch => {
        dispatch(importTaskModule.actions.markTasksAsShowed(tasks));
      },
    });
  }

  buttons.push({
    label: 'import.by-machinery.toast.processing.success.ok',
    onClick: dispatch => {
      dispatch(importTaskModule.actions.markTasksAsShowed(tasks));
    },
  });

  return toastActions.push({
    id: TOAST_KEY_TO_IMPORT,
    title: 'import.by-machinery.toast.processing.success.title',
    text: 'import.by-machinery.toast.processing.success.description',
    icon: 'checkSuccess',
    actions: buttons,
  });
};
