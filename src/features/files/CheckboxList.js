import React from 'react';

import { useTranslate } from 'utils/use-translate';

import Checkbox from 'components/ui/Checkbox/Checkbox';

const CheckboxList = ({ options, value, onChange }) => {
  const { t } = useTranslate();
  return (
    <ul className='list-checkboxes'>
      <li className='list-checkboxes__list-item'>
        <Checkbox
          checked={value.length === options.length}
          onChange={event => {
            const { checked } = event.target;
            if (checked) {
              onChange(options.map(option => option.id));
            } else {
              onChange([]);
            }
          }}
        >
          {t('forms.select_all')}
        </Checkbox>
      </li>
      {options.map(option => (
        <li key={option.id} className='list-checkboxes__list-item'>
          <Checkbox
            checked={value.includes(option.id)}
            onChange={event => {
              const { checked } = event.target;
              if (checked) {
                onChange([...value, option.id]);
              } else {
                onChange(value.filter(item => item !== option.id));
              }
            }}
          >
            {option.label}
          </Checkbox>
        </li>
      ))}
    </ul>
  );
};

export default CheckboxList;
