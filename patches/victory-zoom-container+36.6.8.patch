diff --git a/node_modules/victory-zoom-container/es/zoom-helpers.js b/node_modules/victory-zoom-container/es/zoom-helpers.js
index 2b7833b..b6edf7f 100644
--- a/node_modules/victory-zoom-container/es/zoom-helpers.js
+++ b/node_modules/victory-zoom-container/es/zoom-helpers.js
@@ -65,7 +65,7 @@ var RawZoomHelpers = {
         toBound = _this$getDomain$axis[1];
 
     var percent = this.getScalePercent(evt, props, axis);
-    var point = factor * from + percent * (factor * range);
+    var point = from + percent * (factor * range);
     var minDomain = this.getMinimumDomain(point, props, axis);
 
     var _this$getScaledDomain = this.getScaledDomain(currentDomain, factor, percent),
