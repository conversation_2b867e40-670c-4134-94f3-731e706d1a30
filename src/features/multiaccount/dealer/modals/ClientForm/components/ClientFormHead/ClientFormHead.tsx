import React from 'react';
import { cx } from 'linaria';

import Modal from 'components/ui/Modal/Modal';

import { ClientFormType } from 'features/multiaccount/dealer/modals/ClientForm/types';
import { ErrorData } from 'modules/entities/types';
import { useTranslate } from 'utils/use-translate';

import { styles } from './ClientFormHead.styles';

export type ClientFormHeadProps = {
  designMode?: 'new' | 'default';
  requestErrors: Nullable<ErrorData>;
  subtitle?: string;
  type: ClientFormType;
};

const ClientFormHead = ({
  designMode = 'default',
  requestErrors,
  subtitle,
  type,
}: ClientFormHeadProps) => {
  const { t } = useTranslate();

  return (
    <Modal.Head designMode={designMode}>
      <div
        className={cx(styles.title, designMode === 'new' && styles.newTitle)}
      >
        {t(`multiaccount.${type}-modal.title`)}
      </div>
      {!!subtitle && (
        <div
          className={cx(
            styles.subtitle,
            designMode === 'new' && styles.newSubtitle,
          )}
        >
          {subtitle}
        </div>
      )}
      {requestErrors?.__all__ &&
        requestErrors.__all__.map(error => (
          <div key={error} className={styles.commonError}>
            {t(`multiaccount.dealer.error.${error}`)}
          </div>
        ))}
    </Modal.Head>
  );
};

export default ClientFormHead;
