import { css } from 'linaria';
import { styled } from 'linaria/react';

export const mapDropdownItemButton = css`
  &:after {
    content: '';
    width: 5px;
    height: 5px;
    border-left: 1px solid currentColor;
    border-bottom: 1px solid currentColor;
    transform: rotate(-45deg);
    margin-left: 8px;
    margin-top: -2px;
    transition: transform 0.1s;
  }
`;

export const MapDropdownPopoverStyle = styled.div`
  position: absolute;
  min-width: 100px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.25);
  background-color: #222;
  border-radius: 4px;
  top: 50px;
  font-size: 10px;
  padding: 10px 0;
  left: 0;
  transform: translate(0, 10px);
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.1s, visibility 0.1s, transform 0.1s;
  overflow-x: hidden;
  overflow-y: auto;
  max-height: 422px;
`;

export const MapDropBarStyle = styled.div`
  position: relative;
  &.opened {
    .${mapDropdownItemButton} {
      &:after {
        transform: rotate(-225deg) translate(1px, -2px);
      }
    }
    ${MapDropdownPopoverStyle} {
      opacity: 1;
      visibility: visible;
      transform: translate(0, 0);
    }
  }
`;

export const MapDropdownListStyle = styled.ul`
  list-style-type: none;
  padding: 0;
  margin: 0;
`;

export const MapDropdownListItemStyle = styled.li``;

export const mapDropdownItemLabelStyle = css`
  display: flex;
  cursor: pointer;
  padding: 0 20px;
  min-height: 4em;
  align-items: center;
  color: #ebebeb;
  background-color: rgba(17, 17, 17, 0);
  transition: color 0.1s, background-color 0.1s;
  &:hover {
    color: #fff;
    background-color: #000;
  }
  &.selected {
    color: #fff;
    background-color: #000;
  }
`;

export const MapDropdownItemValueStyle = styled.div`
  font-size: 1.4em;
  white-space: nowrap;
  position: relative;
`;

export const MapDropdownItemIndicatorStyle = styled.div`
  position: absolute;
  width: 8px;
  height: 8px;
  left: -12px;
  top: 5px;

  background: #27ae60;
  border: 1px solid #ffffff;
  border-radius: 50%;
`;

export const MapDropdownSectionStyle = styled.div`
  padding: 0 20px;
  min-height: 1em;

  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  display: flex;
  align-items: flex-end;
  color: #ffffff;
  opacity: 0.5;
`;
