import React from 'react';
import { useSelector } from 'react-redux';
import { Link, RouteComponentProps } from 'react-router-dom';
import { useTranslate } from 'utils/use-translate';
import logEvent from 'sagas/global/logEvent';
import auth from 'modules/auth';
// @ts-ignore
import seasons from 'modules/seasons';
import {
  SoilSidebarEnters,
  SoilSidebarEntersListItem,
  SoilSidebarEntersItem,
  SoilSidebarEntersInner,
  SoilSidebarEntersLabel,
  SoilSidebarEntersLabelInner,
  SoilSidebarEntersIco,
} from './AddFieldsWelcomePane.style';
import {
  FeatureDisabledTooltip,
  FEATURES,
  FEATURE_PERMISSIONS,
} from 'features/permissions';
import { PopoverDeprecatedAlign } from 'components/ui/Popover';
import { useFeaturePermission } from 'features/permissions';

const Modes: {
  id: string;
  to: string;
  icon: TEMPORARY_ANY;
  soon?: boolean;
}[] = [
  {
    id: 'select',
    to: '/add/select',
    icon: require('assets/images/fields-enter-1.svg').default,
  },
  {
    id: 'create',
    to: '/add/create',
    icon: require('assets/images/fields-enter-2.svg').default,
  },
  {
    id: 'upload',
    to: `/add/upload`,
    icon: require('assets/images/fields-enter-3.svg').default,
  },
];

const AddFieldsWelcomePane = ({ match }: RouteComponentProps) => {
  const { t } = useTranslate();
  const userID = useSelector(auth.selectors.getUserID);
  const currentSeason: TEMPORARY_ANY = useSelector(
    seasons.selectors.getCurrent,
  );

  const isFieldCreateFeatureIsAvailable =
    useFeaturePermission(FEATURES.FIELD_ADD) === FEATURE_PERMISSIONS.ALLOWED;

  const onAddItemClick = (id: string) => {
    let method = null;
    switch (id) {
      case 'select':
        method = 'select_on_map';
        break;
      case 'create':
        method = 'draw_fields';
        break;
      case 'upload':
        method = 'upload_file';
        break;
    }
    logEvent('select_clip_plus_field', {
      method,
      clean_seasons: true,
      user_id: userID,
      season_id: currentSeason?.id ?? null,
    });
  };

  return (
    <SoilSidebarEnters>
      {Modes.map(mode => {
        const content = (
          <SoilSidebarEntersInner>
            {mode.soon && (
              <SoilSidebarEntersLabel>
                <SoilSidebarEntersLabelInner data-type='primary'>
                  {(t('welcome.soon_badge') as string).toUpperCase?.()}
                </SoilSidebarEntersLabelInner>
              </SoilSidebarEntersLabel>
            )}
            <SoilSidebarEntersIco>
              <img alt='' src={mode.icon} />
            </SoilSidebarEntersIco>
            <h2>{t(`fields.welcome.${mode.id}.title`)}</h2>
            <p>{t(`fields.welcome.${mode.id}.description`)}</p>
            {mode.to && (
              <FeatureDisabledTooltip
                feature={FEATURES.FIELD_ADD}
                direction={PopoverDeprecatedAlign.MiddleRight}
              >
                <span
                  className='btn btn-success'
                  data-testid={`#button_add_field_${mode.id}`}
                >
                  {t(`fields.welcome.${mode.id}.action`)}
                </span>
              </FeatureDisabledTooltip>
            )}
          </SoilSidebarEntersInner>
        );
        return (
          <SoilSidebarEntersListItem
            key={mode.id}
            onClick={() =>
              isFieldCreateFeatureIsAvailable && onAddItemClick(mode.id)
            }
          >
            {isFieldCreateFeatureIsAvailable && mode.to ? (
              <Link
                to={`${match.url}${mode.to}`}
                className={SoilSidebarEntersItem}
              >
                {content}
              </Link>
            ) : (
              <span className={SoilSidebarEntersItem}>{content}</span>
            )}
          </SoilSidebarEntersListItem>
        );
      })}
    </SoilSidebarEnters>
  );
};

export default AddFieldsWelcomePane;
