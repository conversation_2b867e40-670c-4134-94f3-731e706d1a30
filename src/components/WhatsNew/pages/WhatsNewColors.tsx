import React, { VFC } from 'react';
import Button from 'components/ui/Button/Button';

import WhatsNewColorsImg from 'assets/images/whatsnew/whatsnew_colors.jpg';
import WhatsNewColorsImg2x from 'assets/images/whatsnew/<EMAIL>';
import { WhatsNewProps } from '../types';
import { FeaturesKeyEnum } from '../consts';
import {
  StyledPage,
  styles,
} from 'components/WhatsNew/WhatsNewContent/WhatsNewContent.style';

const Colors: VFC<WhatsNewProps> = ({ style, onClick, isLast, t }) => (
  <StyledPage style={{ ...style }}>
    <div className={styles.semiPage}>
      <div className={styles.pageContent}>
        <div className={styles.textContent}>
          <h2 className={styles.title}>{t('whatsnew.colors.title')}</h2>
          <p className={styles.text}>{t('whatsnew.colors.text')}</p>
        </div>
        <Button className='btn btn-success fit-content' onClick={onClick}>
          {t(`whatsnew.${isLast ? 'end' : 'next'}.action_text`)}
        </Button>
      </div>
    </div>
    <div className={styles.semiPage}>
      <picture className={styles.pagePicture} style={{ marginRight: 40 }}>
        <source srcSet={`${WhatsNewColorsImg} 1x, ${WhatsNewColorsImg2x} 2x`} />
        <img
          src={WhatsNewColorsImg}
          alt={t('whatsnew.colors.title') as string}
        />
      </picture>
    </div>
  </StyledPage>
);

const WhatsNewColors = {
  key: FeaturesKeyEnum.colors,
  component: Colors,
  filter: {},
};

export default WhatsNewColors;
