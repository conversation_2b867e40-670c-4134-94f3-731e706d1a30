import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { styled } from 'linaria/react';

import MapDropdown, {
  MapDropdownProps,
} from 'components/ui/mapToolBar/MapDropdown';
import {
  PopoverDeprecated,
  PopoverDeprecatedAlign,
} from 'components/ui/Popover';
import { FeaturesKeyEnum } from 'components/WhatsNew/consts';
import RgbLayerHintPopover from 'features/platform/PlatformMainPageContent/PlatformMapSection/MapToolbar/RgbLayerHintPopover/RgbLayerHintPopover';

// @ts-ignore
import fields from 'modules/fields';
import settings from 'modules/settings';
import { fieldsSelectors } from 'modules/fields/selectors';

// @ts-ignore
import { FieldFillingTypes } from 'constants/fields';
import logEvent from 'sagas/global/logEvent';

import { useTranslate } from 'utils/use-translate';

import firebase from 'firebase/app';
import 'firebase/remote-config';
import {
  getOperationsValue,
  filterOperationsForField,
} from 'features/files/by-machinery/redux/selectors';
import { isEntityHaveUpdate } from 'features/files/by-machinery/utils/isEntityHaveUpdate';
import { ApiDataFieldOperations } from 'features/files/by-machinery/types';
import { getOperationsRange } from 'features/files/by-machinery/utils/getOperationsRange';
import { dateFormatFactory } from 'utils/dates/dateFormatFactory';

const RGB_HINT_FEATURE_KEY = FeaturesKeyEnum.hintAboutRgbLayer;

const IndicatorStyle = styled.div`
  position: absolute;
  width: 8px;
  height: 8px;
  right: 2px;
  top: 2px;
  background: #27ae60;
  border: 1px solid #ffffff;
  border-radius: 50%;
`;

const WrapperStyle = styled.div`
  position: relative;
`;

export type VegetationTypeDropdownProps = {
  fieldId: string;
};

const VegetationTypeDropdown = ({ fieldId }: VegetationTypeDropdownProps) => {
  const { t } = useTranslate();
  const dispatch = useDispatch();
  const [showHint, setShowHint] = useState(false);
  const featuresUsed = useSelector(settings.selectors.getFeaturesUsed);
  const field = useSelector(state => fieldsSelectors.getByID(state, fieldId));
  const report = useSelector(state =>
    fieldsSelectors.getReportById(state, fieldId),
  );
  const dateFormat = dateFormatFactory(t('seasons.edit.date_format') as string);

  const allOperations = useSelector(getOperationsValue);
  const operationsForField = allOperations.filter(operation =>
    filterOperationsForField(operation, field?.fieldUserSeasonId),
  );

  const hasUpdate = operationsForField.some(isEntityHaveUpdate);
  const getLabelForOperation = useCallback(
    (operation: ApiDataFieldOperations): string => {
      const operations = [operation].concat(
        allOperations.filter(
          v => v.parent_operation_uuid === operation.uuid && v.is_enabled,
        ),
      );
      const range = getOperationsRange(operations, dateFormat);

      if (range.length === 0) {
        return t(
          `import.by-machinery.${operation.type?.toLowerCase()}.title`,
          // eslint-disable-next-line
        ).replace(', ${date}', '');
      }

      return t(
        // @ts-ignore
        'import.by-machinery.' +
          // @ts-ignore
          operation.type?.toLowerCase() +
          // @ts-ignore
          '.title',
        {
          date: range.join(' - '),
        },
      ) as string;
    },
    [t, dateFormat, allOperations],
  );

  const options = useMemo(() => {
    const temp: MapDropdownProps['options'] = FieldFillingTypes.map(
      (id: string) => ({
        label: t(
          ['default', 'contrasted'].includes(id)
            ? `fields.ndvi.types.${id}`
            : `map.toolbar.filling.rgb`,
        ),
        id,
      }),
    );
    if (operationsForField.length > 0) {
      temp.push({
        id: `section:importedData`,
        label: t('map.toolbar.imported_data'),
        type: 'section',
      });

      return temp.concat(
        operationsForField
          .sort(
            (a, b) =>
              new Date(b.end_time!).valueOf() - new Date(a.end_time!).valueOf(),
          )
          .map(operation => ({
            label: getLabelForOperation(operation),
            indicator: isEntityHaveUpdate(operation),
            id: `operation:${operation.uuid}`,
          })),
      );
    }

    if (report?.productivity_map) {
      temp.push({
        id: 'productivity_map',
        label: t('map.toolbar.filling.productivity_map'),
      });
    }

    if (report?.brightness_map) {
      temp.push({
        id: 'brightness_map',
        label: t('map.toolbar.filling.brightness_map'),
      });
    }

    if (report?.elevation_map) {
      temp.push({
        id: 'elevation_map',
        label: t('map.toolbar.filling.elevation_map'),
      });
    }

    temp.push({
      id: 'no_map',
      label: t('map.toolbar.filling.no_map'),
    });

    return temp;
  }, [t, report, operationsForField, getLabelForOperation]);

  useEffect(() => {
    setShowHint(
      featuresUsed.id !== null &&
        !featuresUsed?.value?.includes?.(RGB_HINT_FEATURE_KEY),
    );
  }, [featuresUsed]);

  const fieldFillingType = useSelector(fieldsSelectors.getFieldFillingType);
  const fieldFillingOperation = useSelector(
    fieldsSelectors.getFieldFillingOperation,
  );

  const currentValue =
    fieldFillingType !== 'operation'
      ? fieldFillingType
      : `${fieldFillingType}:${fieldFillingOperation}`;

  const ndvi = useSelector(state =>
    fieldsSelectors.getNDVIByID(state, fieldId),
  );
  const selectedIndex = useSelector(fieldsSelectors.getNDVIIndex);
  const itemsWithShots = ndvi.items?.filter(
    (item: TEMPORARY_ANY) => item.status !== 2,
  );
  const selectedNDVI = itemsWithShots?.[selectedIndex];

  useEffect(() => {
    if (options.some(v => v.id === currentValue)) {
      return;
    }

    const value = options[0]!.id || '';
    const [type, data] = value.split(':');

    dispatch(fields.actions.selectFieldFillingType(type, data));
  }, [currentValue, options, dispatch]);

  //Method runs when user shows that he has learned new RGB feature
  //isManualClose means if the tooltip closed not automatically,
  // the user close it manually with close button
  const onRGBFeatureLearned = (isManualClose?: boolean) => {
    const remoteConfig = firebase.remoteConfig();
    const isRgbLayerActive = remoteConfig.getValue('rgb_layer').asBoolean();

    if (!isRgbLayerActive) {
      return;
    }

    const filteredFeatures = featuresUsed.value;
    dispatch(
      settings.actions.update(
        {
          featuresUsed: {
            id: featuresUsed.id,
            value: [
              ...filteredFeatures,
              ...(filteredFeatures.includes(RGB_HINT_FEATURE_KEY)
                ? []
                : [RGB_HINT_FEATURE_KEY]),
            ],
          },
        },
        featuresUsed.id,
      ),
    );

    if (isManualClose) {
      logEvent('satellite_tooltip_skip', {
        field_id: fieldId,
      });
    }
  };

  const selectedOperation =
    fieldFillingType === 'operation'
      ? operationsForField.find(v => v.uuid === fieldFillingOperation)
      : undefined;

  let label = '';
  switch (fieldFillingType) {
    case 'contrasted':
    case 'default': {
      label = t(`fields.ndvi.types.${fieldFillingType}`);
      break;
    }
    case 'operation': {
      if (selectedOperation) {
        label = getLabelForOperation(selectedOperation);
      }
      break;
    }
    default: {
      label = t(`map.toolbar.filling.${fieldFillingType}`);
      break;
    }
  }

  return (
    <PopoverDeprecated
      active={showHint}
      align={PopoverDeprecatedAlign.BottomMiddle}
      offset={[0, 8]}
      renderTrigger={renderTriggerProps => (
        <WrapperStyle {...renderTriggerProps}>
          <MapDropdown
            value={currentValue}
            label={label}
            options={options}
            onToggle={isOpened => {
              if (isOpened) {
                onRGBFeatureLearned();
              }
            }}
            onChange={value => {
              const [type, data] = value.split(':');
              dispatch(fields.actions.selectFieldFillingType(type, data));

              if (type === 'rgb') {
                logEvent('satellite_layer_on', {
                  field_id: +fieldId?.slice(1),
                  date: selectedNDVI?.date,
                  type,
                });
              } else {
                logEvent('change_ndvi', {
                  field_id: +fieldId?.slice(1),
                  date_pick_type: 'change_type',
                  date: selectedNDVI?.date,
                  type,
                });
              }
            }}
          />
          {hasUpdate && <IndicatorStyle />}
        </WrapperStyle>
      )}
      renderPopover={popoverProps => (
        <RgbLayerHintPopover
          {...popoverProps}
          fieldId={fieldId}
          onClose={onRGBFeatureLearned.bind(null, true)}
        />
      )}
    />
  );
};

export default VegetationTypeDropdown;
