import React from 'react';
import { useTranslate } from 'utils/use-translate';

import { Title, Description } from './NDVITooltip.style';

import { FieldInteractiveTooltip } from 'components/FieldInteractiveTooltip/FieldInteractiveTooltip';

interface NDVITooltipProps {
  isVisible: boolean;
  position: [number, number];
  value: { key: string } | number | null;
  withoutNDVI?: boolean;
}

export const NDVITooltip = ({
  isVisible,
  position,
  value,
  withoutNDVI,
}: NDVITooltipProps) => {
  const { t } = useTranslate();

  return (
    <FieldInteractiveTooltip
      aboveEverything
      isVisible={isVisible}
      position={position}
    >
      {!withoutNDVI ? (
        value != null &&
        (typeof value === 'number' ? value.toFixed(2) : t(value?.key))
      ) : (
        <>
          <Title>{t('fields.avg_ndvi.tooltip.title')}</Title>
          <Description>{t('fields.avg_ndvi.tooltip.description')}</Description>
        </>
      )}
    </FieldInteractiveTooltip>
  );
};
