import { MutableRefObject, useCallback } from 'react';
import { useDispatch } from 'react-redux';

import entities from 'modules/entities';
import { EntityOperation } from 'modules/entities/types';

import { DataStoreType } from 'features/files/by-machinery/pages/MatchingOperationsPage/service/useDataStore';
import { ApiDataFieldOperations } from '../../../types/server';

export type OnSetPropertyType = (arg: {
  parentId: Nullable<string>;
  childId: string;
}) => void;

const getRequestForOneLayer = (
  dataStore: MutableRefObject<DataStoreType>,
  childOperation: ApiDataFieldOperations,
  parentOperation?: ApiDataFieldOperations,
): EntityOperation[] => {
  const requests: EntityOperation[] = [];
  const childId = childOperation.uuid;
  const parentId = parentOperation?.uuid || null;

  // we are trying to remove the parent operation for parent operation (because instead groups we are using tree)
  if (!childOperation!.parent_operation_uuid) {
    // it was parent operation
    const childrenOperations = dataStore.current.operations.filter(
      v => v.parent_operation_uuid === childId,
    );

    const newParentForOther = childrenOperations.find(v => v.uuid !== childId);

    requests.push(
      ...childrenOperations.map(
        child =>
          ({
            type: 'patch',
            entityType: 'fieldsDataOperation',
            path: {
              id: child.uuid,
            },
            query: {
              include: ['data_source.layer.properties'],
            },
            body: {
              parent_operation_uuid:
                newParentForOther?.uuid === child.uuid
                  ? null
                  : newParentForOther?.uuid,
            },
          } as EntityOperation),
      ),
    );
  }

  if (parentId && !parentOperation?.type && childOperation?.type) {
    // add type to parent
    requests.push({
      type: 'patch',
      entityType: 'fieldsDataOperation',
      path: {
        id: parentId,
      },
      query: {
        include: ['data_source.layer.properties'],
      },
      body: {
        type: childOperation?.type,
      },
    });

    const childrenOperations = dataStore.current.operations.filter(
      v => v.parent_operation_uuid === parentId,
    );
    // add type to children

    const childrenRequests: EntityOperation[] = childrenOperations.map(
      item => ({
        type: 'patch',
        entityType: 'fieldsDataOperation',
        path: {
          id: item.uuid,
        },
        query: {
          include: ['data_source.layer.properties'],
        },
        body: {
          type: childOperation?.type,
        },
      }),
    );

    requests.push(...childrenRequests);
  }

  requests.push({
    type: 'patch',
    entityType: 'fieldsDataOperation',
    path: {
      id: childId,
    },
    query: {
      include: ['data_source.layer.properties'],
    },
    body: {
      parent_operation_uuid: parentId,
      type: parentOperation?.type || childOperation?.type,
      is_enabled: (parentOperation || childOperation)!.is_enabled,
    },
  });

  return requests;
};

export const useSetParent = (
  dataStore: MutableRefObject<DataStoreType>,
): OnSetPropertyType => {
  const dispatch = useDispatch();

  return useCallback(
    ({ childId, parentId }) => {
      const baseChildOperation = dataStore.current.operations.find(
        v => v.uuid === childId,
      ) as ApiDataFieldOperations;
      const baseParentOperation = dataStore.current.operations.find(
        v => v.uuid === parentId,
      );

      const layersOperations = dataStore.current.operations.filter(
        operation =>
          operation.data_source.layer.uuid ===
          baseChildOperation.data_source.layer.uuid,
      );

      const requests = layersOperations
        .map(childOperation => {
          const parentOperation = dataStore.current.operations.find(
            item =>
              item.data_source.layer.uuid ===
                baseParentOperation?.data_source.layer.uuid &&
              item.field_uuid === childOperation.field_uuid,
          );

          return getRequestForOneLayer(
            dataStore,
            childOperation,
            parentOperation,
          );
        })
        .flat(1);

      dispatch(entities.actions.request('setRequestId', requests));
    },
    [dataStore, dispatch],
  );
};
