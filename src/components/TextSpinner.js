import React, { Component } from 'react';

class TextSpinner extends Component {
  static defaultProps = {
    component: 'span',
    mode: 'sequential',
    render: text => text,
    states: ['', '.', '..', '...'],
    interval: 1000,
    style: {},
  };

  state = {
    index: 0,
  };

  componentDidMount() {
    const { interval } = this.props;
    this.timer = setInterval(() => {
      this.setState(state => ({ index: state.index + 1 }));
    }, interval);
  }

  componentWillUnmount() {
    clearInterval(this.timer);
  }

  render() {
    const {
      component,
      states,
      mode,
      render,
      interval,
      style,
      ...otherProps
    } = this.props;
    const { index } = this.state;
    return (
      <span style={{ position: 'relative' }}>
        {React.createElement(component, {
          children: states[states.length - 1],
          style: { ...style, opacity: 0 },
        })}
        {React.createElement(component, {
          children: states[index % states.length],
          style: { ...style, position: 'absolute', left: 0 },
          ...otherProps,
        })}
      </span>
    );
  }
}

export default TextSpinner;
