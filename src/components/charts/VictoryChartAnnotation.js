import React from 'react';
import { VictoryPortal, Line } from 'victory';

import ForeignObject from './ForeignObject';

const VictoryChartAnnotation = ({
  x,
  scale,
  domain,
  padding,
  active = true,
  height,
  label,
  style = {},
}) => {
  if (x <= domain.x[0] || x >= domain.x[1]) {
    return null;
  }
  const lineX = scale.x(x);
  return (
    <>
      <VictoryPortal>
        <Line
          x1={lineX}
          x2={lineX}
          y1={padding.top}
          y2={height - padding.bottom}
          style={{ stroke: '#A5B2BC', strokeWidth: 0.5, ...style }}
        />
      </VictoryPortal>
      {label && (
        <ForeignObject active={active} x={lineX} y={padding.top}>
          <div className='fast-tooltip'>
            <div className='fast-tooltip__inner'>{label}</div>
          </div>
        </ForeignObject>
      )}
    </>
  );
};

export default VictoryChartAnnotation;
