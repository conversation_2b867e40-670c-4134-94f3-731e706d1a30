import { MutableRefObject, useCallback } from 'react';
import { useDispatch } from 'react-redux';

import entities from 'modules/entities';
import { DataStoreType } from 'features/files/by-machinery/pages/MatchingAttributesPage/service/useDataStore';
import { UpdatePropertyType } from 'modules/entities/actions';
import { DataTypesKey } from 'features/files/by-machinery/types';

export type OnSetMethodType = (
  newMethodValue: string | null,
  attributeName: string,
  layerUuid: string,
) => void;

export const useSetMethod = (
  dataStore: MutableRefObject<DataStoreType>,
): OnSetMethodType => {
  const dispatch = useDispatch();

  return useCallback(
    (
      newMethodValue: string | null,
      attributeName: string,
      layerUuid: string,
    ) => {
      const dataLayersForUpdate = dataStore.current.dataLayers.filter(item =>
        dataStore.current.layersLinks[layerUuid]?.includes(item.uuid),
      );
      const allProperties = dataLayersForUpdate
        .map(item => item.properties)
        .flat(1);

      const propertyForAttribute = allProperties.filter(
        property => property.target_name === attributeName,
      );

      let requests: UpdatePropertyType[] = [];

      if (propertyForAttribute.length > 0) {
        requests.push({
          payload: {
            target_name: propertyForAttribute[0]!.target_name,
            extra_fields: {
              [DataTypesKey.soilSampling]: {
                method: newMethodValue,
              },
            },
          },
          properties: propertyForAttribute.map(v => v.uuid),
        });
        dispatch(entities.actions.updateFieldsDataLayerProperty(requests));
      }
    },
    [dataStore, dispatch],
  );
};
