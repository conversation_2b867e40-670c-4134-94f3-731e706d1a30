import React, { Component } from 'react';
import { connect } from 'react-redux';
import { withTranslation } from 'react-i18next';

import cx from 'classnames';

import RenderAboveEverything from 'components/RenderAboveEverything';

import { actions } from 'features/fields/BulkUpload/redux/actions';
import { BulkUploadStep } from 'features/fields/BulkUpload/types';
import { TranslateFunction } from 'utils/use-translate';

const hasFiles = (event: DragEvent) =>
  event?.dataTransfer?.types.includes('Files');

interface FieldUploadOverlayProps {
  setFiles: (files: File[]) => void;
  setReachedStep: (step: BulkUploadStep) => void;
  t: TranslateFunction;
}

class FieldUploadOverlay extends Component<FieldUploadOverlayProps> {
  state = {
    hovered: false,
  };

  componentDidMount() {
    document.addEventListener('dragover', this.handleDragOver);
    document.addEventListener('dragleave', this.handleDragLeave);
    document.addEventListener('drop', this.handleDrop);
  }

  componentWillUnmount() {
    document.removeEventListener('dragover', this.handleDragOver);
    document.removeEventListener('dragleave', this.handleDragLeave);
    document.removeEventListener('drop', this.handleDrop);
  }

  handleDragOver = (event: DragEvent) => {
    event.preventDefault();
    this.setState({ hovered: hasFiles(event) });
  };

  handleDragLeave = (event: DragEvent) => {
    event.preventDefault();
    this.setState({ hovered: false });
  };

  handleDrop = (event: DragEvent) => {
    const { setFiles, setReachedStep } = this.props;
    event.preventDefault();
    this.setState({ hovered: false });

    if (!hasFiles(event)) {
      return;
    }

    setFiles(Array.from(event!.dataTransfer!.files));
    setReachedStep(BulkUploadStep.FILE_SELECTION);
  };

  render() {
    const { t } = this.props;
    const { hovered } = this.state;
    return (
      <RenderAboveEverything>
        <div className={cx('upload-container', { __opened: hovered })}>
          <div className='upload-container__outer'>
            <div className='upload-container__inner'>
              <h2 className='upload-container__title'>
                {t('fields.upload_overlay.title')}
              </h2>
              <p>{t('fields.upload_overlay.description')}</p>
            </div>
          </div>
        </div>
      </RenderAboveEverything>
    );
  }
}

const mapDispatchToProps = {
  setFiles: actions.setFiles,
  setReachedStep: actions.setReachedStep,
};

export default connect(
  null,
  mapDispatchToProps,
)(withTranslation()(FieldUploadOverlay));
