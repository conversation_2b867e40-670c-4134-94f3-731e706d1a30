import React from 'react';
import { useTranslate } from 'utils/use-translate';

import {
  AdvertisingBlock,
  AdvertisingTitle,
  AdvertisingDescriptionBlock,
  AdvertisingDescription,
  AdvertisingIvon,
} from './styles';
import Icon from 'components/Icon';

export const NoFieldDescriptionBlock = () => {
  const { t } = useTranslate();

  return (
    <AdvertisingBlock>
      <AdvertisingIvon>
        <Icon name='plant' />
      </AdvertisingIvon>
      <AdvertisingDescriptionBlock>
        <AdvertisingTitle>
          {t('import.matching_operations.no-field.title')}
        </AdvertisingTitle>
        <AdvertisingDescription>
          {t('import.matching_operations.no-field.description')}
        </AdvertisingDescription>
      </AdvertisingDescriptionBlock>
    </AdvertisingBlock>
  );
};
