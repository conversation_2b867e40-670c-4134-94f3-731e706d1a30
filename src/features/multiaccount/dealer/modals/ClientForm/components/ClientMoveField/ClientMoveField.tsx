import React, { forwardRef } from 'react';
import { Controller } from 'react-hook-form';
import { useSelector } from 'react-redux';
import { cx } from 'linaria';

import {
  RadioButton,
  RadioGroup,
} from 'features/multiaccount/dealer/modals/ClientForm/components/ClientMoveField/components';

import multiaccountModule from 'modules/multiaccount';
import { DealerClient } from 'types/multiaccount';
import { useTranslate } from 'utils/use-translate';

import { styles } from './ClientMoveField.styles';

export type ClientMoveFieldProps = {
  isPending: boolean;
  control: TEMPORARY_ANY;
  clients: DealerClient[];
};

const ClientMoveField = forwardRef<HTMLInputElement, ClientMoveFieldProps>(
  ({ control, isPending, clients }, ref) => {
    const { t } = useTranslate();
    const consultants = useSelector(
      multiaccountModule.selectors.getConsultants,
    );
    const consultantsUuids = clients.map(c => c.assigned_consultant_uuid);

    return (
      <div className={cx('form-group', styles.clientMoveFieldContainer)}>
        <Controller
          name='assigned_consultant_uuid'
          control={control}
          rules={{
            validate: value => !consultantsUuids.includes(value),
            required: t('multiaccount.dealer.error.required') as string,
          }}
          disabled={isPending}
          render={({ onChange, value }) => {
            return (
              <RadioGroup
                itemsData={consultants.map(c => ({
                  disabled: consultantsUuids.includes(c.uuid),
                  email: c.email,
                  type: c.name,
                  value: c.uuid,
                }))}
                name={'assigned_consultant_uuid'}
                itemRenderer={item => {
                  return (
                    <RadioButton
                      checked={item.value === value}
                      disabled={item.disabled}
                      email={item.email}
                      key={item.value}
                      name={item.name}
                      type={item.type}
                      onChange={event => {
                        onChange(event.target.value);
                      }}
                      value={item.value}
                    />
                  );
                }}
              />
            );
          }}
        />
      </div>
    );
  },
);

export default ClientMoveField;
