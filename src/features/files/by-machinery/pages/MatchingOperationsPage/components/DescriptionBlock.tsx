import React from 'react';
import { useTranslate } from 'utils/use-translate';
import { useSelector } from 'react-redux';

import {
  AdvertisingBlock,
  AdvertisingTitle,
  AdvertisingDescriptionBlock,
  AdvertisingDescription,
  AdvertisingIvon,
} from './styles';
import Icon from 'components/Icon';
import { ApiDataFieldLayer } from '../../../types';
import { getActiveDataLayers } from '../../../redux/selectors';

export const DescriptionBlock = () => {
  const { t } = useTranslate();
  const dataLayers: ApiDataFieldLayer[] = useSelector(getActiveDataLayers);
  if (dataLayers.every(v => v.source.driver !== 'ADAPT')) {
    return null;
  }

  return (
    <AdvertisingBlock>
      <AdvertisingIvon>
        <Icon name='plant' />
      </AdvertisingIvon>
      <AdvertisingDescriptionBlock>
        <AdvertisingTitle>
          {t('import.matching_operations.info.title')}
        </AdvertisingTitle>
        <AdvertisingDescription>
          {t('import.matching_operations.info.description')}
        </AdvertisingDescription>
      </AdvertisingDescriptionBlock>
    </AdvertisingBlock>
  );
};
