import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import EnterExitTransition from 'components/EnterExitTransition';
import Icon from 'components/Icon';
import Button from 'components/ui/Button/Button';
import ModernSpinner from 'components/ui/ModernSpinner/ModernSpinner';
import Saga from 'components/saga/Saga';

import bulkUploadModule from 'features/fields/BulkUpload/redux';
import { matchCropsSaga } from 'features/fields/BulkUpload/saga';

// @ts-ignore
import ProposeCulturePopup from 'features/fields/ProposeCulturePopup';

// @ts-ignore
import CustomDropdown from 'features/scouting/CustomDropdown';

import api from 'modules/api';
// @ts-ignore
import fields from 'modules/fields';

import logEvent from 'sagas/global/logEvent';

import { RootState } from 'types/rootState';

import { useTranslate } from 'utils/use-translate';
import { FloatingActionsStyle } from 'components/ui/FloatingActions/styles';

export const MatchCropsStep = () => {
  const { t } = useTranslate();

  const saveRequest = useSelector((state: RootState) =>
    api.selectors.getRequestStatus(state, 'update-fields-file'),
  );

  const fieldsFetchingPending = useSelector(
    bulkUploadModule.selectors.getFieldsFetchingPending,
  );

  const [crops, setCrops] = useState([]);
  const [matchCrops, setMatchCrops] = useState({});
  const [showPopup, setShowPopup] = useState(false);
  const dispatch = useDispatch();

  const fileIds = useSelector(
    bulkUploadModule.selectors.getValidFileIdsWithProps,
  );

  // The interface should work with only one fileId
  const fileId = fileIds[0];

  const sortedCropTypes: string[] = useSelector(
    fields.selectors.getSortedCropTypes,
  );
  const cropTypes = ['no_import', 'propose', ...sortedCropTypes];

  const handleSubmit = () => {
    logEvent('fields_import_match_crops', matchCrops);
    dispatch(fields.actions.setMatchedProperties(fileId, matchCrops));
  };

  if (!fileId) {
    return null;
  }

  return (
    <>
      <Saga saga={matchCropsSaga} fileId={fileId} onSetData={setCrops} />
      <EnterExitTransition variant='popup'>
        {showPopup && (
          <ProposeCulturePopup
            onCancel={() => {
              setShowPopup(false);
            }}
          />
        )}
      </EnterExitTransition>
      <div className='main-section__body'>
        <form onSubmit={() => {}}>
          <div className='main-uploader' data-type='columns'>
            <div className='main-uploader-header'>
              <h2 className='main-uploader-header__title'>
                {t('fields.upload.match_crops.title')}
              </h2>
              <p className={'main-uploader-header__description'}>
                {t('fields.upload.match_crops.sub_title')}
              </p>
            </div>
            <div className='main-uploader-naming' data-type='compact'>
              <div className='main-uploader-naming__header'>
                <div className='main-uploader-naming__col'>
                  {t('fields.upload.match_columns.from_file')}
                </div>
                <div className='main-uploader-naming__col'>
                  {t('fields.upload.match_columns.to_platform')}
                </div>
              </div>
              {!crops.length && (
                <div className='main-uploader-spinner'>
                  <ModernSpinner large />
                </div>
              )}
              {crops.map(key => (
                <div
                  key={key}
                  className='main-uploader-naming__row'
                  data-column={matchCrops[`${key}`] ? 'selected' : 'passed'}
                >
                  <div className='main-uploader-naming__arr'>
                    <Icon
                      className='ico-arrow-circle-right-os'
                      name='arrow-circle-right'
                    />
                  </div>
                  <div className='main-uploader-naming__col'>
                    <h3 className='main-uploader-naming__title'>{[key]}</h3>
                  </div>
                  <div className='main-uploader-naming__col'>
                    <CustomDropdown
                      placeholder={t('files.actions.placeholder')}
                      className='form-select form-select-sm'
                      value={matchCrops[key]}
                      renderValue={({ value }: { value: string }) => {
                        return (
                          <div className='form-select__value'>
                            {value
                              ? t(`crop.${value}`)
                              : t('fields.upload.match_crops.no_import')}
                          </div>
                        );
                      }}
                      options={cropTypes.map(type => ({
                        label: t(`crop.${type}`),
                        id: type,
                      }))}
                      withSearchBox={cropTypes.length >= 5}
                      searchBoxProps={{
                        placeholder: t('suggest.crops_type.search.placeholder'),
                      }}
                      onClick={(event: { stopPropagation: () => void }) =>
                        event.stopPropagation()
                      }
                      onChange={(action: string) => {
                        let state = { ...matchCrops };
                        if (action === 'no_import') {
                          delete state[key];
                        } else if (action === 'propose') {
                          delete state[key];
                          setShowPopup(true);
                        } else {
                          state = { ...state, [key]: action };
                        }
                        setMatchCrops(state);
                      }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </form>
      </div>
      {Boolean(crops.length) && (
        <FloatingActionsStyle>
          <Button
            className='btn btn-dark btn-lg'
            onClick={() => {
              dispatch(bulkUploadModule.actions.resetBulkUploadState());
            }}
          >
            {t('forms.cancel')}
          </Button>
          <Button
            className='btn btn-success btn-lg'
            type='submit'
            pending={
              saveRequest.status === 'pending' ||
              fieldsFetchingPending ||
              !crops.length
            }
            onClick={() => handleSubmit()}
          >
            {t('forms.next')}
          </Button>
        </FloatingActionsStyle>
      )}
    </>
  );
};
