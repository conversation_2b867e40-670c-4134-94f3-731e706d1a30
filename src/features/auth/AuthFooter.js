import React from 'react';

import { useTranslate } from 'utils/use-translate';
import { Languages } from 'utils/i18n';
import ExternalLink from 'components/ExternalLink';

import config from 'config';

import SelectInput from 'components/SelectInput';
import LinkButton from 'components/LinkButton';

const AuthFooter = ({ hideCopy, hideLang }) => {
  const { t, i18n } = useTranslate();
  const currentLanguage = Languages.find(
    lang => lang.code === i18n.resolvedLanguage,
  );

  const filteredLangs =
    config.whiteLabel?.title !== 'Agrilever'
      ? Languages.filter(l => l.code !== 'tl')
      : Languages;

  return (
    <div className='onesoil-hello-footer'>
      {!hideCopy && (
        <div className='onesoil-hello-footer__item'>
          © {new Date().getFullYear()}, OneSoil
        </div>
      )}
      <div className='onesoil-hello-footer__item'>
        <ExternalLink href={t('urls.terms_of_use')}>
          {t('bottom.terms_of_use')}
        </ExternalLink>
      </div>
      <div className='onesoil-hello-footer__item'>
        <ExternalLink href={t('urls.privacy_policy')}>
          {t('bottom.privacy_policy')}
        </ExternalLink>
      </div>

      {!hideLang && (
        <div className='onesoil-hello-footer__item'>
          <SelectInput
            options={filteredLangs}
            idKey='code'
            renderValue={({ value }) => value && value.name}
            renderTrigger={({
              value,
              isOpen,
              getToggleButtonProps,
              renderValue,
              ref,
            }) => (
              <LinkButton
                ref={ref}
                data-arrow='show'
                className={isOpen ? '__active' : ''}
                {...getToggleButtonProps()}
              >
                {renderValue(value)}
              </LinkButton>
            )}
            value={currentLanguage.code}
            onChange={value => {
              i18n.changeLanguage(value);
            }}
          />
        </div>
      )}
    </div>
  );
};

export default AuthFooter;
