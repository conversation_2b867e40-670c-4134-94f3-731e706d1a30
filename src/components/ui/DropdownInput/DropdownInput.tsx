import React from 'react';
import Downshift from 'downshift';
import {
  PopoverDeprecated,
  PopoverDeprecatedAlign,
} from 'components/ui/Popover';
import { FormInputTrigger } from 'components/ui/DropdownInput/FormInputTrigger/FormInputTrigger';
import { DropdownInputProps } from 'components/ui/DropdownInput/types';

export const DropdownInput = <T, V>({
  className,
  value,
  disabled,
  idKey = 'id',
  itemToString: itemToStringProps,
  renderValue,
  // @ts-ignore
  renderTrigger = FormInputTrigger,
  renderDropdown,
  placeholder,
  popoverProps,
  onChange,
  align = PopoverDeprecatedAlign.BottomLeft,
}: DropdownInputProps<T, V>) => {
  const itemToString = itemToStringProps
    ? itemToStringProps
    : (i: TEMPORARY_ANY) => (i ? i.label : '');

  return (
    <Downshift
      onChange={selection => {
        if (selection !== undefined && selection !== null) {
          onChange?.(selection[idKey], selection);
        }
      }}
      itemToString={itemToString}
    >
      {({
        getMenuProps,
        getToggleButtonProps,
        getItemProps,
        getInputProps,
        isOpen,
        highlightedIndex,
        setHighlightedIndex,
      }) => (
        /* Downshift state support null as highlightedIndex
        But doesn't support setting it to null */
        //@ts-ignore
        <div onMouseLeave={() => setHighlightedIndex(null)}>
          <PopoverDeprecated
            offset={[0, 10]}
            align={align}
            active={isOpen}
            {...popoverProps}
            renderTrigger={props =>
              renderTrigger({
                className,
                placeholder,
                renderValue,
                disabled,
                //Always use this function in your trigger
                getToggleButtonProps,
                value,
                isOpen,
                // @ts-ignore
                onChange,
                ...props,
              })
            }
            renderPopover={props =>
              renderDropdown({
                ...props,
                value,
                onChange,
                getMenuProps,
                getItemProps,
                getInputProps,
                highlightedIndex,
              })
            }
          />
        </div>
      )}
    </Downshift>
  );
};
