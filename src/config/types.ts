export type GlobalConfigType = {
  apiHost: string;
  iotApiHost: string;
  yieldAppUrl: string;
  whiteLabel?: {
    icon?: string; // IconName
    logoClassName?: string; // big wide logo
    title?: string;
    favicon?: string;
    colors?: string[];
    whiteLabel: string;
    forceSeasonName?: string;
    allowConsultants?: boolean;
    hideSignup?: boolean;
    hideSocialNetworks?: boolean;
    hideIntercom?: boolean;
    hideJohnDeere?: boolean;
    hidePlatform?: boolean;
    hideYieldGuide?: boolean;
    hideLimits?: boolean;
    hideSeasons?: boolean;
    redirectUrl?: string;
    mobile?: {
      ios?: string;
      android?: string;
    };
  };
  features: {
    staticMapProvider: string;
    consoleHelpers: boolean;
    scouting: boolean;
    stations: boolean;
    sowing: boolean;
    fertilizer: boolean;
    weather: boolean;
    fields: boolean;
    dashboard: boolean;
    files: boolean;
    crop_rotation: boolean;
  };
  debug: {
    easterEggs: boolean;
    reduxTiming: boolean;
    renderVT: boolean;
    ndviGif: boolean;
    integrations: boolean;
    clevertapLogs: number;
  };
  authDomainWhitelist: string[];
  apiKeys: {
    bing: string;
    firebase: string;
    google: string;
    mapbox: string;
  };
  appIDs: {
    firebase: string;
    firebaseProjectID: string;
    firebaseMessagingSenderId: string;
    firebaseMeasurementId: string;
    gtag: string;
    google: string;
    lokalise: string;
    facebook: string;
    intercom: string;
    appleSignIn: string;
    adjust: string;
    clevertap: string;
  };
};
