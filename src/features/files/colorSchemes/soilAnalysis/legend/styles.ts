import { styled } from 'linaria/react';

export const LegendRow = styled.div`
  position: absolute;
  bottom: 10px;
  left: 15px;
  z-index: 3;
  display: flex;
`;

export const SoilAnalysisLegend = styled.div`
  width: 300px;
  flex-direction: row;
  display: flex;
`;

export const SoilAnalysisCoverageLegend = styled.div`
  min-width: 150px;
  margin-left: 20px;
`;

export const LegendBox = styled.div`
  height: 40px;
  padding: 10px;
  background: rgba(0, 0, 0, 0.7);
  border: 0.5px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0px 2px 8px rgba(34, 34, 34, 0.35);
  backdrop-filter: blur(15px);
  border-radius: 4px;

  flex-direction: row;
  align-items: flex-end;
  display: flex;
  justify-content: space-between;
`;

type LegendGradientPaletteProps = {
  color?: string;
};
export const LegendPalette = styled.div<LegendGradientPaletteProps>`
  height: 4px;
  display: flex;
  margin-top: 2px;
  background: ${props => props.color || 'transparent'};
`;

export const LegendTextBlock = styled.div`
  display: flex;
  justify-content: space-between;
`;

export const LegendBlock = styled.div`
  width: 100%;

  &:last-child ${LegendPalette} {
    border-radius: 0 4px 4px 0;
  }

  &:only-child ${LegendPalette} {
    border-radius: 4px 4px 4px 4px;
  }
`;

export const LegendText = styled.div`
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 14px;
  color: #ffffff;
  white-space: nowrap;
`;

export const LegendColor = styled.div`
  width: 52.2px;
  height: 100%;
`;
