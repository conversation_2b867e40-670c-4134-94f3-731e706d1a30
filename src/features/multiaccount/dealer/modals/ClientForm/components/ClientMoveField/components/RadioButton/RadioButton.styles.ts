import { styled } from 'linaria/react';

export const StyledValueWrapper = styled.div`
  display: flex;
  flex-basis: auto;
  flex-direction: column;
  flex-grow: 1;
  flex-shrink: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

export const StyledType = styled.span`
  color: #1c1c1e;
  font-size: 14px;
  font-style: normal;
  font-variant-ligatures: normal;
  font-weight: 400;
  line-height: 130%;
`;

export const StyledEmail = styled.span`
  color: #636366;
  font-size: 12px;
  font-style: normal;
  font-variant-ligatures: normal;
  font-weight: 400;
  line-height: 130%;
`;

export const StyledLabel = styled.label<{ checked: boolean }>`
  align-items: center;
  cursor: pointer;
  display: inline-flex;
  font-size: 1rem;
  line-height: 1.25rem;
  min-height: 1.5rem;
  overflow: ${props => (props.checked ? 'hidden' : 'visible')};
  padding: 4px 0;
  vertical-align: top;
  width: 100%;
`;

export const StyledInputEl = styled.span`
  background-color: rgba(39, 174, 96, 0);
  border-radius: 5px;
  border: 2px solid rgba(165, 178, 188, 0.3);
  flex: 0 0 auto;
  height: 20px;
  position: relative;
  transition: opacity 0.15s, border-width 0.15s, border-color 0.15s,
    background-color 0.15s;
  width: 20px;

  &:before {
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 13 10' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cdefs%3E%3Cpath d='M49.59,636.08l-3.17,-3.17l-1.42,1.41l4.59,4.59l8,-8l-1.41,-1.41z' id='a'/%3E%3C/defs%3E%3Cg transform='translate(-45 -629)'%3E%3Cuse xlink:href='%23a' fill='%23fff'/%3E%3C/g%3E%3C/svg%3E");
    background-position: 50%;
    background-size: contain;
    content: '';
    height: 10px;
    left: 0;
    margin: 5px 0 0 4px;
    opacity: 0;
    position: absolute;
    transform: translate(-2px, -7px);
    transition: transform 0.15s, opacity 0.15s;
    width: 13px;
  }
`;

export const StyledInput = styled.input`
  left: -9999px;
  opacity: 0;
  position: absolute;

  &[type='radio'] {
    + ${StyledInputEl} {
      border-radius: 20px;

      &:after {
        background-color: #fff;
        border-radius: 50%;
        content: '';
        height: 8px;
        left: 50%;
        margin-left: -4px;
        margin-top: -4px;
        opacity: 0;
        position: absolute;
        top: 50%;
        transform-origin: center;
        transform: scale(2.5);
        transition: transform 0.25s, opacity 0.25s;
        width: 8px;
      }
    }

    &:checked {
      + ${StyledInputEl} {
        background-color: #3a3a3c;
        border-width: 0;

        &:after {
          opacity: 1;
          transform: scale(1);
        }
      }

      ~ ${StyledValueWrapper} {
        flex: 1 1 auto;
      }

      &:disabled {
        + ${StyledInputEl} {
          &:after {
            background-color: #c5cdd4;
          }
        }
      }
    }

    &:disabled {
      + ${StyledInputEl} {
        background-color: rgba(165, 178, 188, 0.2) !important;
        border-width: 0;
      }
    }
  }
`;
