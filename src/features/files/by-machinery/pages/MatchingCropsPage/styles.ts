import { css } from 'linaria';
import { styled } from 'linaria/react';

export const preview = css`
  width: 56px;
  height: 56px;
  background-size: contain;
  border-radius: 8px;
  margin-right: 12px;
`;

export const FieldBlockStyle = styled.div`
  display: flex;
  flex-direction: column;
  margin-top: 44px;
`;

export const FieldBlockHeaderStyle = styled.div`
  display: flex;
  flex-direction: row;
`;

export const FieldDescriptionStyle = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
`;

export const FieldTitleStyle = styled.div`
  font-weight: 500;
  font-size: 16px;
  line-height: 22px;
  color: #222222;
`;

export const FieldSubtitleStyle = styled.div`
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  color: #5e5e5e;
`;
