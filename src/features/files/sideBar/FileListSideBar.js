import React, { Component } from 'react';
import scrollIntoView from 'scroll-into-view';
import { connect } from 'react-redux';
import { withTranslation, Trans } from 'react-i18next';
import Icon from 'components/Icon';
import Button from 'components/ui/Button/Button';
import Saga from 'components/saga/Saga';

import MissingFieldRedirector from 'features/fields/MissingFieldRedirector';

import FileUploadOverlay from '../FileUploadOverlay';
import ImportFilesButton from '../ImportFilesButton';
import FileListItem from '../FileListItem';

import files from 'modules/files';
import fields from 'modules/fields';

import filesMainSaga from 'sagas/local/files-main';

import { formatTitle } from 'utils/fields-formatters';
import getRootScoutingUrl from 'utils/get-root-scouting-url';
import { fetchFileContentSaga } from 'sagas/local/files-main';

export class FileListSideBar extends Component {
  componentDidMount() {
    this.alignSelectedFile();
  }

  componentDidUpdate(prevProps) {
    if (prevProps.fileIDs.length !== this.props.fileIDs.length) {
      this.alignSelectedFile();
    }
    if (prevProps.match.params.fileId !== this.props.match.params.fileId) {
      this.alignSelectedFile();
    }
  }

  alignSelectedFile() {
    if (this.skipRealign) {
      this.skipRealign = false;
      return;
    }
    const { fileId } = this.props.match.params;
    const listItem = document.querySelector(`[data-scroll-to=file-${fileId}]`);
    if (listItem) {
      scrollIntoView(listItem, {
        align: { top: 0 },
        time: 0,
      });
    }
  }

  render() {
    const {
      match,
      status,
      fileIDs,
      filterByField,
      selectedFile,
      history,
      location,
      t,
      onRemoveFile,
    } = this.props;

    const baseUrl = getRootScoutingUrl(location.pathname);

    return (
      <div
        className={
          filterByField ? 'soil-sidebar-create' : 'soil-sidebar-viewer'
        }
      >
        <Saga saga={filesMainSaga} />
        {selectedFile && (
          <Saga saga={fetchFileContentSaga} fileId={selectedFile.id} />
        )}
        <FileUploadOverlay history={history} />
        {filterByField && <MissingFieldRedirector fieldId={filterByField.id} />}
        <div className='soil-sidebar-header'>
          {filterByField && (
            <Button
              className='app-nav-back'
              onClick={() => {
                history.replace(baseUrl);
              }}
            >
              <Icon className='ico-arrow-long-left-os' name='arrow-left' />
            </Button>
          )}
          <div className='soil-sidebar-header__inner'>
            <h1 className='soil-sidebar-header__main'>
              {filterByField
                ? formatTitle(t, filterByField)
                : t('platform.apps.files')}
            </h1>
          </div>
        </div>
        {status === 'resolved' && fileIDs.length === 0 && (
          <div className='soil-sidebar-empty'>
            <h2 className='soil-sidebar-empty__title'>
              {t(
                `files.${
                  filterByField ? 'no_files_by_field' : 'no_files'
                }.title`,
              )}
            </h2>
            <p>
              <Trans>
                {t(
                  `files.${
                    filterByField ? 'no_files_by_field' : 'no_files'
                  }.description`,
                )}
              </Trans>
            </p>
          </div>
        )}
        {status === 'resolved' && fileIDs.length !== 0 && (
          <div className='soil-sidebar-body __scroll'>
            <ul className='soil-fields-list' data-type='files'>
              {fileIDs.map(id => (
                <FileListItem
                  key={id}
                  fileId={id}
                  history={history}
                  selected={+match.params.fileId === id}
                  onClick={() => {
                    this.skipRealign = true;
                    const baseUrl = getRootScoutingUrl(
                      history.location.pathname,
                    );
                    if (+match.params.fileId === id) {
                      history.replace(
                        filterByField
                          ? `${baseUrl}/by-field/${filterByField.id}`
                          : `${baseUrl}`,
                      );
                    } else {
                      history.replace(
                        filterByField
                          ? `${baseUrl}/by-field/${filterByField.id}/${id}`
                          : `${baseUrl}/${id}`,
                      );
                    }
                  }}
                  enableShare
                  onEdit={() => {
                    const baseUrl = getRootScoutingUrl(
                      history.location.pathname,
                    );
                    history.push(
                      filterByField
                        ? `${baseUrl}/by-field/${filterByField.id}/${id}/edit`
                        : `${baseUrl}/${id}/edit`,
                    );
                  }}
                  onRemove={() => {
                    onRemoveFile(id);
                  }}
                />
              ))}
            </ul>
          </div>
        )}
        <div className='soil-sidebar-addnew'>
          <ImportFilesButton>
            <span className='btn btn-lg btn-success size-full'>
              <span className='btn__ico'>
                <Icon className='ico-plus-os' name='plus-btn' />
              </span>
              {t('files.upload')}
            </span>
          </ImportFilesButton>
        </div>
      </div>
    );
  }
}

const mapStateToProps = (state, ownProps) => {
  const filterByField = fields.selectors.getByID(
    state,
    ownProps.match.params.fieldId,
  );
  return {
    status: files.selectors.getStatus(state),
    fileIDs: filterByField
      ? files.selectors.getIDsByField(state, filterByField)
      : files.selectors.getIDs(state),
    selectedFile: files.selectors.getByID(state, +ownProps.match.params.fileId),
    filterByField,
  };
};

const mapDispatchToProps = {
  onRemoveFile: files.actions.remove,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withTranslation()(FileListSideBar));
