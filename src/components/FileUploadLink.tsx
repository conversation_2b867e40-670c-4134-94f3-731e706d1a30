import React, { FC } from 'react';

const readFileContent = (
  file: File,
): Promise<string | ArrayBuffer | null | undefined> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = event => resolve(event.target?.result);
    reader.onerror = event => reject(event);
    reader.readAsDataURL(file);
  });

export type FileUploadLinkProp = {
  name: string;
  onUpload: (data: FormData, preview: ArrayBuffer | null) => void;
};

const FileUploadLink: FC<FileUploadLinkProp> = ({
  children,
  name,
  onUpload,
  ...inputProps
}) => (
  <div className='form-upload'>
    <input
      className='form-upload__input'
      type='file'
      {...inputProps}
      onChange={async event => {
        const { target } = event;
        // @ts-ignore
        for (const file of Array.from(target.files)) {
          const data = new FormData();
          data.append(name, file);
          let preview = null;
          try {
            preview = (await readFileContent(file)) as Array<PERSON>uffer;
          } catch (error) {
            // Do nothing
          }
          onUpload(data, preview);
        }
        // Without resetting the 'value' input will not
        // trigger change event for the same file again
        // @ts-ignore
        target.value = null;
      }}
    />
    <span className='form-upload__label'>{children}</span>
  </div>
);

export default FileUploadLink;
