import React, { VFC } from 'react';
import { PropGetters } from 'downshift';

import { useAutoFocus } from 'utils/hooks/useAutoFocus';

type DropdownSearchBoxProps = {
  getInputProps: PropGetters<string>['getInputProps'];
  searchValue: string;
  handleChangeSearch: (event: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;

  resetSearch?: () => void;
};

export const DropdownSearchBox: VFC<DropdownSearchBoxProps> = ({
  getInputProps,
  searchValue,
  handleChangeSearch,
  placeholder,
  resetSearch,
}) => {
  const autoFocus = useAutoFocus();

  return (
    <div className='modal-select-searchbox'>
      <div className='form-input-extended __filled'>
        <div className='modal-select-searchbox__icon'>
          <svg width='14' height='15' className='ico-search-os'>
            <g transform='translate(-519 -18)'>
              <path
                d='M524.56963,27.42398c-2.13274,0 -3.85435,-1.72162 -3.85435,-3.85435c0,-2.13274 1.72161,-3.85435 3.85435,-3.85435c2.13273,0 3.85435,1.72161 3.85435,3.85435c0,2.13273 -1.72162,3.85435 -3.85435,3.85435zM529.72041,27.43396h-0.67753l-0.24014,-0.23156c0.84048,-0.9777 1.34649,-2.247 1.34649,-3.62779c0,-3.0789 -2.49571,-5.57461 -5.57462,-5.57461c-3.0789,0 -5.57461,2.49571 -5.57461,5.57461c0,3.07891 2.49571,5.57462 5.57461,5.57462c1.38079,0 2.65009,-0.50601 3.62779,-1.34649l0.23156,0.24014v0.67753l4.28817,4.27959l1.27787,-1.27787z'
                fill='currentColor'
              />
            </g>
          </svg>
        </div>
        <input
          ref={autoFocus}
          {...getInputProps({
            type: 'text',
            value: searchValue,
            onChange: handleChangeSearch,
            className: 'form-input size-full',
            placeholder: placeholder || '',
          })}
        />
        {Boolean(searchValue) && (
          <button
            onClick={resetSearch}
            className='form-input-clear'
            type='button'
          >
            ×
          </button>
        )}
      </div>
    </div>
  );
};
