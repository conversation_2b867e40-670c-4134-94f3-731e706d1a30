import { useState } from 'react';
import { Modals } from './types';

export type EditConsultantManager = {
  editedConsultantUuid: string | null;
  showEditConsultantNameModal: (uuid: string) => void;
  closeEditConsultantModal: () => void;
};

export const useEditConsultant = (
  setVisibleModal: (type: Modals) => void,
): EditConsultantManager => {
  const [editedConsultantUuid, setEditedConsultantUuid] = useState<
    string | null
  >(null);

  const showEditConsultantNameModal = (uuid: string) => {
    setVisibleModal('edit-consultant-name');
    setEditedConsultantUuid(uuid);
  };
  const closeEditConsultantModal = () => {
    setEditedConsultantUuid(null);
    setVisibleModal(null);
  };

  return {
    editedConsultantUuid,
    showEditConsultantNameModal,
    closeEditConsultantModal,
  };
};
