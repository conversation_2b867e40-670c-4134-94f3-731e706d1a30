import React, { ReactNode, useEffect, useState, VFC } from 'react';
import cx from 'classnames';
import moment from 'moment';

import Icon from 'components/Icon';
import {
  PopoverDeprecated,
  PopoverDeprecatedAlign,
  PopoverDeprecatedProps,
} from 'components/ui/Popover';
import CalendarPopover, {
  CalendarPopoverDeprecatedProps,
} from 'components/ui/DateInput/CalendarPopover/CalendarPopover';

import useOutsideClick from 'utils/use-outside-click';

type DateInputProps = {
  value: moment.Moment;

  placeholder?: string;
  align?: PopoverDeprecatedProps['align'];
  renderValue: (value: moment.Moment) => ReactNode;
  calendarProps: Omit<
    CalendarPopoverDeprecatedProps<moment.Moment>,
    'value' | 'onChange'
  >;
  onChange: CalendarPopoverDeprecatedProps<moment.Moment>['onChange'];

  skipAutoAlign?: boolean;
  disabled?: boolean;
  noIcon?: boolean;
  onVisibilityToggle?: (opened: boolean) => void;
};

const DateInput: VFC<DateInputProps> = ({
  value,
  align = PopoverDeprecatedAlign.BottomLeft,
  disabled,
  renderValue,
  placeholder = '',
  skipAutoAlign,
  calendarProps,
  noIcon,
  onChange,
  onVisibilityToggle,
}) => {
  const [opened, setOpened] = useState(false);
  const onInsideClick = useOutsideClick(() => setOpened(false));

  useEffect(() => {
    onVisibilityToggle?.(opened);

    return () => onVisibilityToggle?.(false);
  }, [opened, onVisibilityToggle]);

  return (
    <div onClickCapture={onInsideClick}>
      <PopoverDeprecated
        align={align}
        offset={[0, 10]}
        active={opened}
        skipAutoAlign={skipAutoAlign}
        renderTrigger={props => (
          <div
            className={cx('form-select', {
              'form-select--active': opened,
              'form-select--disabled': disabled,
            })}
            onClick={() => {
              setOpened(!opened);
            }}
            {...props}
          >
            {!noIcon && (
              <div className='form-select__ico'>
                <Icon className='ico-calendar-os' name='calendar-dots' />
              </div>
            )}
            <div className='form-select__value'>
              {value ? renderValue(value) : placeholder}
            </div>
          </div>
        )}
        renderPopover={props => (
          // @ts-ignore
          <CalendarPopover
            value={value}
            onChange={date => {
              onChange(date);
              setOpened(false);
            }}
            {...calendarProps}
            {...props}
          />
        )}
      />
    </div>
  );
};

export default DateInput;
