import React, { useCallback, VFC } from 'react';
import { useTranslate } from 'utils/use-translate';

import { ExpandButtonStyle } from './styles';

type ExpandButtonProps = {
  open: boolean;
  dataType: string;
  setOpen: (arg: (v: boolean) => boolean) => void;
};

export const ExpandButton: VFC<ExpandButtonProps> = ({
  open,
  dataType,
  setOpen,
}) => {
  const { t } = useTranslate();
  const text = open
    ? t(`import.matching_attributes.${dataType}.less`)
    : t(`import.matching_attributes.${dataType}.more`);

  const onToggle = useCallback(() => {
    setOpen(v => !v);
  }, [setOpen]);

  return (
    <ExpandButtonStyle open={open} onClick={onToggle}>
      {text}
    </ExpandButtonStyle>
  );
};
