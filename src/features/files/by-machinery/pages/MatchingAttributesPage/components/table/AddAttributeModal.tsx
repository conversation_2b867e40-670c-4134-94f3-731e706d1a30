import React, { VFC } from 'react';

import Modal from 'components/ui/Modal/Modal';
// import logEvent from 'sagas/global/logEvent';
import { EditAttributeForm } from './EditAttributeForm';
import { CustomAttributeType } from '../MatchingCustomAttributesTable';
import logEvent from 'sagas/global/logEvent';

export type AddAttributeModalProps = {
  isVisible: boolean;
  mode: 'add' | 'edit';
  index?: number;
  defaultValue?: string | null;
  attributes: CustomAttributeType[];
  onAddAttribute?: (name: string) => void;
  onEditAttribute?: (name: string, index: number) => void;
  onClose: () => void;
};

export const AddAttributeModal: VFC<AddAttributeModalProps> = ({
  isVisible,
  mode,
  index,
  defaultValue,
  attributes,
  onAddAttribute,
  onEditAttribute,
  onClose,
}) => {
  const onSubmit = ({ name }: { name: string }) => {
    if (mode === 'add') {
      onAddAttribute!(name);
      logEvent('web_soil_sampling_vis_add_custom');
    } else if (mode === 'edit') {
      onEditAttribute!(name, index!);
      logEvent('web_soil_sampling_vis_rename_confirm_custom');
    }

    onClose();
  };

  return (
    <Modal
      show={isVisible}
      onClose={() => {
        onClose();
      }}
      width={444}
    >
      <EditAttributeForm
        mode={mode}
        attributes={attributes}
        defaultValue={defaultValue}
        onSubmit={onSubmit}
        onClose={onClose}
      />
    </Modal>
  );
};
