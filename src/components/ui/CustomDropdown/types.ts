import { CustomDropdownContentProps } from './parts/CustomDropdownContent';
import { DataForFeatureCheckType } from 'features/permissions';
import { Placement } from '@floating-ui/react';

export type CustomDropdownOption = {
  id: string;
  label?: string;
  subLabelItems?: string[];
  icon?: string;
  isTopItem?: boolean;
  danger?: boolean;
} & DataForFeatureCheckType;

// Don't pass onKeyPress to disable enter inside search input
/* eslint-disable-next-line  @typescript-eslint/no-explicit-any */
export type CustomDropdownProps<Item> = {
  options: CustomDropdownOption[];
  onChange: (value: string, selection: CustomDropdownOption) => void;

  className?: CustomDropdownContentProps<Item>['baseClassName'];
  customStyleClassName?: string;
  PopoverComponent?: CustomDropdownContentProps<Item>['PopoverComponent'];
  placement?: Placement;
  wrapperClassName?: string;
  searchBoxProps?: { placeholder: string };
  ignoreIDs?: string[]; //Selected option with ignored id doesn't have checked styles
  disabledIDs?: string[];
  onClick?: (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => void;
  compactItems?: boolean;
} & Pick<
  CustomDropdownContentProps<Item>,
  | 'onVisibilityToggle'
  | 'value'
  | 'contentForEmptySearch'
  | 'baseClassName'
  | 'onToggle'
  | 'placeholder'
  | 'withSearchBox'
  | 'idKey'
  | 'renderValue'
  | 'disabled'
  | 'sizeByParent'
>;
