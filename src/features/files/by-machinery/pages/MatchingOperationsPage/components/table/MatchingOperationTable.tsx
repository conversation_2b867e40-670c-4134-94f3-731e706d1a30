import React, { useCallback, useMemo, useState, VFC } from 'react';
import { useDispatch } from 'react-redux';

import entities from 'modules/entities';
import { FieldEntityType } from 'types/fields';
import { useTranslate } from 'utils/use-translate';
import { EntityOperation } from 'modules/entities/types';

import { SortDirections } from 'constants/sortDirections';
import { dateFormatFactory } from 'utils/dates/dateFormatFactory';

import {
  MetaAttributesWithChildren,
  TasksByMachineryMeta,
  ApiDataFieldOperations,
} from 'features/files/by-machinery/types';
import { OnSeSetDateType } from 'features/files/by-machinery/pages/MatchingOperationsPage/service/useSetDate';

import DragAndDropTable from 'components/ui/DragAndDropTable/DragAndDropTable';
import { ColumnInfo } from 'components/ui/DragAndDropTable/TableRow/types';
import Checkbox from 'components/ui/Checkbox/Checkbox';
import { LeftSideControlListToggle } from 'components/ui/DragAndDropTable/TableRow/LeftSideControlListToggle/LeftSideControlListToggle';
import { RowOperation, RowOperationProps } from './RowOperation';
import { TableWrapper } from '../styles';

type MatchingOperationTableProps = {
  operations: ApiDataFieldOperations[];
  meta: TasksByMachineryMeta<MetaAttributesWithChildren>;
  field: FieldEntityType;
  setParent: RowOperationProps['setParent'];
  setType: RowOperationProps['setType'];
  setDate: OnSeSetDateType;
  setChecked: RowOperationProps['setChecked'];
  setGroupChecked: RowOperationProps['setChecked'];
};

type SortedColumn = {
  direction: Nullable<SortDirections>;
  id: string;
};
export const MatchingOperationTable: VFC<MatchingOperationTableProps> = ({
  operations,
  meta,
  field,
  setParent,
  setDate,
  setChecked,
  setGroupChecked,
  setType,
}) => {
  const { t } = useTranslate();
  const dispatch = useDispatch();
  const [sorted, setSorted] = useState<SortedColumn>();
  const [isClosed, setIsClosed] = useState<Record<string, boolean>>({});

  const isAllEnabled = operations.some(item => item.type && item.is_enabled);

  const setSortedByColumn = useCallback(
    (columnId: string) => {
      if (columnId === 'operation') {
        return false;
      }

      setSorted(oldState => {
        let newDirection = SortDirections.DESCENDING;

        if (oldState) {
          if (oldState.direction === SortDirections.DESCENDING) {
            newDirection = SortDirections.ASCENDING;
          }
        }

        return {
          id: columnId,
          direction: newDirection,
        };
      });
    },
    [setSorted],
  );

  const columnsInfo: ColumnInfo[] = useMemo(() => {
    const setAllChecked = () => {
      const requests: EntityOperation[] = operations
        .filter(item => item.type)
        .map(item => ({
          type: 'patch',
          entityType: 'fieldsDataOperation',
          path: {
            id: item.uuid,
          },
          query: {
            include: ['data_source.layer.properties'],
          },
          body: {
            is_enabled: !isAllEnabled,
          },
        }));

      requests.length > 0 &&
        dispatch(entities.actions.request('setAllChecked', requests));
    };

    const common: ColumnInfo[] = [
      {
        key: 'operation',
        styledInfo: {
          rightBorder: true,
        },
        headerLabel: (
          <>
            <span style={{ marginLeft: '-8px', marginRight: '14px' }}>
              <Checkbox checked={isAllEnabled} onChange={setAllChecked} />
            </span>
            {t('import.matching_operations.table.operation')}
          </>
        ),
      },
      {
        key: 'date',
        sorted: sorted?.id === 'date' ? sorted.direction : null,
        headerLabel: t('import.matching_operations.table.date') as string,
      },
      {
        key: 'area',
        sorted: sorted?.id === 'area' ? sorted.direction : null,
        headerLabel: t('import.matching_operations.table.area') as string,
      },
    ];

    const propertiesColumns = meta.groups
      .map(group =>
        group.properties
          .filter(property => property.name !== 'timestamp')
          .map((property, propertyIndex) => ({
            key: property.name,
            sorted: sorted?.id === property.name ? sorted.direction : null,
            headerLabel: t(
              'import.matching_attributes.' + property.name + '.title',
            ) as string,
            columnsGroupName:
              propertyIndex === 0 && group.name
                ? (t('import.matching.attribute_group.' + group.name) as string)
                : null,
          })),
      )
      .flat(1);

    return common.concat(propertiesColumns);
  }, [sorted, t, meta.groups, dispatch, isAllEnabled, operations]);

  const sortedOperations = useMemo(() => {
    const temp = [...operations].sort((a, b) =>
      a.data_source.uuid > b.data_source.uuid ? 1 : -1,
    );
    if (!sorted) {
      return temp;
    }

    const comparison: [number, number] =
      sorted.direction === SortDirections.DESCENDING ? [1, -1] : [-1, 1];
    temp.sort((a, b) => {
      // @ts-ignore
      return a[sorted.id] > b[sorted.id] ? comparison[0] : comparison[1];
    });

    return temp;
  }, [sorted, operations]);

  const dateFormat = dateFormatFactory(t('seasons.edit.date_format') as string);

  const parentOperations = sortedOperations.filter(
    v => !v.parent_operation_uuid,
  );

  return (
    <TableWrapper>
      <DragAndDropTable columnsInfo={columnsInfo} setSorted={setSortedByColumn}>
        {parentOperations.map(operation => {
          const childrenOperations = operations.filter(
            item => item.parent_operation_uuid === operation.uuid,
          );
          const props: Partial<RowOperationProps> = {};

          if (childrenOperations.length) {
            props.canDrag = false;
            props.leftSideControl = () => {
              return (
                <LeftSideControlListToggle
                  isOpened={!isClosed[operation.uuid]}
                  onClick={() =>
                    setIsClosed(v => ({
                      ...v,
                      [operation.uuid]: !v[operation.uuid],
                    }))
                  }
                />
              );
            };
          }

          return (
            <React.Fragment key={operation.uuid}>
              <RowOperation
                isVisible={true}
                operation={operation}
                columnsInfo={columnsInfo}
                field={field}
                setDate={setDate}
                childrenOperations={childrenOperations}
                dateFormat={dateFormat}
                setParent={setParent}
                setType={setType}
                setChecked={
                  childrenOperations.length === 0 ? setChecked : setGroupChecked
                }
                {...props}
              />
              {childrenOperations.length > 0 &&
                [operation]
                  .concat(childrenOperations)
                  .map((childOperation, index) => {
                    return (
                      <RowOperation
                        key={childOperation.uuid}
                        isChild={true}
                        parentId={operation.uuid}
                        isVisible={!isClosed[operation.uuid]}
                        operation={childOperation}
                        columnsInfo={columnsInfo}
                        field={field}
                        setDate={setDate}
                        setType={setType}
                        setParent={setParent}
                        setChecked={setChecked}
                        dateFormat={dateFormat}
                        isLastInGroup={childrenOperations.length === index}
                        isFirstInGroup={index === 0}
                      />
                    );
                  })}
            </React.Fragment>
          );
        })}
      </DragAndDropTable>
    </TableWrapper>
  );
};
