import React, { useState, ReactNode } from 'react';

// @ts-ignore
import Toggle from 'components/Toggle';

const OPTIONS = [
  { id: 'sign-in', label: 'Sign in' },
  { id: 'sign-up', label: 'Sign up' },
];

export default {
  title: 'common/Toggle',
  component: Toggle,
  decorators: [
    (storyFn: () => ReactNode) => (
      <div style={{ width: 300, margin: '0 auto' }}>{storyFn()}</div>
    ),
  ],
};

export const Default = () => {
  const [value, setValue] = useState('sign-in');

  return (
    <Toggle
      value={value}
      titleKey='label'
      options={OPTIONS}
      onChange={(id: string) => {
        setValue(id);
      }}
    />
  );
};

export const Dark = () => {
  const [value, setValue] = useState('sign-in');

  return (
    <Toggle
      theme='dark'
      value={value}
      titleKey='label'
      options={OPTIONS}
      onChange={(id: string) => {
        setValue(id);
      }}
    />
  );
};
