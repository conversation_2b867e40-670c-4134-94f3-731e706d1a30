import React from 'react';

const Icons = {
  checkSuccess: (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
    >
      <path
        d='M10 19.9102C15.4492 19.9102 19.9609 15.3887 19.9609 9.94922C19.9609 4.5 15.4395 -0.0117188 9.99023 -0.0117188C4.55078 -0.0117188 0.0390625 4.5 0.0390625 9.94922C0.0390625 15.3887 4.56055 19.9102 10 19.9102ZM8.90625 14.7246C8.57422 14.7246 8.30078 14.5879 8.04688 14.2461L5.5957 11.2383C5.44922 11.043 5.36133 10.8281 5.36133 10.6035C5.36133 10.1641 5.70312 9.80273 6.14258 9.80273C6.42578 9.80273 6.64062 9.89062 6.88477 10.2129L8.86719 12.7715L13.0371 6.07227C13.2227 5.7793 13.4766 5.62305 13.7305 5.62305C14.1602 5.62305 14.5605 5.91602 14.5605 6.375C14.5605 6.58984 14.4336 6.81445 14.3164 7.01953L9.72656 14.2461C9.52148 14.5684 9.23828 14.7246 8.90625 14.7246Z'
        fill='#27AE60'
      />
    </svg>
  ),
  plant: (
    <svg
      width='34'
      height='37'
      viewBox='0 0 34 37'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M30.4838 3.55112C30.3682 8.87572 29.6014 12.9158 27.5626 15.6322C25.7611 18.0325 22.7573 19.6543 17.5 19.9093V19.9086V19.9031V19.8975V19.8919V19.8864V19.8809V19.8753V19.8698V19.8643V19.8588V19.8533V19.8478V19.8423V19.8368V19.8314V19.8259V19.8204V19.8149V19.8095V19.804V19.7986V19.7931V19.7877V19.7823V19.7768V19.7714V19.766V19.7605V19.7551V19.7497V19.7443V19.7389V19.7334V19.728V19.7226V19.7172V19.7118V19.7064V19.701V19.6955V19.6901V19.6847V19.6793V19.6739V19.6685V19.6631V19.6577V19.6522V19.6468V19.6414V19.636V19.6305V19.6251V19.6197V19.6142V19.6088V19.6034V19.5979V19.5925V19.587V19.5815V19.5761V19.5706V19.5651V19.5597V19.5542V19.5487V19.5432V19.5377V19.5322V19.5267V19.5211V19.5156V19.5101V19.5045V19.499V19.4934V19.4878V19.4823V19.4767V19.4711V19.4655V19.4599V19.4542V19.4486V19.443V19.4373V19.4317V19.426V19.4203V19.4146V19.4089V19.4032V19.3974V19.3917V19.386V19.3802V19.3744V19.3686V19.3628V19.357V19.3512V19.3453V19.3395V19.3336V19.3277V19.3218V19.3159V19.31V19.304V19.2981V19.2921V19.2861V19.2801V19.2741V19.268V19.262V19.2559V19.2498V19.2437V19.2376V19.2314V19.2253V19.2191V19.2129V19.2067V19.2004V19.1942V19.1879V19.1816V19.1753V19.169V19.1626V19.1562V19.1498V19.1434V19.137V19.1305V19.1241V19.1176V19.111V19.1045V19.0979V19.0913V19.0847V19.0781V19.0714V19.0648V19.0581V19.0513V19.0446V19.0378V19.031V19.0242V19.0173V19.0104V19.0035V18.9966V18.9897V18.9827V18.9757V18.9686V18.9616V18.9545V18.9474V18.9403V18.9331V18.9259V18.9187V18.9114V18.9042V18.8968V18.8895V18.8821V18.8747V18.8673V18.8599V18.8524V18.8449V18.8373V18.8298V18.8222V18.8145V18.8069V18.7992V18.7914V18.7837V18.7759V18.768V18.7602V18.7523V18.7444V18.7364V18.7284V18.7204V18.7123V18.7043V18.6961V18.688V18.6798V18.6715V18.6633V18.655V18.6466V18.6383V18.6299V18.6214V18.6129V18.6044V18.5959V18.5873V18.5786V18.57V18.5613V18.5525V18.5438V18.5349V18.5261V18.5172V18.5083V18.4993V18.4903V18.4812V18.4721V18.463V18.4538V18.4446V18.4353V18.4261V18.4167V18.4073V18.3979V18.3885V18.379V18.3694V18.3598V18.3502V18.3405V18.3308V18.321V18.3112V18.3014V18.2915V18.2816V18.2716V18.2616V18.2515V18.2414V18.2312V18.221V18.2108V18.2005V18.1901V18.1797V18.1693V18.1588V18.1483V18.1377V18.1271V18.1164V18.1057V18.0949V18.0841V18.0732V18.0623V18.0514V18.0404V18.0293V18.0182V18.007V17.9958V17.9845V17.9732V17.9619V17.9505V17.939V17.9275V17.9159V17.9043V17.8926V17.8809V17.8691V17.8573V17.8454V17.8335V17.8215V17.8094V17.7973V17.7852V17.773V17.7607V17.7484V17.736V17.7236V17.7111V17.6986V17.686V17.6734V17.6607V17.6479V17.6351V17.6222V17.6093V17.5963V17.5833V17.5702V17.557V17.5438V17.5305V17.5172V17.5038V17.4903V17.4768V17.4633V17.4496V17.4359C17.5 12.7127 18.9821 9.40973 21.252 7.2208C23.5217 5.03194 26.7342 3.80834 30.4838 3.55112ZM29.962 17.433C27.4344 20.8007 23.415 22.6502 17.5 22.9121V22.9123V22.9145V22.9167V22.9189V22.9211V22.9233V22.9254V22.9276V22.9298V22.932V22.9342V22.9363V22.9385V22.9407V22.9428V22.945V22.9472V22.9493V22.9511V22.9515V22.9536V22.9558V22.9579V22.9601V22.9622V22.9644V22.9665V22.9687V22.9708V22.9729V22.9751V22.9772V22.9793V22.9814V22.9836V22.9857V22.9878V22.9899V22.9921V22.9942V22.996V22.9963V22.9984V23.0005V23.0026V23.0047V23.0068V23.0089V23.011V23.0131V23.0152V23.0173V23.0194V23.0215V23.0235V23.0256V23.0277V23.0298V23.0319V23.0339V23.036V23.0381V23.0402V23.0408V23.0422V23.0443V23.0464V23.0484V23.0505V23.0525V23.0546V23.0566V23.0587V23.0608V23.0628V23.0648V23.0669V23.0689V23.071V23.073V23.0751V23.0771V23.0791V23.0812V23.0832V23.0852V23.0857V23.0872V23.0893V23.0913V23.0933V23.0953V23.0974V23.0994V23.1014V23.1034V23.1054V23.1074V23.1094V23.1115V23.1135V23.1155V23.1175V23.1195V23.1215V23.1235V23.1255V23.1275V23.1294V23.1304V23.1314V23.1334V23.1354V23.1374V23.1394V23.1414V23.1434V23.1453V23.1473V23.1493V23.1513V23.1532V23.1552V23.1572V23.1592V23.1611V23.1631V23.1651V23.167V23.169V23.171V23.1729V23.1749V23.1751V23.1768V23.1788V23.1807V23.1827V23.1847V23.1866V23.1886V23.1905V23.1925V23.1944V23.1963V23.1983V23.2002V23.2022V23.2041V23.206V23.208V23.2099V23.2119V23.2138V23.2157V23.2177V23.2196V23.2198V23.2215V23.2234V23.2254V23.2273V23.2292V23.2311V23.2331V23.235V23.2369V23.2388V23.2407V23.2426V23.2446V23.2465V23.2484V23.2503V23.2522V23.2541V23.256V23.2579V23.2598V23.2617V23.2637V23.2644V23.2656V23.2675V23.2694V23.2713V23.2732V23.2751V23.277V23.2789V23.2807V23.2826V23.2845V23.2864V23.2883V23.2902V23.2921V23.294V23.2959V23.2978V23.2997V23.3015V23.3034V23.3053V23.3072V23.309V23.3091V23.311V23.3128V23.3147V23.3166V23.3185V23.3203V23.3222V23.3241V23.326V23.3279V23.3297V23.3316V23.3335V23.3353V23.3372V23.3391V23.341V23.3428V23.3447V23.3466V23.3484V23.3503V23.3522V23.3535V23.354V23.3559V23.3577V23.3596V23.3615V23.3633V23.3652V23.3671V23.3689V23.3708V23.3726V23.3745V23.3764V23.3782V23.3801V23.3819V23.3838V23.3856V23.3875V23.3893V23.3912V23.393V23.3949V23.3968V23.398V23.3986V23.4005V23.4023V23.4042V23.406V23.4079V23.4097V23.4116V23.4134V23.4153V23.4171V23.4189V23.4208V23.4226V23.4245V23.4263V23.4282V23.43V23.4319V23.4337V23.4356V23.4374V23.4393V23.4411V23.4425V23.4429V23.4448V23.4466V23.4485V23.4503V23.4522V23.454V23.4558V23.4577V23.4595V23.4614V23.4632V23.465V23.4669V23.4687V23.4706V23.4724V23.4743V23.4761V23.4779V23.4798V23.4816V23.4835V23.4853V23.4869V23.4871V23.489V23.4908V23.4927V23.4945V23.4963V23.4982V23.5V23.5312V23.5755V23.6197V23.6639V23.7081V23.7522V23.7963V23.8403V23.8842V23.9282V23.972V24.0158V24.0596V24.1033V24.147V24.1906V24.2342V24.2777V24.3212V24.3646V24.408V24.4513V24.4946V24.5378V24.581V24.6241V24.6672V24.7102V24.7532V24.7961V24.839V24.8818V24.9246V24.9673V25.01V25.0526V25.0952V25.1377V25.1801V25.2225V25.2649V25.3072V25.3495V25.3917V25.4338V25.4759V25.518V25.56V25.6019V25.6438V25.6856V25.7274V25.7691V25.8108V25.8524V25.894V25.9355V25.9769V26.0183V26.0597V26.101V26.1422V26.1834V26.2245V26.2656V26.3066V26.3476V26.3885V26.4293V26.4701V26.5109V26.5516V26.5922V26.6328V26.6733V26.7138V26.7542V26.7945V26.8348V26.875V26.9152V26.9553V26.9954V27.0354V27.0754V27.1153V27.1551V27.1949V27.2346V27.2743V27.3139V27.3534V27.3929V27.4323V27.4717V27.511V27.5503V27.5894V27.6286V27.6677V27.7067V27.7456V27.7845V27.8234V27.8621V27.9009V27.9395V27.9781V28.0167V28.0551V28.0935V28.1319V28.1702V28.2084V28.2466V28.2847V28.3228V28.3608V28.3987V28.4365V28.4744V28.5121V28.5498V28.5874V28.6249V28.6624V28.6999V28.7372V28.7745V28.8118V28.849V28.8861V28.9231V28.9601V28.9971V29.0339V29.0707V29.1075V29.1441V29.1807V29.2173V29.2538V29.2902V29.3265V29.3628V29.399V29.4352V29.4713V29.5073V29.5433V29.5792V29.615V29.6508V29.6865V29.7221V29.7577V29.7932V29.8286V29.864V29.8993V29.9345V29.9697V30.0048V30.0398V30.0748V30.1097V30.1445V30.1793V30.214V30.2486V30.2832V30.3176V30.3521V30.3864V30.4207V30.4549V30.4891V30.5232V30.5572V30.5911V30.625V30.6588V30.6926V30.7262V30.7598V30.7934V30.8268V30.8602V30.8935V30.9268V30.96V30.9931V31.0261V31.0591V31.092V31.1248V31.1576V31.1902V31.2229V31.2554V31.2879V31.3203V31.3526V31.3848V31.417V31.4491V31.4812V31.5131V31.545V31.5769V31.6086V31.6403V31.6719V31.7034V31.7349V31.7663V31.7976V31.8288V31.86V31.8911V31.9221V31.953V31.9839V32.0147V32.0454V32.076V32.1066V32.1371V32.1675V32.1979V32.2281V32.2583V32.2884V32.3185V32.3484V32.3783V32.4082V32.4379V32.4676V32.4971V32.5267V32.5561V32.5854V32.6147V32.6439V32.6731V32.7021V32.7311V32.76V32.7888V32.8175V32.8462V32.8748V32.9033V32.9317V32.9601V32.9883V33.0165V33.0447V33.0727V33.1007V33.1285V33.1563V33.1841V33.2117V33.2393V33.2667V33.2941V33.3215V33.3487V33.3759V33.403V33.43V33.4569V33.4837V33.5105V33.5372V33.5638V33.5903V33.6167V33.6431V33.6694V33.6956V33.7217V33.7477V33.7736V33.7995V33.8253V33.851V33.8766V33.9022V33.9276V33.953V33.9783V34H28V37L17.5 37H14.5H3.99996L3.99996 34H14.5V33.9783V33.953V33.9276V33.9022V33.8766V33.851V33.8253V33.7995V33.7736V33.7477V33.7217V33.6956V33.6694V33.6431V33.6167V33.5903V33.5638V33.5372V33.5105V33.4837V33.4569V33.43V33.403V33.3759V33.3487V33.3215V33.2941V33.2667V33.2393V33.2117V33.1841V33.1563V33.1285V33.1007V33.0727V33.0447V33.0165V32.9883V32.9601V32.9317V32.9033V32.8748V32.8462V32.8175V32.7888V32.76V32.7311V32.7021V32.6731V32.6439V32.6147V32.5854V32.5561V32.5267V32.4971V32.4676V32.4379V32.4082V32.3783V32.3484V32.3185V32.2884V32.2583V32.2281V32.1979V32.1675V32.1371V32.1066V32.076V32.0454V32.0147V31.9839V31.953V31.9221V31.8911V31.86V31.8288V31.7976V31.7663V31.7349V31.7034V31.6719V31.6403V31.6086V31.5769V31.545V31.5131V31.4812V31.4491V31.417V31.3848V31.3526V31.3203V31.2879V31.2554V31.2229V31.1902V31.1576V31.1248V31.092V31.0591V31.0261V30.9931V30.96V30.9268V30.8935V30.8602V30.8268V30.7934V30.7598V30.7262V30.6926V30.6588V30.625V30.5911V30.5572V30.5232V30.4891V30.4549V30.4207V30.3864V30.3521V30.3176V30.2832V30.2486V30.214V30.1793V30.1445V30.1097V30.0748V30.0398V30.0048V29.9697V29.9345V29.8993V29.864V29.8286V29.7932V29.7577V29.7221V29.6865V29.6508V29.615V29.5792V29.5433V29.5073V29.4713V29.4352V29.399V29.3628V29.3265V29.2902V29.2538V29.2173V29.1807V29.1441V29.1075V29.0707V29.0339V28.9971V28.9601V28.9231V28.8861V28.849V28.8118V28.7745V28.7372V28.6999V28.6624V28.6249V28.5874V28.5498V28.5121V28.4744V28.4365V28.3987V28.3608V28.3228V28.2847V28.2466V28.2084V28.1702V28.1319V28.0935V28.0551V28.0167V27.9781V27.9395V27.9009V27.8621V27.8234V27.7845V27.7456V27.7067V27.6677V27.6286V27.5894V27.5503V27.511V27.4717V27.4323V27.3929V27.3534V27.3139V27.2743V27.2346V27.1949V27.1551V27.1153V27.0754V27.0354V26.9954V26.9553V26.9152V26.875V26.8348V26.7945V26.7542V26.7138V26.6733V26.6328V26.5922V26.5516V26.5109V26.4756C14.4818 26.4753 14.4637 26.4748 14.4455 26.4741C10.3801 26.3322 6.90648 25.5772 4.42439 23.3171C1.91584 21.033 0.7292 17.5023 0.530412 12.5661C0.461469 10.8541 1.85242 9.50002 3.49975 9.50002H4.99996C7.75081 9.50002 10.8495 10.742 13.2562 12.9175C13.7657 13.378 14.2476 13.8836 14.6913 14.432C15.2144 10.4847 16.7902 7.35585 19.1695 5.06135C22.1012 2.2341 26.0977 0.822592 30.4055 0.549777C32.1546 0.439004 33.5188 1.87439 33.4849 3.53335C33.3731 8.99374 32.6152 13.8979 29.962 17.433ZM14.5 22.38C14.5 19.3143 13.141 16.8573 11.2445 15.143C9.31809 13.4017 6.91679 12.5 4.99996 12.5H3.53023C3.72136 17.0533 4.80289 19.6045 6.44416 21.0989C8.11121 22.6168 10.6652 23.3349 14.5 23.4742V23.4724V23.4706V23.4687V23.4669V23.465V23.4632V23.4614V23.4595V23.4577V23.4558V23.454V23.4522V23.4503V23.4485V23.4466V23.4448V23.4429V23.4425V23.4411V23.4393V23.4374V23.4356V23.4337V23.4319V23.43V23.4282V23.4263V23.4245V23.4226V23.4208V23.4189V23.4171V23.4153V23.4134V23.4116V23.4097V23.4079V23.406V23.4042V23.4023V23.4005V23.3986V23.398V23.3968V23.3949V23.393V23.3912V23.3893V23.3875V23.3856V23.3838V23.3819V23.3801V23.3782V23.3764V23.3745V23.3726V23.3708V23.3689V23.3671V23.3652V23.3633V23.3615V23.3596V23.3577V23.3559V23.354V23.3535V23.3522V23.3503V23.3484V23.3466V23.3447V23.3428V23.341V23.3391V23.3372V23.3353V23.3335V23.3316V23.3297V23.3279V23.326V23.3241V23.3222V23.3203V23.3185V23.3166V23.3147V23.3128V23.311V23.3091V23.309V23.3072V23.3053V23.3034V23.3015V23.2997V23.2978V23.2959V23.294V23.2921V23.2902V23.2883V23.2864V23.2845V23.2826V23.2807V23.2789V23.277V23.2751V23.2732V23.2713V23.2694V23.2675V23.2656V23.2644V23.2637V23.2617V23.2598V23.2579V23.256V23.2541V23.2522V23.2503V23.2484V23.2465V23.2446V23.2426V23.2407V23.2388V23.2369V23.235V23.2331V23.2311V23.2292V23.2273V23.2254V23.2234V23.2215V23.2198V23.2196V23.2177V23.2157V23.2138V23.2119V23.2099V23.208V23.206V23.2041V23.2022V23.2002V23.1983V23.1963V23.1944V23.1925V23.1905V23.1886V23.1866V23.1847V23.1827V23.1807V23.1788V23.1768V23.1751V23.1749V23.1729V23.171V23.169V23.167V23.1651V23.1631V23.1611V23.1592V23.1572V23.1552V23.1532V23.1513V23.1493V23.1473V23.1453V23.1434V23.1414V23.1394V23.1374V23.1354V23.1334V23.1314V23.1304V23.1294V23.1275V23.1255V23.1235V23.1215V23.1195V23.1175V23.1155V23.1135V23.1115V23.1094V23.1074V23.1054V23.1034V23.1014V23.0994V23.0974V23.0953V23.0933V23.0913V23.0893V23.0872V23.0857V23.0852V23.0832V23.0812V23.0791V23.0771V23.0751V23.073V23.071V23.0689V23.0669V23.0648V23.0628V23.0608V23.0587V23.0566V23.0546V23.0525V23.0505V23.0484V23.0464V23.0443V23.0422V23.0408V23.0402V23.0381V23.036V23.0339V23.0319V23.0298V23.0277V23.0256V23.0235V23.0215V23.0194V23.0173V23.0152V23.0131V23.011V23.0089V23.0068V23.0047V23.0026V23.0005V22.9984V22.9963V22.996V22.9942V22.9921V22.9899V22.9878V22.9857V22.9836V22.9814V22.9793V22.9772V22.9751V22.9729V22.9708V22.9687V22.9665V22.9644V22.9622V22.9601V22.9579V22.9558V22.9536V22.9515V22.9511V22.9493V22.9472V22.945V22.9428V22.9407V22.9385V22.9363V22.9342V22.932V22.9298V22.9276V22.9254V22.9233V22.9211V22.9189V22.9167V22.9145V22.9123V22.9101V22.9079V22.9061V22.9057V22.9035V22.9013V22.8991V22.8969V22.8946V22.8924V22.8902V22.888V22.8857V22.8835V22.8813V22.8791V22.8768V22.8746V22.8723V22.8701V22.8679V22.8656V22.8634V22.8612V22.8611V22.8588V22.8566V22.8543V22.8521V22.8498V22.8475V22.8453V22.843V22.8407V22.8384V22.8361V22.8339V22.8316V22.8293V22.827V22.8247V22.8224V22.8201V22.8178V22.8161V22.8155V22.8132V22.8109V22.8085V22.8062V22.8039V22.8016V22.7993V22.7969V22.7946V22.7923V22.7899V22.7876V22.7852V22.7829V22.7805V22.7782V22.7758V22.7735V22.7711V22.771V22.7688V22.7664V22.764V22.7617V22.7593V22.7569V22.7545V22.7521V22.7498V22.7474V22.745V22.7426V22.7402V22.7378V22.7354V22.733V22.7306V22.7281V22.7259V22.7257V22.7233V22.7209V22.7185V22.716V22.7136V22.7112V22.7087V22.7063V22.7038V22.7014V22.6989V22.6965V22.694V22.6916V22.6891V22.6866V22.6842V22.6817V22.6807V22.6792V22.6768V22.6743V22.6718V22.6693V22.6668V22.6643V22.6618V22.6593V22.6568V22.6543V22.6518V22.6493V22.6468V22.6442V22.6417V22.6392V22.6367V22.6355V22.6341V22.6316V22.6291V22.6265V22.624V22.6214V22.6189V22.6163V22.6137V22.6112V22.6086V22.606V22.6035V22.6009V22.5983V22.5957V22.5931V22.5905V22.5902V22.5879V22.5853V22.5827V22.5801V22.5775V22.5749V22.5723V22.5697V22.5671V22.5644V22.5618V22.5592V22.5565V22.5539V22.5513V22.5486V22.5459V22.5449V22.5433V22.5406V22.538V22.5353V22.5326V22.53V22.5273V22.5246V22.5219V22.5192V22.5165V22.5139V22.5112V22.5084V22.5057V22.503V22.5003V22.4996V22.4976V22.4949V22.4922V22.4894V22.4867V22.484V22.4812V22.4785V22.4757V22.473V22.4702V22.4675V22.4647V22.4619V22.4592V22.4564V22.4542V22.4536V22.4508V22.448V22.4452V22.4425V22.4397V22.4369V22.434V22.4312V22.4284V22.4256V22.4228V22.42V22.4171V22.4143V22.4115V22.4088V22.4086V22.4058V22.4029V22.4001V22.3972V22.3944V22.3915V22.3886V22.3857V22.3829V22.38Z'
        fill='#1C1C1E'
      />
    </svg>
  ),
  VerticalSwitcher: (
    <svg viewBox='0 0 48 48' fill='none' xmlns='http://www.w3.org/2000/svg'>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M26.1213 7C24.9498 5.82843 23.0503 5.82843 21.8787 7L11.9394 16.9393L14.0607 19.0607L24 9.12132L33.9394 19.0607L36.0607 16.9393L26.1213 7ZM11.9393 31.0607L21.8787 41C23.0502 42.1716 24.9497 42.1716 26.1213 41L36.0607 31.0607L33.9393 28.9393L24 38.8787L14.0606 28.9393L11.9393 31.0607Z'
        fill='currentColor'
      />
    </svg>
  ),
  newReleases: (
    <svg
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M9 3H15H16.5C16.5 2.17157 15.8284 1.5 15 1.5H9C8.17157 1.5 7.5 2.17157 7.5 3L9 3ZM19.5 9H4.5C3.67157 9 3 9.67157 3 10.5V21C3 21.8284 3.67157 22.5 4.5 22.5H13.5011C13.5018 22.3388 13.5026 22.1829 13.5035 22.032L13.5035 22.0307V22.0306C13.5113 20.632 13.5168 19.6589 13.33 18.8082C13.1218 17.8603 12.6651 17.0579 11.5119 16.0694L12.4881 14.9306C13.8015 16.0563 14.4696 17.1042 14.7726 18.3878C15.3694 17.9582 16 17.1976 16 16C16 15.2935 15.8269 14.7808 15.5557 14.379C15.2784 13.9683 14.8651 13.6244 14.3114 13.3154C13.7544 13.0045 13.0884 12.7468 12.335 12.4916C12.0331 12.3893 11.6964 12.2811 11.3488 12.1693C11.1031 12.0903 10.8519 12.0096 10.6036 11.9279C10.3305 12.5517 10.0408 13.2742 9.79899 13.9994C9.46734 14.9939 9.25 15.9239 9.25 16.597C9.25 17.5326 9.60403 18.2841 10.1216 18.7215C10.6203 19.1429 11.3447 19.3378 12.2675 19.037L12.7325 20.463C11.3553 20.9122 10.0797 20.65 9.15339 19.8671C8.24597 19.1003 7.75 17.9003 7.75 16.597C7.75 15.6731 8.03266 14.5546 8.37601 13.5249C8.72515 12.4779 9.1622 11.4503 9.52036 10.6828L9.80712 10.0684L10.4473 10.2919C10.9044 10.4516 11.3316 10.5885 11.7625 10.7266C12.1058 10.8366 12.4514 10.9473 12.8162 11.0709C13.5915 11.3335 14.3626 11.6261 15.0425 12.0055C15.7256 12.3868 16.3489 12.8731 16.7989 13.5398C17.255 14.2154 17.5 15.0274 17.5 16C17.5 18.2856 15.9875 19.5019 14.9808 20.004C15.0136 20.645 15.009 21.34 15.0039 22.1003C15.003 22.2316 15.0022 22.3648 15.0015 22.5H19.5C20.3284 22.5 21 21.8284 21 21V10.5C21 9.67157 20.3284 9 19.5 9ZM4.5 7.5C2.84315 7.5 1.5 8.84315 1.5 10.5V21C1.5 22.6569 2.84315 24 4.5 24H19.5C21.1569 24 22.5 22.6569 22.5 21V10.5C22.5 8.84315 21.1569 7.5 19.5 7.5H4.5ZM18 6H6L4.5 6C4.5 5.17157 5.17157 4.5 6 4.5H18C18.8284 4.5 19.5 5.17157 19.5 6H18Z'
        fill='#1C1C1E'
      />
    </svg>
  ),
  johnDeer: (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <g clipPath='url(#clip0_838_139806)'>
        <circle cx='8' cy='8' r='8' fill='currentColor' />
        <path
          d='M4.08932 11.081C3.81932 11.081 3.60932 11.063 3.45932 11.027C3.30932 10.991 3.18332 10.946 3.08132 10.892V9.956C3.19532 10.004 3.31232 10.043 3.43232 10.073C3.55832 10.103 3.71732 10.118 3.90932 10.118C4.20332 10.118 4.43732 10.034 4.61132 9.866C4.78532 9.698 4.87232 9.416 4.87232 9.02V4.565H6.07832V9.047C6.07832 9.395 6.03032 9.698 5.93432 9.956C5.83832 10.208 5.70332 10.418 5.52932 10.586C5.35532 10.754 5.14532 10.88 4.89932 10.964C4.65332 11.042 4.38332 11.081 4.08932 11.081ZM9.41971 10.055C10.1457 10.055 10.6827 9.86 11.0307 9.47C11.3787 9.074 11.5527 8.519 11.5527 7.805V7.733C11.5527 7.385 11.5107 7.076 11.4267 6.806C11.3487 6.53 11.2227 6.296 11.0487 6.104C10.8747 5.912 10.6527 5.765 10.3827 5.663C10.1127 5.561 9.78872 5.51 9.41072 5.51H8.71771V10.055H9.41971ZM7.52071 4.565H9.47372C10.0317 4.565 10.5177 4.643 10.9317 4.799C11.3517 4.949 11.6997 5.162 11.9757 5.438C12.2517 5.714 12.4587 6.047 12.5967 6.437C12.7347 6.821 12.8037 7.247 12.8037 7.715V7.796C12.8037 8.264 12.7347 8.696 12.5967 9.092C12.4587 9.482 12.2517 9.818 11.9757 10.1C11.6997 10.382 11.3517 10.604 10.9317 10.766C10.5117 10.922 10.0197 11 9.45572 11H7.52071V4.565Z'
          fill='#FFDE00'
        />
      </g>
      <defs>
        <clipPath id='clip0_838_139806'>
          <rect width='16' height='16' rx='8' fill='white' />
        </clipPath>
      </defs>
    </svg>
  ),
  sortArrow: (
    <svg width='16' height='16' viewBox='0 0 24 24' focusable='false'>
      <path d='M5.41 7.59L4 9l8 8 8-8-1.41-1.41L12 14.17'></path>
    </svg>
  ),
  closeSmall: (
    <svg width='8' height='8' viewBox='0 0 8 8'>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M7.33333 0.666667C7.56345 0.896785 7.56345 1.26988 7.33333 1.5L4.83333 4L7.33333 6.5C7.56345 6.73012 7.56345 7.10321 7.33333 7.33333C7.10321 7.56345 6.73012 7.56345 6.5 7.33333L4 4.83333L1.5 7.33333C1.26988 7.56345 0.896785 7.56345 0.666667 7.33333C0.436548 7.10321 0.436548 6.73012 0.666667 6.5L3.16667 4L0.666667 1.5C0.436548 1.26988 0.436548 0.896785 0.666667 0.666667C0.896785 0.436548 1.26988 0.436548 1.5 0.666667L4 3.16667L6.5 0.666667C6.73012 0.436548 7.10321 0.436548 7.33333 0.666667Z'
        fill='#5E5E5E'
      />
    </svg>
  ),
  close: (
    <svg width='12' height='12' viewBox='0 0 12 12'>
      <path
        d='M7.426 6l4.279 4.278c.132.133.22.292.265.459a.995.995 0 0 1-.265.968.995.995 0 0 1-1.426 0L6 7.426l-4.278 4.279a.995.995 0 0 1-1.426 0 .995.995 0 0 1 0-1.426L4.573 6 .295 1.722a.995.995 0 0 1 0-1.426.995.995 0 0 1 1.426 0L6 4.573 10.278.295a1.03 1.03 0 0 1 .459-.265.995.995 0 0 1 .968.265c.132.133.22.292.265.459a.995.995 0 0 1-.265.968L7.426 6z'
        fill='currentColor'
        fillRule='evenodd'
      />
    </svg>
  ),
  'arrow-down': (
    <svg width='16' height='9' viewBox='0 0 16 9'>
      <path
        d='M8.822 8.616a.997.997 0 0 1-.76.348.997.997 0 0 1-.76-.348L.992 2.307A1 1 0 0 1 2.408.893l5.655 5.655L13.717.893a1 1 0 0 1 1.414 1.414L8.822 8.616z'
        fill='currentColor'
        fillRule='evenodd'
      />
    </svg>
  ),
  'arrow-left': (
    <svg viewBox='0 0 14 12'>
      <path
        d='M6 0l1.41 1.41L3.83 5H14v2H3.83l3.58 3.59L6 12 0 6z'
        fill='currentColor'
      />
    </svg>
  ),
  'arrow-left-v2': (
    <svg width='24' height='24' viewBox='0 0 24 24'>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M11 6L12.41 7.41L8.83 11H19V13H8.83L12.41 16.59L11 18L5 12L11 6Z'
        fill='currentColor'
      />
    </svg>
  ),
  'arrow-right2': (
    <svg width='14' height='12' viewBox='0 0 14 12'>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        fill='currentColor'
        d='M8 0L6.59 1.41L10.17 5H0V7H10.17L6.59 10.59L8 12L14 6L8 0Z'
      />
    </svg>
  ),
  'arrow-right': (
    <svg width='6px' height='10px' viewBox='0 0 6 10'>
      <path d='M3.54 5.004L.299 8.248a1.038 1.038 0 0 0-.272.464.983.983 0 0 0 0 .512c.045.168.136.328.272.464s.296.227.464.272a.983.983 0 0 0 .512 0c.168-.045.328-.136.464-.272l3.945-3.944a1.123 1.123 0 0 0 .3-.492.983.983 0 0 0 0-.513 1.038 1.038 0 0 0-.399-.573L1.73.312A1.038 1.038 0 0 0 1.266.04a.983.983 0 0 0-.513 0A1.038 1.038 0 0 0 .29.312a1.038 1.038 0 0 0-.272.464.983.983 0 0 0 0 .512c.046.169.136.329.272.464l3.252 3.252z' />
    </svg>
  ),
  'arrow-down-small': (
    <svg width='8px' height='5px' viewBox='0 0 8 5'>
      <path
        d='M3.45 2.59a.24.24 0 0 0 .35 0L6.29.15a.5.5 0 0 1 .71 0l.27.28a.5.5 0 0 1 0 .71L4 4.44a.49.49 0 0 1-.7 0L.14 1.14a.5.5 0 0 1 0-.7l.32-.3a.49.49 0 0 1 .7 0z'
        fill='currentColor'
      />
    </svg>
  ),
  'trend-arrow-down': (
    <svg viewBox='0 0 8 9'>
      <path
        d='M2.996 5.81V.5h1.4v5.31l1.927-1.926.99.99L4.07 8.116l-.374.384-.373-.384L.08 4.874l.99-.99z'
        fill='currentColor'
      />
    </svg>
  ),
  'trend-arrow-up': (
    <svg viewBox='0 0 8 9'>
      <path
        d='M2.916 3.19V8.5h1.4V3.19l1.927 1.926.99-.99L3.99.884 3.616.5l-.373.384L0 4.126l.99.99z'
        fill='currentColor'
      />
    </svg>
  ),
  ruler: (
    <svg width='21' height='21' viewBox='0 0 21 21'>
      <g transform='rotate(45 11 10.5)'>
        <path
          d='M7.5,19.25c0,0.41421 0.33579,0.75 0.75,0.75h1.5c0.41421,0 0.75,-0.33579 0.75,-0.75c0,-0.41421 -0.33579,-0.75 -0.75,-0.75h-0.75v-2h2.25c0.41421,0 0.75,-0.33579 0.75,-0.75c0,-0.41421 -0.33579,-0.75 -0.75,-0.75h-2.25v-2h0.75c0.41421,0 0.75,-0.33579 0.75,-0.75c0,-0.41421 -0.33579,-0.75 -0.75,-0.75h-0.75v-2h2.25c0.41421,0 0.75,-0.33579 0.75,-0.75c0,-0.41421 -0.33579,-0.75 -0.75,-0.75h-2.25v-2h0.75c0.41421,0 0.75,-0.33579 0.75,-0.75c0,-0.41421 -0.33579,-0.75 -0.75,-0.75h-0.75v-2h2.25c0.41421,0 0.75,-0.33579 0.75,-0.75c0,-0.41421 -0.33579,-0.75 -0.75,-0.75h-3c-0.41421,0 -0.75,0.33579 -0.75,0.75zM6,19.5v-18c0,-1.10457 0.89543,-2 2,-2h5.5c1.10457,0 2,0.89543 2,2v18c0,1.10457 -0.89543,2 -2,2h-5.5c-1.10457,0 -2,-0.89543 -2,-2z'
          fill='currentColor'
        />
      </g>
    </svg>
  ),
  plus: (
    <svg width='14' height='14' viewBox='0 0 14 14'>
      <path
        d='M8 6h5a1 1 0 0 1 0 2H8v5a1 1 0 0 1-2 0V8H1a1 1 0 0 1 0-2h5V1a1 1 0 0 1 2 0v5z'
        fill='currentColor'
        fillRule='evenodd'
      />
    </svg>
  ),
  minus: (
    <svg width='14' height='2' viewBox='0 0 14 2'>
      <path
        d='M1 0h12a1 1 0 0 1 0 2H1a1 1 0 0 1 0-2z'
        fill='currentColor'
        fillRule='evenodd'
      />
    </svg>
  ),
  'location-arrow': (
    <svg viewBox='0 0 15 15'>
      <path
        d='M13.133.5c.755 0 1.367.612 1.367 1.367 0 .133-.026.251-.084.425L10.66 13.555c-.184.56-.692.945-1.282.945-.596 0-1.043-.412-1.306-1.064L6.149 8.91 1.57 6.93C.907 6.682.5 6.23.5 5.622c0-.59.384-1.098.944-1.282L12.709.584c.184-.06.292-.084.424-.084zM7.61 7.45l.27.626 1.44 3.332 2.865-8.593L3.59 5.681l4.02 1.77z'
        fill='currentColor'
      />
    </svg>
  ),
  'field-location': (
    <svg width='22' height='22'>
      <path
        d='M2.282 11.75H1a.75.75 0 110-1.5h1.282a8.752 8.752 0 017.968-7.968V1a.75.75 0 111.5 0v1.282a8.752 8.752 0 017.968 7.968H21a.75.75 0 110 1.5h-1.282a8.752 8.752 0 01-7.968 7.968V21a.75.75 0 11-1.5 0v-1.282a8.752 8.752 0 01-7.968-7.968zm15.93 0H17a.75.75 0 110-1.5h1.212a7.253 7.253 0 00-6.462-6.462V5a.75.75 0 11-1.5 0V3.788a7.253 7.253 0 00-6.462 6.462H5a.75.75 0 110 1.5H3.788a7.253 7.253 0 006.462 6.462V17a.75.75 0 111.5 0v1.212a7.253 7.253 0 006.462-6.462zM11 13.5a2.5 2.5 0 110-5 2.5 2.5 0 010 5z'
        fill='currentColor'
      />
    </svg>
  ),
  marker: (
    <svg width='16px' height='19px' viewBox='0 0 16 19'>
      <path
        d='M7.74 0A7.74 7.74 0 0 0 0 7.74c0 5.091 5.033 7.528 5.663 8.095.644.58 1.176 1.878 1.39 2.614a.73.73 0 0 0 .688.546.731.731 0 0 0 .687-.546c.214-.736.746-2.034 1.39-2.614.63-.567 5.663-3.004 5.663-8.094A7.74 7.74 0 0 0 7.741 0zm0 9.852a2.111 2.111 0 1 1 0-4.222 2.111 2.111 0 0 1 0 4.222z'
        fill='#C8C8C8'
        fillRule='nonzero'
      />
    </svg>
  ),
  'page-prev': (
    <svg width='11px' height='10px' viewBox='0 0 11 10'>
      <path
        d='M3.483 4.002h6.519a.998.998 0 0 1 0 1.996h-6.51l2.24 2.24c.137.138.23.3.275.468a.976.976 0 0 1 0 .515c-.046.168-.138.33-.276.467-.138.138-.299.23-.467.276a.976.976 0 0 1-.515 0 1.053 1.053 0 0 1-.468-.276L.397 5.804a1.053 1.053 0 0 1-.363-.547.976.976 0 0 1 0-.514c.046-.169.138-.33.275-.468L4.273.312c.138-.138.299-.23.468-.276a.976.976 0 0 1 .514 0c.169.046.33.138.468.276s.23.299.276.467a.976.976 0 0 1 0 .515c-.046.169-.138.33-.276.468l-2.24 2.24z'
        fill='currentColor'
        fillRule='evenodd'
      />
    </svg>
  ),
  'page-next': (
    <svg width='11px' height='10px' viewBox='0 0 11 10'>
      <path
        d='M7.517 4.002H.998a.998.998 0 1 0 0 1.996h6.51l-2.24 2.24c-.137.138-.23.3-.275.468a.976.976 0 0 0 0 .515c.046.168.138.33.276.467.138.138.299.23.467.276a.976.976 0 0 0 .515 0c.169-.046.33-.138.468-.276l3.884-3.884a1.053 1.053 0 0 0 .363-.547.976.976 0 0 0 0-.514 1.053 1.053 0 0 0-.275-.468L6.727.312A1.053 1.053 0 0 0 6.26.036a.976.976 0 0 0-.514 0c-.169.046-.33.138-.468.276s-.23.299-.276.467a.976.976 0 0 0 0 .515c.046.169.138.33.276.468l2.24 2.24z'
        fill='currentColor'
        fillRule='evenodd'
      />
    </svg>
  ),
  user: (
    <svg width={20} height={20}>
      <path
        d='M10.435 20C4.48 20 0 15.52 0 10.435 0 4.48 4.48 0 10.435 0 15.52 0 20 4.48 20 10.435 20 15.52 15.52 20 10.435 20zm-3.998-9.102c.222.545.577.794.74.743.163-.05.228-.158.228-.158s.146.569.24.849c.302.89 1.366 1.898 1.968 2.071.338.098.524.073.842 0 .446-.1 1.452-.906 1.92-2.028.095-.259.182-.466.273-.928 0-.043.032.073.261.101.23.028.326-.291.56-.766.422-1 .36-1.678.077-1.73-.137-.026-.234.043-.327.129-.047 0-.094 0-.094-.043 0-.303.061-.753.16-1.607.222-1.93-.264-2.552-.264-2.552s-.284.18-1.462-.3c-.845-.345-2.083-.466-3.113-.14-.732.231-.57.65-.57.65s-.732-.285-1.161.59c-.295.602-.09 1.287.124 2.265.106.48.083.96.136 1.25 0 0-.096.087-.173.013-.077-.074-.293-.284-.42-.284-.142 0-.327.222-.267.674.06.453.1.657.322 1.201m6.802 3.573l-3.226 1.606-3.232-1.606-2.446 1.272s1.49 2.605 5.662 2.605 5.668-2.605 5.668-2.605z'
        fill='currentColor'
      />
    </svg>
  ),
  attach: (
    <svg width='12px' height='12px' viewBox='0 0 12 12'>
      <path
        d='M5.221 11.45a.774.774 0 0 1 0-1.098L9.5 6.11c1.384-1.374 1.072-2.914.147-3.832-1.077-1.068-2.592-.943-3.861.316l-3.71 3.68c-1.007 1-.22 1.87-.212 1.879.195.193.897.757 1.764-.104l3.568-3.54a.786.786 0 0 1 1.108 0 .774.774 0 0 1 0 1.098l-3.569 3.54c-1.496 1.487-3.199.877-3.978.104C.331 8.828.05 8.184.006 7.53-.034 6.93.1 6.035.968 5.174l3.71-3.68c1.882-1.87 4.38-1.998 6.075-.316 1.473 1.461 1.896 4.001-.147 6.028l-4.278 4.244a.786.786 0 0 1-1.107 0z'
        fill='#07C'
        fillRule='evenodd'
      />
    </svg>
  ),
  check: (
    <svg viewBox='0 0 14 10'>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M5.09 7.08L1.92 3.91L0.5 5.32L5.09 9.91L13.09 1.91L11.68 0.5L5.09 7.08Z'
        fill='currentColor'
      />
    </svg>
  ),
  'add-note': (
    <svg width='15px' height='18px' viewBox='0 0 15 18'>
      <path
        d='M8.5 8.5H10a1 1 0 0 0 0-2H8.5V5a1 1 0 1 0-2 0v1.5H5a1 1 0 1 0 0 2h1.5V10a1 1 0 0 0 2 0V8.5zM7.5 0C11.642 0 15 3.284 15 7.335c0 4.824-4.876 7.133-5.487 7.67-.624.55-1.14 1.78-1.348 2.478A.706.706 0 0 1 7.5 18a.705.705 0 0 1-.665-.517c-.208-.698-.724-1.928-1.348-2.477C4.877 14.468 0 12.159 0 7.336 0 3.283 3.358 0 7.5 0z'
        fill='#FFF'
        fillRule='nonzero'
      />
    </svg>
  ),
  info: (
    <svg width='16' height='16'>
      <path
        d='M8 16A8 8 0 1 1 8 0a8 8 0 0 1 0 16zM7 6.5v6h2v-6H7zm0-3v2h2v-2H7z'
        fill='#EBEBEB'
        fillRule='evenodd'
      />
    </svg>
  ),
  calendar: (
    <svg
      width='18'
      height='20'
      viewBox='0 0 18 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M6 9H4V11H6V9ZM10 9H8V11H10V9ZM14 9H12V11H14V9ZM16 2H15V0H13V2H5V0H3V2H2C0.89 2 0.01 2.9 0.01 4L0 18C0 19.1 0.89 20 2 20H16C17.1 20 18 19.1 18 18V4C18 2.9 17.1 2 16 2ZM16 18H2V7H16V18Z'
        fill='white'
      />
    </svg>
  ),
  'calendar-dots': (
    <svg width='15px' height='12px' viewBox='0 0 15 12'>
      <path
        d='M4 11.5v-3h3.001v3H4zm0-4v-3h3v3H4zm-4 4v-3h3v3H0zm0-4V4.499h3V7.5H0zm8 0v-3h3v3H8zm0 4v-3h3v3H8zm0-8v-3h3v3H8zm4 4V4.499h3.001V7.5H12zm0-4v-3h3.001v3H12zm-8 0v-3h3.001v3H4z'
        fill='currentColor'
        fillRule='evenodd'
      />
    </svg>
  ),
  spinner: (
    <svg width='16' height='16' viewBox='0 0 16 16'>
      <path
        d='M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14z'
        fill='currentColor'
        fillRule='evenodd'
      />
    </svg>
  ),
  warning: (
    <svg width={42} height={38} viewBox=' 0 0 42 38'>
      <defs>
        <path
          id='warning_sign'
          d='M352.22 302.95c-.62.6-1.36.92-2.22.92-.86 0-1.6-.31-2.22-.92a3.02 3.02 0 0 1-.93-2.24c0-.86.3-1.62.93-2.25a3.01 3.01 0 0 1 2.22-.94c.86 0 1.6.31 2.22.94.62.63.93 1.39.93 2.25 0 .88-.3 1.62-.93 2.24zm-4.48-21.09c.59-.65 1.34-.97 2.26-.97.92 0 1.67.31 2.27.96.58.63.88 1.44.88 2.4 0 .83-1.23 6.93-1.64 11.36h-2.96c-.36-4.43-1.7-10.53-1.7-11.36 0-.95.3-1.75.89-2.39zm22.4 20.72l-16.48-28.93c-2.01-3.53-5.3-3.53-7.32 0l-16.48 28.93c-2.02 3.53-.36 6.42 3.66 6.42h32.96c4.03 0 5.67-2.89 3.66-6.42z'
        />
      </defs>
      <use
        fill='#a6b2bc'
        xlinkHref='#warning_sign'
        transform='translate(-329 -271)'
      />
    </svg>
  ),
  'nav-files_by_machinery': (
    <svg width='20' height='20' viewBox='0 0 20 20' fill='currentColor'>
      <path d='M0.898244 17.1692C1.07612 17.3471 1.36451 17.3471 1.54239 17.1692L5.14556 13.5661C5.32343 13.3882 5.32343 13.0998 5.14556 12.9219C4.96768 12.744 4.67929 12.744 4.50141 12.9219L0.898244 16.5251C0.720369 16.703 0.720369 16.9914 0.898244 17.1692Z' />
      <path d='M3.47483 19.1016C3.29695 19.2794 3.00856 19.2794 2.83069 19.1016C2.65281 18.9237 2.65281 18.6353 2.83069 18.4574L6.43386 14.8543C6.61173 14.6764 6.90013 14.6764 7.078 14.8543C7.25588 15.0321 7.25588 15.3205 7.078 15.4984L3.47483 19.1016Z' />
      <path d='M5.93074 19.8665C6.10861 20.0444 6.397 20.0444 6.57488 19.8665L9.01054 17.4309C9.18842 17.253 9.18842 16.9646 9.01054 16.7867C8.83267 16.6088 8.54427 16.6088 8.3664 16.7867L5.93074 19.2224C5.75286 19.4002 5.75286 19.6886 5.93074 19.8665Z' />
      <path d='M10.4398 19.8665C10.2619 20.0444 9.9735 20.0444 9.79562 19.8665C9.61775 19.6886 9.61775 19.4002 9.79562 19.2224L10.2989 18.7191C10.4767 18.5413 10.7651 18.5413 10.943 18.7191C11.1209 18.897 11.1209 19.1854 10.943 19.3633L10.4398 19.8665Z' />
      <path d='M13.1973 18.3971C12.8416 18.7528 12.2648 18.7528 11.9091 18.3971L1.60279 8.0908C1.24704 7.73505 1.24704 7.15827 1.60279 6.80252C1.95854 6.44677 2.53532 6.44677 2.89107 6.80252L7.72217 11.6336L9.01046 10.3453L8.3663 9.70117L9.01043 9.05704L9.65458 9.70119L10.2987 9.05707L9.01042 7.76878C8.83255 7.5909 8.83255 7.30251 9.01042 7.12463L12.2311 3.90392C12.409 3.72605 12.6974 3.72605 12.8753 3.90392L13.1973 4.22599L16.9373 1.03607C17.2988 0.727718 17.8366 0.749027 18.1726 1.08501L18.9149 1.82735C19.2509 2.16334 19.2722 2.70114 18.9638 3.06265L15.7739 6.80256L16.096 7.12463C16.2739 7.30251 16.2739 7.5909 16.096 7.76878L12.8753 10.9895C12.6974 11.1674 12.409 11.1674 12.2311 10.9895L10.9429 9.70121L10.2987 10.3453L10.9429 10.9895L10.2987 11.6336L9.6546 10.9895L8.36631 12.2778L13.1973 17.1088C13.5531 17.4645 13.5531 18.0413 13.1973 18.3971Z' />
      <path d='M11.9091 13.8881C12.2648 14.2439 12.8416 14.2439 13.1973 13.8881L16.096 10.9895C16.4517 10.6337 16.4517 10.0569 16.096 9.70119L15.7739 9.37912L13.0685 12.0845C12.7839 12.3691 12.3225 12.3691 12.0379 12.0845L11.587 11.6336L11.2649 11.9557C10.9092 12.3114 10.9092 12.8882 11.2649 13.244L11.9091 13.8881Z' />
      <path d='M10.2988 3.90391L10.6208 4.22592L7.91538 6.93137C7.63078 7.21597 7.63078 7.67739 7.91538 7.96199L8.36634 8.41295L8.04432 8.73497C7.68857 9.09072 7.11178 9.09072 6.75603 8.73497L6.11189 8.09083C5.75614 7.73508 5.75614 7.1583 6.11189 6.80255L9.01053 3.90391C9.36628 3.54816 9.94307 3.54816 10.2988 3.90391Z' />
      <path d='M18.5116 6.64153C18.2448 6.90834 17.8122 6.90834 17.5454 6.64153C17.2786 6.37472 17.2786 5.94213 17.5454 5.67532L18.8337 4.38703C19.1005 4.12022 19.5331 4.12022 19.7999 4.38703C20.0667 4.65384 20.0667 5.08643 19.7999 5.35325L18.5116 6.64153Z' />
      <path d='M14.3247 2.45461C14.0579 2.72142 13.6253 2.72142 13.3585 2.45461C13.0917 2.1878 13.0917 1.75521 13.3585 1.48839L14.6468 0.20011C14.9136 -0.0667036 15.3462 -0.066703 15.613 0.20011C15.8798 0.466922 15.8798 0.89951 15.613 1.16632L14.3247 2.45461Z' />
      <path d='M0.777549 14.0693C0.599674 14.2472 0.311281 14.2472 0.133406 14.0693C-0.0444686 13.8914 -0.0444687 13.603 0.133406 13.4252L2.56907 10.9895C2.74694 10.8116 3.03534 10.8116 3.21321 10.9895C3.39109 11.1674 3.39109 11.4558 3.21321 11.6336L0.777549 14.0693Z' />
      <path d='M0.133406 10.2044C0.311281 10.3823 0.599673 10.3823 0.777549 10.2044L1.28078 9.70118C1.45866 9.5233 1.45866 9.23491 1.28078 9.05703C1.10291 8.87916 0.814518 8.87916 0.636642 9.05703L0.133406 9.56027C-0.0444689 9.73814 -0.0444686 10.0265 0.133406 10.2044Z' />
    </svg>
  ),
  'nav-fields': (
    <svg width={16} height={16} viewBox='0 0 16 16'>
      <defs>
        <path
          id='navFields'
          d='M19 76c0-1.1.9-2 2-2h1.99v4.47c.57.14 1.1.3 1.6.5V74h1.59v7.1c.5-.46 1.04-.89 1.6-1.26V74h1.59v4.94c.52-.28 1.05-.55 1.6-.8V74h1.99a2 2 0 0 1 2 2v1.19c.08 0 0 1.72 0 1.72s-4.96.61-7.95 3.45C24 85.19 23.2 90 23.2 90H21a2 2 0 0 1-2-2v-1.02c.8.16 1.76.47 2.41 1.08.14.13.27.26.38.4.15-.61.35-1.16.59-1.7A6.3 6.3 0 0 0 19 85.32v-1.57c.73.1 1.61.28 2.38.6.67.27 1.26.65 1.71 1l.44-.8c.1-.21.23-.42.35-.63a9.86 9.86 0 0 0-4.88-1.8v-1.57c.9.1 2.02.3 3 .6 1.04.34 2.05.96 2.8 1.47.33-.42.69-.82 1.07-1.2a11.94 11.94 0 0 0-3.29-1.7 17.02 17.02 0 0 0-3.56-.8H19zm15.96 12a2 2 0 0 1-2 2h-1.62c.24-.66.62-1.4 1.21-1.94.66-.6 1.6-.92 2.41-1.08zm-6.9 2c.33-1.3 1-3.09 2.31-4.27 1.34-1.19 3.24-1.72 4.59-1.95v1.54a6.3 6.3 0 0 0-2.32.73 5.8 5.8 0 0 0-2.41 2.42c-.25.46-.46.92-.62 1.53zm-3.21 0c.38-1.8 1.26-4.7 3.24-6.54 2-1.87 5.03-2.6 6.87-2.88v1.54c-1.77.2-2.83.6-3.87 1.16a8.72 8.72 0 0 0-3.63 3.63 9.7 9.7 0 0 0-1.1 3.09z'
        />
      </defs>
      <use
        fill='currentColor'
        xlinkHref='#navFields'
        transform='translate(-19 -74)'
      />
    </svg>
  ),
  'nav-crop_rotation': (
    <svg width='19' height='19' viewBox='0 0 19 19' fill='none'>
      <path
        fill='currentColor'
        d='M8.99594 1.75C5.83467 1.75 3.12633 3.69687 2.0079 6.45986C1.85248 6.84381 1.41524 7.02907 1.03129 6.87365C0.647337 6.71823 0.462076 6.28098 0.617495 5.89703C1.95733 2.58709 5.2028 0.25 8.99594 0.25C12.2315 0.25 15.0685 1.95047 16.6643 4.50471L18.0414 3.77745L18.0458 8.85188L13.8574 5.98704L15.3338 5.20735C13.9921 3.12654 11.6541 1.75 8.99594 1.75ZM7.99561 8.23999C7.99561 8.79228 7.54789 9.23999 6.99561 9.23999C6.44332 9.23999 5.99561 8.79228 5.99561 8.23999C5.99561 7.68771 6.44332 7.23999 6.99561 7.23999C7.54789 7.23999 7.99561 7.68771 7.99561 8.23999ZM9.99561 11.24C9.99561 11.7923 9.54789 12.24 8.99561 12.24C8.44332 12.24 7.99561 11.7923 7.99561 11.24C7.99561 10.6877 8.44332 10.24 8.99561 10.24C9.54789 10.24 9.99561 10.6877 9.99561 11.24ZM10.9956 9.23999C11.5479 9.23999 11.9956 8.79228 11.9956 8.23999C11.9956 7.68771 11.5479 7.23999 10.9956 7.23999C10.4433 7.23999 9.99561 7.68771 9.99561 8.23999C9.99561 8.79228 10.4433 9.23999 10.9956 9.23999ZM3.42573 16.3699C0.509906 13.9606 1.0372 11.24 1.0372 11.24C1.0372 11.24 5.36247 12.1373 6.95124 13.655C8.54001 15.1726 7.82812 18.1697 7.82812 18.1697C7.82812 18.1697 5.44098 18.035 3.42573 16.3699ZM16.9628 11.24C16.9628 11.24 17.4901 13.9606 14.5743 16.3699C12.559 18.035 10.1719 18.1697 10.1719 18.1697C10.1719 18.1697 9.46 15.1726 11.0488 13.655C12.6375 12.1373 16.9628 11.24 16.9628 11.24Z'
      />
    </svg>
  ),
  'nav-scouting': (
    <svg width='16' height='20' viewBox='0 0 16 20'>
      <defs>
        <path
          id='navScouting'
          d='M35 119.82c0 5.15-5.2 7.61-5.85 8.19-.67.58-1.22 2.2-1.44 2.94-.1.36-.41.54-.71.55-.3 0-.6-.19-.71-.55-.22-.74-.77-2.36-1.44-2.94-.65-.58-5.85-3.04-5.85-8.19a7.91 7.91 0 0 1 8-7.82c4.42 0 8 3.5 8 7.82zm-8 2.68a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5z'
        />
      </defs>
      <use
        fill='currentColor'
        xlinkHref='#navScouting'
        transform='translate(-19 -112)'
      />
    </svg>
  ),
  'nav-stations': (
    <svg width='16' height='20' viewBox='0 0 16 20'>
      <g fill='#222' fillRule='evenodd'>
        <path d='M1 0h14a1 1 0 011 1v5H0V1a1 1 0 011-1zM0 7h16v1H0zM0 9h16v1H0zM7 11h2v7.875l-.253.284a1 1 0 01-1.494 0L7 18.875V11z' />
      </g>
    </svg>
  ),
  'nav-weather': (
    <svg width='18' height='19' viewBox='0 0 18 19'>
      <defs>
        <path
          id='a'
          d='M27 161.5c.35 0 .68.04 1 .09v4.91c0 .*********.5a.5.5 0 0 0 .5-.5V165h2v1.5a2.5 2.5 0 0 1-5 0v-4.91c.32-.05.65-.09 1-.09zM26 150v2.06a9 9 0 0 0-8 8.94s1.12-1 2.5-1 2.5 1 2.5 1 1.79-1 4-1 4 1 4 1 1.12-1 2.5-1 2.5 1 2.5 1a9 9 0 0 0-8-8.94V150z'
        />
      </defs>
      <use fill='currentColor' xlinkHref='#a' transform='translate(-18 -150)' />
    </svg>
  ),
  'nav-sowing': (
    <svg width='20' height='20'>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M10 12C10 12 9 14.0147 9 16.5C9 17.4635 9.15028 18.3562 9.33433 19.0883C6.14941 18.9493 3.66667 17.7043 3.66667 12.6057C3.66667 7.15683 6.5022 1.00005 10 1.00005C13.4978 1.00005 16.3333 7.15683 16.3333 12.6057C16.3333 17.7043 13.8506 18.9493 10.6657 19.0883C10.8497 18.3562 11 17.4635 11 16.5C11 14.0147 10 12 10 12Z'
        fill='#222222'
      />
    </svg>
  ),
  'nav-fertilizer': (
    <svg width={17} height={19}>
      <path
        d='M5.853 3.048l-.015.034a1.08 1.08 0 0 0 0 .837l.014.032c.095.214.147.464.148.72.004.728-.385 1.323-.87 1.329h-.002c-.528.002-.98-.572-1.065-1.355a10.382 10.382 0 0 1 0-2.29C4.15 1.572 4.6.998 5.128 1a.692.692 0 0 1 .481.221c.403.403.512 1.221.244 1.827zM13 2a1 1 0 1 1 0 2 1 1 0 0 1 0-2zM0 11a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm6 3a1 1 0 1 1 2 0 1 1 0 0 1-2 0zM5 6.5a1 1 0 1 1 0 2 1 1 0 0 1 0-2zM14 8a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm-2 5a1 1 0 1 1 0-2 1 1 0 0 1 0 2zm-4 4a1 1 0 1 1 0 2 1 1 0 0 1 0-2zm8.19-12.428l.238.237c.367.366.573.863.572 1.382a.81.81 0 0 1-.81.809 1.941 1.941 0 0 1-1.38-.572l-.238-.237A1.941 1.941 0 0 1 14 4.809v-.523c0-.158.128-.286.286-.286h.523a1.941 1.941 0 0 1 1.382.572zM10.715 4c.158 0 .286.128.286.286v.523a1.941 1.941 0 0 1-.572 1.382l-.237.237A1.941 1.941 0 0 1 8.809 7 .81.81 0 0 1 8 6.19a1.941 1.941 0 0 1 .572-1.38l.237-.238A1.941 1.941 0 0 1 10.191 4h.523zm-.286 6.19l-.237.238A1.941 1.941 0 0 1 8.809 11 .81.81 0 0 1 8 10.19a1.941 1.941 0 0 1 .572-1.38l.237-.238A1.941 1.941 0 0 1 10.191 8h.523c.158 0 .286.128.286.286v.523a1.941 1.941 0 0 1-.572 1.382zM2 8a.649.649 0 0 1-.5-.25c-.321-.386-.501-.91-.5-1.458v-.354c-.001-.548.179-1.073.5-1.459l.323-.39a.22.22 0 0 1 .354 0l.323.39c.321.386.501.911.5 1.459v.354c.001.547-.179 1.072-.5 1.458A.649.649 0 0 1 2 8zm.81 8.428l-.238-.237A1.941 1.941 0 0 1 2 14.809v-.523c0-.158.128-.286.286-.286h.523a1.941 1.941 0 0 1 1.382.572l.237.237c.367.366.573.863.572 1.382a.81.81 0 0 1-.81.809 1.941 1.941 0 0 1-1.38-.572zM8.75.277a1.473 1.473 0 0 1 1.904.157 1.198 1.198 0 0 1-.618 2.018L10 2.459a.686.686 0 0 0-.543.539l-.008.037a1.18 1.18 0 0 1-.824.91 1.18 1.18 0 0 1-1.19-.292l-.002-.001a1.47 1.47 0 0 1-.157-1.902A6.302 6.302 0 0 1 8.75.277zM16.129 15c-.528.002-.98-.572-1.065-1.355a10.383 10.383 0 0 1 0-2.29c.086-.783.537-1.357 1.065-1.355a.692.692 0 0 1 .48.22c.404.403.513 1.22.245 1.826l-.015.036a1.08 1.08 0 0 0 0 .837l.014.032c.095.214.146.464.148.72.004.728-.385 1.323-.87 1.329h-.002zm-2.405.75a1.47 1.47 0 0 1-.157 1.902 1.184 1.184 0 0 1-1.194.293 1.182 1.182 0 0 1-.826-.91l-.007-.036a.686.686 0 0 0-.54-.54l-.037-.008a1.199 1.199 0 0 1-.618-2.015l.002-.002a1.472 1.472 0 0 1 1.903-.157c.57.406 1.068.904 1.474 1.473zM3.055 11.37c.126-.423.477-.74.91-.823l.036-.007a.686.686 0 0 0 .54-.54l.008-.037a1.2 1.2 0 0 1 2.015-.619h.002c.512.51.579 1.317.157 1.904a6.303 6.303 0 0 1-1.473 1.475 1.47 1.47 0 0 1-1.902-.157 1.186 1.186 0 0 1-.293-1.196z'
        fill='currentColor'
        fillRule='nonzero'
      />
    </svg>
  ),
  'nav-vra': (
    <svg width='16' height='16' viewBox='0 0 16 16' fill='none'>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M3.2 0H7.18267V7.10623C7.68514 6.63811 8.22095 6.21248 8.77882 5.84515V0H12.7615C13.8816 0 14.4417 0 14.8695 0.217987C15.2458 0.409734 15.5518 0.715695 15.7435 1.09202C15.9607 1.51824 15.9615 2.07569 15.9615 3.1874C16.0385 3.18453 15.9615 4.90696 15.9615 4.90696C15.9615 4.90696 11.0005 5.52369 8.00676 8.35754C5.30164 10.9182 4.37584 15.0911 4.22247 15.872C4.21592 15.9136 4.2095 15.9552 4.20322 15.997H4.19905L4.19854 16H2.56388L2.56424 15.995C1.85042 15.9874 1.42996 15.9513 1.09202 15.7791C0.715695 15.5873 0.409734 15.2814 0.217987 14.905C0 14.4772 0 13.9172 0 12.797V6.54038C0.892577 6.64749 2.01904 6.83247 2.99726 7.14575C4.03175 7.47705 5.05026 8.09807 5.78983 8.61805C6.12374 8.1958 6.4852 7.79005 6.86874 7.40849C6.41736 7.07735 5.94111 6.77771 5.43876 6.50845C3.88058 5.67326 2.08515 5.12535 0 4.90333V3.2C0 2.0799 0 1.51984 0.217987 1.09202C0.409734 0.715695 0.715695 0.409734 1.09202 0.217987C1.51984 0 2.0799 0 3.2 0ZM5.84749 15.997H12.7615C13.8816 15.997 14.4417 15.997 14.8695 15.7791C15.2458 15.5873 15.5518 15.2814 15.7435 14.905C15.9615 14.4772 15.9615 13.9172 15.9615 12.797V6.57515C14.119 6.85777 11.0868 7.58989 9.09203 9.45558C7.11453 11.3051 6.22627 14.2034 5.84749 15.997ZM13 12C13 12.5523 12.5523 13 12 13C11.4477 13 11 12.5523 11 12C11 11.4477 11.4477 11 12 11C12.5523 11 13 11.4477 13 12ZM12 4C12.5523 4 13 3.55228 13 3C13 2.44772 12.5523 2 12 2C11.4477 2 11 2.44772 11 3C11 3.55228 11.4477 4 12 4ZM5 3C5 3.55228 4.55228 4 4 4C3.44772 4 3 3.55228 3 3C3 2.44772 3.44772 2 4 2C4.55228 2 5 2.44772 5 3ZM3 12C3.55228 12 4 11.5523 4 11C4 10.4477 3.55228 10 3 10C2.44772 10 2 10.4477 2 11C2 11.5523 2.44772 12 3 12Z'
        fill='#222222'
      />
    </svg>
  ),
  'nav-files': (
    <svg width={16} height={18}>
      <path
        d='M8 0C3.58 0 0 1.573 0 3.518c0 1.944 3.58 3.518 8 3.518s8-1.574 8-3.518C16 1.573 12.42 0 8 0zM0 4.477v2.696c0 1.944 3.58 3.517 8 3.517s8-1.573 8-3.517V4.477c0 1.945-3.58 3.518-8 3.518S0 6.422 0 4.477zm0 3.655v2.695c0 1.945 3.58 3.518 8 3.518s8-1.573 8-3.518V8.132c0 1.944-3.58 3.518-8 3.518s-8-1.574-8-3.518zm0 3.655v2.695C0 16.427 3.58 18 8 18s8-1.573 8-3.518v-2.695c0 1.944-3.58 3.518-8 3.518s-8-1.574-8-3.518z'
        fill='currentColor'
        fillRule='nonzero'
      />
    </svg>
  ),
  'nav-modems': (
    <svg width={20} height={20}>
      <path
        d='M16,2 L16,12 L18,12 L18,16 C18,17.1045695 17.1045695,18 16,18 L4,18 C2.8954305,18 2,17.1045695 2,16 L2,4 C2,2.8954305 2.8954305,2 4,2 L16,2 Z M15.5,14 C15.2545401,14 15.0503916,14.1768752 15.0080557,14.4101244 L15,14.5 L15,15.5 C15,15.7761424 15.2238576,16 15.5,16 C15.7454599,16 15.9496084,15.8231248 15.9919443,15.5898756 L16,15.5 L16,14.5 C16,14.2238576 15.7761424,14 15.5,14 Z M13.5,14 C13.2545401,14 13.0503916,14.1768752 13.0080557,14.4101244 L13,14.5 L13,15.5 C13,15.7761424 13.2238576,16 13.5,16 C13.7454599,16 13.9496084,15.8231248 13.9919443,15.5898756 L14,15.5 L14,14.5 C14,14.2238576 13.7761424,14 13.5,14 Z M11.5,14 C11.2545401,14 11.0503916,14.1768752 11.0080557,14.4101244 L11,14.5 L11,15.5 C11,15.7761424 11.2238576,16 11.5,16 C11.7454599,16 11.9496084,15.8231248 11.9919443,15.5898756 L12,15.5 L12,14.5 C12,14.2238576 11.7761424,14 11.5,14 Z M9.5,14 C9.25454011,14 9.05039163,14.1768752 9.00805567,14.4101244 L9,14.5 L9,15.5 C9,15.7761424 9.22385763,16 9.5,16 C9.74545989,16 9.94960837,15.8231248 9.99194433,15.5898756 L10,15.5 L10,14.5 C10,14.2238576 9.77614237,14 9.5,14 Z M18,0.0227272727 C18.5522847,0.0227272727 19,0.470442523 19,1.02272727 L19,11.0227273 L17,11.0227273 L17,1.02272727 C17,0.470442523 17.4477153,0.0227272727 18,0.0227272727 Z M17,9.02272727 L19,9.02272727 L19,10.0227273 L17,10.0227273 L17,9.02272727 Z M17,7.02272727 L19,7.02272727 L19,8.02272727 L17,8.02272727 L17,7.02272727 Z'
        fill='currentColor'
      />
    </svg>
  ),
  'nav-converter': (
    <svg width={20} height={20} viewBox='0 0 24 24'>
      <path
        d='M19 8L15 12H18C18 15.31 15.31 18 12 18C10.99 18 10.03 17.75 9.2 17.3L7.74 18.76C8.97 19.54 10.43 20 12 20C16.42 20 20 16.42 20 12H23L19 8ZM6 12C6 8.69 8.69 6 12 6C13.01 6 13.97 6.25 14.8 6.7L16.26 5.24C15.03 4.46 13.57 4 12 4C7.58 4 4 7.58 4 12H1L5 16L9 12H6Z'
        fill='currentColor'
      />
    </svg>
  ),
  'nav-yield': (
    <svg width='16' height='22' viewBox='0 0 16 22'>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M6.36377 1.10514C6.36377 0.879201 6.54693 0.696045 6.77286 0.696045C6.9988 0.696045 7.18195 0.879201 7.18195 1.10514V5.68184C7.18195 5.90777 6.9988 6.09093 6.77286 6.09093C6.54693 6.09093 6.36377 5.90777 6.36377 5.68184V1.10514ZM4.31827 2.17896C4.09234 2.17896 3.90918 2.36211 3.90918 2.58805V5.6818C3.90918 5.90773 4.09234 6.09089 4.31827 6.09089C4.54421 6.09089 4.72736 5.90773 4.72736 5.6818V2.58805C4.72736 2.36211 4.54421 2.17896 4.31827 2.17896ZM1.45459 5.04264C1.45459 4.8167 1.63775 4.63354 1.86368 4.63354C2.08962 4.63354 2.27277 4.8167 2.27277 5.04264V5.68184C2.27277 5.90777 2.08962 6.09093 1.86368 6.09093C1.63775 6.09093 1.45459 5.90777 1.45459 5.68184V5.04264ZM0.63623 7.72724C0.63623 7.27537 1.00254 6.90906 1.45441 6.90906H14.5453C14.9972 6.90906 15.3635 7.27537 15.3635 7.72724C15.3635 8.17911 14.9972 8.54542 14.5453 8.54542H8.409V10.1818H9.22727V11H8.409V11.8182H10.0455C10.2714 11.8182 10.4545 12.0013 10.4545 12.2273V16.3182C10.4545 16.5441 10.2714 16.7273 10.0455 16.7273H9.6364L9.28712 21.1283C9.25335 21.5537 8.89826 21.8818 8.4715 21.8818H7.52859C7.10183 21.8818 6.74674 21.5537 6.71297 21.1283L6.36369 16.7273H5.95455C5.72861 16.7273 5.54545 16.5441 5.54545 16.3182V12.2273C5.54545 12.0013 5.72861 11.8182 5.95455 11.8182H7.59082V11H6.77273V10.1818H7.59082V8.54542H1.45441C1.00254 8.54542 0.63623 8.17911 0.63623 7.72724ZM3.5 10.5909C3.5 10.139 3.86631 9.77271 4.31818 9.77271H5.13636C5.58823 9.77271 5.95455 10.139 5.95455 10.5909V11H5.3816C5.0201 11 4.72705 11.293 4.72705 11.6545V15.0909H4.31818C3.86631 15.0909 3.5 14.7246 3.5 14.2727V10.5909ZM11.2725 15.0909H11.6818C12.1336 15.0909 12.5 14.7246 12.5 14.2727V10.5909C12.5 10.139 12.1336 9.77271 11.6818 9.77271H10.8636C10.4117 9.77271 10.0454 10.139 10.0454 10.5909V11H10.618C10.9795 11 11.2725 11.293 11.2725 11.6545V15.0909ZM4.72727 18.5682C4.72727 18.2293 5.00201 17.9545 5.34091 17.9545C5.67981 17.9545 5.95455 18.2293 5.95455 18.5682V20.2045C5.95455 20.5434 5.67981 20.8182 5.34091 20.8182C5.00201 20.8182 4.72727 20.5434 4.72727 20.2045V18.5682ZM10.0455 18.5682C10.0455 18.2293 10.3202 17.9545 10.6591 17.9545C10.998 17.9545 11.2727 18.2293 11.2727 18.5682V20.2045C11.2727 20.5434 10.998 20.8182 10.6591 20.8182C10.3202 20.8182 10.0455 20.5434 10.0455 20.2045V18.5682ZM9.22745 0.696045C9.00152 0.696045 8.81836 0.879201 8.81836 1.10514V5.68184C8.81836 5.90777 9.00152 6.09093 9.22745 6.09093C9.45339 6.09093 9.63654 5.90777 9.63654 5.68184V1.10514C9.63654 0.879201 9.45339 0.696045 9.22745 0.696045ZM11.2729 2.58805C11.2729 2.36211 11.4561 2.17896 11.682 2.17896C11.908 2.17896 12.0911 2.36211 12.0911 2.58805V5.6818C12.0911 5.90773 11.908 6.09089 11.682 6.09089C11.4561 6.09089 11.2729 5.90773 11.2729 5.6818V2.58805ZM14.1361 4.63354C13.9102 4.63354 13.7271 4.8167 13.7271 5.04264V5.68184C13.7271 5.90777 13.9102 6.09093 14.1361 6.09093C14.3621 6.09093 14.5452 5.90777 14.5452 5.68184V5.04264C14.5452 4.8167 14.3621 4.63354 14.1361 4.63354Z'
        fill='#222222'
      />
    </svg>
  ),
  'nav-beta': (
    <svg width={17} height={8}>
      <rect width='17' height='8' rx='3' fill='#BFBFBF' />
      <path
        d='M2 2H3.23517C3.42667 2 3.60285 2.01678 3.76371 2.05035C3.92457 2.08392 4.06436 2.14172 4.18309 2.22378C4.30182 2.3021 4.39374 2.40839 4.45885 2.54266C4.52396 2.67319 4.55652 2.8373 4.55652 3.03496V3.05734C4.55652 3.24009 4.51056 3.40793 4.41864 3.56084C4.33055 3.71002 4.16969 3.82378 3.93606 3.9021C4.2348 3.97296 4.44545 4.08485 4.56801 4.23776C4.6944 4.39068 4.75759 4.59767 4.75759 4.85874V4.88112C4.75759 5.07133 4.72504 5.2373 4.65993 5.37902C4.59865 5.51702 4.50864 5.63263 4.38991 5.72587C4.27118 5.81538 4.12947 5.88438 3.96478 5.93287C3.8001 5.97762 3.61434 6 3.40752 6H2V2ZM3.39603 5.61958C3.69477 5.61958 3.91499 5.55804 4.0567 5.43497C4.19841 5.30816 4.26927 5.12168 4.26927 4.87552V4.85315C4.26927 4.60699 4.19458 4.42424 4.04521 4.3049C3.89967 4.18555 3.6603 4.12587 3.32709 4.12587H2.48832V5.61958H3.39603ZM3.23517 3.74545C3.53774 3.74545 3.75222 3.68951 3.87861 3.57762C4.005 3.46573 4.06819 3.28671 4.06819 3.04056V3.01818C4.06819 2.79068 3.99925 2.62844 3.86137 2.53147C3.7235 2.43077 3.51093 2.38042 3.22368 2.38042H2.48832V3.74545H3.23517Z'
        fill='black'
      />
      <path
        d='M5.64111 2H8.03677V2.3972H6.14092V3.75664H7.66909V4.15385H6.14092V5.6028H8.14018V6H5.64111V2Z'
        fill='black'
      />
      <path
        d='M9.80694 2.3972H8.61198V2H11.5017V2.3972H10.3068V6H9.80694V2.3972Z'
        fill='black'
      />
      <path
        d='M12.9548 2H13.6097L15 6H14.5059L14.1038 4.84755H12.409L12.0126 6H11.5415L12.9548 2ZM12.5469 4.46154H13.9716L13.2593 2.4028L12.5469 4.46154Z'
        fill='black'
      />
    </svg>
  ),
  'nav-new': (
    <svg width='17' height='8' viewBox='0 0 17 8'>
      <rect width='17' height='8' rx='3' fill='#27AE60' />
      <path
        d='M2.45093 2.0675H3.32543L4.98093 4.91101V2.0675H5.64643V6.00001H4.88743L3.11643 2.986V6.00001H2.45093V2.0675Z'
        fill='white'
      />
      <path
        d='M6.57056 2.0675H9.02906V2.645H7.30206V3.723H8.68806V4.27301H7.30206V5.42251H9.12256V6.00001H6.57056V2.0675Z'
        fill='white'
      />
      <path
        d='M9.49595 2.0675H10.244L10.926 4.955L11.6905 2.0675H12.2955L13.0765 4.9715L13.7805 2.0675H14.4625L13.478 6.00001H12.7465L11.971 3.1345L11.1735 6.00001H10.475L9.49595 2.0675Z'
        fill='white'
      />
    </svg>
  ),
  pencil: (
    <svg viewBox='0 0 10 11'>
      <path
        d='M6.05 2.34L0 8.4v2.11h2.1l6.06-6.05zM8.11.5a.52.52 0 0 0-.37.15l-.9.9 2.1 2.1.9-.9c.21-.2.21-.53 0-.73L8.48.65A.52.52 0 0 0 8.11.5z'
        fill='currentColor'
      />
    </svg>
  ),
  'download-circle': (
    <svg viewBox='0 0 16 16'>
      <path
        d='M11.9 8.8a21.49 21.49 0 0 1-3.6 3.8c-.06 0-.13.08-.27.08-.13 0-.2-.07-.34-.14-.07-.06-2.2-1.87-3.59-3.8-.13-.14-.13-.33 0-.53.14-.07.28-.2.49-.2h1.79s.2-4.08.34-4.28c.2-.2.76-.4 1.31-.4.49 0 1.04.2 1.25.4.13.2.4 4.28.4 4.28h1.8c.21 0 .35.06.42.26.13.14.13.33 0 .54zM8 0C3.6 0 0 3.6 0 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8z'
        fill='currentColor'
      />
    </svg>
  ),
  'question-circle': (
    <svg width='14' height='14' viewBox='0 0 14 14'>
      <path
        d='M0 7a7 7 0 1 1 14 0A7 7 0 0 1 0 7zm7.6 1.88l.02-.34c.04-.36.2-.67.48-.94l.45-.43c.35-.34.6-.65.74-.93s.21-.58.21-.89c0-.7-.21-1.22-.65-1.6a2.68 2.68 0 0 0-1.82-.57c-.78 0-1.4.2-1.85.6-.45.4-.67.95-.68 1.65h1.6c.02-.3.1-.52.26-.69a.88.88 0 0 1 .67-.25c.57 0 .86.3.86.93 0 .21-.05.4-.16.6a3.6 3.6 0 0 1-.68.72c-.33.3-.56.6-.69.9-.12.31-.18.72-.18 1.24zM6.26 11.2a.9.9 0 0 0 .65.24c.27 0 .5-.08.65-.24a.8.8 0 0 0 .25-.6.8.8 0 0 0-.25-.61.88.88 0 0 0-.65-.25.9.9 0 0 0-.65.25.8.8 0 0 0-.25.6c0 .25.09.45.25.61z'
        fill='currentColor'
      />
    </svg>
  ),
  'wind-direction': (
    <svg width={8} height={11}>
      <path
        d='M3.808 0L7.25 11l-3.5-2.85L.25 11z'
        fill='#222'
        fillRule='nonzero'
      />
    </svg>
  ),
  settings: (
    <svg viewBox='0 0 11 11'>
      <path
        d='M5.5 7.55A2.06 2.06 0 1 1 7.55 5.5 2.06 2.06 0 0 1 5.5 7.55zM9.71 6a4.37 4.37 0 0 0 0-.54A3.3 3.3 0 0 0 9.7 5l1.2-.91a.28.28 0 0 0 .1-.38L9.83 1.8a.29.29 0 0 0-.35-.12l-1.41.55a4.17 4.17 0 0 0-1-.54L6.91.23A.28.28 0 0 0 6.63 0H4.37a.27.27 0 0 0-.28.23l-.21 1.46a4.29 4.29 0 0 0-1 .54l-1.36-.55a.28.28 0 0 0-.35.12L0 3.71a.26.26 0 0 0 .07.35L1.31 5a3.15 3.15 0 0 0-.01 1l-1.2.94a.28.28 0 0 0-.1.35L1.17 9.2a.29.29 0 0 0 .35.12l1.41-.55a4.17 4.17 0 0 0 1 .54l.21 1.46a.29.29 0 0 0 .28.23h2.21a.27.27 0 0 0 .28-.23l.21-1.46a4.29 4.29 0 0 0 1-.54l1.41.55a.28.28 0 0 0 .35-.12L11 7.29a.26.26 0 0 0-.07-.35L9.71 6z'
        fill='currentColor'
      />
    </svg>
  ),
  'plus-btn': (
    <svg viewBox='0 0 12 12'>
      <path
        d='M5.007 0v5H.013c-.026 0 0 2 0 2h4.994v5h1.998V7H12V5H7.005V0z'
        fill='currentColor'
      />
    </svg>
  ),
  trashcan: (
    <svg viewBox='0 0 10 13'>
      <path
        d='M6 11h1V5H6zm-3 0h1V5H3zm6-8v8.25C9 11.938 8.5 13 7.25 13h-4.5C1.5 13 1 11.937 1 11.25V3zM6.25 0l.625.74H10V2H0V.74h3.125L3.75 0z'
        fill='currentColor'
      />
    </svg>
  ),
  'trashcan-light': (
    <svg viewBox='0 0 14 16'>
      <path
        d='M8 14h2V7H8zm-4 0h2V7H4zm8.5-9l-.5 9c-.06 1-1 2-2 2H4c-1 0-1.94-1-2-2l-.5-9zM9 0v1h4a1 1 0 011 1v1H0V2a1 1 0 011-1h4V0z'
        fill='currentColor'
      />
    </svg>
  ),
  'chevron-left': (
    <svg viewBox='0 0 8 12'>
      <path
        d='M6 0l1.41 1.41L3.83 5 2.8 6l1.03 1 3.58 3.59L6 12 0 6z'
        fill='currentColor'
      />
    </svg>
  ),
  'chevron-right': (
    <svg viewBox='0 0 8 12'>
      <path
        d='M1.91 0L.5 1.41 4.08 5l1.03 1-1.03 1L.5 10.59 1.91 12l6-6z'
        fill='currentColor'
      />
    </svg>
  ),
  'sidebar-question': (
    <svg width='18' height='18' viewBox='0 0 18 18'>
      <g transform='translate(1 1)' fill='none' fillRule='evenodd'>
        <circle stroke='#222' strokeWidth='2' cx='8' cy='8' r='8' />
        <path
          d='M7.045 10.191c0-.6.073-1.078.219-1.434.146-.356.411-.705.797-1.048.39-.348.65-.628.779-.843.128-.219.192-.448.192-.688 0-.725-.334-1.087-1.003-1.087-.317 0-.572.099-.765.296-.189.193-.287.46-.296.804H5.103c.009-.819.272-1.46.791-1.923.523-.463 1.235-.695 2.135-.695.91 0 1.614.221 2.116.663.502.437.752 1.057.752 1.858 0 .365-.081.71-.244 1.036-.163.321-.448.68-.855 1.074l-.521.495c-.326.313-.513.68-.56 1.1l-.025.392H7.045zM6.859 12.165c0-.287.096-.523.29-.707.196-.189.447-.283.752-.283.304 0 .553.094.746.283.197.184.295.42.295.707a.928.928 0 0 1-.289.701c-.189.185-.44.277-.752.277-.313 0-.566-.092-.76-.277a.939.939 0 0 1-.282-.7z'
          fill='#222'
          fillRule='nonzero'
        />
      </g>
    </svg>
  ),
  'refresh-btn': (
    <svg viewBox='0 0 15 14'>
      <path
        d='M3.414.832a6.626 6.626 0 0 1 8.881 2.366l2.172-1.204-1.34 4.672-4.672-1.34 2.162-1.198a4.705 4.705 0 0 0-6.274-1.62 4.705 4.705 0 0 0-1.835 6.4 4.705 4.705 0 0 0 8.366-.254l.092-.19 1.85.544-.114.26A6.626 6.626 0 1 1 3.414.832z'
        fill='currentColor'
      />
    </svg>
  ),
  x: (
    <svg>
      <path d='M15.77466,0.58v0l0.90722,0.93113v0l-7.43335,7.33266v0l7.40408,7.47816v0l-0.84869,0.93113v0l-7.49188,-7.50725v0l-7.46262,7.39086v0l-0.81942,-0.93114v0l7.40409,-7.36176v0l-7.28703,-7.33266v0l0.87796,-0.90203v0l7.31629,7.33266v0z' />
    </svg>
  ),
  search: (
    <svg viewBox='0 0 15 15'>
      <path
        d='M5.57 9.424A3.85 3.85 0 0 1 1.715 5.57 3.85 3.85 0 0 1 5.57 1.715 3.85 3.85 0 0 1 9.424 5.57 3.85 3.85 0 0 1 5.57 9.424zm5.15.01h-.677l-.24-.232a5.55 5.55 0 0 0 1.346-3.627 5.574 5.574 0 1 0-5.574 5.574 5.55 5.55 0 0 0 3.627-1.346l.232.24v.677L13.722 15 15 13.722z'
        fill='currentColor'
      />
    </svg>
  ),
  'external-link': (
    <svg width='15' height='15' viewBox='0 0 15 15'>
      <path d='M10 0v2h1.584L5.696 7.89 7.11 9.304 13 3.414V5h2V0zM4 0l-.2.005A4 4 0 000 4v7l.005.2A4 4 0 004 15h7l.2-.005A4 4 0 0015 11V8h-2v3l-.005.15A2 2 0 0111 13H4l-.15-.005A2 2 0 012 11V4l.005-.15A2 2 0 014 2h3V0z' />
    </svg>
  ),
  'external-link-color': (
    <svg width='15' height='15' viewBox='0 0 15 15'>
      <path
        d='M10 0v2h1.584L5.696 7.89 7.11 9.304 13 3.414V5h2V0zM4 0l-.2.005A4 4 0 000 4v7l.005.2A4 4 0 004 15h7l.2-.005A4 4 0 0015 11V8h-2v3l-.005.15A2 2 0 0111 13H4l-.15-.005A2 2 0 012 11V4l.005-.15A2 2 0 014 2h3V0z'
        fill='currentColor'
      />
    </svg>
  ),
  revert: (
    <svg width='21' height='16' viewBox='0 0 21 16'>
      <path
        d='M6.848 1.093a1 1 0 011.687.727c.001 1.239-.02 2.164 0 2.678.007.178.215.5.51.5.305 0 .927-.005 1 0 4.5 0 9.699 2.895 10.39 8.686.004.032.043.455-.198.623-.226.157-.365-.163-.503-.069-2.46-2.16-5.19-3.24-8.69-3.24h-2c-.294 0-.5.296-.5.5l-.01 2.679a1 1 0 01-1.686.726L.313 8.725a1 1 0 010-1.453l6.535-6.179z'
        fill='currentColor'
      />
    </svg>
  ),
  cut: (
    <svg width='18' height='16' viewBox='0 0 18 16'>
      <path
        d='M2 4V2h2v2zm0 10v-2h2v2zm7.998-8.089l1.696 1.34L17.5 2.5V1H16zM6 13c0-.336-.055-.66-.158-.962l3.27-2.674L16 15h1.5v-1.5L5.842 3.962a3 3 0 10-1.337 1.633L7.446 8l-2.94 2.405A3 3 0 106 13z'
        fill='currentColor'
      />
    </svg>
  ),
  select: (
    <svg width='16' height='20' viewBox='0 0 16 20'>
      <path
        d='M5.366 5.914c.547 2.07.843 2.968 1.005 2.92.284-.08-.12-.954.623-1.173.927-.269 1.104.455 1.364.383.26-.077.17-.814.91-1.029.742-.216 1.131.688 1.437.599.302-.09.284-.42.742-.551.458-.138 2.196.646 3.184 4.022 1.239 4.244-.162 5.028.263 6.464l-5.53 2.107c-.45-1.077-1.838-1.155-3.065-1.843-1.236-.698-2.089-2.053-5.315-1.987-1.212.024-1.146-.943-.646-1.485.814-.88 1.72-.545 2.945-.359 1.05.162 2.103-.126 2.035-.694-.108-.919-.276-1.323-.623-2.514-.278-.946-.81-2.655-1.293-4.286-.643-2.181-.832-3.196.024-3.447a.81.81 0 01.335-.024c.728.095 1.186 1.305 1.605 2.897zm1.292-2.61c0 .742-.257 1.413-.67 1.963a10.557 10.557 0 00-.527-1.484c.036-.153.072-.314.072-.479a2.187 2.187 0 00-2.179-2.179 2.187 2.187 0 00-2.178 2.179c0 .796.437 1.49 1.077 1.867.099.437.225.883.36 1.341A3.287 3.287 0 01.05 3.304 3.303 3.303 0 013.354 0a3.303 3.303 0 013.304 3.304z'
        fill='currentColor'
      />
    </svg>
  ),
  'split-map': (
    <svg width='22' height='26' viewBox='0 0 22 26'>
      <path
        fill='currentColor'
        fillRule='evenodd'
        d='M21,28 L21,32 C21,32.5522847 20.5522847,33 20,33 C19.4477153,33 19,32.5522847 19,32 L19,28 L21,28 Z M19,12 L19,16 C19,16.5522847 19.4477153,17 20,17 C20.5128358,17 20.9355072,16.6139598 20.9932723,16.1166211 L21,16 L21,12 L29,12 C30.1045695,12 31,12.8954305 31,14 L31,26 C31,27.1045695 30.1045695,28 29,28 L21,28 L21,24 C21,23.4871642 20.6139598,23.0644928 20.1166211,23.0067277 L20,23 C19.4871642,23 19.0644928,23.3860402 19.0067277,23.8833789 L19,24 L19,28 L11,28 C9.8954305,28 9,27.1045695 9,26 L9,14 C9,12.8954305 9.8954305,12 11,12 L19,12 Z M17,14 L11,14 L11,26 L17,26 L17,25 L12,25 L12,15 L17,15 L17,14 Z M29,14 L22,14 L22,26 L29,26 L29,14 Z M20,18 C19.4871642,18 19.0644928,18.3860402 19.0067277,18.8833789 L19,19 L19,21 C19,21.5522847 19.4477153,22 20,22 C20.5128358,22 20.9355072,21.6139598 20.9932723,21.1166211 L21,21 L21,19 C21,18.4477153 20.5522847,18 20,18 Z M20,7 C20.5522847,7 21,7.44771525 21,8 L21,12 L19,12 L19,8 C19,7.44771525 19.4477153,7 20,7 Z'
        transform='translate(-9 -7)'
      />
    </svg>
  ),
  'alert-circle': (
    <svg width='14px' height='14px' viewBox='0 0 14 14'>
      <path
        d='M8.003 15C11.842 15 15 11.849 15 8.003 15 4.158 11.842 1 7.997 1 4.15 1 1 4.158 1 8.003 1 11.85 4.158 15 8.003 15zm0-6.138c-.462 0-.726-.257-.753-.72l-.099-2.616c-.026-.49.33-.826.846-.826.515 0 .872.343.845.832l-.105 2.603c-.027.476-.284.727-.734.727zm0 2.55c-.548 0-.925-.323-.925-.825 0-.496.37-.813.925-.813.535 0 .912.317.912.813 0 .508-.377.825-.912.825z'
        transform='translate(-1 -1)'
        fill='currentColor'
        fillRule='evenodd'
      />
    </svg>
  ),
  'alert-circle-bordered': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <g filter='url(#filter0_d_661_59583)'>
        <rect
          x='2'
          y='1'
          width='12'
          height='12'
          rx='6'
          fill='white'
          shapeRendering='crispEdges'
        />
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M8.00236 12C10.7442 12 13 9.74894 13 7.00236C13 4.25578 10.7442 2 7.99764 2C5.25106 2 3 4.25578 3 7.00236C3 9.74894 5.25578 12 8.00236 12ZM8.00481 7.91976C7.50004 7.91976 7.21159 7.63852 7.18275 7.13375L7.07458 4.27816C7.04574 3.74454 7.43514 3.37677 7.9976 3.37677C8.56007 3.37677 8.94947 3.75175 8.92062 4.28537L8.80525 7.12654C8.7764 7.64573 8.49517 7.91976 8.00481 7.91976ZM8.00485 10.7035C7.40633 10.7035 6.9953 10.3501 6.9953 9.8021C6.9953 9.26126 7.39912 8.91513 8.00485 8.91513C8.58895 8.91513 8.99998 9.26126 8.99998 9.8021C8.99998 10.3573 8.58895 10.7035 8.00485 10.7035Z'
          fill='#FF3B30'
        />
      </g>
      <defs>
        <filter
          id='filter0_d_661_59583'
          x='0'
          y='0'
          width='16'
          height='16'
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'
        >
          <feFlood floodOpacity='0' result='BackgroundImageFix' />
          <feColorMatrix
            in='SourceAlpha'
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
            result='hardAlpha'
          />
          <feOffset dy='1' />
          <feGaussianBlur stdDeviation='1' />
          <feComposite in2='hardAlpha' operator='out' />
          <feColorMatrix
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0'
          />
          <feBlend
            mode='normal'
            in2='BackgroundImageFix'
            result='effect1_dropShadow_661_59583'
          />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='effect1_dropShadow_661_59583'
            result='shape'
          />
        </filter>
      </defs>
    </svg>
  ),
  'success-circle-bordered': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <g filter='url(#filter0_d_643_59399)'>
        <rect
          x='2'
          y='1'
          width='12'
          height='12'
          rx='6'
          fill='white'
          shapeRendering='crispEdges'
        />
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M7.11875 8.3L5.1375 6.31875L4.25 7.2L7.11875 10.0687L12.1188 5.06875L11.2375 4.1875L7.11875 8.3Z'
          fill='#27AE60'
        />
      </g>
      <defs>
        <filter
          id='filter0_d_643_59399'
          x='0'
          y='0'
          width='16'
          height='16'
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'
        >
          <feFlood floodOpacity='0' result='BackgroundImageFix' />
          <feColorMatrix
            in='SourceAlpha'
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
            result='hardAlpha'
          />
          <feOffset dy='1' />
          <feGaussianBlur stdDeviation='1' />
          <feComposite in2='hardAlpha' operator='out' />
          <feColorMatrix
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0'
          />
          <feBlend
            mode='normal'
            in2='BackgroundImageFix'
            result='effect1_dropShadow_643_59399'
          />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='effect1_dropShadow_643_59399'
            result='shape'
          />
        </filter>
      </defs>
    </svg>
  ),
  'eye-closed': (
    <svg width='16px' height='12px' viewBox='0 0 16 12'>
      <path
        d='M13.743 6.593c.147-.2.246-.37.306-.495a.885.885 0 00.04-.098 1.113 1.113 0 00-.04-.098 3.11 3.11 0 00-.306-.495 7.293 7.293 0 00-1.305-1.33C11.295 3.164 9.738 2.4 8 2.4c-.56 0-1.1.08-1.615.217L4.963 1.195A8.23 8.23 0 018 .6c2.263 0 4.205.987 5.562 2.072a9.082 9.082 0 011.632 1.67c.197.27.363.538.484.795.112.238.222.544.222.863 0 .32-.11.625-.222.863-.12.257-.287.526-.484.794a9.082 9.082 0 01-1.632 1.67c-.085.07-.173.137-.263.204L12.01 8.243c.148-.104.291-.212.428-.32a7.293 7.293 0 001.305-1.33zM8 3a3 3 0 012.813 4.045L6.955 3.187A2.994 2.994 0 018 3zM5 6c0-.368.066-.72.187-1.045l3.858 3.858A3 3 0 015 6zM2.438 2.672c.085-.068.173-.136.263-.203L3.99 3.757a8.508 8.508 0 00-.428.32 7.293 7.293 0 00-1.305 1.33 3.11 3.11 0 00-.306.495 1.1 1.1 0 00-.04.098 1.1 1.1 0 00.04.098c.06.126.159.294.306.495.293.4.737.874 1.305 1.33C4.705 8.836 6.262 9.6 8 9.6c.56 0 1.1-.08 1.615-.217l1.422 1.422A8.23 8.23 0 018 11.4c-2.263 0-4.205-.987-5.562-2.072a9.082 9.082 0 01-1.633-1.67 4.876 4.876 0 01-.483-.795C.21 6.625.1 6.319.1 6c0-.32.11-.625.222-.863.12-.257.287-.526.483-.794a9.082 9.082 0 011.633-1.67zm11.416 8.474a.5.5 0 01-.708.708l-11-11a.5.5 0 11.708-.708z'
        fill='currentColor'
      />
    </svg>
  ),
  'eye-opened': (
    <svg
      width='16'
      height='12'
      viewBox='0 0 16 12'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M12.4381 7.92219C12.3334 8.00595 12.2251 8.08848 12.1136 8.16927C11.4001 8.6861 10.5515 9.13175 9.61516 9.38282L9.61513 9.3828C9.47043 9.42161 9.32364 9.45577 9.17493 9.48483C8.79489 9.55908 8.40231 9.6 8.0001 9.6C7.81831 9.6 7.63848 9.59164 7.46088 9.57562C6.08415 9.45143 4.84178 8.86698 3.85926 8.14925C3.75746 8.07486 3.65844 7.99904 3.56232 7.92219C2.99391 7.46747 2.55031 6.99257 2.25711 6.59275C2.11013 6.39231 2.01062 6.22391 1.95153 6.09803C1.93116 6.05464 1.91838 6.02222 1.91047 5.99998C1.91838 5.97773 1.93116 5.94532 1.95153 5.90192C2.01062 5.77604 2.11013 5.60764 2.25711 5.4072C2.55031 5.00738 2.99391 4.53248 3.56232 4.07776C3.68528 3.97939 3.81305 3.88277 3.94531 3.78865C4.6465 3.28974 5.47438 2.86139 6.38524 2.61715L6.38525 2.61716L6.40977 2.61063C6.91692 2.47669 7.44959 2.39998 8.00033 2.39998C8.21747 2.39998 8.43181 2.4119 8.64288 2.43456C8.97568 2.4703 9.30034 2.53273 9.61517 2.61716L9.61521 2.61711C10.5068 2.85618 11.3189 3.2717 12.0105 3.75714L12.0104 3.75732C12.1584 3.86128 12.3015 3.96844 12.4381 4.07776C13.0065 4.53249 13.4501 5.00738 13.7433 5.4072C13.8903 5.60764 13.9898 5.77604 14.0489 5.90192C14.0692 5.94531 14.082 5.97772 14.0899 5.99997C14.082 6.02222 14.0692 6.05464 14.0489 6.09803C13.9898 6.22391 13.8903 6.39231 13.7433 6.59275C13.4501 6.99257 13.0065 7.46747 12.4381 7.92219ZM4.96307 10.8046C3.99027 10.4193 3.13649 9.88665 2.43787 9.3278C1.75628 8.7825 1.19988 8.1949 0.805583 7.6572C0.608819 7.38889 0.442702 7.11979 0.322104 6.86286C0.210359 6.6248 0.100098 6.31923 0.100098 5.99998C0.100098 5.68072 0.210359 5.37515 0.322104 5.13709C0.442702 4.88016 0.608819 4.61106 0.805583 4.34275C1.19988 3.80507 1.75628 3.21747 2.43787 2.67219C2.45922 2.65511 2.48072 2.63805 2.50236 2.62103C3.60579 1.7527 5.08362 0.957057 6.79869 0.692599C7.18756 0.632632 7.58864 0.599976 8.00033 0.599976C8.32737 0.599976 8.64771 0.620583 8.96056 0.659118C10.8124 0.887221 12.4017 1.7435 13.5625 2.67219C14.2441 3.21747 14.8005 3.80507 15.1948 4.34275C15.3916 4.61106 15.5577 4.88016 15.6783 5.13709C15.79 5.37515 15.9003 5.68073 15.9003 5.99998C15.9003 6.31923 15.79 6.6248 15.6783 6.86286C15.5577 7.11979 15.3916 7.38889 15.1948 7.6572C14.8005 8.1949 14.2441 8.7825 13.5625 9.3278C12.7797 9.95399 11.802 10.5473 10.6804 10.9373C10.4472 11.0184 10.2079 11.0907 9.96272 11.1526C9.72434 11.2129 9.48052 11.2634 9.23166 11.3027C8.83345 11.3656 8.42235 11.4 8.0001 11.4C7.78796 11.4 7.57863 11.3913 7.37234 11.3747C6.51105 11.3054 5.70257 11.0976 4.96301 10.8047L4.96307 10.8046ZM8 9C9.65685 9 11 7.65685 11 6C11 4.34315 9.65685 3 8 3C6.34315 3 5 4.34315 5 6C5 7.65685 6.34315 9 8 9Z'
        fill='currentColor'
      />
    </svg>
  ),
  fullscreen: (
    <svg width='14px' height='14px' viewBox='0 0 14 14'>
      <path
        d='M3.887 8.71a1 1 0 011.403 1.403l-.083.094L3.414 12H4a1 1 0 01.993.883L5 13a1 1 0 01-.883.993L4 14H0v-4a1 1 0 011.993-.117L2 10v.584l1.793-1.791.094-.083zm6.226 0l.094.083L12 10.585V10l.007-.117a1 1 0 011.986 0L14 10v4h-4l-.117-.007a1 1 0 01-.876-.876L9 13l.007-.117a1 1 0 01.876-.876L10 12h.585l-1.792-1.793a1 1 0 011.32-1.497zM4 0l.117.007a1 1 0 01.876.876L5 1l-.007.117a1 1 0 01-.876.876L4 2h-.585l1.792 1.793a1 1 0 01-1.32 1.497l-.094-.083L2 3.415V4l-.007.117a1 1 0 01-1.986 0L0 4V0h4zm10 0v4a1 1 0 01-1.993.117L12 4v-.586l-1.793 1.793-.094.083A1 1 0 018.71 3.887l.083-.094L10.584 2H10a1 1 0 01-.993-.883L9 1a1 1 0 01.883-.993L10 0h4z'
        fill='currentColor'
      />
    </svg>
  ),
  'exclamation-mask': (
    <svg viewBox='0 0 16.24 15.45'>
      <defs>
        <clipPath id='exclamation-mask-a' transform='translate(0 .24)'>
          <path
            d='M8.13 12.3a.93.93 0 01-1-.9.92.92 0 011-.9.9.9 0 110 1.8zm0-2.71a.67.67 0 01-.74-.72L7.28 5a.78.78 0 01.84-.82A.81.81 0 019 5l-.15 3.87a.66.66 0 01-.72.72zM14.25 15A2 2 0 0016 12L9.84 1A2 2 0 006.4 1L.27 12A2 2 0 000 13a1.93 1.93 0 002 2z'
            fill='none'
          />
        </clipPath>
        <clipPath id='exclamation-mask-b' transform='translate(0 .24)'>
          <path fill='none' d='M-274.59-116.9h1280v730h-1280z' />
        </clipPath>
      </defs>
      <g clipPath='url(#exclamation-mask-a)'>
        <g clipPath='url(#exclamation-mask-b)'>
          <path fill='currentColor' d='M0 0h16.24v15.45H0z' />
        </g>
      </g>
    </svg>
  ),
  'arrow-circle-right': (
    <svg viewBox='0 0 14 14'>
      <path
        d='M7.301 10.265c-.44 0-.745-.324-.745-.791 0-.227.104-.454.266-.584l.751-.636.59-.39-1.276.138H4.25c-.512 0-.881-.462-.875-.987 0-.532.363-1.014.875-1.014h2.637l1.27.157-.61-.39-.725-.628a.8.8 0 01-.266-.59c0-.468.305-.786.745-.786.26 0 .415.065.616.247L10.3 6.223c.22.208.337.474.337.792 0 .318-.117.584-.337.785l-2.384 2.212c-.201.182-.357.253-.616.253zm-.298 3.741c3.848 0 6.997-3.148 6.997-7.003C14 3.15 10.851 0 6.997 0 3.149 0 0 3.149 0 7.003c0 3.855 3.149 7.003 7.003 7.003z'
        fill='currentColor'
      />
    </svg>
  ),
  filters: (
    <svg width='12' height='12' viewBox='0 0 12 12'>
      <path
        d='M4 6a1 1 0 0 1 1 1v1h7v2H5v1a1 1 0 0 1-2 0v-1H0V8h3V7a1 1 0 0 1 1-1zm4-6a1 1 0 0 1 1 1v1h3v2H9v1a1 1 0 0 1-2 0V4H0V2h7V1a1 1 0 0 1 1-1z'
        fill='currentColor'
        fillRule='evenodd'
      />
    </svg>
  ),
  barchart: (
    <svg width='14' height='14' viewBox='0 0 14 14'>
      <path
        d='M1 8a1 1 0 011 1v4a1 1 0 01-2 0V9a1 1 0 011-1zm4-8a1 1 0 011 1v12a1 1 0 01-2 0V1a1 1 0 011-1zm4 4a1 1 0 011 1v8a1 1 0 01-2 0V5a1 1 0 011-1zm4 4a1 1 0 011 1v4a1 1 0 01-2 0V9a1 1 0 011-1z'
        fill='currentColor'
        fillRule='evenodd'
      />
    </svg>
  ),
  collapseChart: (
    <svg width='14' height='14' viewBox='0 0 14 14'>
      <path
        d='M1 11a1 1 0 011 1v1a1 1 0 01-2 0v-1a1 1 0 011-1zm4-3a1 1 0 011 1v4a1 1 0 01-2 0V9a1 1 0 011-1zm4-4a1 1 0 011 1v8a1 1 0 01-2 0V5a1 1 0 011-1zm4-4a1 1 0 011 1v12a1 1 0 01-2 0V1a1 1 0 011-1z'
        fill='currentColor'
        fillRule='evenodd'
      />
    </svg>
  ),
  attention: (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M8.0033 15C11.8419 15 15 11.8485 15 8.0033C15 4.15809 11.8419 1 7.9967 1C4.15149 1 1 4.15809 1 8.0033C1 11.8485 4.15809 15 8.0033 15Z'
        fill='#FF3B30'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M7.25014 8.14202C7.27657 8.6045 7.54084 8.86217 8.00332 8.86217C8.45259 8.86217 8.71026 8.61111 8.73669 8.13541L8.8424 5.5323C8.86883 5.04339 8.51205 4.69983 7.99672 4.69983C7.48138 4.69983 7.12461 5.03678 7.15103 5.52569L7.25014 8.14202ZM7.07837 10.5866C7.07837 11.0887 7.45496 11.4124 8.00333 11.4124C8.53849 11.4124 8.91508 11.0953 8.91508 10.5866C8.91508 10.0911 8.53849 9.77393 8.00333 9.77393C7.44835 9.77393 7.07837 10.0911 7.07837 10.5866Z'
        fill='white'
      />
    </svg>
  ),
  guide: (
    <svg
      width='16'
      height='18'
      viewBox='0 0 16 18'
      xmlns='http://www.w3.org/2000/svg'
    >
      <g fill='none' fillRule='evenodd'>
        <path fill='#EEEFF2' d='M-19-581h1280v700H-19z' />
        <path fill='#EEEFF2' d='M-19-581h200v700H-19z' />
        <path fill='none' d='M-19-9h200v36H-19z' />
        <path
          d='M2.56 0h10.88c.89 0 1.21.1 **********.***********.***********.65.27 1.54v12.88c0 .89-.1 1.21-.27 1.54-.17.32-.43.58-.75.75-.33.18-.65.27-1.54.27H2.56c-.89 0-1.21-.1-1.54-.27a1.82 1.82 0 01-.75-.75c-.18-.33-.27-.65-.27-1.54V2.56c0-.89.1-1.21.27-1.54C.44.7.7.44 1.02.27 1.35.09 1.67 0 2.56 0zM3 0v18H2V0h1zm11 4v4H5V4h9z'
          fill='#222'
        />
      </g>
    </svg>
  ),
  'apple-utf': (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='12'
      height='12'
      viewBox='0 0 22 22'
    >
      <defs />
      <path
        fill='#fff'
        d='M15.2324 0h.1565c.1256 1.5515-.4666 2.7107-1.1863 3.5503-.7062.8337-1.6732 1.6422-3.2373 1.5196-.1043-1.5293.4889-2.6026 1.2076-3.4402C12.8395.8492 14.0615.1546 15.2324 0zM19.9671 16.1486v.0435c-.4396 1.3312-1.0666 2.4721-1.8317 3.5309-.6985.9612-1.5544 2.2548-3.0827 2.2548-1.3206 0-2.1978-.8492-3.5512-.8724-1.4317-.0232-2.219.7101-3.528.8946H7.527c-.9612-.1391-1.737-.9004-2.302-1.5863-1.6665-2.0268-2.9543-4.6448-3.1939-7.995v-.9845c.1015-2.3977 1.2666-4.3472 2.8151-5.292.8173-.5024 1.9408-.9303 3.1919-.739.5362.083 1.0839.2666 1.564.4482.455.1749 1.0241.485 1.5631.4685.3652-.0106.7284-.2009 1.0965-.3352 1.0781-.3893 2.135-.8356 3.528-.626 1.6742.2531 2.8624.997 3.5967 2.1447-1.4163.9013-2.5359 2.2596-2.3447 4.5791.1701 2.1069 1.395 3.3396 2.9253 4.0661z'
      />
    </svg>
  ),
  'google-utf': (
    <svg
      width='13'
      height='14'
      viewBox='0 0 13 14'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M0.717517 13.5453L6.50327 7.70016L8.54266 9.76023L1.66469 13.6238C1.40317 13.7707 1.08287 13.7685 0.823003 13.6176L0.717517 13.5453ZM5.91981 7.1107L0.403809 12.6831V1.53834L5.91981 7.1107ZM9.36676 4.80794L12.0616 6.3214C12.3264 6.47058 12.4907 6.75 12.4907 7.05281C12.4907 7.35561 12.3264 7.63504 12.0616 7.78421L9.29314 9.33886L7.08729 7.1107L9.36676 4.80794ZM0.656534 0.61434C0.70598 0.565913 0.76147 0.52361 0.823003 0.487986C1.08287 0.337141 1.40317 0.334914 1.66469 0.481863L8.61628 4.38658L6.50327 6.52123L0.656534 0.61434Z'
        fill='#F2F2F2'
      />
    </svg>
  ),
  upload: (
    <svg width={11} height={14}>
      <path
        fill='currentColor'
        d='M9.12573242,11.578125 C9.67801717,11.578125 10.1257324,12.0258403 10.1257324,12.578125 C10.1257324,13.1304097 9.67801717,13.578125 9.12573242,13.578125 L1.12573242,13.578125 C0.573447672,13.578125 0.125732422,13.1304097 0.125732422,12.578125 C0.125732422,12.0258403 0.573447672,11.578125 1.12573242,11.578125 L9.12573242,11.578125 Z M5.1796875,-5.59552404e-14 C5.42089844,0.0126953125 5.66845703,0.114257813 5.82714844,0.279296875 L5.82714844,0.279296875 L9.953125,4.41162109 C10.1499023,4.60839844 10.2514648,4.84326172 10.2514648,5.09716797 C10.2514648,5.63671875 9.85791016,6.01757813 9.34375,6.01757813 C9.05810547,6.01757813 8.8359375,5.890625 8.66455078,5.71289063 L8.66455078,5.71289063 L7.2109375,4.265625 L6.02392578,2.88818359 L6.08105469,4.265625 L6.08105469,10.171875 C6.08105469,10.7685547 5.69384766,11.168457 5.12255859,11.168457 C4.55126953,11.168457 4.17041016,10.7685547 4.17041016,10.171875 L4.17041016,10.171875 L4.17041016,4.265625 L4.22753906,2.88183594 L3.03417969,4.265625 L1.59326172,5.71289063 C1.41552734,5.890625 1.19335937,6.01757813 0.907714844,6.01757813 C0.393554687,6.01757813 0,5.63671875 0,5.09716797 C0,4.84326172 0.1015625,4.60839844 0.298339844,4.41162109 L0.298339844,4.41162109 L4.41796875,0.279296875 C4.58300781,0.114257813 4.83056641,0.0126953125 5.07177734,-5.59552404e-14 L5.07177734,-5.59552404e-14 Z'
      />
    </svg>
  ),
  download: (
    <svg width={12} height={14}>
      <path
        fill='currentColor'
        d='M9.99573242,11.998125 C10.5480172,11.998125 10.9957324,12.4458403 10.9957324,12.998125 C10.9957324,13.5504097 10.5480172,13.998125 9.99573242,13.998125 L1.99573242,13.998125 C1.44344767,13.998125 0.995732422,13.5504097 0.995732422,12.998125 C0.995732422,12.4458403 1.44344767,11.998125 1.99573242,11.998125 L9.99573242,11.998125 Z M5.99890625,0.42 C6.57019531,0.42 6.95105469,0.819902344 6.95105469,1.41658203 L6.95105469,1.41658203 L6.95105469,7.32283203 L6.89392578,8.70662109 L8.08728516,7.32283203 L9.52820312,5.87556641 C9.7059375,5.69783203 9.92810547,5.57087891 10.21375,5.57087891 C10.7279102,5.57087891 11.1214648,5.95173828 11.1214648,6.49128906 C11.1214648,6.74519531 11.0199023,6.98005859 10.823125,7.17683594 L10.823125,7.17683594 L6.70349609,11.3091602 C6.53845703,11.4741992 6.29089844,11.5757617 6.0496875,11.588457 L6.0496875,11.588457 L5.94177734,11.588457 C5.70056641,11.5757617 5.45300781,11.4741992 5.29431641,11.3091602 L5.29431641,11.3091602 L1.16833984,7.17683594 C0.9715625,6.98005859 0.87,6.74519531 0.87,6.49128906 C0.87,5.95173828 1.26355469,5.57087891 1.77771484,5.57087891 C2.06335937,5.57087891 2.28552734,5.69783203 2.45691406,5.87556641 L2.45691406,5.87556641 L3.91052734,7.32283203 L5.09753906,8.70027344 L5.04041016,7.32283203 L5.04041016,1.41658203 C5.04041016,0.819902344 5.42761719,0.42 5.99890625,0.42 Z'
      />
    </svg>
  ),
  'folder-btn': (
    <svg width={16} height={16}>
      <path
        fill='currentColor'
        d='M5.8048735,1 C6.56842641,1 7.26535388,1.43474918 7.60112087,2.12051414 L8.03173828,3 L14,3 C15.1045695,3 16,3.8954305 16,5 L16,12 C16,13.1045695 15.1045695,14 14,14 L2,14 C0.8954305,14 0,13.1045695 0,12 L0,3 C0,1.8954305 0.8954305,1 2,1 L5.8048735,1 Z M2,5 L2,12 L14,12 L14,5 L2,5 Z'
      />
    </svg>
  ),
  folder: (
    <svg width={24} height={24}>
      <path
        d='M1,3.11111111 C1,2.49746139 1.46903502,2 2.04761905,2 L10.9943539,2 C11.30018,2 11.5907366,2.14173637 11.7897656,2.38800959 L12.6472511,3.48691215 C12.899917,3.81071376 13.2877132,4 13.698429,4 L21.952381,4 L21.952381,4 C22.530965,4 23,4.44190583 23,5.05555556 L23,7 L1,7 L1,3.11111111 Z'
        fill='#229653'
      />
      <path
        d='M2.06666667,6.5 C1.75370531,6.5 1.5,6.75370531 1.5,7.06666667 L1.5,19.9333333 C1.5,20.2462947 1.75370531,20.5 2.06666667,20.5 L21.9333333,20.5 C22.2462947,20.5 22.5,20.2462947 22.5,19.9333333 L22.5,7.06666667 C22.5,6.75370531 22.2462947,6.5 21.9333333,6.5 L2.06666667,6.5 Z'
        stroke='#229653'
        fill='#27AE60'
      />
    </svg>
  ),
  file: (
    <svg width={24} height={24}>
      <path
        d='M5,1.5 C4.17157288,1.5 3.5,2.17157288 3.5,3 L3.5,21 C3.5,21.8284271 4.17157288,22.5 5,22.5 L19,22.5 C19.8284271,22.5 20.5,21.8284271 20.5,21 L20.5,7.82842712 C20.5,7.43060239 20.3419647,7.04907152 20.0606602,6.76776695 L15.232233,1.93933983 C14.9509285,1.65803526 14.5693976,1.5 14.1715729,1.5 L5,1.5 Z'
        stroke='#B7C1C9'
        strokeWidth='1'
        fill='#F5F7F9'
      />
      <path
        d='M15,2 L20,7 L16,7 C15.4477153,7 15,6.55228475 15,6 L15,2 L15,2 Z'
        fill='#B7C1C9'
      />
    </svg>
  ),
  'folder-upload': (
    <svg width={16} height={16}>
      <path
        d='M14,12 L14,5 L2,5 L2,12 L5,12 L5,14 L2,14 C0.8954305,14 0,13.1045695 0,12 L0,3 C0,1.8954305 0.8954305,1 2,1 L5.8048735,1 C6.56842641,1 7.26535388,1.43474918 7.60112087,2.12051414 L8.03173828,3 L14,3 C15.1045695,3 16,3.8954305 16,5 L16,12 C16,13.1045695 15.1045695,14 14,14 L11,14 L11,12 L14,12 Z'
        fill='#222222'
      />
      <path
        d='M9,7 L9,12 L11,12 L8,15 L5,12 L7,11.999 L7,7 L9,7 Z'
        fill='#222222'
        transform='translate(8.000000, 11.000000) rotate(-180.000000) translate(-8.000000, -11.000000) '
      />
    </svg>
  ),
  'files-upload': (
    <svg width={16} height={16}>
      <path
        d='M9.17157288,1 C9.70200585,1 10.2107137,1.21071368 10.5857864,1.58578644 L13.4142136,4.41421356 C13.7892863,4.78928632 14,5.29799415 14,5.82842712 L14,13 C14,14.1045695 13.1045695,15 12,15 L11,15 L11,13 L5,13 L5,15 L4,15 C2.8954305,15 2,14.1045695 2,13 L2,3 C2,1.8954305 2.8954305,1 4,1 L9.17157288,1 Z M9,2.999 L4,3 L4,13 L12,13 L12,6 L10,6 C9.44771525,6 9,5.55228475 9,5 L9,2.999 Z'
        fill='#222222'
      />
      <path
        d='M9,7 L9,12 L11,12 L8,15 L5,12 L7,11.999 L7,7 L9,7 Z'
        fill='#222222'
        transform='translate(8.000000, 11.000000) rotate(-180.000000) translate(-8.000000, -11.000000) '
      />
    </svg>
  ),
  'modem-upload': (
    <svg width={24} height={24}>
      <path
        d='M11,6 L11,8 L7,8 C6.48716416,8 6.06449284,8.38604019 6.00672773,8.88337887 L6,9 L6,19 C6,19.5128358 6.38604019,19.9355072 6.88337887,19.9932723 L7,20 L17,20 C17.5128358,20 17.9355072,19.6139598 17.9932723,19.1166211 L18,19 L18,14 L20,14 L20,20 C20,21.1045695 19.1045695,22 18,22 L6,22 C4.8954305,22 4,21.1045695 4,20 L4,8 C4,6.8954305 4.8954305,6 6,6 L11,6 Z M16,16 C16.5522847,16 17,16.4477153 17,17 L17,18 C17,18.5522847 16.5522847,19 16,19 L14,19 C13.4477153,19 13,18.5522847 13,18 L13,17 C13,16.4477153 13.4477153,16 14,16 L16,16 Z M16.9997198,0.999999998 L20.7097085,4.70237672 L20.7929733,4.79651675 C21.0983153,5.18856115 21.0710445,5.75581444 20.7108522,6.11658982 L20.7108522,6.11658982 L20.6167122,6.19985459 C20.2246678,6.50519664 19.6574145,6.47792585 19.2966391,6.11773353 L19.2966391,6.11773353 L18,4.825 L18,11 C18,11.5522847 17.5522847,12 17,12 C16.4477153,12 16,11.5522847 16,11 L16,4.825 L14.7068153,6.11745329 L14.6125737,6.20060306 C14.2201568,6.50546624 13.6529372,6.47750292 13.2926018,6.11687041 C12.9022385,5.72618519 12.9024995,5.09302027 13.2931847,4.70265697 L13.2931847,4.70265697 L16.9997198,0.999999998 Z'
        fill='#A5B2BC'
      />
    </svg>
  ),
  'modem-download': (
    <svg width={24} height={24}>
      <path
        d='M11,6 L11,8 L7,8 C6.48716416,8 6.06449284,8.38604019 6.00672773,8.88337887 L6,9 L6,19 C6,19.5128358 6.38604019,19.9355072 6.88337887,19.9932723 L7,20 L17,20 C17.5128358,20 17.9355072,19.6139598 17.9932723,19.1166211 L18,19 L18,14 L20,14 L20,20 C20,21.1045695 19.1045695,22 18,22 L6,22 C4.8954305,22 4,21.1045695 4,20 L4,8 C4,6.8954305 4.8954305,6 6,6 L11,6 Z M16,16 C16.5522847,16 17,16.4477153 17,17 L17,18 C17,18.5522847 16.5522847,19 16,19 L14,19 C13.4477153,19 13,18.5522847 13,18 L13,17 C13,16.4477153 13.4477153,16 14,16 L16,16 Z M17,1 C17.5522847,1 18,1.44771525 18,2 L18,8.585 L19.2966391,7.29232159 C19.6574145,6.93212928 20.2246678,6.90485849 20.6167122,7.21020054 L20.7108522,7.29346531 C21.0710445,7.65424068 21.0983153,8.22149398 20.7929733,8.61353838 L20.7097085,8.70767841 L16.9997198,12.4100551 L13.2931847,8.70739816 C12.9024995,8.31703486 12.9022385,7.68386993 13.2926018,7.29318472 C13.6529372,6.93255221 14.2201568,6.90458889 14.6125737,7.20945207 L14.7068153,7.29260184 L16,8.585 L16,2 C16,1.44771525 16.4477153,1 17,1 Z'
        fill='#A5B2BC'
      />
    </svg>
  ),
  'modem-done': (
    <svg width={24} height={24}>
      <polyline
        fill='none'
        stroke='#27AE60'
        strokeWidth='2'
        strokeLinecap='round'
        strokeLinejoin='round'
        points='6.14176675 12.6268682 9.99008339 16.8756655 18.1417667 7.87566548'
      />
    </svg>
  ),
  'modem-trash': (
    <svg width={24} height={24}>
      <path
        d='M6,20 C6.55228475,20 7,20.4477153 7,21 C7,21.5522847 6.55228475,22 6,22 C5.44771525,22 5,21.5522847 5,21 C5,20.4477153 5.44771525,20 6,20 Z M10,20 C10.5522847,20 11,20.4477153 11,21 C11,21.5522847 10.5522847,22 10,22 C9.44771525,22 9,21.5522847 9,21 C9,20.4477153 9.44771525,20 10,20 Z M14,20 C14.5522847,20 15,20.4477153 15,21 C15,21.5522847 14.5522847,22 14,22 C13.4477153,22 13,21.5522847 13,21 C13,20.4477153 13.4477153,20 14,20 Z M18,20 C18.5522847,20 19,20.4477153 19,21 C19,21.5522847 18.5522847,22 18,22 C17.4477153,22 17,21.5522847 17,21 C17,20.4477153 17.4477153,20 18,20 Z M8,17 C8.55228475,17 9,17.4477153 9,18 C9,18.5522847 8.55228475,19 8,19 C7.44771525,19 7,18.5522847 7,18 C7,17.4477153 7.44771525,17 8,17 Z M12,17 C12.5522847,17 13,17.4477153 13,18 C13,18.5522847 12.5522847,19 12,19 C11.4477153,19 11,18.5522847 11,18 C11,17.4477153 11.4477153,17 12,17 Z M16,17 C16.5522847,17 17,17.4477153 17,18 C17,18.5522847 16.5522847,19 16,19 C15.4477153,19 15,18.5522847 15,18 C15,17.4477153 15.4477153,17 16,17 Z M6,14 C6.55228475,14 7,14.4477153 7,15 C7,15.5522847 6.55228475,16 6,16 C5.44771525,16 5,15.5522847 5,15 C5,14.4477153 5.44771525,14 6,14 Z M10,14 C10.5522847,14 11,14.4477153 11,15 C11,15.5522847 10.5522847,16 10,16 C9.44771525,16 9,15.5522847 9,15 C9,14.4477153 9.44771525,14 10,14 Z M14,14 C14.5522847,14 15,14.4477153 15,15 C15,15.5522847 14.5522847,16 14,16 C13.4477153,16 13,15.5522847 13,15 C13,14.4477153 13.4477153,14 14,14 Z M18,14 C18.5522847,14 19,14.4477153 19,15 C19,15.5522847 18.5522847,16 18,16 C17.4477153,16 17,15.5522847 17,15 C17,14.4477153 17.4477153,14 18,14 Z M4,13 C3.44771525,13 3,12.5522847 3,12 C3,11.4477153 3.44771525,11 4,11 L5,11 L5,4 C5,2.8954305 5.8954305,2 7,2 L15.5854886,2 C15.8508813,2 16.1053893,2.10549646 16.2929548,2.29325278 L18.7074662,4.71022034 C18.8947827,4.8977274 19,5.15192722 19,5.41696756 L19,11 L20,11 C20.5522847,11 21,11.4477153 21,12 C21,12.5522847 20.5522847,13 20,13 L4,13 Z M14,4 L7,4 L7,11 L17,11 L17,7 L15,7 C14.4477153,7 14,6.55228475 14,6 L14,4 Z'
        fill='#FF3B30'
      />
    </svg>
  ),
  'modem-sync': (
    <svg width={24} height={24}>
      <path
        d='M9.8048735,5 C10.5684264,5 11.2653539,5.43474918 11.6011209,6.12051414 L12.0317383,7 L19,7 C20.1045695,7 21,7.8954305 21,9 L21.0011254,11.4271621 C20.3689016,11.203589 19.6977576,11.0623836 19.0005886,11.016441 L19,9 L10.7841216,9 L9.8048735,7 L4,7 L4,19 L11.016441,19.0005886 C11.0623836,19.6977576 11.203589,20.3689016 11.4271621,21.0011254 L4,21 C2.8954305,21 2,20.1045695 2,19 L2,7 C2,5.8954305 2.8954305,5 4,5 L9.8048735,5 Z'
        fill='#A5B2BC'
        fillRule='nonzero'
      />
      <path
        d='M18.5,13 C21.5375661,13 24,15.4624339 24,18.5 C24,21.5375661 21.5375661,24 18.5,24 C15.4624339,24 13,21.5375661 13,18.5 C13,15.4624339 15.4624339,13 18.5,13 Z M19,19 L19,15 L18,15 L18,18 L16,18 L16,19 L19,19 Z'
        fill='#A5B2BC'
      />
      <rect fill='#A5B2BC' x='3' y='6' width='8' height='3' />
    </svg>
  ),
  'small-right-arrow': (
    <svg
      width='8'
      height='6'
      viewBox='0 0 8 6'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M4.5 0L3.795 0.705L5.585 2.5H0.5V3.5H5.585L3.795 5.295L4.5 6L7.5 3L4.5 0Z'
        fill='#5E5E5E'
      />
    </svg>
  ),
  'autofill-intro-arrow': (
    <svg
      width='222'
      height='137'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <g clipPath='url(#clip0)' fill='#D5DBDF'>
        <path d='M220.7 22a167 167 0 01-47.2 65.3c-12.2 10-26.1 18-41 23.4-17.2 6.2-35.5 9-53.8 8.6a220.5 220.5 0 01-76.8-16c-.7-.2-1.2 1-.4 1.2a236.4 236.4 0 0069.8 15.8c18.7 1 37.7-.8 55.6-6.4 15.5-4.8 30-12.3 43-22.1a155.3 155.3 0 0030.8-31.4 169.3 169.3 0 0021.2-37.8c.3-.8-.9-1.3-1.2-.5z' />
        <path d='M12.9 114.9a21.4 21.4 0 00-6.2-7.9c-1.4-1.3-3-2.5-4.6-3.6l-.5 1.2c2.5.4 5.1.2 7.6 0s5-.6 7.4-1.4c.8-.3.3-1.4-.5-1.1-2.2.8-4.7 1-7 1.3-2.5.2-5 .3-7.3-.1-.6-.1-1 .8-.5 1.1 1.6 1 3 2.2 4.4 3.4 2.4 2 4.9 4.6 6 ******* 1.5.2 1.2-.5z' />
      </g>
      <defs>
        <clipPath id='clip0'>
          <path
            fill='#fff'
            transform='rotate(5.9 6 114.8)'
            d='M0 0h211.2v115.1H0z'
          />
        </clipPath>
      </defs>
    </svg>
  ),
  'logo-short': (
    <svg
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <circle cx='12' cy='12' r='12' fill='#27AE60' />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M6.75007 12.0294C6.74253 9.94665 7.35017 7.90488 8.50192 6.14297L8.9231 5.625C9.3964 5.78996 9.87947 5.92752 10.3698 6.03702C11.5641 6.32194 12.7384 6.67978 13.8857 7.10836C14.7808 7.45253 15.578 7.99735 16.2113 8.6977C16.9043 9.53112 17.2716 10.5729 17.249 11.6409C17.2572 12.1561 17.1527 12.6672 16.9424 13.1408C16.732 13.6145 16.4205 14.0401 16.0282 14.3899C15.7835 14.61 15.5082 14.7962 15.2103 14.9432C15.4258 16.0747 15.5178 17.225 15.4849 18.375H13.7331V18.0571C13.7331 15.8556 13.7331 13.5776 11.1877 11.6409L12.2559 10.3047C13.3115 11.0741 14.1509 12.0859 14.6975 13.2479C14.7464 13.2067 14.7769 13.1949 14.8318 13.142C15.0552 12.9546 15.2308 12.7201 15.3452 12.4568C15.4595 12.1934 15.5094 11.9082 15.4911 11.6232C15.523 10.9362 15.2879 10.2624 14.8318 9.73372C14.3836 9.23982 13.8211 8.85463 13.1898 8.60939C12.5077 8.33841 11.8059 8.11608 11.09 7.94422C10.6078 7.81472 10.1317 7.68525 9.69222 7.5322C8.85409 8.87534 8.43059 10.4212 8.47141 11.9882C8.47141 13.5893 8.92312 14.2721 9.31988 14.6136C9.60778 14.8565 9.97508 14.994 10.3575 15.0021C10.7478 14.9328 11.1246 14.8057 11.4746 14.6253L12.085 14.3722L12.7442 15.9204C12.5977 15.9792 12.439 16.0558 12.2742 16.1382C11.6935 16.468 11.0371 16.654 10.3636 16.6797C9.54423 16.6702 8.75649 16.373 8.14788 15.8438C7.36046 15.1728 6.75007 13.9484 6.75007 12.0294Z'
        fill='white'
      />
    </svg>
  ),
  'logo-square': (
    <svg
      width='32'
      height='32'
      viewBox='0 0 32 32'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <rect width='32' height='32' rx='8' fill='#27AE60' />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M9.00009 16.0392C8.99003 13.2622 9.80023 10.5398 11.3359 8.19063L11.8975 7.5C12.5285 7.71994 13.1726 7.90336 13.8263 8.04936C15.4188 8.42925 16.9846 8.90637 18.5142 9.47781C19.7077 9.9367 20.7706 10.6631 21.6151 11.5969C22.5391 12.7082 23.0288 14.0972 22.9987 15.5212C23.0096 16.2082 22.8703 16.8896 22.5898 17.5211C22.3094 18.1527 21.894 18.7201 21.3709 19.1865C21.0447 19.48 20.6776 19.7283 20.2803 19.9243C20.5677 21.433 20.6904 22.9666 20.6466 24.5H18.3108V24.0762C18.3108 21.1408 18.3108 18.1034 14.917 15.5212L16.3412 13.7396C17.7487 14.7655 18.8678 16.1145 19.5967 17.6639C19.6618 17.6089 19.7025 17.5932 19.7757 17.5226C20.0736 17.2728 20.3078 16.9602 20.4602 16.609C20.6126 16.2579 20.6792 15.8776 20.6547 15.4977C20.6974 14.5816 20.3839 13.6832 19.7757 12.9783C19.1781 12.3198 18.4281 11.8062 17.5864 11.4792C16.677 11.1179 15.7412 10.8214 14.7867 10.5923C14.1437 10.4196 13.5089 10.247 12.923 10.0429C11.8055 11.8338 11.2408 13.8949 11.2952 15.9843C11.2952 18.1191 11.8975 19.0295 12.4265 19.4847C12.8104 19.8087 13.3001 19.9921 13.81 20.0028C14.3304 19.9105 14.8328 19.741 15.2994 19.5005L16.1133 19.163L16.9923 21.2272C16.797 21.3056 16.5854 21.4077 16.3656 21.5176C15.5913 21.9573 14.7161 22.2053 13.8182 22.2396C12.7256 22.2269 11.6753 21.8307 10.8638 21.1251C9.81395 20.2304 9.00009 18.5978 9.00009 16.0392Z'
        fill='white'
      />
    </svg>
  ),
  kebab: (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M3 9.75C3.9665 9.75 4.75 8.9665 4.75 8C4.75 7.0335 3.9665 6.25 3 6.25C2.0335 6.25 1.25 7.0335 1.25 8C1.25 8.9665 2.0335 9.75 3 9.75ZM9.75 8C9.75 8.9665 8.9665 9.75 8 9.75C7.0335 9.75 6.25 8.9665 6.25 8C6.25 7.0335 7.0335 6.25 8 6.25C8.9665 6.25 9.75 7.0335 9.75 8ZM14.75 8C14.75 8.9665 13.9665 9.75 13 9.75C12.0335 9.75 11.25 8.9665 11.25 8C11.25 7.0335 12.0335 6.25 13 6.25C13.9665 6.25 14.75 7.0335 14.75 8Z'
        fill='currentColor'
      />
    </svg>
  ),
  'modal-success': (
    <svg
      width='44'
      height='44'
      viewBox='0 0 44 44'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <circle opacity='0.15' cx='22' cy='22' r='22' fill='#27AE60' />
      <path
        d='M13.5 23L20 31L30.5 14'
        stroke='#27AE60'
        strokeWidth='3'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  ),
  'modal-error': (
    <svg
      width='44'
      height='44'
      viewBox='0 0 44 44'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <circle opacity='0.15' cx='22' cy='22' r='22' fill='#FF3B30' />
      <path
        d='M21.9102 26.127C20.7122 26.127 20.0983 25.513 20.0684 24.2852L19.7764 12.0439C19.7614 11.46 19.9486 10.9733 20.3379 10.584C20.7422 10.1947 21.2588 10 21.8877 10C22.5016 10 23.0107 10.2021 23.415 10.6064C23.8343 10.9958 24.0365 11.4824 24.0215 12.0664L23.6846 24.2852C23.6696 25.513 23.0781 26.127 21.9102 26.127ZM21.9102 33.6514C21.2363 33.6514 20.6449 33.4193 20.1357 32.9551C19.6416 32.4909 19.3945 31.9219 19.3945 31.248C19.3945 30.5892 19.6416 30.0202 20.1357 29.541C20.6299 29.0618 21.2214 28.8223 21.9102 28.8223C22.584 28.8223 23.168 29.0618 23.6621 29.541C24.1712 30.0052 24.4258 30.5742 24.4258 31.248C24.4258 31.9219 24.1712 32.4909 23.6621 32.9551C23.168 33.4193 22.584 33.6514 21.9102 33.6514Z'
        fill='#FF3B30'
      />
    </svg>
  ),
  'archive-file': (
    <svg
      width='24'
      height='30'
      viewBox='0 0 24 30'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M14.8103 1C15.3251 1 15.8188 1.21071 16.1829 1.58579L22.4314 8.02369C22.7955 8.39876 23 8.90747 23 9.4379V27C23 27.5523 22.7827 28.0523 22.4314 28.4142C22.0802 28.7761 21.5949 29 21.0588 29H2.94118C2.40514 29 1.91984 28.7761 1.56856 28.4142C1.21727 28.0523 1 27.5523 1 27V3C1 2.44772 1.21727 1.94772 1.56856 1.58579C1.91984 1.22386 2.40514 1 2.94118 1H14.8103Z'
        fill='white'
        stroke='#B7C1C9'
        strokeWidth='2'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M15 1L23 9H16.6C15.7163 9 15 8.28366 15 7.4V1Z'
        fill='#B7C1C9'
      />
      <rect x='5' y='2' width='3' height='2' fill='#B7C1C9' />
      <rect x='8' y='4' width='3' height='2' fill='#B7C1C9' />
      <rect x='5' y='6' width='3' height='2' fill='#B7C1C9' />
      <rect x='8' y='8' width='3' height='2' fill='#B7C1C9' />
      <rect x='5' y='10' width='3' height='2' fill='#B7C1C9' />
      <rect x='8' y='12' width='3' height='2' fill='#B7C1C9' />
      <rect x='5' y='14' width='3' height='2' fill='#B7C1C9' />
      <rect x='8' y='16' width='3' height='2' fill='#B7C1C9' />
      <rect x='5' y='18' width='3' height='2' fill='#B7C1C9' />
    </svg>
  ),
  'green-circle': (
    <svg
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M10 1C11.1819 1 12.3522 1.23279 13.4442 1.68508C14.5361 2.13738 15.5282 2.80031 16.364 3.63604C17.1997 4.47177 17.8626 5.46392 18.3149 6.55585C18.7672 7.64778 19 8.8181 19 10C19 11.1819 18.7672 12.3522 18.3149 13.4442C17.8626 14.5361 17.1997 15.5282 16.364 16.364C15.5282 17.1997 14.5361 17.8626 13.4441 18.3149C12.3522 18.7672 11.1819 19 10 19C8.8181 19 7.64778 18.7672 6.55585 18.3149C5.46392 17.8626 4.47176 17.1997 3.63604 16.364C2.80031 15.5282 2.13737 14.5361 1.68508 13.4441C1.23279 12.3522 0.999999 11.1819 1 10C1 8.8181 1.23279 7.64777 1.68509 6.55584C2.13738 5.46391 2.80031 4.47176 3.63604 3.63604C4.47177 2.80031 5.46392 2.13737 6.55585 1.68508C7.64778 1.23279 8.81811 0.999999 10 1L10 1Z'
        stroke='#27AE60'
        strokeWidth='2'
        strokeMiterlimit='1.41421'
        strokeLinecap='round'
      />
    </svg>
  ),
  'check-circle': (
    <svg
      width='25'
      height='25'
      viewBox='0 0 25 25'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M12.0508 24.4922C13.6836 24.4922 15.2188 24.1797 16.6562 23.5547C18.1016 22.9297 19.375 22.0664 20.4766 20.9648C21.5781 19.8633 22.4414 18.5938 23.0664 17.1562C23.6914 15.7109 24.0039 14.1719 24.0039 12.5391C24.0039 10.9062 23.6914 9.37109 23.0664 7.93359C22.4414 6.48828 21.5781 5.21484 20.4766 4.11328C19.375 3.01172 18.1016 2.14844 16.6562 1.52344C15.2109 0.898438 13.6719 0.585938 12.0391 0.585938C10.4062 0.585938 8.86719 0.898438 7.42188 1.52344C5.98438 2.14844 4.71484 3.01172 3.61328 4.11328C2.51953 5.21484 1.66016 6.48828 1.03516 7.93359C0.410156 9.37109 0.0976562 10.9062 0.0976562 12.5391C0.0976562 14.1719 0.410156 15.7109 1.03516 17.1562C1.66016 18.5938 2.52344 19.8633 3.625 20.9648C4.72656 22.0664 5.99609 22.9297 7.43359 23.5547C8.87891 24.1797 10.418 24.4922 12.0508 24.4922ZM12.0508 22.5C10.668 22.5 9.375 22.2422 8.17188 21.7266C6.96875 21.2109 5.91016 20.5 4.99609 19.5938C4.08984 18.6797 3.37891 17.6211 2.86328 16.418C2.35547 15.2148 2.10156 13.9219 2.10156 12.5391C2.10156 11.1562 2.35547 9.86328 2.86328 8.66016C3.37891 7.45703 4.08984 6.39844 4.99609 5.48438C5.90234 4.57031 6.95703 3.85937 8.16016 3.35156C9.36328 2.83594 10.6562 2.57812 12.0391 2.57812C13.4219 2.57812 14.7148 2.83594 15.918 3.35156C17.1211 3.85937 18.1797 4.57031 19.0938 5.48438C20.0078 6.39844 20.7227 7.45703 21.2383 8.66016C21.7539 9.86328 22.0117 11.1562 22.0117 12.5391C22.0117 13.9219 21.7539 15.2148 21.2383 16.418C20.7305 17.6211 20.0195 18.6797 19.1055 19.5938C18.1992 20.5 17.1406 21.2109 15.9297 21.7266C14.7266 22.2422 13.4336 22.5 12.0508 22.5ZM10.7617 18.0938C11.1602 18.0938 11.4766 17.9141 11.7109 17.5547L17.0664 9.11719C17.1367 9.00781 17.1992 8.89062 17.2539 8.76562C17.3164 8.63281 17.3477 8.5 17.3477 8.36719C17.3477 8.10938 17.25 7.90234 17.0547 7.74609C16.8594 7.58984 16.6406 7.51172 16.3984 7.51172C16.0703 7.51172 15.7969 7.6875 15.5781 8.03906L10.7148 15.8438L8.40625 12.8555C8.26562 12.668 8.125 12.543 7.98438 12.4805C7.85156 12.4102 7.70312 12.375 7.53906 12.375C7.28906 12.375 7.07422 12.4688 6.89453 12.6562C6.72266 12.8359 6.63672 13.0547 6.63672 13.3125C6.63672 13.5547 6.72656 13.7969 6.90625 14.0391L9.76562 17.5547C9.91406 17.7422 10.0664 17.8789 10.2227 17.9648C10.3867 18.0508 10.5664 18.0938 10.7617 18.0938Z'
        fill='#27AE60'
      />
    </svg>
  ),
  drag: (
    <svg
      width='8'
      height='14'
      viewBox='0 0 8 14'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M1 0C0.447715 0 0 0.447715 0 1C0 1.55228 0.447715 2 1 2H7C7.55228 2 8 1.55228 8 1C8 0.447715 7.55228 0 7 0H1ZM1 4C0.447715 4 0 4.44772 0 5C0 5.55228 0.447715 6 1 6H7C7.55228 6 8 5.55228 8 5C8 4.44772 7.55228 4 7 4H1ZM0 9C0 8.44771 0.447715 8 1 8H7C7.55228 8 8 8.44771 8 9C8 9.55229 7.55228 10 7 10H1C0.447715 10 0 9.55229 0 9ZM1 12C0.447715 12 0 12.4477 0 13C0 13.5523 0.447715 14 1 14H7C7.55228 14 8 13.5523 8 13C8 12.4477 7.55228 12 7 12H1Z'
        fill='#A5B2BC'
      />
    </svg>
  ),
  'table-parent-arrow-opened': (
    <svg
      width='13'
      height='8'
      viewBox='0 0 13 8'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M6.06445 7.375C6.18294 7.375 6.29232 7.35449 6.39258 7.31348C6.49284 7.2679 6.58854 7.19954 6.67969 7.1084L11.8887 1.7832C12.0436 1.62825 12.1211 1.44141 12.1211 1.22266C12.1211 1.07226 12.0846 0.937825 12.0117 0.819335C11.9434 0.696288 11.8477 0.598307 11.7246 0.52539C11.6061 0.452473 11.474 0.416015 11.3281 0.416015C11.1094 0.416015 10.9157 0.500325 10.7471 0.668945L6.06445 5.46777L1.38184 0.668945C1.21322 0.500325 1.02181 0.416015 0.807617 0.416015C0.657226 0.416015 0.522786 0.452474 0.404296 0.52539C0.285807 0.598307 0.192382 0.696289 0.124023 0.819336C0.0511062 0.937825 0.0146479 1.07227 0.0146479 1.22266C0.0146479 1.44141 0.0898433 1.62826 0.240234 1.7832L5.44922 7.1084C5.54036 7.19954 5.63607 7.2679 5.73633 7.31348C5.83659 7.35449 5.94596 7.375 6.06445 7.375Z'
        fill='#222222'
      />
    </svg>
  ),
  'table-parent-arrow-closed': (
    <svg
      width='8'
      height='14'
      viewBox='0 0 8 14'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M7.81055 7C7.81055 6.88151 7.78776 6.76986 7.74219 6.66504C7.69661 6.56022 7.62825 6.46452 7.53711 6.37793L2.21191 1.17578C2.05697 1.02083 1.8724 0.943359 1.6582 0.943359C1.50781 0.943359 1.37109 0.979817 1.24805 1.05273C1.125 1.12109 1.0293 1.21452 0.960937 1.33301C0.88802 1.4515 0.851562 1.58594 0.851562 1.73633C0.851562 1.95508 0.933593 2.14648 1.09766 2.31055L6.24512 7.32812L6.24512 6.66504L1.09766 11.6895C0.933594 11.8535 0.851562 12.0449 0.851562 12.2637C0.851562 12.4141 0.888021 12.5485 0.960937 12.667C1.0293 12.7855 1.125 12.8812 1.24805 12.9541C1.37109 13.0225 1.50781 13.0566 1.6582 13.0566C1.76758 13.0566 1.87012 13.0384 1.96582 13.002C2.05697 12.9609 2.139 12.9017 2.21191 12.8242L7.53711 7.62207C7.7194 7.43522 7.81055 7.22786 7.81055 7Z'
        fill='#222222'
      />
    </svg>
  ),
  'christmas-tree': (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='104'
      height='206'
      fill='none'
    >
      <path
        stroke='#FFDC68'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth='2.645'
        d='m58.954 24.921-3.441-8.335 8.316-5.358-9.177-.595L51.498 2l-3.442 8.633-8.03.595 6.883 5.358-2.915 8.335'
      />
      <path
        stroke='#FFFADF'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth='5.289'
        d='M101.296 192.862 77.016 203l24.28-25.566L52.081 203l43.935-39.671-71.642 37.854L90.87 152.75 3 199.003l81.933-58.857-77.975 39.238 73.144-51.591-67.603 34.515 64.518-47.231-60.56 30.519 54.865-41.333-46.948 23.53 42.54-36.313-37.79 18.147 34.705-31.811-30.352 14.372L61.58 62.759 37.831 74.75l20.978-23.98-17.02 6.177 13.458-18.892-10.291 3.633 7.125-18.53'
      />
    </svg>
  ),
  'yield-logo': (
    <svg xmlns='http://www.w3.org/2000/svg' width='128' height='28' fill='none'>
      <path
        fill='#FFDC68'
        fillRule='evenodd'
        d='M67.786 9.625A16.116 16.116 0 0 1 70.266 1l.6-.757c.675.248 1.365.453 2.066.612 1.699.41 3.367.934 4.995 1.569a8.612 8.612 0 0 1 3.293 2.31 6.7 6.7 0 0 1 1.475 4.348 5.384 5.384 0 0 1-1.709 4.044 5.47 5.47 0 0 1-1.157.805c.3 1.663.429 3.352.386 5.042h-2.508v-.468c0-3.233 0-6.576-3.61-9.423l1.515-1.96a11.324 11.324 0 0 1 3.445 4.346c.069-.055.117-.082.193-.15a2.752 2.752 0 0 0 .93-2.236c.039-1-.292-1.98-.93-2.752a6.2 6.2 0 0 0-2.329-1.65 23.133 23.133 0 0 0-2.976-.977c-.689-.193-1.378-.378-1.984-.605a12.083 12.083 0 0 0-1.709 6.548c0 2.345.648 3.35 1.206 3.845.406.358.926.562 1.468.577a5.448 5.448 0 0 0 1.584-.557l.861-.364.93 2.27c-.206.082-.427.192-.689.309a5.746 5.746 0 0 1-2.714.805 4.827 4.827 0 0 1-3.135-1.231c-1.123-1.046-1.977-2.862-1.977-5.675ZM2.047 25.706a7.406 7.406 0 0 0 10.196 0 7.042 7.042 0 0 0 2.08-4.993 7.034 7.034 0 0 0-2.08-4.994 7.161 7.161 0 0 0-7.814-1.576 7.156 7.156 0 0 0-3.222 2.636A7.138 7.138 0 0 0 0 20.761a6.745 6.745 0 0 0 2.046 4.945Zm8.143-8.137a4.174 4.174 0 0 1 1.247 3.116 4.08 4.08 0 0 1-1.226 3.102 4.42 4.42 0 0 1-6.139 0 4.244 4.244 0 0 1-1.226-3.102 4.19 4.19 0 0 1 1.247-3.116 4.316 4.316 0 0 1 6.097 0Zm7.744 4.54v5.393h-2.625V17.439h2.57v1.375a3.587 3.587 0 0 1 3.176-1.678 3.958 3.958 0 0 1 2.81 1.087 4.063 4.063 0 0 1 1.151 3.102v6.149h-2.618v-5.448c0-1.609-.854-2.496-2.142-2.496-1.289 0-2.301 1.08-2.301 2.579h-.021Zm18.209 1.272c.057-.378.09-.759.096-1.141a4.807 4.807 0 0 0-1.433-3.694 5.573 5.573 0 0 0-7.461.13 5.143 5.143 0 0 0-1.467 3.784 5.055 5.055 0 0 0 1.561 3.878 5.073 5.073 0 0 0 3.95 1.384 7.36 7.36 0 0 0 4.43-1.376l-1.04-1.823a6.326 6.326 0 0 1-3.28 1.011c-1.598 0-2.583-.713-2.955-2.139l7.599-.014Zm-7.579-1.94a2.592 2.592 0 0 1 2.625-2.104 2.442 2.442 0 0 1 2.529 2.105h-5.154Zm7.579 3.729a6.197 6.197 0 0 0 5.208 2.552 5.517 5.517 0 0 0 3.645-1.163 3.722 3.722 0 0 0 1.378-2.992c0-1.513-.779-2.641-2.343-3.398-.349-.165-.907-.405-1.674-.722l-1.378-.584c-.744-.372-1.116-.819-1.116-1.376 0-.798.689-1.314 1.77-1.314 1.132.018 2.211.475 3.011 1.273l1.468-1.995a6.125 6.125 0 0 0-4.52-1.844 4.993 4.993 0 0 0-3.382 1.142 3.59 3.59 0 0 0-1.303 2.848 3.78 3.78 0 0 0 2.178 3.398c.524.278 1.064.523 1.619.736l.31.117c.544.206.888.337 1.047.42.833.384 1.247.866 1.247 1.416 0 .874-.84 1.458-1.977 1.458a4.464 4.464 0 0 1-3.645-1.898l-1.543 1.926Zm12.222-6.459a5.394 5.394 0 0 0 0 7.511 5.737 5.737 0 0 0 7.812 0 5.395 5.395 0 0 0 0-7.51 5.703 5.703 0 0 0-7.812 0Zm3.906 6.63a2.76 2.76 0 0 1-2.012-.832 2.887 2.887 0 0 1-.799-2.063 2.795 2.795 0 0 1 .827-2.002 2.693 2.693 0 0 1 2.011-.832 2.644 2.644 0 0 1 1.985.833 2.812 2.812 0 0 1 .82 2.029c.01.768-.285 1.51-.82 2.063a2.658 2.658 0 0 1-1.985.805h-.027Zm8.943-11.775a1.629 1.629 0 0 1 0 2.27 1.683 1.683 0 0 1-2.308 0 1.629 1.629 0 0 1 0-2.27 1.683 1.683 0 0 1 2.308 0Zm-2.48 3.859h2.624v10.063h-2.625V17.425Zm4.133-3.969h2.618v14.032h-2.618V13.456Z'
        clipRule='evenodd'
      />
      <path
        fill='#FFDC68'
        d='m91.816 21.628-5.078-8.75h1.916l3.982 7.133 3.982-7.133h1.908l-5.091 8.777v5.778h-1.62v-5.805ZM99.491 14.041a1.12 1.12 0 0 1 1.344-1.092 1.12 1.12 0 0 1-.214 2.22 1.109 1.109 0 0 1-1.13-1.128Zm.331 3.322h1.585v10.042h-1.585V17.363ZM103.418 22.281c0-3.013 2.136-5.124 4.761-5.124 2.507 0 4.519 1.967 4.519 4.856v.88h-7.702c.099.949.56 1.823 1.287 2.441a3.725 3.725 0 0 0 2.619.881 6.553 6.553 0 0 0 3.08-.818v1.5a6.409 6.409 0 0 1-3.059.687c-3.307.028-5.505-2.187-5.505-5.303Zm7.785-.57c-.103-1.954-1.337-3.144-3.059-3.165-1.722-.02-3.024 1.252-3.19 3.164h6.249ZM114.627 12.438h1.585v14.967h-1.585V12.438ZM118.142 22.343c0-2.972 2.177-5.186 4.746-5.186a3.932 3.932 0 0 1 3.528 2.152v-6.878H128v14.967h-1.584v-1.926a3.977 3.977 0 0 1-3.528 2.133c-2.59.006-4.746-2.29-4.746-5.262Zm8.398 0c0-2.064-1.413-3.776-3.445-3.776-1.729 0-3.348 1.499-3.348 3.755 0 2.112 1.495 3.831 3.327 3.831 1.833 0 3.466-1.582 3.466-3.79v-.02Z'
      />
    </svg>
  ),
  play: (
    <svg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none'>
      <path
        fill='#222'
        d='M.857 12A.857.857 0 0 1 0 11.143V.857A.857.857 0 0 1 1.24.091l10.286 5.142a.858.858 0 0 1 0 1.534L1.241 11.91c-.12.059-.25.09-.384.09Z'
      />
    </svg>
  ),
  'logo-leaf': (
    <svg
      width='12'
      height='14'
      viewBox='0 0 12 14'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M0.750069 7.02941C0.742526 4.94665 1.35017 2.90488 2.50192 1.14297L2.9231 0.625C3.3964 0.789957 3.87947 0.927523 4.36976 1.03702C5.56408 1.32194 6.73843 1.67978 7.88569 2.10836C8.78077 2.45253 9.57798 2.99735 10.2113 3.6977C10.9043 4.53112 11.2716 5.57287 11.249 6.64092C11.2572 7.15612 11.1527 7.66719 10.9424 8.14084C10.732 8.61449 10.4205 9.04008 10.0282 9.38987C9.78349 9.61001 9.50819 9.79624 9.21025 9.9432C9.42576 11.0747 9.51783 12.225 9.48494 13.375H7.73309V13.0571C7.73309 10.8556 7.7331 8.57755 5.18771 6.64092L6.25592 5.30469C7.31152 6.0741 8.15086 7.08586 8.69753 8.2479C8.74637 8.2067 8.77687 8.19493 8.8318 8.14195C9.05518 7.95457 9.23084 7.72013 9.34516 7.45677C9.45947 7.19341 9.50939 6.9082 9.49105 6.62324C9.52303 5.93619 9.28794 5.2624 8.8318 4.73372C8.38361 4.23982 7.8211 3.85463 7.18982 3.60939C6.50771 3.33841 5.8059 3.11608 5.09003 2.94422C4.60781 2.81472 4.13171 2.68525 3.69222 2.5322C2.85409 3.87534 2.43059 5.42115 2.47141 6.98823C2.47141 8.58934 2.92312 9.27215 3.31988 9.61356C3.60778 9.85649 3.97508 9.99404 4.35754 10.0021C4.74781 9.93284 5.12463 9.80575 5.47459 9.62534L6.08499 9.37223L6.74424 10.9204C6.59774 10.9792 6.43902 11.0558 6.27421 11.1382C5.69346 11.468 5.03709 11.654 4.36365 11.6797C3.54423 11.6702 2.75649 11.373 2.14788 10.8438C1.36046 10.1728 0.750069 8.94838 0.750069 7.02941Z'
        fill='currentColor'
      />
    </svg>
  ),
  'yield-plus': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M7.5 8.5V13H8.5V8.5H13V7.5H8.5V3H7.5V7.5H3V8.5H7.5Z'
        fill='currentColor'
      />
    </svg>
  ),
  'yield-cross': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M7.29288 8.00002L2.97977 12.3131L3.68688 13.0202L7.99999 8.70712L12.3131 13.0202L13.0202 12.3131L8.70709 8.00002L13.0202 3.6869L12.3131 2.9798L7.99999 7.29291L3.68687 2.9798L2.97977 3.6869L7.29288 8.00002Z'
        fill='currentColor'
      />
    </svg>
  ),
  'yield-edit': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M6.85717 7.89043L6.07438 9.92567L8.10962 9.14289L12.9393 4.31317L11.6869 3.06071L6.85717 7.89043ZM14.9191 2.33338L13.6464 3.60606L12.394 2.35361L13.6667 1.08093L14.9191 2.33338ZM6.15006 7.18333C6.05112 7.28226 5.97405 7.40086 5.92382 7.53146L5.14104 9.56669C4.82998 10.3754 5.62462 11.1701 6.43336 10.859L8.4686 10.0762C8.59919 10.026 8.71779 9.94893 8.81673 9.84999L15.6262 3.04049C16.0168 2.64997 16.0168 2.0168 15.6262 1.62628L14.3738 0.373826C13.9833 -0.0166981 13.3501 -0.0166988 12.9596 0.373825L6.15006 7.18333ZM2 3.00004H6V4.00004H2L2 14H12V10H13V14C13 14.5523 12.5523 15 12 15H2C1.44772 15 1 14.5523 1 14V4.00004C1 3.44776 1.44772 3.00004 2 3.00004Z'
        fill='currentColor'
      />
    </svg>
  ),
  'yield-link': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M7.58579 10.4142C8.36684 11.1953 9.63316 11.1953 10.4142 10.4142L10.463 10.3655L10.4869 10.3416L10.5105 10.3179L10.6021 10.2263L10.6132 10.2152L10.6243 10.2042L10.6352 10.1932L10.6462 10.1823L10.657 10.1714L10.6678 10.1607L10.6785 10.15L10.6891 10.1393L10.6997 10.1288L10.7102 10.1183L10.7206 10.1078L10.731 10.0975L10.7412 10.0872L10.7515 10.077L10.7616 10.0668L10.7717 10.0567L10.7818 10.0467L10.7917 10.0367L10.8016 10.0268L10.8114 10.017L10.8212 10.0072L10.8309 9.99753L10.8405 9.9879L10.8501 9.97833L10.8596 9.96882L10.869 9.95938L10.8784 9.95L10.8877 9.94069L10.897 9.93144L10.9062 9.92225L10.9153 9.91312L10.9244 9.90405L10.9334 9.89505L10.9423 9.88611L10.9512 9.87723L10.96 9.86841L10.9688 9.85965L10.9775 9.85095L10.9861 9.84231L10.9947 9.83373L11.0032 9.8252L11.0117 9.81674L11.0201 9.80834L11.0284 9.79999L11.0367 9.79171L11.0449 9.78348L11.0531 9.77531L11.0612 9.76719L11.0693 9.75914L11.0773 9.75113L11.0852 9.74319L11.0931 9.7353L11.101 9.72747L11.1087 9.71969L11.1165 9.71197L11.1241 9.7043L11.1317 9.69669L11.1393 9.68913L11.1468 9.68163L11.1543 9.67418L11.1616 9.66678L11.169 9.65944L11.1763 9.65214L11.1835 9.6449L11.1907 9.63772L11.1978 9.63058L11.2049 9.6235L11.212 9.61647L11.2189 9.60949L11.2259 9.60256L11.2328 9.59568L11.2396 9.58885L11.2464 9.58207L11.2531 9.57534L11.2598 9.56865L11.2664 9.56202L11.273 9.55544L11.2795 9.5489L11.286 9.54242L11.2925 9.53597L11.2988 9.52958L11.3052 9.52324L11.3115 9.51694L11.3177 9.51069L11.3239 9.50448L11.3301 9.49832L11.3362 9.49221L11.3423 9.48614L11.3483 9.48012L11.3543 9.47414L11.3602 9.4682L11.3661 9.46231L11.372 9.45647L11.3778 9.45066L11.3835 9.44491L11.3892 9.43919L11.3949 9.43352L11.4005 9.42788L11.4061 9.4223L11.4117 9.41675L11.4172 9.41124L11.4226 9.40578L11.4281 9.40035L11.4335 9.39497L11.4388 9.38963L11.4441 9.38433L11.4494 9.37906L11.4546 9.37384L11.4598 9.36865L11.4649 9.36351L11.47 9.3584L11.4751 9.35333L11.4801 9.3483L11.4851 9.34331L11.4901 9.33835L11.495 9.33343L11.4999 9.32855L11.5047 9.32371L11.5095 9.3189L11.5143 9.31412L11.519 9.30939L11.5237 9.30468L11.5284 9.30001L11.533 9.29538L11.5376 9.29078L11.5422 9.28622L11.5467 9.28169L11.5512 9.27719L11.5557 9.27273L11.5601 9.2683L11.5645 9.2639L11.5689 9.25953L11.5732 9.2552L11.5775 9.2509L11.5818 9.24662L11.586 9.24238L11.5903 9.23817L11.5944 9.234L11.5986 9.22985L11.6027 9.22573L11.6068 9.22164L11.6108 9.21758L11.6149 9.21355L11.6189 9.20955L11.6228 9.20558L11.6268 9.20163L11.6307 9.19772L11.6346 9.19383L11.6385 9.18996L11.6423 9.18613L11.6461 9.18232L11.6499 9.17854L11.6536 9.17478L11.6574 9.17105L11.6611 9.16735L11.6648 9.16367L11.6684 9.16001L11.672 9.15638L11.6756 9.15278L11.6792 9.1492L11.6828 9.14564L11.6863 9.1421L11.6898 9.13859L11.6933 9.1351L11.6968 9.13164L11.7002 9.12819L11.7037 9.12477L11.7071 9.12137L11.7104 9.11799L11.7138 9.11463L11.7171 9.1113L11.7204 9.10798L11.7237 9.10468L11.727 9.10141L11.7303 9.09815L11.7335 9.09491L11.7367 9.09169L11.7399 9.08849L11.7431 9.08531L11.7463 9.08215L11.7494 9.079L11.7526 9.07587L11.7557 9.07276L11.7588 9.06967L11.7618 9.06659L11.7649 9.06353L11.7679 9.06048L11.771 9.05746L11.774 9.05444L11.777 9.05144L11.78 9.04846L11.7829 9.04549L11.7859 9.04253L11.7888 9.03959L11.7918 9.03666L11.7947 9.03375L11.7976 9.03085L11.8005 9.02796L11.8033 9.02508L11.8062 9.02222L11.8091 9.01936L11.8119 9.01652L11.8147 9.0137L11.8176 9.01088L11.8204 9.00807L11.8232 9.00527L11.8259 9.00248L11.8287 8.99971L11.8315 8.99694L11.8342 8.99418L11.837 8.99143L11.8397 8.98869L11.8425 8.98595L11.8452 8.98323L11.8479 8.98051L11.8506 8.9778L11.8533 8.9751L11.856 8.9724L11.8587 8.96971L11.8614 8.96703L11.8641 8.96435L11.8667 8.96168L11.8694 8.95901L11.8721 8.95635L11.8747 8.9537L11.8774 8.95104L11.88 8.9484L11.8827 8.94575L11.8853 8.94311L11.888 8.94047L11.8906 8.93784L11.8932 8.93521L11.8958 8.93258L11.8985 8.92995L11.9011 8.92732L11.9037 8.9247L11.9063 8.92208L11.909 8.91946L11.9116 8.91684L11.9142 8.91421L11.9168 8.91159L11.9195 8.90897L11.9221 8.90635L11.9247 8.90373L11.9273 8.9011L11.9299 8.89848L11.9326 8.89585L11.9352 8.89322L11.9378 8.89059L11.9405 8.88795L11.9431 8.88532L11.9458 8.88268L11.9484 8.88003L11.951 8.87738L11.9537 8.87473L11.9564 8.87207L11.959 8.86941L11.9617 8.86675L11.9644 8.86408L11.967 8.8614L11.9697 8.85871L11.9724 8.85603L11.9751 8.85333L11.9778 8.85063L11.9805 8.84792L11.9832 8.8452L11.986 8.84247L11.9887 8.83974L11.9914 8.837L11.9942 8.83425L11.9969 8.83149L11.9997 8.82872L12.0025 8.82594L12.0053 8.82316L12.0081 8.82036L12.0109 8.81755L12.0137 8.81473L12.0165 8.8119L12.0194 8.80906L12.0222 8.80621L12.0251 8.80335L12.028 8.80047L12.0308 8.79758L12.0337 8.79468L12.0367 8.79176L12.0396 8.78884L12.0425 8.78589L12.0455 8.78294L12.0485 8.77997L12.0514 8.77699L12.0544 8.77399L12.0575 8.77097L12.0605 8.76794L12.0635 8.7649L12.0666 8.76184L12.0697 8.75876L12.0728 8.75566L12.0759 8.75255L12.079 8.74942L12.0821 8.74628L12.0853 8.74311L12.0885 8.73993L12.0917 8.73673L12.0949 8.73351L12.0982 8.73028L12.1014 8.72702L12.1047 8.72374L12.108 8.72045L12.1113 8.71713L12.1146 8.71379L12.118 8.71044L12.1214 8.70706L12.1248 8.70366L12.1282 8.70023L12.1316 8.69679L12.1351 8.69332L12.1386 8.68984L12.1421 8.68632L12.1456 8.68279L12.1492 8.67923L12.1528 8.67565L12.1564 8.67204L12.16 8.66841L12.1637 8.66476L12.1673 8.66108L12.1711 8.65738L12.1748 8.65365L12.1785 8.64989L12.1823 8.64611L12.1861 8.6423L12.19 8.63846L12.1938 8.6346L12.1977 8.63071L12.2016 8.6268L12.2056 8.62285L12.2095 8.61888L12.2136 8.61488L12.2176 8.61085L12.2216 8.60679L12.2257 8.6027L12.2298 8.59858L12.234 8.59443L12.2382 8.59025L12.2424 8.58604L12.2466 8.5818L12.2509 8.57753L12.2552 8.57323L12.2595 8.5689L12.2639 8.56453L12.2683 8.56013L12.2727 8.5557L12.2772 8.55124L12.2817 8.54674L12.2862 8.54221L12.2908 8.53764L12.2954 8.53305L12.3 8.52841L12.3047 8.52375L12.3094 8.51904L12.3141 8.5143L12.3189 8.50953L12.3237 8.50472L12.3286 8.49988L12.3334 8.49499L12.3384 8.49007L12.3433 8.48512L12.3483 8.48013L12.3533 8.4751L12.3584 8.47003L12.3635 8.46492L12.3687 8.45977L12.3738 8.45459L12.3791 8.44937L12.3843 8.4441L12.3896 8.4388L12.395 8.43346L12.4004 8.42807L12.4058 8.42265L12.4112 8.41719L12.4167 8.41168L12.4223 8.40613L12.4279 8.40054L12.4335 8.39491L12.4392 8.38924L12.4449 8.38352L12.4507 8.37776L12.4565 8.37196L12.4623 8.36611L12.4682 8.36022L12.4741 8.35429L12.4801 8.34831L12.4861 8.34229L12.4922 8.33622L12.4983 8.33011L12.5045 8.32395L12.5107 8.31774L12.5169 8.31149L12.5232 8.30519L12.5296 8.29884L12.536 8.29245L12.5424 8.28601L12.5489 8.27953L12.5554 8.27299L12.562 8.26641L12.5687 8.25977L12.5753 8.25309L12.5821 8.24636L12.5888 8.23958L12.5957 8.23275L12.6026 8.22587L12.6095 8.21894L12.6165 8.21196L12.6235 8.20493L12.6306 8.19784L12.6377 8.19071L12.6449 8.18352L12.6521 8.17628L12.6594 8.16899L12.6668 8.16165L12.6742 8.15425L12.6816 8.1468L12.6891 8.1393L12.6967 8.13174L12.7043 8.12413L12.712 8.11646L12.7197 8.10874L12.7275 8.10096L12.7353 8.09313L12.7432 8.08524L12.7511 8.07729L12.7591 8.06929L12.7672 8.06123L12.7753 8.05312L12.7835 8.04495L12.7917 8.03672L12.8 8.02843L12.8083 8.02009L12.8167 8.01168L12.8252 8.00322L12.8337 7.9947L12.8423 7.98612L12.8509 7.97748L12.8596 7.96878L12.8684 7.96002L12.8772 7.9512L12.8861 7.94232L12.895 7.93338L12.9041 7.92437L12.9131 7.91531L12.9222 7.90618L12.9314 7.89699L12.9407 7.88774L12.95 7.87843L12.9594 7.86905L12.9688 7.85961L12.9783 7.8501L12.9879 7.84053L12.9975 7.8309L13.0072 7.8212L13.017 7.81144L13.0268 7.80161L13.0367 7.79171L13.0467 7.78175L13.0567 7.77173L13.0668 7.76163L13.077 7.75148L13.0872 7.74125L13.0975 7.73096L13.1078 7.72059L13.1183 7.71017L13.1288 7.69967L13.1393 7.6891L13.15 7.67847L13.1607 7.66777L13.1714 7.65699L13.1823 7.64615L13.1932 7.63524L13.2042 7.62426L13.2152 7.6132L13.2263 7.60208L13.4142 7.41421C14.1953 6.63317 14.1953 5.36683 13.4142 4.58579C12.6332 3.80474 11.3668 3.80474 10.5858 4.58579L9.8131 5.35847L9.106 4.65136L9.87868 3.87868C11.0503 2.70711 12.9497 2.70711 14.1213 3.87868C15.2929 5.05025 15.2929 6.94975 14.1213 8.12132L11.1213 11.1213C9.94975 12.2929 8.05025 12.2929 6.87868 11.1213C5.70711 9.94975 5.70711 8.05025 6.87868 6.87868L6.98468 6.77268L7.69178 7.47979L7.58579 7.58579C6.80474 8.36684 6.80474 9.63317 7.58579 10.4142ZM2.58579 12.4142C3.36684 13.1953 4.63316 13.1953 5.41421 12.4142L5.46295 12.3655L5.48687 12.3416L5.5105 12.3179L5.60208 12.2263L5.6132 12.2152L5.62426 12.2042L5.63524 12.1932L5.64615 12.1823L5.65699 12.1714L5.66777 12.1607L5.67847 12.15L5.6891 12.1393L5.69967 12.1288L5.71017 12.1183L5.72059 12.1078L5.73096 12.0975L5.74125 12.0872L5.75148 12.077L5.76163 12.0668L5.77173 12.0567L5.78175 12.0467L5.79171 12.0367L5.80161 12.0268L5.81143 12.017L5.8212 12.0072L5.8309 11.9975L5.84053 11.9879L5.8501 11.9783L5.8596 11.9688L5.86905 11.9594L5.87842 11.95L5.88774 11.9407L5.89699 11.9314L5.90618 11.9222L5.91531 11.9131L5.92437 11.9041L5.93338 11.895L5.94232 11.8861L5.9512 11.8772L5.96002 11.8684L5.96878 11.8596L5.97748 11.8509L5.98612 11.8423L5.9947 11.8337L6.00322 11.8252L6.01168 11.8167L6.02009 11.8083L6.02021 11.8082L6.72732 12.5153L6.12132 13.1213C4.94975 14.2929 3.05025 14.2929 1.87868 13.1213C0.707107 11.9497 0.707107 10.0503 1.87868 8.87868L4.87868 5.87868C6.05025 4.70711 7.94975 4.70711 9.12132 5.87868C10.2929 7.05025 10.2929 8.94975 9.12132 10.1213L8.84864 10.394L8.14153 9.6869L8.14996 9.67847L8.16066 9.66777L8.17143 9.65699L8.18228 9.64615L8.19319 9.63524L8.20417 9.62426L8.21522 9.6132L8.22635 9.60208L8.41421 9.41421C9.19526 8.63317 9.19526 7.36684 8.41421 6.58579C7.63316 5.80474 6.36683 5.80474 5.58579 6.58579L2.58579 9.58579C1.80474 10.3668 1.80474 11.6332 2.58579 12.4142Z'
        fill='currentColor'
      />
    </svg>
  ),
  'yield-launch': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M15 2C15 1.44772 14.5523 1 14 1H9.99999V2H13.2929L8.97977 6.31311L9.68688 7.02022L14 2.70711V6H15V2ZM11 13H4C3.44772 13 3 12.5523 3 12V5C3 4.44772 3.44772 4 4 4H8V3H4C2.89543 3 2 3.89543 2 5V12C2 13.1046 2.89543 14 4 14H11C12.1046 14 13 13.1046 13 12V8H12V12C12 12.5523 11.5523 13 11 13Z'
        fill='currentColor'
      />
    </svg>
  ),
  'yield-arrow-right': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M8.68689 13.6869L13.6667 8.7071C14.0572 8.31658 14.0572 7.68341 13.6667 7.29289L8.68689 2.31311L7.97978 3.02022L12.4596 7.5H2V8.5H12.4596L7.97978 12.9798L8.68689 13.6869Z'
        fill='currentColor'
      />
    </svg>
  ),
  'yield-chevron-up': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M7.29289 5.3333C7.68342 4.94277 8.31658 4.94277 8.70711 5.3333L13.0202 9.64641L12.3131 10.3535L8 6.04041L3.68689 10.3535L2.97978 9.64641L7.29289 5.3333Z'
        fill='currentColor'
      />
    </svg>
  ),
  'yield-chevron-down': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M8.70711 10.6666C8.31658 11.0572 7.68342 11.0572 7.2929 10.6666L2.97978 6.35353L3.68689 5.64642L8 9.95954L12.3131 5.64642L13.0202 6.35353L8.70711 10.6666Z'
        fill='currentColor'
      />
    </svg>
  ),
  'yield-trash': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M7 2H9V3H7V2ZM6 3V2C6 1.44772 6.44772 1 7 1H9C9.55228 1 10 1.44772 10 2V3H12.2762H14V4H13.2763C13.2762 4.01839 13.2757 4.03688 13.2747 4.05547L12.7191 14.0555C12.6897 14.5854 12.2514 15 11.7207 15H4.27932C3.74859 15 3.3103 14.5854 3.28086 14.0555L2.7253 4.05547C2.72427 4.03688 2.72375 4.01839 2.72373 4H2V3H3.72376H6ZM9 4H7H3.72376L4.27932 14L11.7207 14L12.2762 4H9ZM6 12V6H7V12H6ZM9 12V6H10V12H9Z'
        fill='currentColor'
      />
    </svg>
  ),
  'yield-mail': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M12.819 4H3.18103L8 8.01581L12.819 4ZM2 4.31752V12H14V4.31752L8.64018 8.78403C8.26934 9.09307 7.73066 9.09307 7.35982 8.78403L2 4.31752ZM1 4C1 3.44772 1.44772 3 2 3H14C14.5523 3 15 3.44772 15 4V12C15 12.5523 14.5523 13 14 13H2C1.44772 13 1 12.5523 1 12V4Z'
        fill='currentColor'
      />
    </svg>
  ),
  'yield-calendar': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M5 0V2H11V0H12V2H14C14.5523 2 15 2.44772 15 3V5V6V13C15 13.5523 14.5523 14 14 14H2C1.44772 14 1 13.5523 1 13V6V5V3C1 2.44772 1.44772 2 2 2H4V0H5ZM11 3V4H12V3H14V5H2V3H4V4H5V3H11ZM14 6V13H2V6H14ZM4 11H5V12H4V11ZM5 9H4V10H5V9ZM11 9H12V10H11V9ZM12 7H11V8H12V7ZM6.33333 11H7.33333V12H6.33333V11ZM7.33333 9H6.33333V10H7.33333V9ZM6.33333 7H7.33333V8H6.33333V7ZM9.66667 11H8.66667V12H9.66667V11ZM8.66667 9H9.66667V10H8.66667V9ZM9.66667 7H8.66667V8H9.66667V7Z'
        fill='currentColor'
      />
    </svg>
  ),
  'yield-fields': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M6 1H1V7.2457L6 8.67427V1ZM7 1C7 0.447715 6.55228 0 6 0H1C0.447715 0 0 0.447715 0 1V7.2457C0 7.69218 0.295977 8.08456 0.725279 8.20722L5.72528 9.63579C6.36409 9.81831 7 9.33865 7 8.67427V1ZM6 11.7601L2 10.649V14H6V11.7601ZM7 11.7601C7 11.3109 6.70046 10.9168 6.26764 10.7966L2.26764 9.68546C1.63045 9.50846 1 9.98765 1 10.649V14C1 14.5523 1.44772 15 2 15H6C6.55228 15 7 14.5523 7 14V11.7601ZM13 3H9L9 7H13V3ZM9 2C8.44771 2 8 2.44772 8 3V7C8 7.55228 8.44771 8 9 8H13C13.5523 8 14 7.55228 14 7V3C14 2.44772 13.5523 2 13 2H9ZM9 10H14V11.6667L11.5 15H9L9 10ZM8 10C8 9.44771 8.44771 9 9 9H14C14.5523 9 15 9.44771 15 10V11.6667C15 11.883 14.9298 12.0936 14.8 12.2667L12.3 15.6C12.1111 15.8518 11.8148 16 11.5 16H9C8.44771 16 8 15.5523 8 15V10Z'
        fill='currentColor'
      />
    </svg>
  ),
  'yield-crop-rotation': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M2.20444 6.44709C2.54629 5.17129 3.29956 4.04394 4.34743 3.23988C5.3953 2.43582 6.67919 2 8 2C9.32081 2 10.6047 2.43582 11.6526 3.23988C12.7004 4.04394 13.4537 5.17129 13.7956 6.44709L14.7615 6.18827C14.3627 4.69984 13.4838 3.38459 12.2613 2.44653C11.0388 1.50846 9.54094 1 8 1C6.45906 1 4.96118 1.50846 3.73867 2.44653C2.51616 3.38459 1.63734 4.69984 1.23851 6.18827L2.20444 6.44709ZM1.54892 8.01578C1.40474 8.00746 1.26401 8.06189 1.16294 8.16506C1.06188 8.26822 1.01036 8.41005 1.02164 8.55402C1.30359 12.1512 4.30365 14.9842 7.96932 14.9999C7.9795 15 7.98961 14.9997 7.99966 14.9991C8.00993 14.9997 8.02028 15 8.03069 15C11.6964 14.9842 14.6964 12.1513 14.9784 8.55404C14.9897 8.41006 14.9381 8.26824 14.8371 8.16508C14.736 8.06191 14.5953 8.00748 14.4511 8.0158C11.5688 8.18214 9.03741 9.67925 8.00001 11.8296C6.96263 9.67924 4.43122 8.18212 1.54892 8.01578ZM7.49995 13.9794C4.77806 13.7548 2.57102 11.7132 2.09518 9.07057C5.24239 9.47945 7.48708 11.6006 7.49995 13.9794ZM8.00001 9C8.5523 9 9.00001 8.55228 9.00001 8C9.00001 7.44772 8.5523 7 8.00001 7C7.44773 7 7.00001 7.44772 7.00001 8C7.00001 8.55228 7.44773 9 8.00001 9ZM11 5C11 5.55228 10.5523 6 10 6C9.44773 6 9.00001 5.55228 9.00001 5C9.00001 4.44772 9.44773 4 10 4C10.5523 4 11 4.44772 11 5ZM6.00001 6C6.5523 6 7.00001 5.55228 7.00001 5C7.00001 4.44772 6.5523 4 6.00001 4C5.44773 4 5.00001 4.44772 5.00001 5C5.00001 5.55228 5.44773 6 6.00001 6ZM13.9048 9.07059C13.429 11.7132 11.222 13.7549 8.50007 13.9795C8.51293 11.6006 10.7576 9.47947 13.9048 9.07059Z'
        fill='currentColor'
      />
    </svg>
  ),
  'yield-notes': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M10.686 10.2359C9.72951 11.1836 8.59956 12.3031 8 14.0082C7.40044 12.3031 6.27049 11.1836 5.31399 10.2359C5.2399 10.1625 5.16685 10.0902 5.09517 10.0186C3.87971 8.80563 3 7.80416 3 5.95238C3 3.22475 5.23105 1 8 1C10.769 1 13 3.22475 13 5.95238C13 7.80416 12.1203 8.80563 10.9048 10.0186C10.8332 10.0901 10.7601 10.1625 10.686 10.2359L10.686 10.2359ZM14 5.95238C14 8.35359 12.7286 9.61662 11.4168 10.9199C10.2349 12.0941 9.02011 13.3009 8.67271 15.4022C8.61745 15.7365 8.33884 16 8 16C7.66116 16 7.38256 15.7365 7.32729 15.4022C6.97989 13.3009 5.76511 12.0941 4.58318 10.9199C3.27135 9.61662 2 8.35359 2 5.95238C2 2.66497 4.68629 0 8 0C11.3137 0 14 2.66497 14 5.95238ZM9 6C9 6.55228 8.55228 7 8 7C7.44772 7 7 6.55228 7 6C7 5.44772 7.44772 5 8 5C8.55228 5 9 5.44772 9 6ZM10 6C10 7.10457 9.10457 8 8 8C6.89543 8 6 7.10457 6 6C6 4.89543 6.89543 4 8 4C9.10457 4 10 4.89543 10 6Z'
        fill='currentColor'
      />
    </svg>
  ),
  'yield-sensor': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <g clipPath='url(#clip0_1_1203)'>
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M12 2H4V4H12V2ZM4 6V5H12V6H4ZM4 7V8H12V7H4ZM4 1C3.44772 1 3 1.44772 3 2V8C3 8.55228 3.44772 9 4 9H6L6 16L7 16L7 9H9L9 16L10 16L10 9H12C12.5523 9 13 8.55228 13 8V2C13 1.44772 12.5523 1 12 1H4Z'
          fill='currentColor'
        />
      </g>
      <defs>
        <clipPath id='clip0_1_1203'>
          <rect width='16' height='16' />
        </clipPath>
      </defs>
    </svg>
  ),
  'yield-productivity-map': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M4 2.33334H12L12 8.33334H11V7.16667V6.66667H10.5H8.83333H7.5V5.83334V5.33334H7H6V4.5V4H5.5H4V2.33334ZM3 5V4V2.33334C3 1.78105 3.44772 1.33334 4 1.33334H12C12.5523 1.33334 13 1.78105 13 2.33334V8.33334V9.33334V14.0149C13 14.6771 12.368 15.1564 11.7304 14.9778L3.73037 12.7378C3.29854 12.6169 3 12.2233 3 11.7749V5ZM12 9.33334H10.5H10V8.83334V7.66667H9V10.5V11H8.5H7V12.6149L12 14.0149V9.33334ZM8 7.66667V10H6.5H6V10.5V12.3349L4 11.7749V5H5V5.83334V6.33334H5.5H6.5V7.16667V7.66667H7H8Z'
        fill='currentColor'
      />
    </svg>
  ),
  'yield-field-data': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M3 2H12V5H3L3 2ZM3 6L3 9H8V10H3L3 13H8V14H3C2.44771 14 2 13.5523 2 13V2C2 1.44772 2.44772 1 3 1H12C12.5523 1 13 1.44772 13 2V8H12V6H3ZM4.5 4C4.77614 4 5 3.77614 5 3.5C5 3.22386 4.77614 3 4.5 3C4.22386 3 4 3.22386 4 3.5C4 3.77614 4.22386 4 4.5 4ZM5 7.5C5 7.77614 4.77614 8 4.5 8C4.22386 8 4 7.77614 4 7.5C4 7.22386 4.22386 7 4.5 7C4.77614 7 5 7.22386 5 7.5ZM4.5 12C4.77614 12 5 11.7761 5 11.5C5 11.2239 4.77614 11 4.5 11C4.22386 11 4 11.2239 4 11.5C4 11.7761 4.22386 12 4.5 12ZM10 15L10 10H14V11.6667L11.5 15H10ZM9 10C9 9.44771 9.44772 9 10 9H14C14.5523 9 15 9.44772 15 10V11.6667C15 11.883 14.9298 12.0936 14.8 12.2667L12.3 15.6C12.1111 15.8518 11.8148 16 11.5 16H10C9.44772 16 9 15.5523 9 15V10Z'
        fill='currentColor'
      />
    </svg>
  ),
  'yield-upload': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M10.962 5.1869L11.0804 5.92541L11.8223 6.02049C13.0504 6.1779 14 7.22884 14 8.50002C14 9.88073 12.8807 11 11.5 11H10.6667V12H11.5C13.433 12 15 10.433 15 8.50002C15 6.71928 13.6701 5.24916 11.9494 5.02861C11.6439 3.12275 9.99197 1.66669 8 1.66669C6.00803 1.66669 4.35615 3.12275 4.05062 5.0286C2.32986 5.24915 1 6.71929 1 8.50002C1 10.433 2.567 12 4.5 12H5.33333V11H4.5C3.11929 11 2 9.88073 2 8.50002C2 7.22884 2.94965 6.1779 4.17775 6.02049L4.91962 5.9254L5.03801 5.18689C5.26703 3.75832 6.50698 2.66669 8 2.66669C9.49302 2.66669 10.733 3.75832 10.962 5.1869ZM8.70711 6.33333L11.6869 9.31311L10.9798 10.0202L8.5 7.54044L8.5 15L7.5 15L7.5 7.54044L5.02022 10.0202L4.31311 9.31311L7.29289 6.33333C7.68342 5.94281 8.31658 5.94281 8.70711 6.33333Z'
        fill='currentColor'
      />
    </svg>
  ),
  'yield-modem': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M1.5 2C1.22386 2 1 2.22386 1 2.5C1 2.77614 1.22386 3 1.5 3H5V4H3C2.44772 4 2 4.44772 2 5V14C2 14.5523 2.44772 15 3 15H13C13.5523 15 14 14.5523 14 14V5C14 4.44772 13.5523 4 13 4H11V3H14.5C14.7761 3 15 2.77614 15 2.5C15 2.22386 14.7761 2 14.5 2H11H10H9.5C9.22386 2 9 2.22386 9 2.5C9 2.77614 9.22386 3 9.5 3H10V4H6V3H6.5C6.77614 3 7 2.77614 7 2.5C7 2.22386 6.77614 2 6.5 2H6H5H1.5ZM3 5H5H6H10H11H13V14H3L3 5ZM9.5 12C9.22386 12 9 12.2239 9 12.5C9 12.7761 9.22386 13 9.5 13H11.5C11.7761 13 12 12.7761 12 12.5C12 12.2239 11.7761 12 11.5 12H9.5Z'
        fill='currentColor'
      />
    </svg>
  ),
  'yield-guide': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M7.5 3H1V13H7.5V3ZM7.5 2H8.5H15C15.5523 2 16 2.44772 16 3V13C16 13.5523 15.5523 14 15 14H8.5H7.5H1C0.447715 14 0 13.5523 0 13V3C0 2.44772 0.447715 2 1 2H7.5ZM8.5 13H15V3H8.5V13ZM2 6H6.66667V5H2V6ZM9.33333 6H14V5H9.33333V6ZM14 8H9.33333V7H14V8ZM2 8H6.66667V7H2V8ZM12 10H9.33333V9H12V10ZM2 10H6.66667V9H2V10Z'
        fill='currentColor'
      />
    </svg>
  ),
  'yield-mobile': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M11.6667 2H4.33333L4.33333 14H11.6667L11.6667 2ZM4.33333 1C3.78105 1 3.33333 1.44772 3.33333 2V14C3.33333 14.5523 3.78105 15 4.33333 15H11.6667C12.219 15 12.6667 14.5523 12.6667 14V2C12.6667 1.44772 12.219 1 11.6667 1H4.33333ZM9 12C9 12.5523 8.55229 13 8 13C7.44772 13 7 12.5523 7 12C7 11.4477 7.44772 11 8 11C8.55229 11 9 11.4477 9 12ZM6.66667 3C6.39052 3 6.16667 3.22386 6.16667 3.5C6.16667 3.77614 6.39052 4 6.66667 4H9.33333C9.60948 4 9.83333 3.77614 9.83333 3.5C9.83333 3.22386 9.60948 3 9.33333 3H6.66667Z'
        fill='currentColor'
      />
    </svg>
  ),
  'yield-user': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M2 8C2 9.55209 2.58933 10.9665 3.55642 12.0318C3.99784 11.4006 4.72852 11 5.53659 11H10.4634C11.2715 11 12.0022 11.4006 12.4436 12.0318C13.4107 10.9665 14 9.55209 14 8C14 4.68629 11.3137 2 8 2C4.68629 2 2 4.68629 2 8ZM8 14C6.60447 14 5.32022 13.5236 4.30103 12.7245C4.54617 12.2862 5.01374 12 5.53659 12H10.4634C10.9863 12 11.4538 12.2862 11.699 12.7245C10.6798 13.5236 9.39553 14 8 14ZM8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1ZM10 7C10 8.10457 9.10457 9 8 9C6.89543 9 6 8.10457 6 7C6 5.89543 6.89543 5 8 5C9.10457 5 10 5.89543 10 7ZM11 7C11 8.65685 9.65685 10 8 10C6.34315 10 5 8.65685 5 7C5 5.34315 6.34315 4 8 4C9.65685 4 11 5.34315 11 7Z'
        fill='currentColor'
      />
    </svg>
  ),
  'yield-exit': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M2 1C1.44772 1 1 1.44772 1 2V14C1 14.5523 1.44771 15 2 15H8C8.55228 15 9 14.5523 9 14V12H8V14H2V2L8 2V4H9V2C9 1.44772 8.55228 1 8 1H2ZM15.6667 7.29289C16.0572 7.68342 16.0572 8.31658 15.6667 8.7071L12.3536 12.0202L11.6464 11.3131L14.4596 8.5H6V7.5H14.4596L11.6464 4.68688L12.3536 3.97978L15.6667 7.29289Z'
        fill='currentColor'
      />
    </svg>
  ),
  'yield-settings': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M12.2846 12.9473C12.7328 13.0989 13.2259 12.9156 13.4663 12.5081L14.6463 10.5082C14.8872 10.0998 14.8083 9.57842 14.4573 9.25969L13.425 8.3223V7.65127L14.4466 6.74984C14.8062 6.4326 14.8899 5.90482 14.6463 5.49185L13.4663 3.49185C13.228 3.08805 12.7412 2.90398 12.2954 3.04913L11.0009 3.47061L10.4272 3.13703L10.1606 1.80388C10.0671 1.33646 9.65669 1 9.18001 1H6.82001C6.34252 1 5.93165 1.33757 5.83901 1.80599L5.57187 3.15673L5.00096 3.48748L3.71537 3.05271C3.26721 2.90114 2.77415 3.08438 2.53374 3.49185L1.35374 5.49185C1.11282 5.90019 1.19175 6.42158 1.54274 6.74031L2.57501 7.6777V8.34873L1.55339 9.25016C1.19385 9.5674 1.11009 10.0952 1.35374 10.5082L2.53374 12.5081C2.77198 12.912 3.25878 13.096 3.70459 12.9509L4.99913 12.5294L5.5728 12.863L5.83943 14.1961C5.93291 14.6635 6.34333 15 6.82001 15H9.18001C9.65749 15 10.0684 14.6624 10.161 14.194L10.4281 12.8433L10.9991 12.5125L12.2846 12.9473ZM9.53501 3.775L10.885 4.56L12.605 4L13.785 6L12.425 7.2V8.765L13.785 10L12.605 12L10.89 11.42L9.53501 12.205L9.18001 14H6.82001L6.46501 12.225L5.11501 11.44L3.39501 12L2.21501 10L3.57501 8.8V7.235L2.21501 6L3.39501 4L5.11001 4.58L6.46501 3.795L6.82001 2H9.18001L9.53501 3.775ZM10 8C10 9.10457 9.10457 10 8 10C6.89543 10 6 9.10457 6 8C6 6.89543 6.89543 6 8 6C9.10457 6 10 6.89543 10 8ZM11 8C11 9.65685 9.65685 11 8 11C6.34315 11 5 9.65685 5 8C5 6.34315 6.34315 5 8 5C9.65685 5 11 6.34315 11 8Z'
        fill='currentColor'
      />
    </svg>
  ),
  'yield-user-access': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 48 48'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M7.66927 33.0953C4.76798 29.8996 3 25.6563 3 21C3 11.0589 11.0589 3 21 3C30.9411 3 39 11.0589 39 21H42C42 9.40202 32.598 0 21 0C9.40202 0 0 9.40202 0 21C0 32.598 9.40202 42 21 42V39C16.8134 39 12.9607 37.5707 9.90307 35.1734C10.6385 33.8586 12.0412 33 13.6098 33H27V30H13.6098C11.1856 30 8.99351 31.2017 7.66927 33.0953ZM21 24C24.3137 24 27 21.3137 27 18C27 14.6863 24.3137 12 21 12C17.6863 12 15 14.6863 15 18C15 21.3137 17.6863 24 21 24ZM21 27C25.9706 27 30 22.9706 30 18C30 13.0294 25.9706 9 21 9C16.0294 9 12 13.0294 12 18C12 22.9706 16.0294 27 21 27ZM33 36C31.3431 36 30 37.3431 30 39V45C30 46.6569 31.3431 48 33 48H45C46.6569 48 48 46.6569 48 45V39C48 37.3431 46.6569 36 45 36V32C45 28.6863 42.3137 26 39 26C35.6863 26 33 28.6863 33 32V36ZM36 36H42V32C42 30.3431 40.6569 29 39 29C37.3431 29 36 30.3431 36 32V36ZM33 39V45H45V39H42H36H33Z'
        fill='currentColor'
      />
    </svg>
  ),
  'yield-users': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 48 48'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M29.75 7.00481C30.8901 6.34655 32.1835 6 33.5 6C34.8165 6 36.1098 6.34655 37.25 7.00481C38.3901 7.66307 39.3369 8.60986 39.9952 9.75C40.6535 10.8901 41 12.1835 41 13.5C41 14.8165 40.6535 16.1099 39.9952 17.25C39.3369 18.3901 38.3901 19.3369 37.25 19.9952C36.1098 20.6535 34.8165 21 33.5 21C32.1835 21 30.8901 20.6535 29.75 19.9952L28.25 22.5933C29.8462 23.5148 31.6569 24 33.5 24C35.3431 24 37.1538 23.5148 38.75 22.5933C40.3462 21.6717 41.6717 20.3462 42.5933 18.75C43.5148 17.1538 44 15.3431 44 13.5C44 11.6569 43.5148 9.8462 42.5933 8.25C41.6717 6.6538 40.3462 5.3283 38.75 4.40673C37.1538 3.48517 35.3431 3 33.5 3C31.6569 3 29.8462 3.48517 28.25 4.40673L29.75 7.00481ZM33 30V27H37.5C43.299 27 48 31.701 48 37.5V45H45V37.5C45 33.3579 41.6421 30 37.5 30H33ZM15 21C19.1421 21 22.5 17.6421 22.5 13.5C22.5 9.35787 19.1421 6.00001 15 6.00001C10.8579 6.00001 7.5 9.35787 7.5 13.5C7.5 17.6421 10.8579 21 15 21ZM15 24C20.799 24 25.5 19.299 25.5 13.5C25.5 7.70102 20.799 3.00001 15 3.00001C9.20101 3.00001 4.5 7.70102 4.5 13.5C4.5 19.299 9.20101 24 15 24ZM10.5 30C6.35786 30 3 33.3579 3 37.5V45H0V37.5C0 31.701 4.70101 27 10.5 27H19.5C25.299 27 30 31.701 30 37.5V45H27V37.5C27 33.3579 23.6421 30 19.5 30H10.5Z'
        fill='currentColor'
      />
    </svg>
  ),
  'yield-john-deere': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 48 48'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M37.3304 9.64213C39.6547 10.4177 41.4599 12.2544 42.1429 14.5762C43.0275 17.583 43.5 20.7423 43.5 24.0033C43.5 27.2626 43.0288 30.4205 42.1453 33.4254C41.4629 35.7461 39.6582 37.5819 37.3346 38.3573C33.1848 39.7419 28.6653 40.5 24.0063 40.5C19.3271 40.5 14.8061 39.7375 10.6499 38.3419C8.32971 37.5628 6.52916 35.7259 5.84887 33.4063C4.96944 30.4076 4.5 27.2567 4.5 24.0033C4.5 20.7492 4.9694 17.5969 5.84919 14.5967C6.52982 12.2756 8.33224 10.4378 10.6546 9.65846C14.8095 8.26407 19.3291 7.5 24.0063 7.5C28.6647 7.5 33.1819 8.25784 37.3304 9.64213Z'
        fill='#367C2B'
        stroke='#FFDE00'
        strokeWidth='3'
      />
      <path
        d='M29.0215 29.8382H23.9652V18H29.0633C30.498 18 31.733 18.237 32.7684 18.711C33.8038 19.1811 34.6001 19.8574 35.1573 20.7399C35.7191 21.6224 36 22.6782 36 23.9075C36 25.1407 35.7191 26.2004 35.1573 27.0867C34.6001 27.973 33.7992 28.6532 32.7545 29.1272C31.7145 29.6012 30.4701 29.8382 29.0215 29.8382ZM26.9809 27.6936H28.8961C29.7876 27.6936 30.5374 27.5626 31.1457 27.3006C31.7586 27.0347 32.2182 26.6243 32.5247 26.0694C32.8358 25.5106 32.9913 24.79 32.9913 23.9075C32.9913 23.0328 32.8358 22.3179 32.5247 21.763C32.2182 21.2081 31.7609 20.7996 31.1526 20.5376C30.5444 20.2755 29.7946 20.1445 28.9031 20.1445H26.9809V27.6936Z'
        fill='#FFDE00'
      />
      <path
        d='M18.1677 18H21V26.2543C21 27.0173 20.8037 27.6802 20.411 28.2428C20.0228 28.8054 19.4824 29.2389 18.7897 29.5434C18.0971 29.8478 17.2919 30 16.3743 30C15.5581 30 14.817 29.8748 14.1508 29.6243C13.489 29.3699 12.964 28.9846 12.5758 28.4682C12.1876 27.948 11.9957 27.2948 12.0001 26.5087H14.8523C14.8611 26.8208 14.9339 27.0886 15.0706 27.3121C15.2118 27.5318 15.4037 27.7013 15.6464 27.8208C15.8934 27.9364 16.1846 27.9942 16.5199 27.9942C16.8728 27.9942 17.1706 27.9287 17.4133 27.7977C17.6603 27.6628 17.8478 27.4663 17.9758 27.2081C18.1037 26.9499 18.1677 26.632 18.1677 26.2543V18Z'
        fill='#FFDE00'
      />
    </svg>
  ),
  'yield-app-store': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M9.78601 2.68158L9.78592 2.6815L9.77917 2.68983C9.58371 2.93089 9.30717 3.15116 9.00487 3.31C8.81533 3.40959 8.62882 3.47823 8.45919 3.51597C8.53071 2.99059 8.81303 2.4666 9.12858 2.10962L9.1286 2.10963L9.13184 2.10589C9.46524 1.72112 9.95931 1.41112 10.4296 1.26189C10.3594 1.77942 10.1163 2.29165 9.78601 2.68158ZM10.6938 4.71191C11.0381 4.71191 12.076 4.74257 12.8947 5.577C12.7449 5.70285 12.572 5.865 12.4068 6.05875C11.9608 6.58182 11.5219 7.37485 11.5219 8.46127C11.5219 9.71522 12.081 10.5754 12.6434 11.1144C12.9062 11.3662 13.172 11.5506 13.3873 11.6777C13.3609 11.7464 13.3303 11.8228 13.2953 11.9054C13.1413 12.2688 12.9059 12.7453 12.5765 13.2236C12.2569 13.6814 11.9643 14.0866 11.6425 14.3818C11.3298 14.6688 11.0296 14.8142 10.6952 14.8142C10.3167 14.8142 10.0884 14.7153 9.76593 14.5755L9.74332 14.5657C9.38715 14.4114 8.95852 14.2348 8.27785 14.2348C7.61207 14.2348 7.15506 14.4164 6.78229 14.5732C6.77129 14.5778 6.7604 14.5824 6.74962 14.5869C6.39849 14.7347 6.16421 14.8333 5.83245 14.8333C5.54406 14.8333 5.27031 14.7074 4.95705 14.419C4.63487 14.1225 4.31988 13.7 3.94777 13.1806C3.15081 12.0521 2.5 10.2792 2.5 8.61382C2.5 6.01207 4.16499 4.75005 5.69684 4.75005C6.08083 4.75005 6.45013 4.8755 6.84761 5.03313C6.88868 5.04942 6.93096 5.06642 6.974 5.08372C7.12648 5.14502 7.28849 5.21015 7.43986 5.26108C7.63563 5.32694 7.86158 5.38665 8.09507 5.38665C8.32651 5.38665 8.55685 5.32152 8.75318 5.25415C8.88955 5.20735 9.04153 5.14744 9.1891 5.08928C9.25299 5.0641 9.31605 5.03924 9.37666 5.01592C9.80519 4.85101 10.2369 4.71191 10.6938 4.71191Z'
        fill='black'
        stroke='black'
      />
    </svg>
  ),
  'yield-google-play': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M7.91539 7.67554L2.05243 13.8181C2.11826 14.0514 2.23657 14.2666 2.39832 14.4472C2.56007 14.6278 2.76096 14.769 2.98566 14.8601C3.21035 14.9512 3.45289 14.9896 3.69473 14.9726C3.93657 14.9556 4.17131 14.8834 4.38101 14.7618L10.9779 11.0046L7.91539 7.67554Z'
        fill='#EA4335'
      />
      <path
        d='M13.8439 6.63138L10.991 4.99744L7.77995 7.81533L11.0041 10.9958L13.8351 9.37936C14.0859 9.24805 14.296 9.05059 14.4426 8.80838C14.5892 8.56618 14.6667 8.28848 14.6667 8.00537C14.6667 7.72227 14.5892 7.44456 14.4426 7.20236C14.296 6.96016 14.0859 6.76269 13.8351 6.63138H13.8439Z'
        fill='#FBBC04'
      />
      <path
        d='M2.05244 2.16211C2.017 2.29313 1.99937 2.42832 2.00002 2.56404V13.4162C2.00037 13.5519 2.01799 13.6869 2.05244 13.8181L8.11637 7.83284L2.05244 2.16211Z'
        fill='#4285F4'
      />
      <path
        d='M7.95908 7.99012L10.991 4.99747L4.40286 1.22281C4.15501 1.07765 3.87311 1.00077 3.58589 1C3.23899 0.999316 2.90139 1.11208 2.62454 1.32109C2.34769 1.53011 2.14678 1.82392 2.05243 2.15774L7.95908 7.99012Z'
        fill='#34A853'
      />
    </svg>
  ),
  'agrilever-square': (
    <svg
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M11.9882 2.31668C10.5176 3.36221 9.35294 5.26009 8.34118 8.24895C7.56471 10.5673 7.38824 12.7266 7.75294 15.4654C7.87059 16.2723 7.96471 17.0337 7.97647 17.136C8.01176 17.4087 6.87059 18.7611 6.12941 19.3407C5.36471 19.943 4.49412 20.3635 3.84706 20.4431C3.2 20.5226 3 20.6931 3 21.159C3 21.6136 3.24706 21.7727 3.97647 21.7727C5.16471 21.7727 6.30588 21.1818 7.75294 19.7953C8.25882 19.318 8.70588 18.9316 8.74118 18.9316C8.78824 18.9316 8.98824 19.1475 9.17647 19.3975C9.58824 19.9317 10.6706 20.7613 11.5176 21.1704C12.3647 21.5909 14.2 22 15.2235 22C17.0235 22 19.1647 21.2727 20.4706 20.2158C20.8588 19.9089 21 19.7157 21 19.4884C21 18.7952 20.2235 18.602 19.5647 19.1248C18.2 20.2271 16.3176 20.7385 14.4118 20.5226C12.4824 20.2953 10.8353 19.3975 9.98824 18.1133C9.83529 17.8747 9.70588 17.6247 9.70588 17.5451C9.70588 17.4656 9.89412 17.0905 10.1294 16.7155C10.9765 15.3631 11.7882 13.6585 12.5294 11.6583C12.8353 10.806 13.2588 9.27176 13.5294 7.96484C14.0353 5.53283 13.9529 2.86218 13.3412 2.14621C13.1176 1.89619 12.4824 1.97574 11.9882 2.31668ZM12.4118 4.74868C12.4118 5.90786 12.2118 7.4648 11.9294 8.53306C11.8 9.0331 11.6353 9.64678 11.5765 9.8968C11.1765 11.4083 9.54118 15.1699 9.27059 15.1813C9.07059 15.1813 8.95294 13.8517 9.03529 12.2379C9.14118 10.1582 9.64706 8.40805 10.8353 6.03287C11.4353 4.8396 12.0706 3.93044 12.3059 3.93044C12.3647 3.93044 12.4118 4.30547 12.4118 4.74868Z'
        fill='#4BA45A'
      />
    </svg>
  ),
  'agrilever-small': (
    <svg
      width='14'
      height='14'
      viewBox='0 0 14 14'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M6.99216 0.221676C6.01177 0.95355 5.23529 2.28206 4.56078 4.37427C4.04314 5.99712 3.92549 7.5086 4.16863 9.42579C4.24706 9.9906 4.3098 10.5236 4.31765 10.5952C4.34118 10.7861 3.58039 11.7328 3.08627 12.1385C2.57647 12.5601 1.99608 12.8545 1.56471 12.9101C1.13333 12.9658 1 13.0852 1 13.4113C1 13.7295 1.16471 13.8409 1.65098 13.8409C2.44314 13.8409 3.20392 13.4272 4.16863 12.4567C4.50588 12.1226 4.80392 11.8521 4.82745 11.8521C4.85882 11.8521 4.99216 12.0033 5.11765 12.1783C5.39216 12.5522 6.11373 13.1329 6.67843 13.4193C7.24314 13.7136 8.46667 14 9.14902 14C10.349 14 11.7765 13.4909 12.6471 12.751C12.9059 12.5363 13 12.401 13 12.2419C13 11.7566 12.4824 11.6214 12.0431 11.9873C11.1333 12.759 9.87843 13.117 8.60784 12.9658C7.32157 12.8067 6.22353 12.1783 5.65882 11.2793C5.55686 11.1123 5.47059 10.9373 5.47059 10.8816C5.47059 10.8259 5.59608 10.5634 5.75294 10.3009C6.31765 9.35419 6.85882 8.16092 7.35294 6.76081C7.55686 6.16417 7.83922 5.09023 8.01961 4.17539C8.35686 2.47298 8.30196 0.603523 7.89412 0.102349C7.7451 -0.0726648 7.32157 -0.0169788 6.99216 0.221676ZM7.27451 1.92408C7.27451 2.7355 7.14118 3.82536 6.95294 4.57314C6.86667 4.92317 6.75686 5.35275 6.71765 5.52776C6.45098 6.5858 5.36078 9.21895 5.18039 9.22691C5.04706 9.22691 4.96863 8.29616 5.02353 7.16652C5.09412 5.71073 5.43137 4.48564 6.22353 2.82301C6.62353 1.98772 7.04706 1.35131 7.20392 1.35131C7.24314 1.35131 7.27451 1.61383 7.27451 1.92408Z'
        fill='currentColor'
      />
    </svg>
  ),
  'agrology-square': (
    <svg
      width='40'
      height='40'
      viewBox='0 0 40 40'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <rect width='40' height='40' rx='8' fill='white' />
      <path
        d='M7.91833 16.7185V8H16.6369C22.1954 8 26.7129 12.5221 26.7129 18.0761V26.7946H17.9944C12.4404 26.7946 7.91833 22.2771 7.91833 16.7185ZM24.1217 18.0761C24.1217 13.9484 20.7645 10.5912 16.6369 10.5912H10.5096V16.7185C10.5096 20.8462 13.8667 24.2033 17.9944 24.2033H24.1217V18.0761Z'
        fill='#6CC72B'
      />
      <path
        d='M28.4377 26.7078L22.6131 20.8832C22.8974 19.9568 22.6727 18.9065 21.9389 18.1727C20.8841 17.1179 19.1642 17.1179 18.1094 18.1727C17.0545 19.2276 17.0545 20.9474 18.1094 22.0023C18.8432 22.7361 19.8888 22.9562 20.8153 22.6765L26.6398 28.501C26.495 28.9707 26.4812 29.471 26.6 29.9479C26.7189 30.4248 26.9657 30.8602 27.314 31.2069C28.3689 32.2618 30.0887 32.2618 31.1436 31.2069C32.1984 30.1521 32.1984 28.4322 31.1436 27.3774C30.7958 27.0306 30.3602 26.7851 29.8835 26.6671C29.4068 26.5492 28.907 26.5632 28.4377 26.7078ZM19.3477 19.411C19.7192 19.0395 20.3291 19.0395 20.7006 19.411C21.0721 19.7825 21.0721 20.3925 20.7006 20.764C20.3291 21.1355 19.7192 21.1355 19.3477 20.764C18.9762 20.3925 18.9762 19.7825 19.3477 19.411ZM29.9099 29.9732C29.5384 30.3447 28.9284 30.3447 28.5569 29.9732C28.1854 29.6017 28.1854 28.9918 28.5569 28.6203C28.9284 28.2488 29.5384 28.2488 29.9099 28.6203C30.2814 28.9918 30.2814 29.5971 29.9099 29.9732Z'
        fill='#F98A3C'
      />
    </svg>
  ),
  'agropro-square': (
    <svg
      width='40'
      height='40'
      viewBox='0 0 40 40'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <rect width='40' height='40' rx='8' fill='white' />
      <path
        d='M18.3068 26.9471L18.4761 26.7355L16.8253 25.5926C16.529 23.1376 15.4285 20.0053 12.9735 18.3122C10.4338 16.6614 7.00523 16.8307 4.63486 17.5926C4.38089 17.6773 4.25391 17.8889 4.25391 18.1429C4.42322 20.1323 5.1428 22.2064 6.24332 23.7725C7.80946 26.0159 10.5608 27.0741 13.947 26.7778C14.7936 26.6931 15.5555 26.5662 16.2327 26.3969L17.8835 27.5397L17.9682 27.4127L18.3068 26.9471ZM9.92586 21.4445C9.67189 21.7831 9.75655 22.2487 10.1375 22.5027C10.3491 22.672 10.6031 22.672 10.8148 22.6297L15.1745 25.5926C13.6931 25.8889 12.1692 25.9312 10.8571 25.6349C7.47084 24.9577 6.11634 21.8254 5.6084 20.0476C5.43909 19.4551 5.31211 18.9048 5.26978 18.4392C7.97877 17.7196 10.6454 17.9736 12.4232 19.1588C14.074 20.3016 15.2592 22.3757 15.6825 24.873L11.3227 21.9101C11.3227 21.6561 11.1957 21.4445 10.9841 21.3175C10.6454 21.0212 10.1375 21.1058 9.92586 21.4445Z'
        fill='#8AC440'
      />
      <path
        d='M35.2803 17.3388C33.3332 16.7886 31.1744 16.7462 29.312 17.1695C26.6453 17.8044 24.6559 20.0055 23.7247 23.2647C23.4707 24.1113 23.3438 24.8309 23.3014 25.5505L21.6506 26.6933L21.7353 26.8203L22.0316 27.2859L22.2009 27.4975L23.8517 26.3547C26.1797 26.9896 29.566 27.0743 32.021 25.4235C34.476 23.6457 35.5342 20.3864 35.7035 17.8891C35.6612 17.5928 35.5342 17.4235 35.2803 17.3388ZM29.2274 22.5875C29.439 22.6722 29.693 22.6298 29.9046 22.5028C30.2432 22.2489 30.3279 21.7833 30.1162 21.4023C29.9046 21.0637 29.3967 20.979 29.058 21.1907C28.8464 21.3177 28.7194 21.5293 28.7194 21.7833L24.3596 24.7886C24.6136 23.3071 25.1215 21.8679 25.8834 20.7251C27.7459 17.8468 31.1744 17.7198 33.0369 17.8891C33.6718 17.9314 34.1797 18.0161 34.6877 18.1431C34.3914 20.9367 33.2062 23.3071 31.4707 24.5769C29.8199 25.7198 27.4496 26.0584 24.9522 25.5928L29.2274 22.5875Z'
        fill='#00A8D5'
      />
      <path
        d='M20.5925 25.8466V23.9842C22.455 22.4181 24.4444 19.7091 24.4867 16.7461C24.4444 13.6985 22.3703 10.9895 20.4232 9.42337C20.2116 9.25406 19.9576 9.25406 19.746 9.42337C18.1798 10.6932 16.8677 12.4287 16.1904 14.2064C15.1745 16.7461 15.8095 19.6244 17.9682 22.2911C18.5184 22.9683 19.0264 23.5186 19.5767 23.9419V25.8043C18.5184 26.016 17.7142 26.9472 17.7142 28.0477C17.7142 29.3175 18.7301 30.3334 19.9999 30.3334C21.2698 30.3334 22.2856 29.3175 22.2856 28.0477C22.328 26.9895 21.6084 26.1006 20.5925 25.8466ZM20.6772 17.381C20.8888 17.2117 20.9735 17.0001 20.9735 16.7461C20.9735 16.3228 20.6349 15.9419 20.2116 15.9419C19.7883 15.9419 19.4497 16.2805 19.4497 16.7038C19.4497 16.9578 19.5343 17.1694 19.746 17.3387L19.6613 22.672C18.6031 21.6138 17.7142 20.344 17.2063 19.1165C15.9364 15.8996 17.7565 13.0636 18.9841 11.6244C19.4073 11.1588 19.7883 10.7779 20.1269 10.4392C22.2433 12.2593 23.5132 14.6297 23.5555 16.7884C23.5132 18.7779 22.455 20.9366 20.6349 22.7144L20.6772 17.381ZM20.0423 29.4445C19.2804 29.4445 18.6878 28.8519 18.6878 28.09C18.6878 27.3281 19.2804 26.7355 20.0423 26.7355C20.8042 26.7355 21.3968 27.3281 21.3968 28.09C21.3968 28.8519 20.8042 29.4445 20.0423 29.4445Z'
        fill='#00A59B'
      />
    </svg>
  ),
  'info-circle': (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
    >
      <g id='_Info'>
        <path
          id='Icon'
          fill-rule='evenodd'
          clip-rule='evenodd'
          d='M12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21ZM1.5 12C1.5 6.20101 6.20101 1.5 12 1.5C17.799 1.5 22.5 6.20101 22.5 12C22.5 17.799 17.799 22.5 12 22.5C6.20101 22.5 1.5 17.799 1.5 12ZM9 18H12H13.5H16.5V16.5H13.5V11.25V10.5H12.75H9.75V12H12V16.5H9V18ZM14 7.25C14 7.94036 13.4404 8.5 12.75 8.5C12.0596 8.5 11.5 7.94036 11.5 7.25C11.5 6.55964 12.0596 6 12.75 6C13.4404 6 14 6.55964 14 7.25Z'
          fill='#1C1C1E'
        />
      </g>
    </svg>
  ),
  'soil-sampling': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fill-rule='evenodd'
        clip-rule='evenodd'
        d='M10.9999 2H9.99985V6.42482C9.99985 6.63616 10.0668 6.84207 10.1911 7.01299L13.6898 11.8237C14.6512 13.1456 13.7069 15 12.0723 15H3.92739C2.29282 15 1.34851 13.1456 2.30992 11.8237L5.80859 7.01299C5.9329 6.84207 5.99985 6.63616 5.99985 6.42482V2L4.99986 2V1H10.9999V2ZM8.99985 2H8.99985V6.42482C8.99985 6.8475 9.13377 7.25932 9.38238 7.60116L10.3997 9H5.59999L6.61733 7.60116C6.86594 7.25932 6.99985 6.8475 6.99985 6.42482V3.66667H6.99985V2H8.99985ZM4.87272 10L3.11866 12.4118C2.63795 13.0728 3.11011 14 3.92739 14H12.0723C12.8896 14 13.3618 13.0728 12.8811 12.4118L11.127 10H4.87272Z'
        fill='currentColor'
      />
    </svg>
  ),
  edit: (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fill-rule='evenodd'
        clip-rule='evenodd'
        d='M6.85717 7.89056L6.07438 9.92579L8.10962 9.14301L12.9393 4.31329L11.6869 3.06083L6.85717 7.89056ZM14.9191 2.33351L13.6464 3.60618L12.394 2.35373L13.6667 1.08105L14.9191 2.33351ZM6.15006 7.18345C6.05112 7.28239 5.97405 7.40099 5.92382 7.53158L5.14104 9.56681C4.82998 10.3756 5.62462 11.1702 6.43336 10.8591L8.4686 10.0764C8.59919 10.0261 8.71779 9.94905 8.81673 9.85012L15.6262 3.04061C16.0168 2.65009 16.0168 2.01692 15.6262 1.6264L14.3738 0.373948C13.9833 -0.0165761 13.3501 -0.0165767 12.9596 0.373948L6.15006 7.18345ZM2 3.00016H6V4.00016H2L2 14.0002H12V10.0002H13V14.0002C13 14.5524 12.5523 15.0002 12 15.0002H2C1.44772 15.0002 1 14.5524 1 14.0002V4.00016C1 3.44788 1.44772 3.00016 2 3.00016Z'
        fill='currentColor'
      />
    </svg>
  ),
  'btn-check-mark': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fill-rule='evenodd'
        clip-rule='evenodd'
        d='M7.70698 10.6667C7.31646 11.0572 6.6833 11.0572 6.29277 10.6667L3.31299 7.68692L4.02009 6.97982L6.99988 9.9596L12.313 4.64648L13.0201 5.35359L7.70698 10.6667Z'
        fill='currentColor'
      />
    </svg>
  ),
  'btn-cross': (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fill-rule='evenodd'
        clip-rule='evenodd'
        d='M7.2931 7.99972L2.97999 12.3128L3.6871 13.0199L8.0002 8.70682L12.3133 13.0199L13.0204 12.3128L8.70731 7.99972L13.0204 3.6866L12.3133 2.97949L8.0002 7.29261L3.68709 2.97949L2.97998 3.6866L7.2931 7.99972Z'
        fill='currentColor'
      />
    </svg>
  ),
};

export default Icons;
