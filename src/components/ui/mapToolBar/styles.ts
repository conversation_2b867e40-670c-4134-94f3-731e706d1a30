import { styled } from 'linaria/react';

export const StyledMapTopPanel = styled.div`
  position: absolute;
  z-index: 3;
  left: 0;
  right: 0;
  top: 0;
  padding: 5px;
  display: flex;
  align-items: flex-start;
`;

type StyledTopPanelItemProps = {
  isFluid?: boolean;
};

export const StyledTopPanelItem = styled.div<StyledTopPanelItemProps>`
  flex-grow: ${props => (props.isFluid ? '1' : '0')};
  flex-shrink: ${props => (props.isFluid ? '1' : '0')};
  flex-basis: auto;
  min-width: 0;
  margin-right: 10px;
  border-radius: 20px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.25);
  background-color: #222;
`;

type StyledBottomMapWidgetProps = {
  isCropsList?: boolean;
};
export const StyledBottomMapWidget = styled.div<StyledBottomMapWidgetProps>`
  position: absolute;
  left: 5px;
  bottom: 5px;
  padding: ${props => (props.isCropsList ? '10px 10px 0 10px' : '10px')};
  background: #222222;
  font-family: Roboto, Arial, sans-serif;
  font-size: 10px;
  color: #ebebeb;
  z-index: 3;
  border-radius: 6px;
  max-width: 76%;
  min-width: 320px;
`;
