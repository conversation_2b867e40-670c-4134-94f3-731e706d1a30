import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { useForm } from 'react-hook-form';
import cx from 'classnames';

import Icon from 'components/Icon';
import Button from 'components/ui/Button/Button';
import Saga from 'components/saga/Saga';
import ClearableInput from 'components/ClearableInput';
import RenderAboveEverything from 'components/RenderAboveEverything';
import ToastContainer from 'features/platform/ToastContainer/ToastContainer';
import { createFolderSaga } from 'sagas/local/modems';

import toasts from 'modules/toasts';
import { useTranslate } from 'utils/use-translate';

const CreateFolderPopup = ({
  onConfirm,
  onCancel,
  history,
  autoFocus,
  files,
}) => {
  const dispatch = useDispatch();
  const { t } = useTranslate();
  const [error, setError] = useState(false);
  const { register, handleSubmit } = useForm();
  const onError = ({ folder }) => {
    setError(true);
    dispatch(
      toasts.actions.push('modems-folder-error', folder.message, {
        target: 'modems-folder-popup',
        type: 'error',
        ttl: Infinity,
      }),
    );
  };
  const onHideToast = () => {
    if (error) {
      setError(false);
      dispatch(toasts.actions.removeGroup('modems-folder-error'));
    }
  };

  return (
    <RenderAboveEverything>
      <Saga saga={createFolderSaga} history={history} />
      <div className='modal modal-editseasons'>
        <ToastContainer id='modems-folder-popup' />
        <div className='modal-outer'>
          <div className='modal-editseasons-content'>
            <form onSubmit={handleSubmit(onConfirm, onError)}>
              <div className='modal-editseasons-content__header'>
                {t('modems.folder.new')}
              </div>
              <div className='modal-editseasons-content__body'>
                <div
                  className={cx('form-group', {
                    'shake-error': error,
                  })}
                >
                  <label
                    className={cx('form-label', {
                      'c-error': error,
                    })}
                  >
                    {t(`modems.folder.new.label`)}
                  </label>
                  <ClearableInput
                    autoFocus={autoFocus}
                    name='folder'
                    ref={register({
                      required: t('modems.folder.error.empty'),
                      validate: {
                        empty: v =>
                          !!v.trim() || t('modems.folder.error.empty'),
                        exists: v =>
                          !files
                            .map(file => file.filename.toLowerCase())
                            .includes(v.toLowerCase()) ||
                          t('modems.folder.error.already_exists', {
                            name: v,
                          }),
                      },
                    })}
                    onChange={() => {
                      onHideToast();
                    }}
                    placeholder={t('modems.folder.new.placeholder')}
                  />
                </div>
              </div>
              <div className='modal-editseasons-content__actions'>
                <Button
                  className='btn btn-primary btn-lg'
                  onClick={() => {
                    onCancel();
                    onHideToast();
                  }}
                >
                  {t('forms.cancel')}
                </Button>
                <Button className='btn btn-success btn-lg' type='submit'>
                  <span className='btn__ico'>
                    <Icon className='ico-check-os' name='check' />
                  </span>
                  {t(`modems.folder.create`)}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </RenderAboveEverything>
  );
};

export default CreateFolderPopup;
