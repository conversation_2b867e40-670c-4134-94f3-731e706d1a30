import { styled } from 'linaria/react';

export const TooltipContainer = styled.div<{
  translateX: number;
  translateY: number;
}>`
  position: absolute;
  z-index: 90;
  top: 0;
  transform: ${({ translateX, translateY }) =>
    `translate3d(${translateX}px, ${translateY}px, 0px)`};
`;

export const TooltipDot = styled.div`
  top: 0;
  left: -1px;
  background-color: #222222;
  position: absolute;
  border-radius: 50%;
  width: 2px;
  height: 2px;
`;
