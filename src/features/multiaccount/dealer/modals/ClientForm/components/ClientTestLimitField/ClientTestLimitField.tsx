import React, { forwardRef, useEffect } from 'react';
import { Controller } from 'react-hook-form';
import { cx } from 'linaria';

// @ts-ignore
import TextInput from 'components/forms/TextInput';
import { ClientHookForm } from 'features/multiaccount/dealer/modals/ClientForm/types';

import { EntityWithLimits } from 'types/multiaccount';
import { ErrorData } from 'modules/entities/types';
import { useTranslate } from 'utils/use-translate';

import { styles } from './ClientTestLimitField.styles';

export type ClientTestLimitFieldProps = {
  form: ClientHookForm;

  requestErrors: Nullable<ErrorData>;
  isPending: boolean;
  client?: EntityWithLimits;
};

const MAX_DEMO_FIELD_LIMIT = 20;

const ClientTestLimitField = forwardRef<
  HTMLInputElement,
  ClientTestLimitFieldProps
>(({ requestErrors, client, form, isPending }, ref) => {
  const { t } = useTranslate();
  const { watch, errors: formErrors, trigger, control } = form;
  const hasLimits = client?.status === 'active' && !!client?.accountUuid;
  const commercialLimits = watch('commercial_limits');
  const testLimits = watch('test_limits');
  const unitType = watch('unitType');
  const validateAtLeastOne = () => {
    return !!commercialLimits || !!testLimits;
  };

  useEffect(() => {
    // trigger validation for limits after changing unitType
    trigger(['test_limits', 'commercial_limits']);
  }, [unitType, testLimits, commercialLimits, trigger]);

  return (
    <div className='form-group'>
      <label className='form-label'>
        {
          // @ts-ignore
          t('multiaccount.invite-client-modal.column.test-limits.title.max', {
            max: MAX_DEMO_FIELD_LIMIT,
          })
        }
      </label>
      <label className='form-label-descr'>
        {t('multiaccount.invite-client-modal.column.test-limits.subtitle')}
      </label>
      <Controller
        name='test_limits'
        control={control}
        disabled={isPending}
        rules={{
          validate: validateAtLeastOne,
          required: hasLimits,
          min: {
            value: client?.usedTestLimit || 0,
            // @ts-ignore
            message: t('multiaccount.dealer.error.trial.min', {
              value: client?.usedTestLimit || 0,
            }),
          },
          max: {
            value: 20,
            // @ts-ignore
            message: t('multiaccount.dealer.error.trial.max', {
              max: MAX_DEMO_FIELD_LIMIT,
            }),
          },
        }}
        render={({ onChange, value }, { invalid }) => {
          return (
            <>
              <TextInput
                ref={ref}
                id='test_limits'
                disabled={isPending}
                tabIndex={1}
                isValid={!invalid}
                className={cx('size-full __fluid', styles.field)}
                numeric
                positiveOnly
                precision={0}
                value={value}
                onChange={onChange}
                hideArrows
                groupThousands={false}
              />
              {formErrors.test_limits && (
                <div className='error-message c-error'>
                  {formErrors.test_limits.message}
                </div>
              )}
              {requestErrors?.test_limits &&
                requestErrors.test_limits.map(error => (
                  <div key={error} className={styles.fieldError}>
                    {t(`multiaccount.dealer.error.workspace_title.${error}`)}
                  </div>
                ))}
            </>
          );
        }}
      />
    </div>
  );
});

export default ClientTestLimitField;
