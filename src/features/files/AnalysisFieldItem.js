import React, { useState } from 'react';
import { connect, useSelector } from 'react-redux';
import { Control, getModel } from 'react-redux-form';
import cx from 'classnames';
import moment from 'moment';

import Icon from 'components/Icon';
import LinkButton from 'components/LinkButton';
import { DropdownInput } from 'components/ui/DropdownInput/DropdownInput';
import VegetationDatePopover from 'components/VegetationDatePopover';

import fields from 'modules/fields';
import auth from 'modules/auth';
import seasons from 'modules/seasons';

import forms from 'constants/forms';
import { formatDate } from 'utils/format-date';
import { useTranslate } from 'utils/use-translate';
import logEvent from 'sagas/global/logEvent';

const AnalysisFieldItem = ({
  index,
  type,
  ndvi,
  rgb,
  analysisNDVIIndexes,
  analysisRGBIndexes,
  fieldId,
}) => {
  const userID = useSelector(auth.selectors.getUserID);
  const currentSeason = useSelector(seasons.selectors.getCurrent);

  const selectedIndex =
    type === 'rgb'
      ? analysisRGBIndexes[`${type}${index}`]
      : analysisNDVIIndexes[`${type}${index}`];

  const { t } = useTranslate();
  const [isActive, setActive] = useState(true);
  const { items = [], status } = type === 'rgb' ? rgb : ndvi;
  const filteredItems =
    type === 'rgb'
      ? items
      : items.filter(item => item.validity > 0 && item.status === 1);
  const disabled = status !== 'resolved';
  const hasPrevPage = !disabled && selectedIndex > 0;
  const hasNextPage = !disabled && selectedIndex < filteredItems.length - 1;
  const title = `${index}. ${t(`analysis.item.${type}`)}`;
  const currentYear = new Date().getFullYear();

  const renderValue = index => {
    const value = filteredItems[index];
    if (!value) {
      return t('analysis.loading');
    }
    return formatDate(
      value.date,
      t(
        moment(value.date).year() === currentYear
          ? 'fields.ndvi.date_format'
          : 'fields.ndvi.full_date_format',
      ),
    );
  };

  const logChangeDateEvent = (eventName, newSelectedIndex) => {
    let date1Value =
      filteredItems[
        index === 1
          ? newSelectedIndex
          : type === 'rgb'
          ? analysisRGBIndexes[`${type}${1}`]
          : analysisNDVIIndexes[`${type}${1}`]
      ];
    let date2Value =
      filteredItems[
        index === 2
          ? newSelectedIndex
          : type === 'rgb'
          ? analysisRGBIndexes[`${type}${2}`]
          : analysisNDVIIndexes[`${type}${2}`]
      ];

    let date_1 = date1Value.date;
    let date_2 = date2Value.date;

    logEvent(eventName, {
      date_1,
      date_2,
      user_id: userID,
      season_id: currentSeason?.id ?? null,
    });
  };

  const toggleActive = () => {
    setActive(prevActive => !prevActive);
  };

  return (
    <li className='analyses-sidebar__list-item' data-opened={isActive}>
      <div className='analyses-sidebar-header' onClick={toggleActive}>
        <button className='analyses-sidebar-header__arrow' type='button' />
        <div className='analyses-sidebar-header-name'>
          <div className='analyses-sidebar-header-name__value'>{title}</div>
          <div className='analyses-sidebar-header-name__meta'>
            {renderValue(selectedIndex)}
          </div>
        </div>
      </div>
      <div className='analyses-sidebar-body'>
        <Control.select
          className='form-select-sm'
          disabled={disabled}
          component={DropdownInput}
          idKey='index'
          renderValue={renderValue}
          renderTrigger={({
            className,
            placeholder,
            renderValue,
            disabled,
            value,
            isOpen,
            getToggleButtonProps,
            onChange,
            ...otherProps
          }) => {
            return (
              <div className='form-group-grid __sps-default'>
                <div
                  className='form-group-grid__col __fluid'
                  tabIndex={1}
                  {...getToggleButtonProps({ disabled })}
                >
                  <div
                    className={cx('form-select', className, {
                      'form-select--active': isOpen,
                      'form-select--disabled': disabled,
                    })}
                    {...otherProps}
                  >
                    <div className='form-select__ico'>
                      <Icon className='ico-calendar-os' name='calendar-dots' />
                    </div>
                    <div className='form-select__value'>
                      {value != null ? renderValue(value) : placeholder}
                    </div>
                  </div>
                </div>
                <div className='form-group-grid__col __fixed'>
                  <div className='pagination pagination-small'>
                    <LinkButton
                      className={cx('pagination__item', {
                        __disabled: !hasNextPage,
                      })}
                      disabled={!hasNextPage}
                      onClick={() => {
                        logChangeDateEvent(
                          'map-glance-change-date_previous',
                          selectedIndex + 1,
                        );
                        onChange(selectedIndex + 1);
                      }}
                    >
                      <span className='pagination-arrow-back' />
                    </LinkButton>
                    <LinkButton
                      className={cx('pagination__item', {
                        __disabled: !hasPrevPage,
                      })}
                      disabled={!hasPrevPage}
                      onClick={() => {
                        logChangeDateEvent(
                          'map-glance-change-date_next',
                          selectedIndex - 1,
                        );
                        onChange(selectedIndex - 1);
                      }}
                    >
                      <span className='pagination-arrow-forward' />
                    </LinkButton>
                  </div>
                </div>
              </div>
            );
          }}
          renderDropdown={({ value, ...props }) => {
            return (
              <VegetationDatePopover
                fieldId={fieldId}
                value={filteredItems[value]}
                logChangeDateEvent={logChangeDateEvent}
                {...props}
              />
            );
          }}
          placeholder={t('fertilizer.nitrogen.date.placeholder')}
          model={`.${type}${index}`}
        />
      </div>
    </li>
  );
};

const mapStateToProps = (state, ownProps) => ({
  ndvi: fields.selectors.getNDVIByID(state, ownProps.fieldId),
  rgb: fields.selectors.getRGBById(state, ownProps.fieldId),
  analysisNDVIIndexes: getModel(state, forms.analysis),
  analysisRGBIndexes: getModel(state, forms.analysisRGB),
});

export default connect(mapStateToProps)(AnalysisFieldItem);
