import React, { useState } from 'react';
import { Switch, Route } from 'react-router-dom';
import Saga from 'components/saga/Saga';

import MatchColumnsStep from './MatchColumnsStep';
import MatchCropsStep from './MatchCropsStep';
import SaveFieldsStep from './SaveFieldsStep';

import importFieldsSaga from 'sagas/local/import-fields';

const UploadFieldsLastSteps = ({ match, history, location }) => {
  const [valid, setValid] = useState(false);

  return (
    <>
      <Saga
        saga={importFieldsSaga}
        history={history}
        match={match}
        location={location}
        onSetValid={setValid}
      />
      {valid ? (
        <Switch>
          <Route
            path={`${match.path}/match-columns`}
            component={MatchColumnsStep}
          />
          <Route
            path={`${match.path}/match-crops`}
            component={MatchCropsStep}
          />
          <Route
            path={`${match.path}/save-fields`}
            component={SaveFieldsStep}
          />
        </Switch>
      ) : null}
    </>
  );
};

export default UploadFieldsLastSteps;
