import React, { MutableRefObject, PureComponent, SVGProps } from 'react';
import Icons from 'constants/icons';

export type IconName = keyof typeof Icons;

export type IconProps = {
  name: IconName;
  innerRef?: MutableRefObject<HTMLElement>;
} & Partial<SVGProps<SVGElement>>;

class Icon extends PureComponent<IconProps> {
  render() {
    const { innerRef, name, ...otherProps } = this.props;
    const icon = Icons[name];
    if (!icon) {
      return null;
    }
    return React.cloneElement(icon, {
      ref: innerRef,
      ...otherProps,
    });
  }
}

export default Icon;
