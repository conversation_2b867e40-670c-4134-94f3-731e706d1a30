import { Component } from 'react';
import noop from 'lodash/noop';

/**
 * @deprecated. Use useEffect hook instead.
 */
class Lifecycle extends Component {
  static defaultProps = {
    onMount: noop,
    onUnmount: noop,
    onUpdate: noop,
  };
  componentDidMount() {
    this.props.onMount();
  }
  componentDidUpdate() {
    this.props.onUpdate();
  }
  componentWillUnmount() {
    this.props.onUnmount();
  }
  render() {
    return null;
  }
}

export default Lifecycle;
