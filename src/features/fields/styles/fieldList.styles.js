import { css } from 'linaria';
import { styled } from 'linaria/react';

export const styles = {
  expandableTriggerContent: css`
    display: flex;
    align-items: center;
    justify-content: space-between;
  `,
  expandableTriggerTitle: css`
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  `,
  fieldItemHeader: css`
    font-size: 14px;
    font-weight: 500;
    line-height: 19px;
  `,
  fieldGroupTriggerCount: css`
    font-size: 12px;
    color: #a5b2bc;
    line-height: 16px;
    margin-left: 5px;
  `,
  fieldGroupTriggerArea: css`
    font-size: 12px;
    line-height: 16px;
  `,
};

const statusColors = {
  suitable: 'rgba(39, 174, 96, 1)',
  'partially suitable': 'rgba(244, 211, 89, 1)',
  'not suitable': 'rgba(94, 94, 94, 1)',
  failed: 'rgba(255, 59, 48, 1)',
  edited: 'rgba(0, 122, 255, 1)',
};

const statusBordersColors = {
  suitable: 'rgba(39, 174, 96, 0.3)',
  'partially suitable': 'rgba(244, 211, 89, 0.3)',
  'not suitable': 'rgba(183, 193, 201, 1)',
  failed: 'rgba(255, 59, 48, 0.5)',
  edited: 'rgba(0, 122, 255, 0.5)',
};

export const VraStatus = styled.div`
  flex-shrink: 0;
  border-width: 1px;
  border-style: solid;
  border-color: ${props => statusBordersColors[props.status]};
  font-size: 12px;
  font-weight: 500;
  line-height: 16px;
  letter-spacing: 0px;
  text-align: center;
  color: ${props => statusColors[props.status]};
  text-transform: capitalize;
  padding: 2px 4px;
  border-radius: 4px;
  margin-right: -7px;
  margin-left: 10px;
`;
