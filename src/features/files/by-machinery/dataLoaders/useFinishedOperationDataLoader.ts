import { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import entities from 'modules/entities';

import {
  DataLoader,
  ApiDataFieldOperations,
} from 'features/files/by-machinery/types';

const REQUEST_ID = 'useFinishedOperationDataLoader';

export const useFinishedOperationDataLoader = (): DataLoader<
  ApiDataFieldOperations[]
> => {
  const dispatch = useDispatch();
  const fetch = useCallback(() => {
    dispatch(
      entities.actions.request(REQUEST_ID, [
        {
          type: 'fetchMany',
          entityType: 'fieldsDataOperation',
          query: {
            include: ['layer'],
          },
        },
      ]),
    );
  }, [dispatch]);

  const data: ApiDataFieldOperations[] = useSelector(state => {
    return entities.selectors.getAll(state, 'fieldsDataOperation');
  });

  const request = useSelector(state =>
    entities.selectors.getRequestStatus(state, REQUEST_ID),
  );

  useEffect(fetch, [fetch]);

  return [data, request, fetch];
};
