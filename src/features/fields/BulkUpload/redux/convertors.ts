import {
  BatchResponse,
  FieldsFromFilesResponse,
  FieldsUsersFilesFeature,
} from '../types';

export function convertFieldsFromFilesBatchResponseToImportedFields(
  batchResponse: BatchResponse<FieldsFromFilesResponse>,
): FieldsUsersFilesFeature[] {
  return batchResponse.responses
    .map(item => item?.response?.data.features)
    .reduce((acc: FieldsUsersFilesFeature[], features) => {
      for (const feature of features) {
        acc.push({
          id: feature.id!,
          preview_url: feature.preview_url!,
          title: feature.title || `Field ${feature.id}`,
          file_id: feature.field_user_file_id,
        });
      }
      return acc;
    }, []);
}
