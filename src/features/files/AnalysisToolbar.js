import React, { useEffect, useState } from 'react';
import { connect, useSelector } from 'react-redux';

import { getModel } from 'react-redux-form';

import fields from 'modules/fields';
import auth from 'modules/auth';
import seasons from 'modules/seasons';
import forms from 'constants/forms';

import Toggle from 'components/Toggle';

import getRootScoutingUrl from 'utils/get-root-scouting-url';
import { useTranslate } from 'utils/use-translate';
import logEvent from 'sagas/global/logEvent';

const Types = ['grid', 'side-by-side'];

const AnalysisToolbar = ({
  match: {
    params: { type, fieldId },
  },
  history,
  location,
  fieldFillingType,
  ndvi,
  rgb,
  analysisNDVIIndexes,
  analysisRGBIndexes,
}) => {
  const [imageItems, setImageItems] = useState(null);
  const { t } = useTranslate();
  const baseUrl = getRootScoutingUrl(location.pathname);
  const userID = useSelector(auth.selectors.getUserID);
  const currentSeason = useSelector(seasons.selectors.getCurrent);

  useEffect(() => {
    if (fieldFillingType === 'rgb') {
      const { items = [] } = rgb;
      setImageItems(items);
    } else {
      const { items = [] } = ndvi;
      const filteredItems = items.filter(
        item => item.validity > 0 && item.status === 1,
      );
      setImageItems(filteredItems);
    }
  }, [fieldFillingType, ndvi, rgb]);

  const onToggleChange = type => {
    logToggleEvent(type);
    history.replace(`${baseUrl}/fields/${fieldId}/${type}`);
  };

  const logToggleEvent = type => {
    let date_1 = null;
    let date_2 = null;

    if (fieldFillingType === 'rgb') {
      const date1RGBIndex = analysisRGBIndexes?.rgb1 ?? null;
      const date2RGBIndex = analysisRGBIndexes?.rgb2 ?? null;

      date_1 = imageItems?.[date1RGBIndex]?.date ?? null;
      date_2 = imageItems?.[date2RGBIndex]?.date ?? null;
    } else {
      const date1NDVIIndex = analysisNDVIIndexes?.ndvi1 ?? null;
      const date2NDVIIndex = analysisNDVIIndexes?.ndvi2 ?? null;

      date_1 = imageItems?.[date1NDVIIndex]?.date ?? null;
      date_2 = imageItems?.[date2NDVIIndex]?.date ?? null;
    }

    let view = type;
    if (type === 'side-by-side') {
      view = 'slider';
    }

    logEvent('map-glance-view', {
      view,
      date_1,
      date_2,
      user_id: userID,
      season_id: currentSeason?.id ?? null,
      type: fieldFillingType,
    });
  };

  return (
    <div className='map-view-type-control'>
      <Toggle
        theme='dark'
        value={type}
        titleKey='label'
        options={Types.map(type => ({
          label: t(`analysis.type.${type}`),
          id: type,
        }))}
        onChange={onToggleChange}
      />
    </div>
  );
};

const mapStateToProps = (state, ownProps) => {
  let field = fields.selectors.getByID(state, ownProps.match.params.fieldId);

  return {
    fieldFillingType: fields.selectors.getFieldFillingType(state),
    ndvi: fields.selectors.getNDVIByID(state, field.id),
    rgb: fields.selectors.getRGBById(state, field.id),
    analysisNDVIIndexes: getModel(state, forms.analysis),
    analysisRGBIndexes: getModel(state, forms.analysisRGB),
  };
};

export default connect(mapStateToProps)(AnalysisToolbar);
