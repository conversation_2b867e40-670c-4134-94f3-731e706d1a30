import React from 'react';
import cx from 'classnames';

import Icon from 'components/Icon';
import Modal from 'components/ui/Modal/Modal';
import Button from 'components/ui/Button/Button';

import { useTranslate } from 'utils/use-translate';

import { styles } from './Alert.style';

import { AlertType } from 'types/alertState';

const Alert = ({
  show,
  icon,
  title,
  text,
  success,
  danger,
  onCancel,
  onSuccess,
  onDanger,
}: AlertType) => {
  const { t } = useTranslate();

  return (
    <Modal show={show} width={360} onClose={() => {}}>
      <div className={styles.content}>
        {!!icon && <Icon name={icon} />}
        {!!title && <p className={styles.title}>{title}</p>}
        <p className={styles.text}>{text}</p>
        <div className={styles.buttons}>
          {!!onCancel && (
            <Button
              className={cx('btn btn-primary btn-lg', styles.button)}
              onClick={onCancel}
            >
              {t('alert.cancel')}
            </Button>
          )}
          {!!onSuccess && (
            <Button
              className={cx('btn btn-success btn-lg', styles.button)}
              onClick={onSuccess}
            >
              {success || t('alert.success')}
            </Button>
          )}
          {!!onDanger && !!danger && (
            <Button
              className={cx('btn btn-danger btn-lg', styles.button)}
              onClick={onDanger}
            >
              {danger}
            </Button>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default Alert;
