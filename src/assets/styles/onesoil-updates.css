/* Remove safari `bounce` effect on overscroll */
html {
  overflow: hidden;
}
body {
  overflow: auto;
}

.collapsed-marker.hovered svg {
  animation: enlarge 0.1s linear;
}

.leaflet-tooltip.marker-tooltip {
  background: rgba(34, 34, 34, 0.65);
  border-radius: 100px;
  padding-left: 30px;

  line-height: 12px;
  font-size: 12px;
  color: #ffffff;

  animation: slideRight 0.2s linear;
  border: none;
}
.leaflet-tooltip.marker-tooltip:before {
  border: none;
}

.soil-sidebar-gallery__progress {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 25px;
  background: rgba(0, 0, 0, 0.5);
}
.soil-sidebar-gallery__progress .CircularProgressbar {
  height: 100%;
}
.soil-sidebar-gallery__progress .CircularProgressbar-path {
  stroke: var(--color-primary);
}
.soil-sidebar-gallery__progress .CircularProgressbar-text {
  fill: var(--color-primary);
}
.soil-sidebar-gallery__progress .CircularProgressbar-background {
  fill: rgba(0, 0, 0, 0.5);
}

.LinesEllipsis,
.LinesEllipsis-canvas {
  word-wrap: break-word;
}

.map-timeline-scroll::-webkit-scrollbar,
.soil-sidebar-gallery::-webkit-scrollbar {
  display: none;
}

.note-marker-icon {
  transition: transform 0.1s ease;
}
.note-marker-icon > * {
  transition: all 0.5s ease;
}
.note-marker.__collapsed .note-marker-icon__fg {
  opacity: 0;
}
.note-marker.__collapsed .note-marker-icon__bg {
  fill: #298bf2;
  stroke: #fff;
  stroke-width: 2;
  r: 2px;
}
.note-marker.__highlighted .note-marker-icon__bg {
  fill: #ef3e3e;
}
.note-marker.__highlighted .note-marker-icon__fg {
  fill: white;
}
.note-marker.__hovered .note-marker-icon, .note-marker.__active .note-marker-icon {
  transform: scale(1.3);
}

.fields-pane-fields .leaflet-interactive:hover {
  stroke: #ffe767;
  stroke-width: 4;
}
.fields-pane-scouting .leaflet-interactive:hover {
  stroke: white;
}

.fields-table-area__value_small {
  font-size: 12px;
  line-height: 1.25;
  color: #a5b2bc;
}

.map-draw-mode .leaflet-grab {
  cursor: crosshair;
}

.note-popup .leaflet-popup-content-wrapper {
  box-shadow: none;
  background: transparent;
  padding: 0;
}
.note-popup .leaflet-popup-content {
  margin: 0;
}
.note-popup .leaflet-popup-close-button,
.note-popup .leaflet-popup-tip-container {
  display: none;
}
.note-popup .map-tooltip {
  position: initial;
  min-width: 240px;
}

/*
  Make images a little smaller, so that part of third image is
  vissible to hint that scrolling is available
*/
.soil-sidebar-gallery__pic {
  min-width: 118px !important;
  cursor: pointer;
}
.soil-old-notes-list__item {
  box-sizing: border-box;
}
.soil-old-notes-list__item.__selected .soil-old-notes-list-actions,
.soil-old-notes-list__item:hover .soil-old-notes-list-actions {
  display: block;
}
/* .soil-fields-list__item .soil-fields-list-meta__quest {
  opacity: 0;
}
.soil-fields-list__item:hover .soil-fields-list-meta__quest,
.soil-fields-list__item.__selected .soil-fields-list-meta__quest {
  opacity: 1;
} */

.soil-modems-list-meta__date {
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.soil-modems-list-meta__storage {
  font-weight: 500;
  font-size: 12px;
  color: #a5b2bc;
  flex: 0 0 auto;
  white-space: nowrap;
}

.soil-old-notes-list-footer {
  white-space: nowrap;
}

.soil-old-notes-list-footer__col-wrap {
  display: flex;
  margin-right: 5px;
  overflow: hidden;
}

.soil-old-notes-list-footer__col-field {
  color: rgba(94, 94, 94, 1);
}

.soil-old-notes-list-footer__col {
  overflow: hidden;
  text-overflow: ellipsis;
}

.soil-old-notes-list-footer__col-link {
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 5px;
}

.soil-old-notes-list-footer__col a {
  white-space: nowrap;
}

.soil-old-notes-list-footer__col-coord {
  white-space: nowrap;
  flex-shrink: 0;
}

a.soil-old-notes-list-footer__col {
  color: currentColor;
}

.soil-old-notes-list-body p {
  word-break: break-all;
}
/* fix leaflet default p margins */
.leaflet-popup-content .soil-old-notes-list-body p {
  margin-top: 0;
  margin-bottom: 10px;
}

.map-tooltip-footer {
  display: flex;
  justify-content: space-between;
}

/*
  Leaflet measures initial position, and adds `position: relative` when css
  is not yet loaded. This line hotfixes this behaviour
*/
.map-viewer {
  position: absolute !important;
}

.map-viewer .leaflet-image-layer {
  image-rendering: optimizeSpeed;
  image-rendering: -moz-crisp-edges;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: optimize-contrast;
  image-rendering: pixelated;
  -ms-interpolation-mode: nearest-neighbor;
}

.ndvi-pane img {
  image-rendering: optimizeSpeed;
  image-rendering: -moz-crisp-edges;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: optimize-contrast;
  image-rendering: pixelated;
  -ms-interpolation-mode: nearest-neighbor;
}

/* Fix border-radius for map panel in edit field popup */
.modal-editfield-map .map-container {
  border-bottom-right-radius: 8px;
  border-top-right-radius: 8px;
}

.notifications {
  transition: transform 0.5s linear, opacity 0.5s linear;
}


.form-split-group .form-select.__error {
  padding-top: 2px;
  padding-bottom: 2px;
}

/* Add labels to platform apps */
.navigation__badge {
  padding-left: 10px;
}

.hidden-overlay {
  position: absolute;
  top: -10000px;
  left: -10000px;
}

.site-wide-message {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 50px;
  background-color: #ffeba2;
  font-size: 15px;
  color: #676767;
  padding: 15px 22px;
  z-index: 100;
}

.form-input::placeholder {
  color: #a5b2bc;
}

.form-input::-webkit-outer-spin-button,
.form-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.form-input[type=number] {
  -moz-appearance: textfield;
}

.soil-sidebar-empty__ico {
  font-size: 5em;
}
.soil-sidebar-empty__spinner {
  margin-bottom: 15px;
}

.soil-sidebar-body > div:first-child {
  overscroll-behavior: contain;
}

.nitro-table tfoot td {
  vertical-align: top;
  line-height: 1.25;
}

.nitro-table tfoot small {
  font-size: 12px;
}

.soil-sidebar-create input[type='number']::-webkit-inner-spin-button,
.soil-sidebar-create input[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.weather-info-days-timeline__title {
  text-transform: capitalize;
}

/* Capitalize calendar title */
.modal-calendar-header__inner {
  text-transform: capitalize;
}

/* Fix extra scrollbar on weather pane */
.weather-info-days-timeline {
  overflow-y: hidden;
}

/* Hotfix dashboard size */
.map-with-panels__map {
  flex: 2 0 !important;
}
.map-with-panels__content {
  flex: 1 0 !important;
  display: flex;
  flex-direction: column;
}
.map-dashboard {
  flex: 1 0 auto;
}

/* Custom spinner for dashboard panels */
.map-content-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
}

/* Fix phosphor form styles */
.form-group-items-row__col.size-sx {
  display: flex;
  align-items: center;
}
.form-group-items .form-select,
.form-group-items .form-input {
  border-radius: 6px !important;
}
.form-group-items-row__col {
  border-left: none !important;
}
.form-title.__no-underline {
  margin-bottom: 10px;
  padding-bottom: 0;
}
.form-title.__no-underline::after {
  display: none;
}
.form-title small {
  font-weight: normal;
}
.form-hr.__alt {
  height: 1px !important;
  opacity: 0.5;
}

/* Add animation to upload container */
.upload-container {
  pointer-events: none;
  transition: opacity 0.1s linear;
  opacity: 0;
}
.upload-container.__opened {
  opacity: 1;
}

/* Fix native tooltip position */
.leaflet-tooltip .fast-tooltip {
  position: relative;
  pointer-events: all;
}
/* Fix native tooltip style */
.ruler-tooltip.leaflet-tooltip {
  background-color: initial;
  border: none;
  box-shadow: none;
}
.ruler-tooltip.leaflet-tooltip:before {
  content: none;
}

/* fix leaflet default popup for ruler popup */
.ruler-popup.leaflet-popup {
  margin: 0;
}
.ruler-popup .leaflet-popup-content {
  margin: 0;
}
.ruler-popup .leaflet-popup-tip-container {
  display: none;
}
.ruler-popup .leaflet-popup-content-wrapper {
  padding: 0;
}
.ruler-popup .modal-select {
  position: relative;
}

/* Add another button size */
.btn.btn-tooltip {
  z-index: 953;
  padding: 6.5px 10px 5.5px;
  min-height: 26px;
  border-radius: 6px;
  font-size: 12px;
}

/* Fix dropdowns z-index for popups */
.modal-select {
  z-index: 1000 !important;
}

/* Fix file icon background color */
.soil-fields-list[data-type='files'] .soil-fields-list__pic {
  background-color: initial;
}

/* Fix hover for fake button input */
.form-upload:hover .btn-success {
  background-color: var(--color-secondary);
  color: #fff;
}

/* Fix layer order for files app */
.leaflet-map-pane .fields-pane-fields svg {
  z-index: auto;
}

/* Hide file icons */
.soil-fields-list[data-type='files'] .soil-fields-list__pic {
  display: none;
}

/* Disable artificial padding for virtualized fields lists */
.ReactVirtualized__Grid.soil-fields-list:after {
  display: none;
}
/* And add it back to proper div */
/* .ReactVirtualized__Grid.soil-fields-list.__with-actions .ReactVirtualized__Grid__innerScrollContainer {
  margin-bottom: 70px;
} */

/* Add a variant of select modal that fits in any viewport */
.modal-select.__fit-viewport .modal-select__inner {
  max-height: 50vh;
}

/* Implement leaflet-compatible dark tooltip */
.leaflet-tooltip.tooltip-dark {
  background-color: rgba(34, 34, 34, 0.85);
  border-radius: 4px;
  max-width: 300px;
  font-size: 14px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
  padding: 4px 8px 4px;
  border: none;
  color: #fff;
}
.leaflet-tooltip-top.tooltip-dark:before {
  border-top: 3px solid rgba(34, 34, 34, 0.85);
  border-left: 3px solid transparent;
  border-right: 3px solid transparent;
  margin-left: -3px;
  margin-bottom: -9px;
}

/* Add missing hover state to othermonth dates in calendar */
.modal-calendar-dates__othermonth {
  cursor: pointer;
}
.modal-calendar-dates__othermonth .modal-calendar-dates__item:hover {
  background-color: #e9ecef;
}

/* Some custom utility class */
.cursor-pointer {
  cursor: pointer;
}

/* Add popover animation directions */
.modal-nav.__state-initial.__from-left,
.modal-select.__state-initial.__from-left {
  -webkit-transform: translate(20px, 0);
  transform: translate(20px, 0);
  opacity: 0;
}

/* Increase fake file input size to better fit */
.form-upload__input {
  opacity: 0;
}

/* Fix map sizing on edit field page */
.field-edit-bound__map {
  display: flex;
}

/* Safari with Graphic renders some weird ligatures */
html {
  -webkit-font-variant-ligatures: no-common-ligatures;
  font-variant-ligatures: no-common-ligatures;
}
/* Fix even weirder safari bug with font ligatures and `"` symbols */
.form-checkbox__value {
  -webkit-font-variant-ligatures: normal;
  font-variant-ligatures: normal;
}

/* Add checkbox disabled state, without pointer cursor */
.form-checkbox.__disabled {
  cursor: default;
}

/* Fix sidebar bottom padding, adding it manually with markup */
.soil-sidebar-create .soil-sidebar-create__form {
  padding-bottom: 0;
}

/* Remove visual disabled style for form submit buttons with spinners */
.btn.btn-success.__no-visual-disable[disabled] {
  background-color: var(--color-primary) !important;
  color: #fff !important;
  /* hotfix for spinner not fitting inside button */
  padding-top: 0;
  padding-bottom: 0;
}

/* Hide some visual glitch for arrow buttons */
.map-topbar .map-timeline__arrow:before {
  display: none;
}

/* Sidebar header help button variant */
.soil-sidebar .app-nav-help {
  position: absolute;
  right: 16px;
  top: 15px;
}
.app-nav-help {
  width: 20px;
  border-width: 0;
  font-size: 0;
  line-height: 2px;
  vertical-align: top;
  color: #222;
  padding: 3px 0 4px;
  background: none;
}
.app-nav-help svg {
  vertical-align: top;
  width: 16px;
  height: 16px;
}

/* Remove sub-toolbar popup left margin */
.popup-edit-bound-tools-sub {
  left: 0;
}

/* Add number animations in progress bar */
.progress-bar-numbers__count {
  transition: transform 0.4s ease, opacity 0.4s ease;
}

/* Move map below toast when in fullscreen popup */
.popup-edit-bound .map-container {
  z-index: -1;
}

/* Disable hover animations conditionally */
.chart-bubble-note.__opened {
  transform: scale(1.2);
}
.chart-bubble-note.__opened .chart-bubble-note__value {
  transform: translate(0, -0.1px);
}

/* Add missing chart label */
.chart-label {
  background: rgba(255, 255, 255, 0.8);
  border: 0 solid #d6dce1;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  padding: 5px 10px;
  font-weight: 500;
}

/* Add move animation to chart tooltip */
.chart-tooltip {
  z-index: 952 !important;
  transition: left 0.3s linear, top 0.3s linear;
  pointer-events: none;
  transition: opacity 0.15s, transform 0.15s, left 0.3s, top 0.3s;
}
.chart-tooltip.__state-initial {
  opacity: 0;
}
.chart-tooltip.__state-initial[data-direction='from-top'] {
  -webkit-transform: translate(0, -10px);
  transform: translate(0, -10px);
}
.chart-tooltip.__state-initial[data-direction='from-bottom'] {
  -webkit-transform: translate(0, 10px);
  transform: translate(0, 10px);
}
.chart-tooltip.__state-initial[data-direction='from-left'] {
  -webkit-transform: translate(-10px, 0);
  transform: translate(-10px, 0);
}
.chart-tooltip.__state-initial[data-direction='from-right'] {
  -webkit-transform: translate(10px, 0);
  transform: translate(10px, 0);
}
.chart-tooltip.__state-normal {
  -webkit-transform: translate(0, 0);
  transform: translate(0, 0);
}

/* Fix chart tooltip wrapping */
.chart-tooltip-value__aside {
  white-space: nowrap;
}

/* Add proper cursor to chart bubbles */
.chart-bubble-note {
  cursor: pointer;
}

.form-hint .fast-tooltip {
  z-index: 1000 !important;
}

/* Add selected state to search item */
.map-search--results .map-search-results__item.__highlight {
  background-color: #101010;
}

/* Add weather chart section title */
.weather-info-charts__title h2 {
  font-size: 20px;
  font-weight: 700;
  line-height: 1.3;
  margin-top: 0;
  margin-bottom: 0;
}
.weather-info-charts__title small {
  display: block;
  font-size: 12px;
  line-height: 1;
  font-weight: normal;
}

/* Add margin bottom to weather chart legend item  */
.weather-info-charts-legend {
  margin-bottom: 9px;
}
.weather-info-charts-legend__item {
  margin-bottom: 9px;
}

/* FIXME: There should be a better way */
* {
  outline: none;
}

/* Hotfix for fertilizer right-floating link */
.form-label.__with-right-addon {
  display: flex;
  justify-content: space-between;
}

/* Update notification bar to fit content */
.main-container-notification {
  min-height: 50px;
  height: auto;
}

/*
  Always show scrollbar in note list as it greatly simplifies
  dynamic height calculation in virtuazlied note list
*/
.soil-old-notes-list {
  overflow-y: scroll !important;
}

/* Fix button text wrapping */
.popup-edit-bound-tools__item {
  white-space: nowrap;
}

/* Fix public shared page header shade covering heading text */
.header:after {
  z-index: -1;
}

/* Fix scrolling of station charts pane */
.sensors-section {
  overflow-y: scroll;
}

/* Align stations tooltip dot to the first line (in multiline rows) */
.chart-tooltip-value__dot {
  align-self: flex-start;
  margin-top: 6px;
}

/* Station map markers */
.marker {
  display: inline-block;
  position: relative;
  transform: translateX(-50%) translateY(-100%);
}
.marker-labels {
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.1s ease-in-out;
  position: absolute;
  padding-right: 4px;
  right: 100%;
  top: 0;
}
.marker-label {
  line-height: 22px;
  color: white;
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}
.marker-dates {
  pointer-events: none;
  transition: opacity 0.1s ease-in-out;
  position: absolute;
  padding-left: 4px;
  left: 100%;
  top: 0;
}
.marker-date {
  line-height: 24px;
  color: white;
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  white-space: nowrap;
}
.marker-stats {
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  overflow: hidden;
  box-sizing: border-box;
  border: 2px solid transparent;
  transition: border-color 0.1s ease-in-out;
}
.marker-stats-round {
  min-width: 25px;
  border-radius: 999px;
}
.marker:hover .marker-stats {
  border-color: white;
}
.marker:hover .marker-labels {
  opacity: 1;
}
.marker-stat {
  text-align: center;
  padding: 4px 5px 2px 5px;
  position: relative;
  white-space: nowrap;
  font-weight: 500;
  font: 14px Graphik;
}
.marker-stat + .marker-stat {
  border-top: 2px solid white;
}
.marker-rod {
  width: 2px;
  height: 16px;
  background-color: white;
  display: block;
  margin: 0 auto;
  margin-top: -2px;
}
.marker-shadow {
  width: 22px;
  height: 3px;
  position: absolute;
  bottom: -2px;
  left: calc(50% - 11px);
  background-image: url('../images/marker-shadow.png');
  background-size: 22px 3px;
  background-repeat: no-repeat;
  background-position: center;
  z-index: -1;
}

/* Scroll cursor on zoomable charts */
.sensors-section-body__fullscreen, .sensors-viewer-chart {
  cursor: ew-resize;
}

/* Decrease font size for chart metrics when they don't fit */
.sensors-viewer-chars.__small .sensors-viewer-chars__label {
  font-size: 10px;
}
.sensors-viewer-chars.__small .sensors-viewer-chars__value {
  font-size: 12px;
}

@keyframes slideRight {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes enlarge {
  0% {
    transform: scale(0.1);
  }
  100% {
    transform: scale(1);
  }
}

body.new-note-map-mode .leaflet-grab,
body.new-note-map-mode .leaflet-interactive {
  cursor: url('../images/add-note-cursor.svg') 21 48,
    url('../images/add-note-cursor.svg'), crosshair;
}

.soil-sidebar-create .soil-fields-list__list-item {
  user-select: none;
}

.modal-calendar-header__item {
  text-transform: capitalize;
}

.modal-calendar-header__inner > div {
  display: inline-block;
}

.soil-fields-list[data-mode="edit"] .soil-fields-list__item {
  padding-left: 50px;
}
.soil-old-notes-list__list-item[data-mode="edit"] .soil-old-notes-list__item {
  padding-left: 50px;
}
.soil-old-notes-list__list-item {
  box-sizing: border-box;
}
.soil-old-notes-list__item {
  box-sizing: border-box;
}
.soil-fields-list__edit-chk {
  z-index: 2;
}

.modal-select[data-searchbox="true"] .modal-select__inner {
  min-height: 60px;
}
.modal-editseasons-content__description {
  padding: 0 25px 10px;
  font-size: 16px;
}

.modal .main-container-notification {
  top: 0
}

.map-timeline-body.__shd-right:after {
  width: 35px;
  pointer-events: none;
}

.map-timeline-area__list-item:last-child {
  padding-right: 20px;
}

.map-timeline-body.__shd-left:before {
  border-top-left-radius: 20px;
  border-bottom-left-radius: 20px;
  pointer-events: none;
}
.map-timeline-header + .map-timeline-body.__shd-left:before {
  border-radius: 0;
}

.form-select__option-title {
  padding-right: 30px;
}
.form-select__option-hint {
  font-size: 0.8em;
  font-weight: normal;
  color: #aaa;
  padding-right: 30px;
}

/* Fix pagination hover color from <a> */
.pagination__item:hover {
  color: inherit;
}

.pagination-small .pagination-arrow-back,
.pagination-small .pagination-arrow-forward {
  min-width: initial;
}

.map-viewer-grid.hidden {
  z-index: -1;
}

.share-undefined-container {
  display: flex;
  height: 100%;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  overflow-x: hidden;
}

.share-undefined-container > div {
  display: flex;
  flex-direction: column;
  width: 30%;
  min-width: 450px;
  text-align: center;
  justify-content: center;
  align-items: center;
}

.share-undefined-container > div:first-child,
.share-undefined-container > div:last-child {
  flex: 1;
}

.share-undefined-container > div:last-child {
  justify-content: flex-end;
}

.share-undefined-container .logo {
  transform: scale(1.3);
}

.main-content.share-undefined-container h1 {
  margin: 0;
  padding-bottom: 10px;
}

.main-content.share-undefined-container p {
  margin: 0;
  padding-bottom: 25px;
}

.main-content.share-undefined-container .call-to-action {
  min-width: 300px;
}

/* browser not supported page */

.onesoil-not-supported-page {
  background-color: #fafafb;
}

.onesoil-not-supported-page .page-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 0 30px 15px;
  height: auto;
  min-height: 100%;
}

.onesoil-not-supported-page .page-container::before {
  content: '';
  height: 25px;
}

.not-supported {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.not-supported .logo {
  margin: 0;
  width: 105px;
}

.not-supported .logo::before {
  width: 105px;
  height: 35px;
}

.not-supported__header {
  margin: 0 0 64px 0;
}

.not-supported__info {
  display: flex;
  flex-grow: 1;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-bottom: 15%;
}

.not-supported h2 {
  max-width: 570px;
  text-align: center;
  font-weight: normal;
  font-size: 20px;
  margin-bottom: 56px;
}

.not-supported h1 {
  text-align: center;
  margin: 0;
}

.not-supported__browsers {
  display: flex;
  flex-direction: row;
  width: 100%;
  max-width: 660px;
  justify-content: space-between;
}

.not-supported__browser:last-child {
  margin-right: 0;
}

.not-supported__browser {
  margin-right: 10px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 33.333%;
  background-color: rgb(245, 247, 249);
  border-radius: 8px;
  padding: 16px;
  transition: all 0.5s linear;
}

.not-supported__browser:visited {
  background-color: rgb(245, 247, 249);
}

.not-supported__browser:hover {
  background-color: rgb(228, 231, 234);
}

.not-supported__browser:focus {
  background-color: rgb(245, 247, 249);
}

.not-supported__browser:active {
  background-color: rgb(245, 247, 249);
}

.not-supported__info .emoji {
  display: block;
  width: 76px;
  height: 76px;
  margin-bottom: 24px;
  background: url(../images/emoji-sad.png);
  background-size: cover;
}

.not-supported__meta {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  margin-left: 15px;
}

.not-supported__browser-title {
  font-size: 16px;
  font-weight: 500;
  color: rgb(34, 34 ,34);
}

.not-supported__browser-desc {
  color: rgb(165, 178, 188);
  font-size: 12px;
  font-weight: 500;
  line-height: 16px;
}

/* end browser not supported page */

@media screen and (min-width: 2000px) {
  .share-undefined-container .logo {
    transform: scale(2);
  }
}

@media screen and (max-width: 767px) {
  .share-undefined-container {
    overflow-y: scroll;
  }

  .main-content.share-undefined-container h1 {
    padding-top: 15px;
    padding-bottom: 5px;
    font-size: 24px;
  }

  .main-content.share-undefined-container p {
    padding-bottom: 20px;
  }

  .share-undefined-container > div:first-child {
    justify-content: flex-end;
  }

  .share-undefined-container > div,
  .main-content.share-undefined-container .call-to-action {
    min-width: auto;
    width: 100%;
  }

  .share-undefined-container .logo {
    transform: scale(1.1);
  }

  /* browser not supported page */

  .not-supported {
    padding-bottom: 0;
  }

  .not-supported__browsers {
    align-items: center;
    flex-direction: column;
  }

  .not-supported__browser {
    margin: 0 0 15px 0;
    width: 100%;
    max-width: 250px;
    transition: none;
  }

  .not-supported__browser:hover {
    background-color: rgb(245, 247, 249);
  }

  /* browser not supported page */
}
.ico-field-location-os {
  width: 22px;
  height: 22px;
}

.leaflet-tooltip.leaflet-interactive-tooltip {
  position: relative;
  bottom: 30px;
  left: -50%;
}

.map-settings-tooltip {
  font-weight: 600;
}

/* side-by-side field glance */

.side-by-side__map {
  position: absolute;
  height: 100%;
  width: 100%;
}

.side-by-side__slider {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  cursor: ew-resize;
  position: absolute;
  top: 0;
  width: 2px;
}

.side-by-side__ndvi {
  position: relative;
  flex-basis: 50%;
}

.map-viewer-grid__header--right {
  padding-left: 130px;
  text-align: right;
}

/* animation for side-by-side field glance 😄*/

@keyframes mapLeft {
  0%, 100% {
    clip-path: polygon(0% 0%, 50% 0%, 50% 100%, 0% 100%);
    -webkit-clip-path: polygon(0% 0%, 50% 0%, 50% 100%, 0% 100%);
  }
  25% {
    clip-path: polygon(0% 0%, 45% 0%, 45% 100%, 0% 100%);
    -webkit-clip-path: polygon(0% 0%, 45% 0%, 45% 100%, 0% 100%);
  }
  75% {
    clip-path: polygon(0% 0%, 55% 0%, 55% 100%, 0% 100%);
    -webkit-clip-path: polygon(0% 0%, 55% 0%, 55% 100%, 0% 100%);
  }
}

@keyframes mapRight {
  0%, 100% {
    clip-path: polygon(50% 0%, 100% 0%, 100% 100%, 50% 100%);
    -webkit-clip-path: polygon(50% 0%, 100% 0%, 100% 100%, 50% 100%);
  }
  25% {
    clip-path: polygon(55% 0%, 100% 0%, 100% 100%, 55% 100%);
    -webkit-clip-path: polygon(45% 0%, 100% 0%, 100% 100%, 45% 100%);
  }
  75% {
    clip-path: polygon(55% 0%, 100% 0%, 100% 100%, 55% 100%);
    -webkit-clip-path: polygon(55% 0%, 100% 0%, 100% 100%, 55% 100%);
  }
}

@keyframes slider {
  0%, 100% {
    left: calc(50% - 2px);
  }
  25% {
    left: calc(45% - 2px);
  }
  75% {
    left: calc(55% - 2px);
  }
}

.side-by-side__map--animated-left {
  animation: mapLeft ease-in-out 1.5s;
}

.side-by-side__map--animated-right {
  animation: mapRight ease-in-out 1.5s;
}

.side-by-side__slider--animated {
  animation: slider ease-in-out 1.5s;
}

.map-viewer-grid-vdivider.matrix {
  cursor: default;
}

.map-viewer-grid .map-legend.show {
  opacity: 1;
  visibility: visible;
}

/* map tooltip for map settings  */
.map-settings-tooltip {
  border: none;
  background: transparent;
  color: #ffffff;
  text-shadow: 1px 1px 0px #000, 1px -1px 0px #000, -1px 1px 0px #000, -1px -1px 0px #000, 1px 0px 0px #000, 0px 1px 0px #000, -1px 0px 0px #000, 0px -1px 0px #000;
  box-shadow: none;
  display: block;
  padding: 2px;
}

/* end of map tooltip styles */

.modal-select__list-item.__disabled {
  pointer-events: none;
  opacity: 0.2;
}

.main-uploader-ways__other {
  display: flex;
  margin: 10px 0;
}

.main-uploader-spinner {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

.main-section__overlay {
  position: absolute;
  background: #fff;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 10;
  opacity: 0.4;
}

.main-uploader-types-files + .main-uploader-types-files {
  margin-left: 10px;
}

/* icon for Sign in with apple */
.ico-apple-utf {
  margin-right: 3px;
}

.form-group-grid.__sps-default {
  align-items: flex-end;
}

.main-container-notification-actions__value-item + .main-container-notification-actions__value-item {
  margin-left: 15px;
}

.modems-section {
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
  transition: margin-top 0.15s linear;
}
.global-notification-true .modems-section {
  margin-top: 50px;
}
.modems-section-header {
  padding: 15px 20px 5px;
  flex: 0 0 auto;
  display: flex;
  justify-content: space-between;
}
.modems-section-header h1 {
  margin: 0;
  font-size: 20px;
  line-height: 28px;
  font-weight: bold;
  flex: 0 1 auto;
  min-width: 0;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.modems-section-header .breadcrumbs-container {
  margin-bottom: 2px;
}
.modems-selected-stats {
  font-size: 12px;
  line-height: 16px;
  height: 16px;
  color: #a5b2bc;
  margin-top: 10px;
  text-align: right;
}
.modems-selected-stats b {
  font-weight: 500;
}
.modems-section-title {
  min-width: 0;
  white-space: nowrap;
}
.modems-section-subtitle {
  text-overflow: ellipsis;
  overflow: hidden;
  font-size: 12px;
  font-weight: 500;
  color: #a5b2bc;
  margin-bottom: 5px;
}
.modems-section-subtitle__index {
  font-weight: normal;
}
.modems-section-header__buttons {
  display: flex;
  margin-top: 10px;
}
.modems-section-header__buttons > .btn,
.modems-section-header__buttons .form-upload {
  margin-left: 10px;
}
.modems-section-header__buttons .ico-download-os {
  width: 12px;
  height: 14px;
}
.modems-section-body {
  padding: 0 20px;
}
.modems-files-input {
  font-size: 0;
  width: 0;
  height: 0;
}
.modems-files-table .ReactVirtualized__Table__Grid {
  padding-bottom: 90px;
}
.modems-files-table .ReactVirtualized__Table__headerRow {
  font-size: 12px;
  font-weight: 500;
  text-transform: none;
  color: #a5b2bc;
  border-bottom: 1px solid rgba(214, 220, 225, 0.5);
}
.modems-files-table .ReactVirtualized__Table__row {
  font-size: 12px;
  border-bottom: 1px solid rgba(214, 220, 225, 0.5);
}
.modems-files-table .ReactVirtualized__Table__row.__disabled {
  color: #a5b2bc;
}
.modems-files-table-row-file.__disabled .modems-filename svg {
  opacity: 0.5;
}
.modems-files-table .modems-files-table-row-folder:not(.__disabled) {
  cursor: pointer;
}
.modems-files-table .ReactVirtualized__Table__row.__highlighted {
  background-color: #fffcf4;
}
.modems-files-table .ReactVirtualized__Table__row:hover {
  background-color: #f5f7f9;
}
.modems-files-table .ReactVirtualized__Table__headerTruncatedText {
  display: block;
}
.modems-files-table .ReactVirtualized__Table__sortableHeaderIcon {
  width: 16px;
  height: 16px;
}
.modems-files-table .ReactVirtualized__Table__sortableHeaderColumn {
  display: flex;
  align-items: center;
}
.modems-files-table .form-checkbox {
  height: 24px;
  padding-left: 10px;
  padding-right: 10px;
}
.modems-filename {
  display: flex;
  align-items: center;
}
.modems-filename svg {
  flex-shrink: 0;
  margin-right: 6px;
}
.modems-filename span {
  overflow: hidden;
  text-overflow: ellipsis;
}
.form-checkbox.__small .form-checkbox__el {
  width: 14px;
  height: 14px;
  border-width: 1.5px;
  border-radius: 3.5px;
}
.modems-files-table .form-checkbox.__small .form-checkbox__el {
  margin-top: 5px;
  margin-left: 0px;
}
.form-checkbox.__small .form-checkbox__el:before {
  margin-top: 4px;
  margin-left: 3px;
  width: 9px;
  height: 7px;
}
.modems-status {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.modems-status span {
  text-overflow: ellipsis;
  overflow: hidden;
}
.modems-status svg {
  margin-left: 6px;
  flex-shrink: 0;
}
.modems-uploads-count {
  font-size: 12px;
  font-weight: normal;
  color: #A5B2BC;
  margin-right: 5px;
}
.db {
  display: block;
}

.overlap-tooltip {
  background-color: rgba(255,255,255, 1);
  transition: none;
}

.popover-editseasons-content__actions {
  display: flex;
  flex: 1;
}

.popover-editseasons-content__actions .btn {
  flex: 1;
}

.popover-editseasons-content__remove {
  text-align: center;
  margin-top: 25px;
  margin-bottom: 20px;
}

.popover-editseasons-content__remove .edit-action-delete {
  font-size: 14px;
  line-height: 18px;
}

.crop-tooltip {
  padding: 0;
}

.crop-tooltip .popover-select__item {
  padding-left: 13px;
  padding-right: 16px;
}
.crop-tooltip .btn__ico {
  min-width: 16px;
  justify-content: center;
}

.crop-tooltip .popover-select__item .ico-trash-n-os {
  margin-top: -1px;
}

.popover-select__list {
  list-style-type: none;
  margin: 0;
  padding: 0;
}

.popover-select__item {
    display: flex;
    color: #222;
    font-size: 14px;
    line-height: 18px;
    padding: 16px 14px;
    align-items: center;
    cursor: pointer;
}

.popover-select__item:hover {
  background-color: #f5f7f9
}

.popover-select__item-danger {
  color: #FF3B30;
}

.popover-select__list-item:first-child .popover-select__item {
  border-radius: 8px 8px 0 0;
}

.popover-select__list-item:last-child .popover-select__item {
  border-radius: 0 0 8px 8px;
}


.popover-select__list-item + .popover-select__list-item .popover-select__item {
  border-top: 1px solid #E4E7EA;
}

.add-crop-searchbox {
  padding: 15px;
}

.add-crop-searchbox .modal-select-searchbox__icon {
  top: 10px;
  left: 16px;
}

.add-crop-searchbox .form-input {
  padding-top: 4px;
  padding-bottom: 4px;
  line-height: 22px;
}

.modal-select__list + .addCropSectionTitle {
  margin-bottom: 20px;
}

.popover-select__list-item.__disabled {
  pointer-events: none;
  opacity: 0.4;
}
.soil-fields-list__item {
  height: 100%;
}

.soil-fields-list__column {
  flex-direction: column;
  align-items: flex-start;
  padding-bottom: 28px;
  padding-top: 20px;
}

.soil-fields-list__row {
  display: flex;
  position: relative;
  width: 100%;
  margin-bottom: 16px;
}

.soil-fields-list__list-item.__nohover:hover {background-color:inherit;}

.table-seasons td {
  border-bottom: 0
}

.soil-fields-list-actions:focus {
  visibility: visible;
}

.soil-old-notes-list-actions:focus {
  visibility: visible;
}

[role='combobox'] div[type='button'], [role='combobox'] a[type='button'] {
  -webkit-appearance: initial;
}

.unit-append {
  position: relative;
}
.unit-append__wrapper {
  position: absolute;
  top: 2px;
  right: 2px;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 7px 8px 7px 3px;
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
  cursor: pointer;
  max-width: 70%;
}
.unit-append__select {
  color: #a5b2bc;
  font-size: 1rem;
  line-height: 1.25rem;
  display: flex;
  align-items: center;
}
.unit-append__select:hover,
.unit-append__select.form-select--active {
  color: var(--color-primary);
}
.unit-append__select.form-select--disabled {
  background-color: initial;
  color: rgba(165, 178, 188, 0.4);
}
.unit-append__select .ico-chevron-down-small-os {
  flex: 0 0 auto;
  margin-left: 4px;
  transition: -webkit-transform 0.2s;
  transition: transform 0.2s;
  transition: transform 0.2s, -webkit-transform 0.2s;
}
.unit-append__select.form-select--active .ico-chevron-down-small-os {
  -webkit-transform: rotate(-180deg);
  transform: rotate(-180deg);
}

.navigation__ico .navigation__ico__new {
  position: absolute;
  right: -6px;
  top:  -3px;
}

.soil-sidebar-vra-info {
  padding: 16px;
  border-bottom: 1px solid #E4E7EA;
}

.soil-sidebar-vra-info__content {
  background-color: rgba(34, 34, 34, 1);
  border-radius: 8px;
  padding: 16px;
  color: #fff;
  text-align: center;
}

.soil-sidebar-vra-info__title {
  font-size: 14px;
  font-weight: 700;
  line-height: 18px;
  letter-spacing: 0px;
  margin-bottom: 8px;
}

.soil-sidebar-vra-info__text {
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  letter-spacing: 0px;
}

.soil-sidebar-vra-info__text + .btn {
  margin-top: 8px;
}

/* .soil-fields-list__item-vra-item {
  padding-right: 13px;
} */

.soil-fields-list-meta__vra-status {
  flex-shrink: 0;
  border: 1px solid rgba(39, 174, 96, 0.3);
  font-size: 12px;
  font-weight: 500;
  line-height: 16px;
  letter-spacing: 0px;
  text-align: center;
  color: rgba(39, 174, 96, 1);
  text-transform: capitalize;
  padding: 2px 4px;
  border-radius: 4px;
  margin-right: -7px;
}

.soil-fields-list-meta__crops {
  /* display: flex;
  flex: 1 1 auto; */
  display: flex;
  overflow: hidden;
  white-space: nowrap;
}

.soil-fields-list-meta__crops .c-neutral {
  text-overflow: ellipsis;
  overflow: hidden;
}

.soil-fields-list__list-disabled {
  position: absolute;
  top:0;
  left:0;
  right:0;
  bottom: 0;
  background-color: rgba(255,255,255,.7);
  cursor: pointer;
}

.try-zones-modal .modal-outer {
  width: 520px;
  border-radius: 16px;
  /* height: 600px; */
}

.try-zones-modal .modal-editseasons-content {
  height: 100%;
}

.g-loader.huge .g-loader__inner {
  width: 35px;
  height: 35px;
}

.modal-vra-status .modal-outer {
  max-width: 360px;
}

.inner-input {
  border: none;
  padding: 9px 13px;
  margin: 0;
  font-size: 1rem;
  line-height: 1.25rem;
}

.btn-yield {
  display: flex;
  align-items: center;
  min-height: 40px;
  padding: 0 12px;
  position: relative;
}

.btn-yield-tiny {
  display: flex;
  align-items: center;
  min-height: 30px;
  padding: 0 7px;
  border-radius: 4px;
}

.btn-yield .btn__ico.btn__ico-right {
  margin-top: -1px;
  margin-left: 4px;
  margin-right: 0px;
}

.btn-yield-text {
  margin: 0 4px;
}

.btn-black {
  background-color: #1c1c1e;
  color: #fff;
}
.btn-black:hover,
.btn-black:focus {
  background-color: #48484a;
  color: #fff;
}
.btn-black.disabled,
.btn-black[disabled] {
  cursor: default;
  background-color: #f5f7f9;
  color: rgba(165,178,188,0.7);
}
.btn-sidebar {
  background-color: transparent;
  color: var(--color-black);
}
.btn-sidebar:enabled:hover,
.btn-sidebar:enabled:focus {
  background-color: var(--color-grey-30);
}

.tr {
  display: flex;
}

.th {
  display: flex;
  align-items: center;
}

.td {
  display: flex;
  align-items: center;
}

.td > span,
.td > a {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.table-yield th,
.table-yield .th {
  font-size: 12px;
  color: #8E8E93;
  height: 48px;
  padding: 0 0 0 8px;
  vertical-align: middle;
  cursor: default;
}

.table-yield td,
.table-yield .td {
  font-size: 14px;
  height: 56px;
  padding: 0 0 0 8px;
}

.table-yield tr:last-child td,
.table-yield .tr:last-child .td {
  border-bottom: none;
}

.agrilever-logo {
  position: relative;
  display: block;
  margin: 0 auto;
  width: 109px;
}

.agrilever-logo:before {
  display: block;
  content: '';
  width: 109px;
  height: 40px;
  background: url('../images/whiteLabel/agrilever-logo.svg') no-repeat 50% / contain;
  transition: opacity 0.2s, -webkit-transform 0.2s;
  transition: opacity 0.2s, transform 0.2s;
  transition: opacity 0.2s, transform 0.2s, -webkit-transform 0.2s;
}

.unico-logo {
  position: relative;
  display: block;
  margin: 0 auto;
  width: 109px;
}

.unico-logo:before {
  display: block;
  content: '';
  width: 104px;
  height: 40px;
  background: url('../images/whiteLabel/unico-logo.svg') no-repeat 50% / contain;
  transition: opacity 0.2s, -webkit-transform 0.2s;
  transition: opacity 0.2s, transform 0.2s;
  transition: opacity 0.2s, transform 0.2s, -webkit-transform 0.2s;
}


.agrolab-logo {
  position: relative;
  display: block;
  margin: 0 auto;
  width: 150px;
}

.agrolab-logo:before {
  display: block;
  content: '';
  width: 150px;
  height: 40px;
  background: url('../images/whiteLabel/agrolab-logo.png') no-repeat 50% / contain;
  transition: opacity 0.2s, -webkit-transform 0.2s;
  transition: opacity 0.2s, transform 0.2s;
  transition: opacity 0.2s, transform 0.2s, -webkit-transform 0.2s;
}

/* Checkbox new design mode styles */
.form-checkbox[data-design-mode='new'] .form-checkbox__chk[type='checkbox']:checked + .form-checkbox__el {
  background-color: #3A3A3C;
}

.form-checkbox[data-design-mode='new']:hover .form-checkbox__chk:checked + .form-checkbox__el {
  background-color: #2A2A2C;
}

.form-checkbox[data-design-mode='new'] .form-checkbox__chk[type='radio']:checked + .form-checkbox__el {
  background-color: #3A3A3C;
}




