import React from 'react';
import { DragPreviewIconContainer } from 'components/ui/DragAndDropTable/TableRow/TableRow.style';
import Icon from 'components/Icon';

export const LeftSideControlListToggle = ({
  onClick,
  isOpened,
}: {
  onClick?: (isOpened: boolean) => void;
  isOpened: boolean;
}) => (
  <div className={DragPreviewIconContainer} onClick={() => onClick?.(isOpened)}>
    <Icon
      name={
        isOpened ? 'table-parent-arrow-opened' : 'table-parent-arrow-closed'
      }
    />
  </div>
);
