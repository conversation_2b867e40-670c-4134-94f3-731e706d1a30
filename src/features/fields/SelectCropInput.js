import React, { useState } from 'react';
import { useSelector } from 'react-redux';

import EnterExitTransition from 'components/EnterExitTransition';
import { CustomDropdown } from 'components/ui/CustomDropdown/CustomDropdown';
import ProposeCulturePopup from 'features/fields/ProposeCulturePopup';

import fields from 'modules/fields';

import { useTranslate } from 'utils/use-translate';

const SelectCropInput = ({ value, onChange, disabled }) => {
  const { t } = useTranslate();

  const [showProposePopup, setShowProposePopup] = useState(false);

  const sortedCropTypes = useSelector(fields.selectors.getSortedCropTypes);

  return (
    <>
      <EnterExitTransition variant='popup'>
        {showProposePopup && (
          <ProposeCulturePopup
            onCancel={() => {
              setShowProposePopup(false);
            }}
          />
        )}
      </EnterExitTransition>

      <CustomDropdown
        disabled={disabled}
        className='form-select'
        withSearchBox={sortedCropTypes.length >= 5}
        placeholder={t('fields.select')}
        searchBoxProps={{
          placeholder: t('suggest.crops_type.search.placeholder'),
        }}
        ignoreIDs={['propose', 'remove']}
        options={sortedCropTypes.map(type => ({
          label: t(`crop.${type}`),
          id: type,
        }))}
        renderValue={({ value }) => {
          let label = 'fields.select';
          if (value && value !== 'propose' && value !== 'remove') {
            label = `crop.${value}`;
          }
          return (
            <div className='form-select__value u-select__value'>{t(label)}</div>
          );
        }}
        value={value}
        onChange={crop => {
          if (crop === 'propose') {
            setShowProposePopup(true);
            return;
          }
          onChange(crop);
        }}
      />
    </>
  );
};

export default SelectCropInput;
