import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import Measure from 'react-measure';

import { styles, StyledValuesList } from './CropsLegend.style';
import { styles as widgetStyles } from '../features/fields/styles/fieldsLegend.styles';

import map from 'modules/map';
import { useTranslate } from 'utils/use-translate';
import Button from './ui/Button/Button';
import { cx } from 'linaria';

const CropsLegend = () => {
  const [expanded, setExpanded] = useState(false);
  const [showMore, setShowMore] = useState(false);
  const cropsList = useRef<HTMLUListElement>(null);

  const { t } = useTranslate();

  const mapLegend = useSelector(map.selectors.getMapLegend);
  // @ts-ignore
  const data: { crop: string; color: string }[] = mapLegend.data;

  const handleSizeChange = useCallback(() => {
    if (
      cropsList.current?.lastChild &&
      // @ts-ignore
      cropsList.current?.lastChild.offsetTop > 0
    ) {
      setShowMore(true);
    } else {
      setShowMore(false);
    }
  }, []);

  useEffect(() => {
    handleSizeChange();
  }, [data, handleSizeChange]);

  return (
    <Measure bounds onResize={() => handleSizeChange()}>
      {({ measureRef }) => (
        <div ref={measureRef} className={widgetStyles.widgetWrapper}>
          <StyledValuesList ref={cropsList} expanded={expanded}>
            {data.map(({ crop, color }) => (
              <li
                key={crop}
                className={styles.valuesListItem}
                style={{
                  // @ts-ignore
                  '--marker-color': color,
                  '--border-color': color === '#222222' ? '#A5B2BC' : color,
                }}
              >
                {t(`crop.${crop}`)}
              </li>
            ))}
          </StyledValuesList>
          {showMore && (
            <Button
              className={cx(styles.showMoreTrigger, expanded && '__opened')}
              onClick={() => {
                setExpanded(!expanded);
              }}
            />
          )}
        </div>
      )}
    </Measure>
  );
};

export default CropsLegend;
