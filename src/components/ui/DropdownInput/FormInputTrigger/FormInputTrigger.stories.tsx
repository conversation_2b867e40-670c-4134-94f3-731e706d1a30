import React from 'react';
import { FormInputTrigger } from './FormInputTrigger';
import { DropdownInput } from 'components/ui/DropdownInput/DropdownInput';
import { css } from 'linaria';

export default {
  title: 'ui/DropdownInput/FormInputTrigger',
  component: FormInputTrigger,
  decorators: [
    (Story: React.FC) => (
      <div style={{ padding: 16 }}>
        <Story />
      </div>
    ),
  ],
};

export const Simple = () => {
  return (
    <DropdownInput
      placeholder={'Form Input Trigger'}
      renderDropdown={({ ref, style }) => (
        <div ref={ref} style={style}>
          Dropdown
        </div>
      )}
    />
  );
};

export const WithClassName = () => {
  const CustomClassName = css`
    color: red;
  `;

  return (
    <DropdownInput
      placeholder={'Form Input Trigger with className'}
      className={CustomClassName}
      renderDropdown={({ ref, style }) => (
        <div ref={ref} style={style}>
          Dropdown
        </div>
      )}
    />
  );
};
