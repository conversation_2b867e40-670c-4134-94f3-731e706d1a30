import { InitialState } from './state';

// @ts-ignore
import { createReducer } from 'utils/redux';

// @ts-ignore
import { resolved, rejected } from 'sagas/global/api';

import { actions, actionsConfig } from './actions';
import {
  BulkUploadState,
  BatchResponse,
  FieldsFromFilesResponse,
  FileBundleStatus,
} from '../types';
import { convertFieldsFromFilesBatchResponseToImportedFields } from './convertors';
import { BulkUploadStepOrder, isBundleValid } from '../utils';

export const reducer = createReducer<typeof actionsConfig, BulkUploadState>(
  InitialState,
  {
    [actions.setFiles.key]: (state, { payload }) => {
      return { ...state, files: [...payload.files] };
    },
    [actions.setCurrentStep.key]: (state, { payload }) => {
      return {
        ...state,
        currentStep: payload.step,
        reachedStep:
          BulkUploadStepOrder[payload.step] >
          BulkUploadStepOrder[state.reachedStep]
            ? payload.step
            : state.reachedStep,
      };
    },
    [actions.setReachedStep.key]: (state, { payload }) => {
      return { ...state, reachedStep: payload.step };
    },
    [actions.setProcessingProgress.key]: (state, { payload }) => {
      return { ...state, processingProgress: payload.progress };
    },
    [actions.updateProcessingStatus.key]: (state, { payload }) => {
      return {
        ...state,
        processingStatus: {
          ...state.processingStatus,
          ...payload.status,
        },
      };
    },
    [actions.setFileBundles.key]: (state, { payload }) => {
      return { ...state, fileBundles: payload.bundles };
    },
    [actions.setBundleStatus.key]: (state, { payload }) => {
      const newBundles = state.fileBundles.map(bundle => {
        return bundle.name === payload.name
          ? { ...bundle, status: payload.status }
          : bundle;
      });
      return { ...state, fileBundles: newBundles };
    },
    [actions.setBundlesStatuses.key]: (state, { payload }) => {
      const statuses = payload.statuses;
      const newBundles = state.fileBundles.map(bundle => {
        return statuses[bundle.name]
          ? { ...bundle, status: statuses[bundle.name]! }
          : bundle;
      });
      return { ...state, fileBundles: newBundles };
    },
    [actions.resetBundlesUploadStatus.key]: state => {
      const newBundles = state.fileBundles.map(bundle => {
        return {
          ...bundle,
          uploadingError: undefined,
          uploadingResult: undefined,
          status: isBundleValid(bundle)
            ? FileBundleStatus.WAITING_FOR_UPLOAD
            : bundle.status,
        };
      });
      return { ...state, fileBundles: newBundles };
    },
    [actions.setBundleUploadingError.key]: (state, { payload }) => {
      const newBundles = state.fileBundles.map(bundle => {
        return bundle.name === payload.name
          ? { ...bundle, uploadingError: payload.error }
          : bundle;
      });
      return { ...state, fileBundles: newBundles };
    },
    [actions.setBundleUploadingResult.key]: (state, { payload }) => {
      const newBundles = state.fileBundles.map(bundle => {
        return bundle.name === payload.name
          ? { ...bundle, uploadingResult: payload.result }
          : bundle;
      });
      return { ...state, fileBundles: newBundles };
    },
    [actions.fetchFieldsFromFiles.key]: state => {
      return {
        ...state,
        importedFields: [],
        checkedIDs: [],
        fieldsFetchingPending: true,
      };
    },
    [resolved(actions.fetchFieldsFromFiles)]: (
      state: BulkUploadState,
      { payload }: { payload: BatchResponse<FieldsFromFilesResponse> },
    ) => {
      const importedFields =
        convertFieldsFromFilesBatchResponseToImportedFields(payload);
      return {
        ...state,
        importedFields,
        checkedIDs: importedFields.map(fields => fields.id),
        fieldsFetchingPending: false,
      };
    },
    [rejected(actions.fetchFieldsFromFiles)]: (state: BulkUploadState) => {
      return {
        ...state,
        importedFields: [],
        checkedIDs: [],
        fieldsFetchingPending: false,
      };
    },
    [actions.setCropMatchingAvailable.key]: (state, { payload }) => {
      return { ...state, cropMatchingAvailable: payload.available };
    },
    [actions.setCheckedIDs.key]: (state, { payload }) => {
      return { ...state, checkedIDs: payload.ids };
    },
    [actions.setBbox.key]: (state, { payload }) => {
      return { ...state, bbox: payload.bbox };
    },
    [actions.resetImportedFields.key]: state => {
      return { ...state, importedFields: InitialState.importedFields };
    },
    [actions.resetBulkUploadState.key]: () => {
      return { ...InitialState };
    },
  },
);
