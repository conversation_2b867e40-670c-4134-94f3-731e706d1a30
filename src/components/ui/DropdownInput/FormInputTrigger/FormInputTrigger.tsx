import React from 'react';
import cx from 'classnames';
import { GetToggleButtonPropsType } from '../types';

interface FormInputTriggerProps<V> {
  className?: string;
  placeholder?: string;
  renderValue?: (value: V) => React.ReactNode;
  disabled?: boolean;
  value?: V;
  isOpen: boolean;
  getToggleButtonProps: GetToggleButtonPropsType;
  onChange?: (value: V, selection?: TEMPORARY_ANY) => void;
}

export const FormInputTrigger = <V,>({
  className,
  placeholder,
  renderValue,
  disabled,
  value,
  isOpen,
  getToggleButtonProps,
  onChange,
  ...otherProps
}: FormInputTriggerProps<V>) => {
  return (
    <button
      className={cx('form-select', className, {
        'form-select--active': isOpen,
        'form-select--disabled': disabled,
      })}
      {...otherProps}
      tabIndex={1}
      {...getToggleButtonProps({ disabled: disabled ?? false })}
    >
      <div className='form-select__value'>
        {value != null ? renderValue?.(value) : placeholder}
      </div>
    </button>
  );
};
