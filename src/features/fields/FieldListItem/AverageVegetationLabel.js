import React from 'react';
import { connect } from 'react-redux';
import moment from 'moment';

import Icon from 'components/Icon';
import {
  ModernTooltip,
  ModernTooltipDirection,
} from 'components/ui/Tooltip/ModernTooltip/ModernTooltip';
import { SimpleTooltipTheme } from 'components/ui/Tooltip/SimpleTooltip/SimpleTooltip';

import fields from 'modules/fields';
import { formatDate } from 'utils/format-date';
import { useTranslate } from 'utils/use-translate';

const Precision = 2;
const NDVIStates = {
  [-1]: 'negative',
  0: 'neutral',
  1: 'positive',
};

const round = (v, precision) => {
  const e = Math.pow(10, precision);
  return Math.round(v * e) / e;
};

const AverageVegetationLabel = ({ ndvi }) => {
  const { t } = useTranslate();
  if (!ndvi) {
    return null;
  }
  return (
    <ModernTooltip
      offset={[5, 0]}
      animate={ModernTooltipDirection.FromRight}
      active={ndvi && ndvi.value != null}
      small
      theme={SimpleTooltipTheme.DARK}
      renderTrigger={props => {
        let { value, dynamics } = ndvi || {};
        const delta = round(dynamics, Precision);
        const arrow = delta > 0 ? 'up' : 'down';
        const haveData = value != null;
        return (
          <div
            {...props}
            className='soil-fields-list-meta__item soil-fields-list-meta__ndvi'
            data-state={
              ndvi && haveData ? NDVIStates[Math.sign(delta)] : 'progress'
            }
          >
            {!ndvi && t('fields.avg_ndvi.loading')}
            {ndvi && !haveData && t('fields.avg_ndvi.none')}
            {ndvi && haveData && (
              <>
                {value.toFixed(Precision)}{' '}
                {delta !== 0 && (
                  <Icon
                    className={`ico-arrow-${arrow}-os`}
                    name={`trend-arrow-${arrow}`}
                  />
                )}
              </>
            )}
          </div>
        );
      }}
      renderTooltip={() => {
        const { date, dynamics } = ndvi;
        const delta = round(dynamics, Precision);
        return (
          <>
            <span className='text-muted'>{t('fields.avg_ndvi.tooltip')}</span>
            <br />
            <span style={{ textTransform: 'capitalize' }}>
              {moment(date).isSame(moment(), 'day')
                ? t('weather.today')
                : formatDate(date, t('fields.ndvi.date_format'))}
            </span>
            {delta !== 0 &&
              `, ${delta > 0 ? '+' : ''}${delta.toFixed(Precision)}`}
          </>
        );
      }}
    />
  );
};

const mapStateToProps = (state, ownProps) => ({
  ndvi: fields.selectors.getAverageNdviByID(state, ownProps.fieldId),
});

export default connect(mapStateToProps)(AverageVegetationLabel);
