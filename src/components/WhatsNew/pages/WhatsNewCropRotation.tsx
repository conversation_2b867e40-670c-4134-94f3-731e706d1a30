import React, { VFC } from 'react';
import {
  StyledPage,
  styles,
} from 'components/WhatsNew/WhatsNewContent/WhatsNewContent.style';
import Button from 'components/ui/Button/Button';

import WhatsNewImgEn from 'assets/images/whatsnew/whatsnew_croprotation_en.jpg';
import WhatsNewImgEn2x from 'assets/images/whatsnew/<EMAIL>';
import WhatsNewImgDe from 'assets/images/whatsnew/whatsnew_croprotation_de.jpg';
import WhatsNewImgDe2x from 'assets/images/whatsnew/<EMAIL>';
import WhatsNewImgFr from 'assets/images/whatsnew/whatsnew_croprotation_fr.jpg';
import WhatsNewImgFr2x from 'assets/images/whatsnew/<EMAIL>';
import WhatsNewImgHu from 'assets/images/whatsnew/whatsnew_croprotation_hu.jpg';
import WhatsNewImgHu2x from 'assets/images/whatsnew/<EMAIL>';
import WhatsNewImgIt from 'assets/images/whatsnew/whatsnew_croprotation_it.jpg';
import WhatsNewImgIt2x from 'assets/images/whatsnew/<EMAIL>';
import WhatsNewImgPt from 'assets/images/whatsnew/whatsnew_croprotation_pt.jpg';
import WhatsNewImgPt2x from 'assets/images/whatsnew/<EMAIL>';
import WhatsNewImgRu from 'assets/images/whatsnew/whatsnew_croprotation_ru.jpg';
import WhatsNewImgRu2x from 'assets/images/whatsnew/<EMAIL>';
import WhatsNewImgEs from 'assets/images/whatsnew/whatsnew_croprotation_es.jpg';
import WhatsNewImgEs2x from 'assets/images/whatsnew/<EMAIL>';
import WhatsNewImgUk from 'assets/images/whatsnew/whatsnew_croprotation_uk.jpg';
import WhatsNewImgUk2x from 'assets/images/whatsnew/<EMAIL>';
import { ImageLanguageMap, WhatsNewProps } from '../types';
import { FeaturesKeyEnum } from '../consts';

const images: ImageLanguageMap = {
  en: [WhatsNewImgEn, WhatsNewImgEn2x],
  de: [WhatsNewImgDe, WhatsNewImgDe2x],
  fr: [WhatsNewImgFr, WhatsNewImgFr2x],
  hu: [WhatsNewImgHu, WhatsNewImgHu2x],
  it: [WhatsNewImgIt, WhatsNewImgIt2x],
  pt: [WhatsNewImgPt, WhatsNewImgPt2x],
  ru: [WhatsNewImgRu, WhatsNewImgRu2x],
  es: [WhatsNewImgEs, WhatsNewImgEs2x],
  uk: [WhatsNewImgUk, WhatsNewImgUk2x],
  default: [WhatsNewImgEn, WhatsNewImgEn2x],
};

const CropRotation: VFC<WhatsNewProps> = ({
  style,
  onClick,
  t,
  language,
  isLast,
}) => {
  const Img = images[language] ? images[language]?.[0] : images.default[0];
  const Img2x = images[language] ? images[language]?.[1] : images.default[1];

  return (
    <StyledPage style={{ ...style }}>
      <div className={styles.semiPage}>
        <div className={styles.pageContent}>
          <div className={styles.textContent}>
            <h2 className={styles.title}>{t('whatsnew.croprotation.title')}</h2>
            <p className={styles.text}>{t('whatsnew.croprotation.text')}</p>
          </div>
          <Button className='btn btn-success fit-content' onClick={onClick}>
            {t(`whatsnew.${isLast ? 'end' : 'next'}.action_text`)}
          </Button>
        </div>
      </div>
      <div className={styles.semiPage}>
        <picture
          className={styles.pagePicture}
          style={{
            marginTop: 40,
            borderBottomRightRadius: 8,
            overflow: 'hidden',
          }}
        >
          <source srcSet={`${Img} 1x, ${Img2x} 2x`} />
          <img src={Img} alt={t('whatsnew.croprotation.title') as string} />
        </picture>
      </div>
    </StyledPage>
  );
};

const WhatsNewCropRotation = {
  key: FeaturesKeyEnum.cropRotation,
  component: CropRotation,
  filter: {},
};

export default WhatsNewCropRotation;
