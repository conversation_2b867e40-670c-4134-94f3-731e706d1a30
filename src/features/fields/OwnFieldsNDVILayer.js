import React, { memo, useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, useLeaflet } from 'react-leaflet';
import pointOnFeature from '@turf/point-on-feature';
import { createSelector } from 'reselect';
import { useSelector } from 'react-redux';
import L from 'leaflet';

import NDVIOverlay from './NDVIOverlay';
import { NDVITooltip } from 'features/fields/NDVITooltip/NDVITooltip';

import map from 'modules/map';
import fields from 'modules/fields';

import ZIndex from 'constants/zindex';
import { WarningIconHTML } from 'constants/markers';

export const RenderFromZoomLevel = 11;
export const TooltipRenderFromZoomLevel = 12;

export const NDVIMapFillings = ['last_ndvi_default', 'last_ndvi_contrasted'];

const NoImageIcon = L.divIcon({
  className: 'b-comment-point',
  html: WarningIconHTML,
  iconSize: [14, 14],
  iconAnchor: [7, 7],
});

const bboxIntersects = (a, b) => {
  return !(a[0] > b[2] || a[2] < b[0] || a[3] < b[1] || a[1] > b[3]);
};

const getFieldsInViewport = createSelector(
  state => fields.selectors.getAllOwn(state),
  state => map.selectors.getViewport(state),
  (fields, viewport) => {
    if (viewport.properties.zoom < RenderFromZoomLevel) {
      return [];
    }
    return fields.filter(field => bboxIntersects(field?.bbox, viewport?.bbox));
  },
);

const NotNDVIOverlay = ({ field, fieldId, zoomLevel }) => {
  const { map } = useLeaflet();
  const [hoveredPoint, setHoveredPoint] = useState(null);
  const highlighted = useSelector(
    state => fields.selectors.getHighlightedID(state) === fieldId,
  );

  useEffect(() => {
    if (!highlighted) {
      return;
    }

    const handleMouseMove = event => {
      const { clientX, clientY } = event.originalEvent;
      setHoveredPoint({
        mouse: [clientX, clientY],
      });
    };

    map.on('mousemove', handleMouseMove);
    return () => map.off('mousemove', handleMouseMove);
  }, [highlighted, fieldId, map]);

  if (zoomLevel < TooltipRenderFromZoomLevel) {
    return null;
  }

  const shouldRenderTooltip = highlighted && hoveredPoint?.mouse;

  return (
    <>
      <Marker
        key={field.id}
        icon={NoImageIcon}
        position={pointOnFeature(field.geometry).geometry.coordinates.reverse()}
      />
      <NDVITooltip
        isVisible={shouldRenderTooltip}
        withoutNDVI
        position={hoveredPoint?.mouse}
      />
    </>
  );
};

const OwnFieldsNDVILayer = ({ fieldId }) => {
  const aggregateNdvi = useSelector(fields.selectors.getAggregateNdvi);
  const fieldsInViewport = useSelector(getFieldsInViewport);
  const mapFilling = useSelector(map.selectors.getMapFilling);
  const viewport = useSelector(map.selectors.getViewport);
  const ndviType =
    mapFilling === 'last_ndvi_default' ? 'default' : 'contrasted';

  return (
    <Pane style={{ zIndex: ZIndex.FieldsUnderlayPane }}>
      {fieldsInViewport.map(field => {
        const ndvi = aggregateNdvi[field.id];

        if (!ndvi || field.id === fieldId) {
          return null;
        }

        if (!ndvi?.last?.uuid) {
          return (
            <NotNDVIOverlay
              key={field.id}
              field={field}
              fieldId={field.id}
              zoomLevel={viewport?.properties?.zoom}
            />
          );
        }

        return (
          <NDVIOverlay
            key={field.id}
            fieldId={field.id}
            uuid={ndvi?.last?.uuid}
            bbox={field?.bbox}
            type={ndviType}
          />
        );
      })}
    </Pane>
  );
};

export default memo(OwnFieldsNDVILayer);
