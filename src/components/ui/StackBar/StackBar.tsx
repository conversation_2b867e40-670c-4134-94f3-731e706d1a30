import React, { CSSProperties, VFC } from 'react';
import { useSelector } from 'react-redux';
import {
  ModernTooltip,
  ModernTooltipDirection,
} from 'components/ui/Tooltip/ModernTooltip/ModernTooltip';
import { PopoverDeprecatedAlign } from 'components/ui/Popover';
import { SimpleTooltipTheme } from 'components/ui/Tooltip/SimpleTooltip/SimpleTooltip';

import settings from 'modules/settings';
import {
  legend,
  StyledLegendLeft,
  StyledLegendRight,
  StyledStackbarBase,
  stackbar,
  TooltipTitle,
  StyledStackbarCell,
  StyledLegendBottomLeft,
} from './StackBar.style';
import { UnitSystemType } from 'types/units';
import { FormatUnit } from 'utils/functions';
import { AllMeasuresUnits } from 'convert-units';
import { TranslateFunction } from 'utils/use-translate';

type InnerDataType = {
  crop: string;
  value: number;
  color: string;
  fields: number;
};

type StackBarProps = {
  total: number;
  data: TEMPORARY_ANY[];
  valueKey?: string;
  colorKey?: string;
  fieldsKey?: string;
  cropKey?: string;
  baseColor?: string;
  dynamic?: boolean;
  unitsType?: AllMeasuresUnits;
  LegendComponent?: VFC;
  legendLeftText?: string;
  legendRightText?: string;
  legendBottomText?: string;
  legendLeftStyle?: CSSProperties;
  legendRightStyle?: CSSProperties;
  t: TranslateFunction;
};

const StackBar: VFC<StackBarProps> = ({
  total,
  data,
  valueKey = 'value',
  colorKey = 'color',
  fieldsKey = 'fields',
  cropKey = 'crop',
  baseColor = '#E9EDF0',
  dynamic,
  unitsType,
  LegendComponent,
  legendLeftText,
  legendRightText,
  legendBottomText,
  legendLeftStyle,
  legendRightStyle,
  t,
}) => {
  const units: UnitSystemType = useSelector(settings.selectors.getUnits);
  const formatUnit: FormatUnit = useSelector(
    settings.selectors.getUnitFormatter,
  );
  let dataTotalWidth = 0;
  const innerData: InnerDataType[] = data.reduce((acc, cur) => {
    dataTotalWidth += cur[valueKey];
    if (cur[valueKey])
      acc.push({
        crop: cur[cropKey],
        value: cur[valueKey],
        color: cur[colorKey],
        fields: cur[fieldsKey] || 1,
      });
    return acc;
  }, []);

  const getWidth = (value: number, dataWidth: number, totalWidth: number) => {
    if (value === 0) {
      return 0;
    }
    if (totalWidth) {
      return value / totalWidth;
    }
    return value / dataWidth;
  };

  return (
    <div className='container'>
      {!!LegendComponent && <LegendComponent />}
      {!legendBottomText && (
        <div className={legend}>
          <StyledLegendLeft style={legendLeftStyle}>
            {legendLeftText}
          </StyledLegendLeft>
          <StyledLegendRight style={legendRightStyle}>
            {legendRightText}
          </StyledLegendRight>
        </div>
      )}
      <div className={stackbar}>
        <StyledStackbarBase baseColor={baseColor}>
          {innerData.map((el, index: number) => {
            const w = getWidth(el.value, dataTotalWidth, total);
            return (
              <ModernTooltip
                key={`bar${index}`}
                active
                offset={[0, 10]}
                align={PopoverDeprecatedAlign.BottomMiddle}
                animate={ModernTooltipDirection.FromBottom}
                small
                theme={SimpleTooltipTheme.DARK}
                renderTooltip={() => (
                  <>
                    <TooltipTitle>{t(`crop.${el.crop}`)}</TooltipTitle>
                    {!dynamic
                      ? formatUnit(el.value, unitsType || 'ha', 'area')
                      : `${el.value} ${t(`units.${units.area.unit}.short`)}`}
                  </>
                )}
                renderTrigger={(props: TEMPORARY_ANY) => (
                  <StyledStackbarCell
                    last={index === innerData.length - 1}
                    flex={w}
                    color={el.color || baseColor}
                    {...props}
                  />
                )}
              />
            );
          })}
        </StyledStackbarBase>
      </div>
      <div className={legend}>
        <StyledLegendBottomLeft>{legendBottomText}</StyledLegendBottomLeft>
      </div>
    </div>
  );
};

export default StackBar;
