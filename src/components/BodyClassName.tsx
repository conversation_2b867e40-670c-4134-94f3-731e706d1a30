import React, { VFC } from 'react';
// @ts-ignore
import Lifecycle from 'components/Lifecycle';

export type BodyClassNameProps = {
  className: string;
  replace?: TEMPORARY_ANY;
};

const BodyClassName: VFC<BodyClassNameProps> = ({ className, replace }) => (
  <Lifecycle
    onMount={() => {
      className
        .split(' ')
        .map(classItem => document.body.classList.add(classItem));
    }}
    onUnmount={() => {
      className
        .split(' ')
        .map(classItem => document.body.classList.remove(classItem));
    }}
  />
);

export default BodyClassName;
