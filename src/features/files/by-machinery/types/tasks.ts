import { FileImportError } from './core';
import { FieldEntityType } from 'types/fields';
import { ApiDataSource, ApiProcessedTask } from './server';

export enum DataTypesKey {
  machinery = 'machinery',
  soilSampling = 'soil-sampling',
  electroconductivity = 'electroconductivity',
}

export enum ImportingTaskStatus {
  inProgress = 'inProgress',
  empty = 'empty',
  success = 'success',
  partlySuccess = 'partlySuccess',
  failed = 'failed',
}

export enum TaskStatus {
  uploading = 'uploading', // frontend

  waiting = 'waiting',
  processing = 'processing',
  processed = 'processed',
  importing = 'importing',

  done = 'done',
  error = 'error',
}

export type ProcessedFile = {
  uuid: string;
  filename: string;
  geometry: string;
  operation_type: string;
  fields: FieldEntityType[];
  time_start: Date;
  time_end: Date;
  created_at: Date;
  updated_at: Date;
};

type BaseImportTaskType = {
  temporaryUuid?: string;
  disabled?: boolean;

  uuid?: string;
  status: TaskStatus;
  name: ApiProcessedTask['name'];
  data_type?: DataTypesKey;

  /** viewed as file ready for matching */
  viewed_at: ApiProcessedTask['viewed_at'] | boolean;
  size: ApiProcessedTask['size'];
  created_at?: ApiProcessedTask['created_at'];
  updated_at?: ApiProcessedTask['updated_at'];
  sources: ApiDataSource[];
};

export type TaskUploadingType = {
  requestId: string;
  temporaryUuid: string;
  progress: number;
  status: TaskStatus.uploading;
} & BaseImportTaskType;

export type TaskProcessingType = {
  uuid: string;
  status: TaskStatus.processing;
} & BaseImportTaskType;

export type TaskMatchingType = {
  uuid: string;
  status: TaskStatus.processed;
  found_columns: TEMPORARY_ANY[];
} & BaseImportTaskType;

export type TaskFailedType = {
  err_code: FileImportError;
  status: TaskStatus.error;
} & BaseImportTaskType;

export type ServerTaskFailedType = {
  err_code: FileImportError;
  uuid: string;
  status: TaskStatus.error;
} & BaseImportTaskType;

export type TaskSuccessType = {
  uuid: string;
  status: TaskStatus.done;
  data_type: DataTypesKey;
} & BaseImportTaskType;

export type TaskImportingType = {
  uuid: string;
  status: TaskStatus.importing;
} & BaseImportTaskType;

export type TaskWaitingType = {
  uuid: string;
  status: TaskStatus.waiting;
} & BaseImportTaskType;

enum ColumnDataTypes {
  int64 = 'int64',
  object = 'object',
  geometry = 'geometry',
  float64 = 'float64',
}

type ColumnObject = {
  name: string;
  info: {
    p3: string;
    avg: string;
    p97: string;
  };
  dtype: ColumnDataTypes.object;
};

type ColumnGeometry = {
  name: string;
  info: {
    p3: string;
    avg: string;
    p97: string;
  };
  dtype: ColumnDataTypes.geometry;
};

type ColumnInt64 = {
  name: string;
  info: {
    p3: number;
    avg: number;
    p97: number;
  };
  dtype: ColumnDataTypes.int64;
};

type ColumnFloat64 = {
  name: string;
  info: {
    p3: number;
    avg: number;
    p97: number;
  };
  dtype: ColumnDataTypes.float64;
};

export type ColumnType =
  | ColumnFloat64
  | ColumnInt64
  | ColumnObject
  | ColumnGeometry;

export type ImportTaskType =
  | TaskMatchingType
  | TaskUploadingType
  | ServerTaskFailedType
  | TaskProcessingType
  | TaskFailedType
  | TaskImportingType
  | TaskWaitingType
  | TaskSuccessType;

export type LayerMatchingTaskType = {
  uuid: string;
  status: 'PENDING' | 'SUCCESS' | 'ERROR';
  meta: {
    total: number;
    matched_fields: number;
    matched_soil_sampling_maps: number;
  };
};
