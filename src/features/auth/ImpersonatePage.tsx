import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Redirect } from 'react-router-dom';
import jwtDecode from 'jwt-decode';

import BodyClassName from 'components/BodyClassName';
import Loader from 'components/progress/Loader';

import auth from 'modules/auth';
// @ts-ignore
import { resolved } from 'sagas/global/api';
import { getYieldHost } from 'utils/yield';

type JwtPayload = {
  email: string;
  iat: number;
  user_id: number;
};

const ImpersonatePage = () => {
  const dispatch = useDispatch();
  const token = window.location.hash.slice(1);

  useEffect(() => {
    if (!token) {
      return;
    }
    const { email, iat, user_id } = jwtDecode<JwtPayload>(token);
    // iat is not created_at, but it doesnt matter
    const created_at = new Date(iat * 1000).toISOString();

    // emulate success authorize request
    // to do all side effects, like saving to local storage or init libraries
    dispatch({
      type: resolved(auth.actions.authorize),
      payload: {
        token,
        user_id,
        created_at,
        email,
      },
      meta: {
        requestAction: {
          type: auth.actions.authorize.key,
          payload: {
            type: 'login',
            provider: 'impersonate',
            form: {
              login: email,
            },
            isForgot: false,
          },
        },
        response: {
          success: true,
          data: {
            token,
            user_id,
            created_at,
            email,
          },
        },
      },
    });

    // redirect to yield authorize
    const url = new URL(
      `${getYieldHost()}/authorize${window.location.search}${
        window.location.hash
      }`,
    );
    (window as Window).location = url.toString();
  }, [dispatch, token]);

  if (!token) {
    return <Redirect to='/' />;
  }

  return (
    <div>
      <BodyClassName className='onesoil-hello-page' />
      <Loader />
    </div>
  );
};

export default ImpersonatePage;
