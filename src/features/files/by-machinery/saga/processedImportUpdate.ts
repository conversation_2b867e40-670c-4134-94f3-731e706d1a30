import { put, select } from 'redux-saga/effects';

import { getMessageForPreprocessingSuccess } from './toasts/getMessageForPreprocessingSuccess';
import { getMessageForPreprocessingErrors } from './toasts/getMessageForPreprocessingErrors';
import { ImportingTaskStatus } from '../types/tasks';
import { getImportStatusForNotifications } from '../redux/selectors/getImportStatusForNotifications';

export function* processedImportUpdate() {
  const importStatus: ImportingTaskStatus = yield select(
    getImportStatusForNotifications,
  );

  let message;
  if (
    importStatus === ImportingTaskStatus.success ||
    importStatus === ImportingTaskStatus.partlySuccess
  ) {
    message = getMessageForPreprocessingSuccess();
  }

  if (importStatus === ImportingTaskStatus.failed) {
    message = getMessageForPreprocessingErrors();
  }

  if (message) {
    yield put(message);
  }
}
