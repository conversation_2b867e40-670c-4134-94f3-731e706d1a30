import { styled } from 'linaria/react';

export const SubLabelItemStyle = styled.div`
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;

  color: #a5b2bc;
`;

export const CompactItemStyle = styled.div`
  &.modal-select__item {
    font-size: 12px;
    line-height: 130%;
    padding: 5px 7px;
  }

  &.modal-select__item.__danger {
    color: #f44336;
  }

  &.modal-select__item.__danger:hover {
    color: #f44336;
    background-color: #ffebee;
  }
`;

export const RegularItemStyle = styled.div`
  &.modal-select__item.__danger {
    color: #f44336;
  }

  &.modal-select__item.__danger:hover {
    color: #f44336;
    background-color: #ffebee;
  }
`;
