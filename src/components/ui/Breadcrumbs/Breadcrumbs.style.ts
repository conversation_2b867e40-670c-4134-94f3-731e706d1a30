import { styled } from 'linaria/react';

export const BreadCrumbsItem = styled.li<{ withoutLastItem?: boolean }>`
  position: relative;
  padding-right: 9px;
  margin-right: 7px;

  &:last-child {
    padding-right: 0;
    margin-right: 0;
    display: ${props => (props.withoutLastItem ? 'none' : 'inline')};
  }

  &:last-child:after {
    display: none;
  }

  &:after {
    position: absolute;
    right: 0;
    content: '';
    width: 4px;
    height: 7px;
    top: 7px;
    background-image: url("data:image/svg+xml,%3Csvg width='4' height='7' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cg transform='translate(-343 -24)'%3E%3Cpath d='M343.14643,24.14623c0.19497,-0.19497 0.51109,-0.19497 0.70606,0l0.00048,0.00049l2.47511,2.48193l0.34953,0.34568c0.19635,0.19418 0.19811,0.51075 0.00393,0.7071l-0.00196,0.00197l-0.3515,0.3515l-2.47511,2.48193c-0.1947,0.19524 -0.51081,0.19568 -0.70605,0.00097l-0.00049,-0.00048c-0.19524,-0.19524 -0.19524,-0.51179 0,-0.70703l2.30174,-2.30174c0.09763,-0.09763 0.09763,-0.25592 0,-0.35355l-2.30174,-2.30174c-0.19524,-0.19524 -0.19524,-0.51179 0,-0.70703z' fill='%23000000'/%3E%3C/g%3E%3C/svg%3E");
  }
`;

export const BreadCrumbsContainer = styled.nav`
  padding: 0;
  margin: 0;
  flex: 1 1 auto;
`;

export const BreadCrumbsList = styled.ul`
  list-style-type: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-wrap: wrap;
`;
