import React, { Fragment, useCallback, useState } from 'react';
import { useSelector } from 'react-redux';
import { GeoJSON } from 'react-leaflet';
import { connect } from 'react-redux';
import groupBy from 'lodash/groupBy';
import keyBy from 'lodash/keyBy';
import sortBy from 'lodash/sortBy';
import pick from 'lodash/pick';
import {
  Form,
  Control,
  getModel,
  actions as formActions,
} from 'react-redux-form';

import { css } from 'linaria';
import Icon from 'components/Icon';
import Button from 'components/ui/Button/Button';
import Saga from 'components/saga/Saga';
import Breadcrumbs from 'components/ui/Breadcrumbs/Breadcrumbs';
import EmbeddedMap from 'features/platform/EmbeddedMap';
import RRFUnsavedPrompt from 'features/platform/RRFUnsavedPrompt';
import SeasonSelector from 'features/seasons/SeasonSelector';
import RRFFloatingFormActions from 'components/ui/FloatingActions/RRFFloatingFormActions';
import EnterExitTransition from 'components/EnterExitTransition';
import EditFieldCurrentSeasonRow from './EditFieldCurrentSeasonRow';
import EditFieldOtherSeasonRow from './EditFieldOtherSeasonRow';
import EditFieldGeometryPopup from './EditFieldGeometryPopup';
import VRAGeometryAlertPopup from './VRAGeometryAlertPopup';
import LinkedNotesWarningPopup from 'features/fields/LinkedNotesWarningPopup';
import MissingFieldRedirector from './MissingFieldRedirector';
import ProposeCulturePopup from './ProposeCulturePopup';
import EditFieldTitle from './EditFieldTitle';
import { DefaultStyle } from './OwnFieldsLayer';
import { PopoverDeprecatedAlign } from 'components/ui/Popover';

import fields from 'modules/fields';
import notes from 'modules/notes';
import seasons from 'modules/seasons';
import settings from 'modules/settings';
import entities from 'modules/entities';
import multiaccount from 'modules/multiaccount';

import {
  FEATURES,
  FeatureDisabledTooltip,
  ProtectedRouteHOC,
} from 'features/permissions';

import { paSuitableStatuses } from 'constants/fields';
import forms from 'constants/forms';
import editFieldSaga from 'sagas/local/edit-field';
import { formatTitle } from 'utils/fields-formatters';
import { getViewportHash } from 'utils/get-root-scouting-url';
import getFieldArea from 'utils/get-field-area';
import {
  formatTitle as formatSeasonTitle,
  formatDateRange,
} from 'utils/seasons-formatters';
import { parseQuery } from 'utils/query';
import { formatDate } from 'utils/format-date';
import { useTranslate } from 'utils/use-translate';
import { getYieldUrl } from 'utils/yield';

const styles = {
  seasonNameRow: css`
    td {
      border-bottom: none;
      height: auto;
      padding-top: 14px;
      padding-bottom: 0;
      font-size: 14px;
      line-height: 18px;
      border-top: 0.5px solid #d6dce0;
    }

    + tr {
      td {
        padding-top: 5px;
      }
    }
  `,
  seasonDates: css`
    margin-left: 10px;
    color: #a5b2bc;
  `,
};

const EditFieldSection = ({
  history,
  location,
  seasons,
  field,
  crops,
  layer,
  fieldTitle,
  currentSeason,
  yieldUnitsByCrop,
  formatUnit,
  onAddCrop,
  onUpdate,
  onRemove,
  onUpdateSettings,
}) => {
  const { i18n, t } = useTranslate();
  const query = parseQuery(location);
  const hasYieldAccess = useSelector(
    multiaccount.selectors.hasYieldAccessInCurrentWorkspace,
  );
  const fieldSeason = useSelector(state =>
    entities.selectors.getByID(state, 'fieldSeason', field?.fieldUserSeasonId),
  );
  const season = useSelector(state =>
    entities.selectors.getByID(state, 'season', currentSeason?.id),
  );
  const currentMembership = useSelector(
    multiaccount.selectors.getCurrentMembership,
  );
  const notesInSeason = useSelector(state =>
    notes.selectors.getInSeason(state, currentSeason?.id),
  );
  const processingStatus = useSelector(state =>
    entities.selectors.getByID(
      state,
      'paInfo',
      fieldSeason?.active_pa_info_uuid,
    ),
  );
  const [selectedSeasonId, setSelectedSeasonId] = useState(
    query.season ? +query.season : currentSeason?.id,
  );
  const [showEditGeometryPopup, setShowEditGeometryPopup] = useState(false);
  const [showVRAGeometryAlert, setShowVRAGeometryAlert] = useState(false);
  const [showPopup, setShowPopup] = useState(false);
  const [showFieldWarning, setShowFieldWarning] = useState(null);
  const cropsBySeason = groupBy(
    sortBy(
      crops.map((crop, index) => ({ ...crop, index })),
      'created_at',
    ),
    'season_id',
  );
  const fieldSeasons = seasons.filter(season => cropsBySeason[season.id]);

  const hasAttachedNotes = useCallback(() => {
    return notesInSeason.some(
      note => fieldSeason.uuid === note.field_user_season_uuid,
    );
  }, [notesInSeason, fieldSeason]);

  const removeField = () => {
    if (hasAttachedNotes()) {
      setShowFieldWarning({ type: 'fieldWithNotes' });
    } else {
      setShowFieldWarning({ type: 'field' });
    }
  };

  if (!field || !currentSeason) {
    return null;
  }

  return (
    <div className='main-section'>
      <Saga saga={editFieldSaga} field={field} history={history} />
      <MissingFieldRedirector fieldId={field.id} />
      <RRFUnsavedPrompt
        message={t('toasts.unsaved_prompt')}
        model={forms.field}
      />
      <EnterExitTransition variant='popup'>
        {showFieldWarning && (
          <LinkedNotesWarningPopup
            t={t}
            title={t('fields.warning.title')}
            text={
              showFieldWarning?.type === 'field'
                ? t('fields.delete-field.warning.text', {
                    count: 1,
                  })
                : t('fields.linked-notes.warning.text', {
                    count: 1,
                  })
            }
            onConfirm={() => {
              onRemove(field, currentSeason.id);
              setShowFieldWarning(false);
            }}
            onCancel={() => {
              setShowFieldWarning(false);
            }}
          />
        )}
      </EnterExitTransition>
      <EnterExitTransition variant='popup'>
        {showVRAGeometryAlert && (
          <VRAGeometryAlertPopup
            t={t}
            processingStatus={processingStatus}
            onProceed={() => {
              setShowVRAGeometryAlert(false);
              setShowEditGeometryPopup(true);
            }}
            onClose={() => setShowVRAGeometryAlert(false)}
          />
        )}
      </EnterExitTransition>
      {showEditGeometryPopup && (
        <EditFieldGeometryPopup
          location={location}
          history={history}
          field={field}
          onClose={() => {
            setShowEditGeometryPopup(false);
          }}
        />
      )}

      <EnterExitTransition variant='popup'>
        {showPopup && (
          <ProposeCulturePopup
            onCancel={() => {
              setShowPopup(false);
            }}
          />
        )}
      </EnterExitTransition>
      <div className='main-section__body'>
        <div className='main-section__header'>
          <Breadcrumbs>
            <Breadcrumbs.Item>
              <a
                href={getYieldUrl(
                  i18n,
                  `/${fieldSeason.uuid}/status`,
                  getViewportHash(location.pathname),
                  season?.uuid,
                  currentMembership?.workspace_id,
                )}
              >
                {formatTitle(t, field)}
              </a>
            </Breadcrumbs.Item>
            <Breadcrumbs.Item>{t('fields.edit.title')}</Breadcrumbs.Item>
          </Breadcrumbs>
          <Button
            className='edit-action-delete'
            onClick={() => {
              removeField();
            }}
          >
            <span className='edit-action-delete__ico'>
              <Icon className='ico-trash-n-os' name='trashcan' />
            </span>
            {t('fields.edit.remove_from_season', {
              season: formatSeasonTitle(t, currentSeason),
            })}
          </Button>
        </div>
        <div className='field-edit'>
          <Form
            model={forms.field}
            onSubmit={form => {
              const fieldToSend = {
                ...field,
                ...pick(form, 'title'),
              };

              fieldToSend.crops = form.crops
                .filter(crop => crop.crop)
                .map(crop => {
                  return {
                    ...crop,
                    source: crop.source || 'field_card',
                    yield_value:
                      crop.yield_value != null && crop.yield_value !== ''
                        ? +crop.yield_value
                        : null,
                  };
                });
              fieldToSend.seasons = Object.keys(
                keyBy(form.crops, 'season_id'),
              ).map(id => ({
                season_id: +id,
              }));

              onUpdateSettings({
                yieldUnitsByCrop: {
                  ...yieldUnitsByCrop,
                  ...fieldToSend.crops.reduce((acc, cur) => {
                    if (cur.yield_value_units) {
                      acc[cur.crop] = cur.yield_value_units;
                    }
                    return acc;
                  }, {}),
                },
              });

              onUpdate({
                field: fieldToSend,
                seasonId: currentSeason.id,
                origin: field,
              });
            }}
          >
            <Control.select
              model='.title'
              component={EditFieldTitle}
              placeholder={t('fields.edit.title_placeholder')}
            />
            <div className='field-edit-bound'>
              <div
                className='field-edit-bound__map'
                data-label={getFieldArea({ field, formatUnit })}
              >
                <EmbeddedMap
                  id='preview-field'
                  interactive={false}
                  initialViewport={getViewportHash(location.pathname)}
                >
                  <GeoJSON
                    key={layer.key}
                    data={layer.geojson}
                    style={DefaultStyle}
                    interactive={false}
                  />
                </EmbeddedMap>
              </div>
              <div className='field-edit-bound__content'>
                <h2 className='field-edit__title'>
                  {t('fields.edit.geometry.title')}
                </h2>
                <p>{t('fields.edit.geometry.description')}</p>
                <div className='field-edit-bound__action'>
                  <FeatureDisabledTooltip
                    feature={FEATURES.FIELD_EDIT_BOUNDARIES}
                    data={fieldSeason}
                    direction={PopoverDeprecatedAlign.MiddleLeft}
                  >
                    <Button
                      className='btn btn-default btn-lg'
                      onClick={() => {
                        if (
                          !hasYieldAccess &&
                          (paSuitableStatuses.includes(
                            processingStatus?.suitability_status,
                          ) ||
                            processingStatus?.status === 'processing')
                        ) {
                          setShowVRAGeometryAlert(true);
                        } else {
                          setShowEditGeometryPopup(true);
                        }
                      }}
                    >
                      <span className='btn__ico'>
                        <Icon width={16} height={16} name='pencil' />
                      </span>
                      {t('fields.edit.geometry')}
                    </Button>
                  </FeatureDisabledTooltip>
                </div>
              </div>
            </div>
            <div className='field-edit-details'>
              <h2 className='field-edit__title'>{t('fields.crops.label')}</h2>
              <div className='table-wrapper'>
                <table className='table table-seasons' data-layout='fixed'>
                  <colgroup>
                    <col width={151} />
                    <col />
                    <col />
                    <col />
                    <col width={130} />
                    <col width={123} />
                  </colgroup>
                  <thead>
                    <tr>
                      <th>{t('fields.edit.crops.crop')}</th>
                      <th>{t('fields.edit.crops.variety_hybrid')}</th>
                      <th>{t('fields.edit.crops.sowing_date')}</th>
                      <th>{t('fields.edit.crops.harvest_date')}</th>
                      <th>{t('fields.edit.crops.yield_value')}</th>
                      <th>&nbsp;</th>
                    </tr>
                  </thead>
                  <tbody>
                    {fieldSeasons.map(season =>
                      season.id === selectedSeasonId ? (
                        <Fragment key={season.id}>
                          <tr className={styles.seasonNameRow}>
                            <td colSpan={6}>
                              <span>{formatSeasonTitle(t, season)}</span>
                              <span className={styles.seasonDates}>
                                {formatDateRange(formatDate, t, season)}
                              </span>
                            </td>
                          </tr>
                          <EditFieldCurrentSeasonRow
                            season={season}
                            crops={cropsBySeason[season.id]}
                            fieldUserSeasonId={field.fieldUserSeasonId}
                            setShowPopup={setShowPopup}
                          />
                        </Fragment>
                      ) : (
                        <Fragment key={season.id}>
                          <tr className={styles.seasonNameRow}>
                            <td colSpan={6}>
                              <span>{formatSeasonTitle(t, season)}</span>
                              <span className={styles.seasonDates}>
                                {formatDateRange(formatDate, t, season)}
                              </span>
                            </td>
                          </tr>
                          <EditFieldOtherSeasonRow
                            season={season}
                            crops={cropsBySeason[season.id]}
                            onSelect={() => {
                              setSelectedSeasonId(season.id);
                            }}
                            styles={styles}
                          />
                        </Fragment>
                      ),
                    )}
                  </tbody>
                </table>
                <SeasonSelector
                  disableFieldCopying
                  excludeIDs={fieldSeasons.map(season => season.id)}
                  renderTrigger={({ noMoreSeasons, ...props }) => (
                    <div className='table-seasons-addnew'>
                      <Button className='btn btn-default' {...props}>
                        <span className='btn__ico'>
                          <Icon className='ico-plus-os' name='plus-btn' />
                        </span>
                        {t(
                          `fields.edit.${
                            noMoreSeasons ? 'create_season' : 'add_season'
                          }`,
                        )}{' '}
                        {!noMoreSeasons && <span className='btn__arrow' />}
                      </Button>
                    </div>
                  )}
                  onSelect={seasonId => {
                    onAddCrop({ season_id: seasonId });
                    setSelectedSeasonId(seasonId);
                  }}
                />
              </div>
            </div>
          </Form>
        </div>
      </div>
      <RRFFloatingFormActions
        model={forms.field}
        title={t('fields.edit.title')}
        entityName={formatTitle(t, {
          index_number: field.index_number,
          title: fieldTitle,
        })}
        submitLabel={t('forms.save')}
        cancelLabel={t('forms.cancel')}
        onCancel={() => {
          if (query.next) {
            window.location = decodeURIComponent(query.next);
            return;
          }
          history.goBack();
        }}
      />
    </div>
  );
};

const mapStateToProps = (state, ownProps) => ({
  seasons: seasons.selectors.getAll(state),
  currentSeason: seasons.selectors.getCurrent(state),
  crops: getModel(state, `${forms.field}.crops`, []),
  fieldTitle: getModel(state, `${forms.field}.title`),
  field: fields.selectors.getByID(state, ownProps.match.params.fieldId),
  layer: fields.selectors.getFieldLayer(state, ownProps.match.params.fieldId),
  formatUnit: settings.selectors.getUnitFormatter(state),
  yieldUnitsByCrop: settings.selectors.getYieldUnitsByCrop(state),
});

const mapDispatchToProps = {
  onAddCrop: crop => formActions.push(`${forms.field}.crops`, crop),
  onUpdate: fields.actions.update,
  onRemove: fields.actions.remove,
  onUpdateSettings: settings.actions.updateLocal,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(
  ProtectedRouteHOC({
    Component: EditFieldSection,
    feature: FEATURES.FIELD_EDIT,
    dataForPermission: (state, ownProps) => {
      const field = fields.selectors.getByID(
        state,
        ownProps.match.params.fieldId,
      );

      return entities.selectors.getByID(
        state,
        'fieldSeason',
        field?.fieldUserSeasonId,
      );
    },
    redirect: '/fields',
  }),
);
