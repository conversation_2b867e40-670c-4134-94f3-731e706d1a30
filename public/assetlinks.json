[{"relation": ["delegate_permission/common.handle_all_urls"], "target": {"namespace": "android_app", "package_name": "io.onesoil.scouting", "sha256_cert_fingerprints": ["46:62:C5:A7:D6:80:8A:69:44:F6:91:4A:82:45:4D:A7:F9:5C:B5:43:10:51:7B:ED:D6:0C:7F:BC:2D:61:FD:C1", "B8:D0:8C:1C:B6:52:5C:67:A5:19:32:2E:43:B6:56:11:76:AA:6B:FF:82:29:81:B5:17:C5:15:28:C1:19:D9:BB"]}}, {"relation": ["delegate_permission/common.handle_all_urls"], "target": {"namespace": "android_app", "package_name": "io.onesoil.scouting.staging", "sha256_cert_fingerprints": ["46:62:C5:A7:D6:80:8A:69:44:F6:91:4A:82:45:4D:A7:F9:5C:B5:43:10:51:7B:ED:D6:0C:7F:BC:2D:61:FD:C1", "B8:D0:8C:1C:B6:52:5C:67:A5:19:32:2E:43:B6:56:11:76:AA:6B:FF:82:29:81:B5:17:C5:15:28:C1:19:D9:BB"]}}, {"relation": ["delegate_permission/common.handle_all_urls"], "target": {"namespace": "android_app", "package_name": "io.onesoil.scouting.debug", "sha256_cert_fingerprints": ["46:62:C5:A7:D6:80:8A:69:44:F6:91:4A:82:45:4D:A7:F9:5C:B5:43:10:51:7B:ED:D6:0C:7F:BC:2D:61:FD:C1", "B8:D0:8C:1C:B6:52:5C:67:A5:19:32:2E:43:B6:56:11:76:AA:6B:FF:82:29:81:B5:17:C5:15:28:C1:19:D9:BB"]}}]