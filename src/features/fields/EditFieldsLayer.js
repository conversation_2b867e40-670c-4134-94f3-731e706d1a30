import React from 'react';
import { GeoJSON, Pane } from 'react-leaflet';
import { connect } from 'react-redux';
import L from 'leaflet';

import SelectedFieldScouting from 'features/scouting/SelectedFieldScouting/SelectedFieldScouting';

import map from 'modules/map';
import fields from 'modules/fields';

import ZIndex from 'constants/zindex';

export const DefaultStyle = {
  color: '#FFE767',
  fillOpacity: 0,
  opacity: 1,
  weight: 2,
};

const HiddenStyle = {
  opacity: 0,
  fillOpacity: 0,
};

const AddedStyle = {
  color: '#FFE767',
  fillOpacity: 0.25,
  opacity: 1,
  weight: 2,
};

const SelectedStyle = {
  color: 'white',
  fillOpacity: 0,
  opacity: 1,
  weight: 2,
};

const EditFieldsLayer = ({
  ownLayer,
  highlightedId,
  checkedIDs,
  location,
  locked,
  match,
  history,
  onHighlight,
  onCheck,
  onRemoveCheck,
  onRemoveAll,
}) => (
  <Pane style={{ zIndex: ZIndex.FieldsPane }}>
    <GeoJSON
      key={ownLayer.key}
      data={ownLayer.geojson}
      style={feature => {
        const { fieldId } = match.params;
        if (
          location.pathname.endsWith('/edit') &&
          fieldId === feature.properties.id
        ) {
          return HiddenStyle;
        }
        if (fieldId === 'add') {
          return AddedStyle;
        }
        const selected =
          fieldId === feature.properties.id ||
          highlightedId === feature.properties.id ||
          checkedIDs.includes(feature.properties.id);
        return selected ? SelectedStyle : DefaultStyle;
      }}
      onMouseover={mapEvent => {
        const { feature } = mapEvent.layer;
        onHighlight(feature.properties.id);
      }}
      onMouseout={mapEvent => {
        onHighlight(null);
      }}
      onClick={mapEvent => {
        const { feature } = mapEvent.layer;
        if (locked) {
          return;
        }
        L.DomEvent.stopPropagation(mapEvent);
        if (checkedIDs.includes(feature.properties.id)) {
          onRemoveCheck(feature.properties.id);
        } else {
          onCheck(feature.properties.id);
        }
      }}
    />
    {match.params.fieldId && !location.pathname.endsWith('/edit') && (
      <SelectedFieldScouting fieldId={match.params.fieldId} />
    )}
  </Pane>
);

const mapStateToProps = state => ({
  ownLayer: fields.selectors.getOwnLayer(state),
  highlightedId: fields.selectors.getHighlightedID(state),
  checkedIDs: fields.selectors.getCheckedIDs(state),
  locked: map.selectors.isNavigationLocked(state),
});

const mapDispatchToProps = {
  onHighlight: fields.actions.highlight,
  onCheck: fields.actions.check,
  onRemoveCheck: fields.actions.removeCheck,
  onRemoveAll: fields.actions.removeAllChecked,
};

export default connect(mapStateToProps, mapDispatchToProps)(EditFieldsLayer);
