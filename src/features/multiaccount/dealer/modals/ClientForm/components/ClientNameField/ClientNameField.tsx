import React, { forwardRef } from 'react';
import type { Control } from 'react-hook-form';
import type { FieldErrors } from 'react-hook-form/dist/types/errors';

import { ClientField } from 'features/multiaccount/dealer/modals/ClientForm/components/ClientField';
import {
  ClientForm,
  ClientFormType,
} from 'features/multiaccount/dealer/modals/ClientForm/types';

import { ErrorData } from 'modules/entities/types';

export type ClientNameFieldProps = {
  control: Control<ClientForm>;
  formErrors: FieldErrors<ClientForm>;
  isPending: boolean;
  requestErrors: Nullable<ErrorData>;
  type: ClientFormType;
  name?: string;
};

const ClientNameField = forwardRef<HTMLInputElement, ClientNameFieldProps>(
  (props, ref) => {
    return (
      <ClientField
        {...props}
        ref={ref}
        name={props.name || 'client_name'}
        columnTranslationKey={`multiaccount.${props.type}-modal.column.client-name`}
        placeholderTranslationKey={`multiaccount.${props.type}-modal.placeholder.client-name`}
      />
    );
  },
);

export default ClientNameField;
