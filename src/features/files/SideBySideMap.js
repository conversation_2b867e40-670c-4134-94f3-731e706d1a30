import React, { useEffect, useRef, useState, useCallback } from 'react';
import { useSelector } from 'react-redux';

import Saga from 'components/saga/Saga';

import analyzeFieldSaga from 'sagas/local/analyze-field';
import ControlledMap from 'components/maps/ControlledMap';
import DirectGoogleLayer from 'components/maps/DirectGoogleLayer';
import { StyledBottomMapWidget } from 'components/ui/mapToolBar/styles';
import RangeLegend from 'components/RangeLegend';

import SelectedFieldFiles from 'features/files/SelectedFieldFiles';
import ZoomControls, {
  MinZoom,
  MaxZoom,
} from 'features/platform/ZoomControls/ZoomControls';

import { isSafari } from 'utils/devices';
import { safeSetItem, safeGetItem } from 'utils/storage';
import { BarsCount, LegendColorsMap } from 'constants/style';
import fields from 'modules/fields';

import { useTranslate } from 'utils/use-translate';

const ANIMATION_SEEN_KEY = 'OneSoilSideBySideAnimationSeen';
const ANIMATION_MAP = 'side-by-side__map--animated';
const ANIMATION_SLIDER = 'side-by-side__slider--animated';

const SideBySideMap = ({
  history,
  field,
  firstDateIndex,
  secondDateIndex,
  zoom,
  renderValue,
  isVisible,
}) => {
  const { t } = useTranslate();
  const [mapPercent, setMapPercent] = useState(50);
  const [isSliding, setIsSliding] = useState(false);

  const MapA = useRef(null);
  const MapB = useRef(null);
  const containerRef = useRef(null);
  const sliderRef = useRef(null);
  const leftMapRef = useRef(null);
  const rightMapRef = useRef(null);
  const fieldFillingType = useSelector(fields.selectors.getFieldFillingType);
  const animateSlider = useCallback(() => {
    const animationAlreadySeen = safeGetItem(ANIMATION_SEEN_KEY);
    if (
      !animationAlreadySeen &&
      [leftMapRef, rightMapRef, sliderRef].every(Boolean)
    ) {
      setTimeout(() => {
        leftMapRef.current.classList.add(`${ANIMATION_MAP}-left`);
        rightMapRef.current.classList.add(`${ANIMATION_MAP}-right`);
        sliderRef.current.classList.add(ANIMATION_SLIDER);
        safeSetItem(ANIMATION_SEEN_KEY, 'true');
      }, 1500);
    }
  }, [leftMapRef, rightMapRef, sliderRef]);

  useEffect(() => {
    if (MapA.current?.map && MapB.current?.map) {
      MapA.current.map.sync(MapB.current.map);
      MapB.current.map.sync(MapA.current.map);
    }
  }, []);

  useEffect(() => {
    isVisible && animateSlider();
  }, [isVisible, animateSlider]);

  useEffect(() => {
    let currentContainerWidth =
      containerRef.current.getBoundingClientRect().width;

    const startSliding = e => {
      // Prevent default behavior other than mobile scrolling
      if (!('touches' in e)) {
        e.preventDefault();
      }

      currentContainerWidth =
        containerRef.current.getBoundingClientRect().width;

      // Slide the image even if you just click or tap (not drag)
      handleSliding(e);

      window.addEventListener('mousemove', handleSliding);
      window.addEventListener('touchmove', handleSliding);
    };

    const handleSliding = event => {
      setIsSliding(true);
      const e = event || window.event;

      const cursorXfromViewport = e.touches ? e.touches[0].pageX : e.pageX;
      const cursorXfromWindow = cursorXfromViewport - window.pageXOffset;

      const mapPosition = rightMapRef.current.getBoundingClientRect();

      let pos = cursorXfromWindow - mapPosition.left;

      if (pos < 1) pos = 1;
      if (pos > currentContainerWidth - 1) pos = currentContainerWidth - 1;

      const mapPercent = (pos * 100) / currentContainerWidth;

      setMapPercent(mapPercent);
    };

    const finishSliding = () => {
      setIsSliding(false);
      window.removeEventListener('mousemove', handleSliding);
      window.removeEventListener('touchmove', handleSliding);
    };

    const sliderElement = sliderRef.current;

    sliderElement.addEventListener('mousedown', startSliding);
    window.addEventListener('mouseup', finishSliding);
    // for mobile
    sliderElement.addEventListener('touchstart', startSliding);
    window.addEventListener('touchend', finishSliding);

    return () => {
      finishSliding();
      sliderElement.removeEventListener('mousedown', startSliding);
      sliderElement.removeEventListener('touchstart', startSliding);
      window.removeEventListener('mouseup', finishSliding);
      window.removeEventListener('touchend', finishSliding);
    };
  }, []);

  // Due to safari bug https://bugs.webkit.org/show_bug.cgi?id=152548,
  // clip-path works visually, but clicks go to wrong side of the map.
  // We hotfix it here by manipulating z-index of halves as mouse moves over
  const applySafariHack = event => {
    let containerBox = containerRef.current.getBoundingClientRect();

    const cursorXfromViewport = event.touches
      ? event.touches[0].pageX
      : event.pageX;
    const cursorXfromWindow = cursorXfromViewport - window.pageXOffset;
    const cursorXfromContainer = cursorXfromWindow - containerBox.left;

    const hoveredLeftMap =
      cursorXfromContainer < (containerBox.width * mapPercent) / 100;

    (hoveredLeftMap ? leftMapRef : rightMapRef).current.style.zIndex = 2;
    (hoveredLeftMap ? rightMapRef : leftMapRef).current.style.zIndex = 1;
  };

  const styles = {
    slider: {
      left: `calc(${mapPercent}% - 2px)`,
    },
    rightMap: {
      clipPath: `polygon(${mapPercent}% 0%, 100% 0%, 100% 100%, ${mapPercent}% 100%)`,
      WebkitClipPath: `polygon(${mapPercent}% 0%, 100% 0%, 100% 100%, ${mapPercent}% 100%)`,
    },
    leftMap: {
      clipPath: `polygon(0% 0%, ${mapPercent}% 0%, ${mapPercent}% 100%, 0% 100%)`,
      WebkitClipPath: `polygon(0% 0%, ${mapPercent}% 0%, ${mapPercent}% 100%, 0% 100%)`,
    },
  };

  const setView = () => {
    if (MapA.current?.map) {
      MapA.current.map._selfSetView();
    }
  };

  return (
    <>
      <Saga
        saga={analyzeFieldSaga}
        field={field}
        mapID='side-by-side'
        history={history}
      />
      <div
        ref={containerRef}
        className='map-viewer-grid-hrow'
        onMouseMove={isSafari() ? applySafariHack : null}
      >
        <div className='side-by-side__ndvi'>
          <div className='map-viewer-grid__header' data-align='left'>
            1.{' '}
            {t(`analysis.item.${fieldFillingType === 'rgb' ? 'rgb' : 'ndvi'}`)},{' '}
            {renderValue(firstDateIndex)}
          </div>
        </div>
        <div className='side-by-side__ndvi'>
          <div
            className='map-viewer-grid__header map-viewer-grid__header--right'
            data-align='right'
          >
            2.{' '}
            {t(`analysis.item.${fieldFillingType === 'rgb' ? 'rgb' : 'ndvi'}`)},{' '}
            {renderValue(secondDateIndex)}
          </div>
        </div>
        <div
          ref={leftMapRef}
          className='side-by-side__map'
          style={styles.leftMap}
        >
          <ControlledMap
            ref={MapA}
            id='side-by-side'
            viewportHash='default'
            onViewportHashChange={() => {}}
            mapProps={{
              className: 'map-viewer',
              trackResize: true,
              zoomControl: false,
              worldCopyJump: true,
              editable: false,
              minZoom: MinZoom,
              boxZoom: false,
              doubleClickZoom: zoom < MaxZoom,
            }}
          >
            <DirectGoogleLayer />
            {field && field.id && (
              <SelectedFieldFiles
                fieldId={field.id}
                index={firstDateIndex}
                hideVegetationTooltip={isSliding}
              />
            )}
          </ControlledMap>
        </div>
        <div
          ref={rightMapRef}
          className='side-by-side__map'
          style={styles.rightMap}
        >
          <ControlledMap
            ref={MapB}
            id='side-by-side'
            viewportHash='default'
            onViewportHashChange={() => {}}
            mapProps={{
              className: 'map-viewer',
              trackResize: true,
              zoomControl: false,
              worldCopyJump: true,
              editable: false,
              minZoom: MinZoom,
              boxZoom: false,
              doubleClickZoom: zoom < MaxZoom,
            }}
          >
            <DirectGoogleLayer />
            {field && field.id && (
              <SelectedFieldFiles
                fieldId={field.id}
                index={secondDateIndex}
                hideVegetationTooltip={isSliding}
              />
            )}
          </ControlledMap>
        </div>

        <div
          ref={sliderRef}
          className='map-viewer-grid-vdivider side-by-side__slider'
          style={styles.slider}
        >
          <button type='button' className='map-viewer-grid-vdivider__button' />
        </div>
        {fieldFillingType === 'rgb' ? null : (
          <StyledBottomMapWidget>
            <RangeLegend
              colorsRange={LegendColorsMap[
                fieldFillingType === 'default'
                  ? 'field_ndvi'
                  : 'contrasted_ndvi'
              ].colors(BarsCount)}
              labels={[
                t('fields.ndvi.legend.low'),
                t('fields.ndvi.legend.high'),
              ]}
              withClouds
            />
          </StyledBottomMapWidget>
        )}
        <ZoomControls
          id='side-by-side'
          showFieldLocation
          showGlance
          field={field}
          setView={setView}
        />
      </div>
    </>
  );
};

export default SideBySideMap;
