import React from 'react';
import { css } from 'linaria';

import Modal from 'components/ui/Modal/Modal';
import Button from 'components/ui/Button/Button';

import { formatTitle } from 'utils/seasons-formatters';
import { useTranslate } from 'utils/use-translate';

const popupText = css`
  font-size: 16px;
  line-height: 22px;
  text-align: center;
`;

const AutoFillWarningPopup = ({ baseUrl, season, onConfirm, onCancel }) => {
  const { t } = useTranslate();
  const seasonTitle = season.title || formatTitle(t, season);

  return (
    <Modal
      show
      width={400}
      onClose={onCancel}
      ModalHeader={() => (
        <div style={{ textAlign: 'center' }}>
          {t('croprotation.autofill.warning.popup.title')}
        </div>
      )}
      ModalBody={() => (
        <div className={popupText}>
          {t('croprotation.autofill.warning.popup.text', {
            seasonTitle,
          })}
        </div>
      )}
      ModalActions={() => (
        <>
          <Button className='btn btn-primary btn-lg' onClick={onCancel}>
            {t('forms.cancel')}
          </Button>
          <Button className='btn btn-success btn-lg' onClick={onConfirm}>
            {t('forms.proceed')}
          </Button>
        </>
      )}
    />
  );
};

export default AutoFillWarningPopup;
