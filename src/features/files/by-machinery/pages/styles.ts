import { styled } from 'linaria/react';

export const CentralSection = styled.div`
  margin: 60px auto;
  max-width: 670px;
`;

export const FullPageCentralSection = styled.div`
  margin: 60px auto 120px;
  width: fit-content;
  display: flex;
  align-items: center;
  flex-direction: column;
  padding: 0 40px;
`;

export const PageStyle = styled.div`
  padding: 48px;
`;

export type ImportTaskHeaderProps = {
  isTableTooWide: boolean;
};

export const ImportTaskHeader = styled.div<ImportTaskHeaderProps>`
  display: flex;
  align-self: flex-start;
  flex-direction: column;
  align-items: ${({ isTableTooWide }) =>
    isTableTooWide ? 'flex-start' : 'center'};
`;

export const ImportTaskTitle = styled.h2`
  font-size: 36px;
  font-weight: 700;
  text-align: left;
  margin: 0;
`;

export const ImportTaskDescription = styled.div`
  font-size: 17px;
  font-weight: 400;
  max-width: 640px;
  line-height: 24px;
  text-align: left;
  margin: 10px 0 0;
`;
