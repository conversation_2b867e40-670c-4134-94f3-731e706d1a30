import React, { forwardRef } from 'react';
import { Controller } from 'react-hook-form';
import { cx } from 'linaria';
import { FieldErrors } from 'react-hook-form/dist/types/errors';

import { ClientForm } from 'features/multiaccount/dealer/modals/ClientForm/types';
import { useTranslate } from 'utils/use-translate';
import { ErrorData } from 'modules/entities/types';

import { styles } from './ClientBulkEmailField.styles';

export type ClientBulkEmailFieldProps = {
  requestErrors: Nullable<ErrorData>;
  formErrors: FieldErrors<ClientForm>;
  isPending: boolean;
  control: TEMPORARY_ANY;
};

const ClientBulkEmailField = forwardRef<
  HTMLTextAreaElement,
  ClientBulkEmailFieldProps
>(({ requestErrors, formErrors, control }, ref) => {
  const { t } = useTranslate();

  return (
    <div className='form-group'>
      <label className='form-label'>
        {t('multiaccount.invite-client-modal.column.email')}
      </label>
      <Controller
        name='emailBulk'
        control={control}
        rules={{
          required: t('multiaccount.dealer.error.required') as string,
          pattern: {
            // https://github.com/react-hook-form/react-hook-form/issues/3060
            // I choose to everytime create new RegExp object to reset lastIndex because of global flag
            value: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,6}\b/g,
            message: t('multiaccount.dealer.error.email') as string,
          },
        }}
        render={({ value, onChange }, { invalid }) => (
          <>
            <textarea
              ref={ref}
              id='emailBulk'
              tabIndex={1}
              autoFocus
              className={cx(
                'form-input size-full form-textarea-no-resize',
                invalid && '__error',
              )}
              rows={3}
              value={value || ''}
              onChange={onChange}
            />
            {formErrors.emailBulk && (
              <div className='error-message c-error'>
                {formErrors.emailBulk.message}
              </div>
            )}
            {requestErrors?.email &&
              requestErrors.email.map(error => (
                <div key={error} className={styles.fieldError}>
                  {t(`multiaccount.dealer.error.email.${error}`)}
                </div>
              ))}
            <div className={styles.description}>
              {t('multiaccount.invite-client-modal.description.bulk')}
            </div>
          </>
        )}
      />
    </div>
  );
});

export default ClientBulkEmailField;
