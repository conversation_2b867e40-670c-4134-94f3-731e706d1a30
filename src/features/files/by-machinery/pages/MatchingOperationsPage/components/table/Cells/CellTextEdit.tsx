import React, { useState, VFC } from 'react';

import { CellStyle } from './styles';

export type CellCalendarProps = {
  initText: string;
  hovered: boolean;
};

export const CellTextEdit: VFC<CellCalendarProps> = ({ hovered, initText }) => {
  const [value, setValue] = useState<string>(initText);

  if (!hovered) {
    return <CellStyle hovered={false}>{initText}</CellStyle>;
  }

  return (
    <CellStyle hovered={hovered}>
      <div className='unit-append'>
        <input
          className='form-input size-full'
          value={value}
          onChange={e => setValue(e.target.value)}
        />
      </div>
    </CellStyle>
  );
};
