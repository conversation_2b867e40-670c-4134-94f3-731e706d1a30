import React, { VFC } from 'react';

import { colors } from 'constants/style';

import { useUndercoverPercentage } from 'features/files/colorSchemes/hooks/useUndercoverPercentage';
import { FileDataSourceType } from 'features/files/colorSchemes/types';
import LegendBar from 'features/files/colorSchemes/default/legend/LegendBar';

const UndercoverFileLegend: VFC<FileDataSourceType> = dataSource => {
  const undercover = useUndercoverPercentage(dataSource);

  if (undercover === null) {
    return null;
  }

  return (
    <div className='map-legend__item'>
      <LegendBar from={undercover} colors={[colors.undercoverColor]} />
    </div>
  );
};

export default UndercoverFileLegend;
