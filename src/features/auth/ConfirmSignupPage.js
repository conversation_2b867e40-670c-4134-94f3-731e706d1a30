import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Redirect } from 'react-router-dom';
import { Form, getField } from 'react-redux-form';
import cx from 'classnames';
import { GoogleOAuthProvider, useGoogleLogin } from '@react-oauth/google';

import config from 'config/index';

import Button from 'components/ui/Button/Button';
import Saga from 'components/saga/Saga';
import BodyClassName from 'components/BodyClassName';
import SubmitButton from 'components/forms/SubmitButton';
import AcceptTermsSection from './AcceptTermsSection';
import Logo from 'components/Logo';

import authLoginSaga from 'sagas/local/auth-login';
import { checked } from 'utils/validators';
import { parseQuery } from 'utils/query';
import { useTranslate } from 'utils/use-translate';

import forms from 'constants/forms';
import auth from 'modules/auth';

const ConfirmSignupForm = ({ history, location }) => {
  const { t } = useTranslate();
  const dispatch = useDispatch();
  const submitFailed = useSelector(
    state => getField(state, forms.login).submitFailed,
  );
  const login = useGoogleLogin({
    flow: 'auth-code',
    onSuccess: ({ code }) => {
      const query = parseQuery(location);

      if (code) {
        dispatch(
          auth.actions.socialSignup('google', { code }, undefined, query.d),
        );
      } else {
        alert(t('auth.social.error'));
      }
    },
  });
  return (
    <section className={cx('onesoil-hello', { 'shake-error': submitFailed })}>
      {(!location.state || !location.state.provider) && (
        <Redirect to='/auth/login' replace />
      )}
      <BodyClassName className='onesoil-hello-page' />
      <Saga
        saga={authLoginSaga}
        {...parseQuery(location)}
        history={history}
        noReset
      />

      <div className='onesoil-hello__header'>
        <Logo />
      </div>

      <p className='onesoil-hello__description'>{t('auth.confirm.hint')}</p>

      <Form
        className='onesoil-hello-form'
        model={forms.login}
        onSubmit={() => {
          const { provider, token, code, partner, data } = location.state;
          if (provider === 'google') {
            login();
          } else {
            dispatch(
              auth.actions.socialSignup(
                provider,
                { token, code },
                partner,
                data,
              ),
            );
          }
        }}
      >
        <div className='form-group'>
          <AcceptTermsSection validators={{ checked }} forceExpand />
        </div>

        <div className='form-group'>
          <SubmitButton
            native
            model={forms.login}
            render={({ ...otherProps }) => (
              <Button
                className='btn btn-success btn-lg size-full'
                type='submit'
                {...otherProps}
              >
                {t(`auth.confirm.social_signup`)}
              </Button>
            )}
          />
        </div>
      </Form>
    </section>
  );
};

const ConfirmSignupPage = props => {
  return (
    <GoogleOAuthProvider clientId={config.appIDs.google}>
      <ConfirmSignupForm {...props} />
    </GoogleOAuthProvider>
  );
};

export default ConfirmSignupPage;
