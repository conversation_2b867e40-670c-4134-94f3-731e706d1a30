import React from 'react';

const YieldLogo = ({ color = '#27AE60', width = 186, height = 40 }) => (
  <svg
    width={width}
    height={height}
    viewBox='0 0 186 40'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
  >
    <g clipPath='url(#clip0_271_48473)'>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M98.39 13.64C98.3802 9.20371 99.6284 4.85551 101.99 1.09998L102.86 0C103.84 0.360839 104.842 0.658137 105.86 0.890015C108.325 1.48637 110.747 2.24806 113.11 3.16998C114.946 3.89665 116.584 5.04809 117.89 6.52997C119.324 8.31939 120.082 10.5572 120.03 12.85C120.052 13.9492 119.843 15.0408 119.415 16.0538C118.988 17.0668 118.353 17.9786 117.55 18.73C117.043 19.1908 116.478 19.5842 115.87 19.9C116.305 22.318 116.493 24.774 116.43 27.23H112.79V26.55C112.79 21.85 112.79 16.99 107.55 12.85L109.75 10C111.915 11.6598 113.633 13.8316 114.75 16.32C114.85 16.24 114.92 16.2 115.03 16.1C115.488 15.697 115.848 15.1947 116.082 14.6314C116.316 14.0681 116.418 13.4588 116.38 12.85C116.436 11.3962 115.955 9.97256 115.03 8.84998C114.105 7.79787 112.948 6.97611 111.65 6.45001C110.244 5.87949 108.8 5.40503 107.33 5.02997C106.33 4.74997 105.33 4.48002 104.45 4.15002C102.735 7.0229 101.874 10.3254 101.97 13.67C101.97 17.08 102.91 18.54 103.72 19.26C104.31 19.7807 105.064 20.078 105.85 20.1C106.654 19.9506 107.43 19.6774 108.15 19.29L109.4 18.76L110.75 22.06C110.45 22.18 110.13 22.34 109.75 22.51C108.555 23.223 107.2 23.6252 105.81 23.68C104.127 23.6496 102.512 23.0142 101.26 21.89C99.63 20.37 98.39 17.73 98.39 13.64ZM2.96995 37.02C4.96626 38.9179 7.61545 39.9762 10.3699 39.9762C13.1244 39.9762 15.7737 38.9179 17.77 37.02C18.7266 36.0684 19.4857 34.9371 20.0038 33.6912C20.5218 32.4453 20.7885 31.1093 20.7885 29.76C20.7885 28.4107 20.5218 27.0747 20.0038 25.8287C19.4857 24.5828 18.7266 23.4516 17.77 22.5C16.3197 21.0365 14.467 20.0373 12.4475 19.6293C10.4279 19.2213 8.33265 19.4229 6.42797 20.2086C4.52329 20.9942 2.89521 22.3284 1.75064 24.0416C0.606063 25.7548 -0.00330501 27.7697 -3.60765e-05 29.83C-0.0208129 31.1676 0.231906 32.4952 0.742624 33.7316C1.25334 34.968 2.01128 36.087 2.96995 37.02ZM14.79 25.19C15.3886 25.7788 15.8585 26.4854 16.1701 27.2651C16.4816 28.0449 16.628 28.8808 16.6 29.72C16.6371 30.5546 16.4978 31.3877 16.1911 32.1649C15.8844 32.942 15.4171 33.6457 14.82 34.23C13.6247 35.3853 12.0274 36.0312 10.365 36.0312C8.70259 36.0312 7.10521 35.3853 5.90995 34.23C5.32407 33.6374 4.86431 32.9323 4.55839 32.1572C4.25246 31.382 4.10672 30.553 4.12995 29.72C4.10374 28.881 4.25097 28.0456 4.56242 27.2662C4.87386 26.4867 5.34281 25.7799 5.93995 25.19C7.11381 24.0169 8.70544 23.358 10.365 23.358C12.0245 23.358 13.6161 24.0169 14.79 25.19ZM26.0299 31.79V39.63H22.2199V25H25.95V27C26.4362 26.224 27.1183 25.5899 27.9276 25.1616C28.737 24.7332 29.6449 24.5257 30.5599 24.56C32.0752 24.5274 33.5419 25.0954 34.64 26.14C35.2189 26.7327 35.6667 27.4405 35.9544 28.2175C36.2421 28.9944 36.3633 29.8232 36.3099 30.65V39.59H32.51V31.67C32.51 29.33 31.2699 28.04 29.3999 28.04C27.5299 28.04 26.0599 29.61 26.0599 31.79H26.0299ZM52.46 33.64C52.543 33.0904 52.5898 32.5358 52.6 31.98C52.6539 30.9886 52.4964 29.997 52.1378 29.0712C51.7792 28.1453 51.2277 27.3064 50.52 26.61C49.0117 25.295 47.0691 24.5865 45.0684 24.6216C43.0677 24.6567 41.1512 25.4329 39.69 26.8C38.9807 27.5245 38.4261 28.3857 38.06 29.3312C37.6938 30.2767 37.5237 31.2867 37.5599 32.3C37.5146 33.3469 37.6933 34.3914 38.0842 35.3637C38.4751 36.336 39.0691 37.2136 39.8264 37.9379C40.5838 38.6621 41.4871 39.2162 42.4758 39.5632C43.4646 39.9102 44.5161 40.0421 45.5599 39.95C47.863 39.9949 50.1187 39.2933 51.99 37.95L50.48 35.3C49.0593 36.2199 47.4119 36.7287 45.72 36.77C43.4 36.77 41.97 35.7333 41.43 33.66L52.46 33.64ZM41.46 30.82C41.6282 29.9406 42.104 29.1498 42.802 28.5891C43.5001 28.0284 44.375 27.7346 45.27 27.76C46.1534 27.7195 47.0199 28.0111 47.6991 28.5774C48.3783 29.1437 48.821 29.9437 48.94 30.82H41.46ZM52.46 36.24C53.322 37.428 54.4616 38.3871 55.7793 39.0338C57.097 39.6804 58.5528 39.995 60.02 39.95C61.9285 40.0364 63.805 39.4369 65.3099 38.26C65.9547 37.737 66.4705 37.0726 66.8174 36.3183C67.1642 35.564 67.3327 34.7399 67.3099 33.91C67.3099 31.71 66.18 30.07 63.91 28.97C63.4033 28.73 62.5933 28.38 61.48 27.92L59.48 27.07C58.4 26.53 57.86 25.88 57.86 25.07C57.86 23.91 58.86 23.16 60.43 23.16C62.0714 23.1866 63.6383 23.85 64.8 25.01L66.93 22.11C66.0787 21.2374 65.0571 20.5492 63.9285 20.0881C62.8 19.6271 61.5887 19.4031 60.3699 19.43C58.5845 19.3585 56.8357 19.9497 55.46 21.09C54.8528 21.5926 54.3673 22.2262 54.04 22.9432C53.7127 23.6602 53.552 24.442 53.57 25.23C53.577 26.2676 53.8775 27.2821 54.4367 28.1562C54.9958 29.0304 55.7909 29.7285 56.73 30.17C57.4908 30.5741 58.2756 30.9315 59.08 31.24L59.5299 31.41C60.32 31.71 60.82 31.9 61.05 32.02C62.26 32.58 62.86 33.28 62.86 34.08C62.86 35.35 61.64 36.2 59.99 36.2C58.9514 36.1983 57.9285 35.9466 57.0078 35.4662C56.087 34.9858 55.2955 34.2908 54.7 33.44L52.46 36.24ZM70.2 26.85C68.7825 28.3141 67.9901 30.2721 67.9901 32.31C67.9901 34.3478 68.7825 36.3059 70.2 37.77C71.7398 39.204 73.7658 40.0012 75.8699 40.0012C77.9741 40.0012 80.0001 39.204 81.54 37.77C82.9574 36.3059 83.7499 34.3478 83.7499 32.31C83.7499 30.2721 82.9574 28.3141 81.54 26.85C80.0057 25.405 77.9776 24.6003 75.8699 24.6003C73.7623 24.6003 71.7342 25.405 70.2 26.85ZM75.8699 36.49C75.3266 36.4972 74.7874 36.3937 74.2854 36.1857C73.7833 35.9776 73.329 35.6694 72.95 35.28C72.569 34.8807 72.2704 34.4102 72.0714 33.8954C71.8723 33.3807 71.7767 32.8317 71.79 32.28C71.7868 31.7395 71.8912 31.2039 72.0972 30.7043C72.3033 30.2047 72.6068 29.7511 72.99 29.37C73.3657 28.9762 73.8194 28.6651 74.3222 28.4568C74.8251 28.2484 75.3658 28.1473 75.91 28.16C76.4481 28.148 76.9827 28.2495 77.4789 28.458C77.9752 28.6665 78.4219 28.9773 78.79 29.37C79.1753 29.7573 79.4791 30.2179 79.6835 30.7245C79.8879 31.2312 79.9887 31.7737 79.98 32.32C79.9954 33.438 79.5675 34.5166 78.79 35.32C78.4172 35.704 77.9687 36.0063 77.4729 36.2077C76.9771 36.4091 76.4449 36.5053 75.91 36.49H75.8699ZM88.85 19.37C89.2788 19.8122 89.5187 20.404 89.5187 21.02C89.5187 21.636 89.2788 22.2278 88.85 22.67C88.3971 23.0975 87.7978 23.3358 87.175 23.3358C86.5521 23.3358 85.9528 23.0975 85.4999 22.67C85.0711 22.2278 84.8312 21.636 84.8312 21.02C84.8312 20.404 85.0711 19.8122 85.4999 19.37C85.9528 18.9424 86.5521 18.7042 87.175 18.7042C87.7978 18.7042 88.3971 18.9424 88.85 19.37ZM85.2499 24.98H89.0599V39.61H85.2499V24.98ZM91.2499 19.21H95.05V39.61H91.2499V19.21Z'
        fill={color}
      />
      <path
        d='M133.27 31.0901L125.9 18.3701H128.68L134.46 28.7401L140.24 18.3701H143.01L135.62 31.1301V39.5301H133.27V31.0901Z'
        fill={color}
      />
      <path
        d='M144.41 20.0599C144.412 19.7384 144.509 19.4246 144.689 19.1581C144.869 18.8916 145.123 18.6842 145.421 18.5621C145.718 18.44 146.045 18.4085 146.36 18.4718C146.676 18.535 146.965 18.69 147.193 18.9173C147.42 19.1447 147.575 19.4342 147.638 19.7495C147.701 20.0647 147.67 20.3916 147.548 20.689C147.426 20.9865 147.218 21.2412 146.952 21.421C146.685 21.6009 146.371 21.6979 146.05 21.6999C145.833 21.704 145.618 21.6643 145.418 21.5833C145.217 21.5024 145.034 21.3817 144.881 21.2286C144.728 21.0755 144.607 20.8931 144.526 20.6923C144.446 20.4915 144.406 20.2764 144.41 20.0599ZM144.89 24.8899H147.19V39.4899H144.89V24.8899Z'
        fill={color}
      />
      <path
        d='M150.11 32.0399C150.11 27.6599 153.21 24.5898 157.02 24.5898C160.66 24.5898 163.58 27.4499 163.58 31.6499C163.58 32.0399 163.58 32.5499 163.58 32.9299H152.4C152.544 34.309 153.213 35.5797 154.269 36.4789C155.324 37.3781 156.685 37.8367 158.07 37.7599C159.635 37.738 161.171 37.3292 162.54 36.5699V38.7499C161.166 39.4462 159.64 39.7898 158.1 39.7499C153.3 39.7899 150.11 36.5699 150.11 32.0399ZM161.41 31.2099C161.26 28.3699 159.47 26.6399 156.97 26.6099C154.47 26.5799 152.58 28.4299 152.34 31.2099H161.41Z'
        fill={color}
      />
      <path d='M166.38 17.73H168.68V39.49H166.38V17.73Z' fill={color} />
      <path
        d='M171.48 32.1302C171.48 27.8102 174.64 24.5902 178.37 24.5902C179.43 24.5842 180.471 24.874 181.375 25.4269C182.28 25.9799 183.012 26.774 183.49 27.7202V17.7202H185.79V39.4802H183.49V36.6802C183.001 37.615 182.265 38.3981 181.362 38.9446C180.46 39.491 179.425 39.78 178.37 39.7802C174.61 39.7902 171.48 36.4502 171.48 32.1302ZM183.67 32.1302C183.67 29.1302 181.62 26.6403 178.67 26.6403C176.16 26.6403 173.81 28.8202 173.81 32.1002C173.81 35.1702 175.98 37.6702 178.64 37.6702C181.3 37.6702 183.67 35.3702 183.67 32.1602V32.1302Z'
        fill={color}
      />
    </g>
    <defs>
      <clipPath id='clip0_271_48473'>
        <rect width='185.79' height='40' fill='white' />
      </clipPath>
    </defs>
  </svg>
);

export default YieldLogo;
