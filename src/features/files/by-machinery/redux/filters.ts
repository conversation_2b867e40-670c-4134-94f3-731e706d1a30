import {
  ImportTaskType,
  ServerTaskFailedType,
  TaskFailedType,
  TaskMatchingType,
  TaskProcessingType,
  TaskStatus,
  TaskSuccessType,
  TaskUploadingType,
} from '../types/tasks';

const isCompletedTask = (task: ImportTaskType): task is TaskSuccessType => {
  return task.status === TaskStatus.done;
};

const isHistoryTask = (task: ImportTaskType): task is TaskSuccessType => {
  return isCompletedTask(task);
};

const isActiveTask = (task: ImportTaskType) => {
  return !isCompletedTask(task);
};

const isFinishedTask = (
  task: ImportTaskType,
): task is TaskSuccessType | TaskFailedType | ServerTaskFailedType => {
  return isCompletedTask(task) || isFailedTask(task);
};

const isFailedTask = (
  task: ImportTaskType,
): task is TaskFailedType | ServerTaskFailedType => {
  return task.status === TaskStatus.error;
};

const isUploadingTask = (task: ImportTaskType): task is TaskUploadingType => {
  return task.status === TaskStatus.uploading;
};

const isMatchingTask = (task: ImportTaskType): task is TaskMatchingType => {
  return task.status === TaskStatus.processed;
};

export const BackendWaitingStatuses = [
  TaskStatus.uploading,
  TaskStatus.processing,
  TaskStatus.importing,
  TaskStatus.waiting,
];

const isWaitingBackendTask = (
  task: ImportTaskType,
): task is TaskMatchingType => {
  return BackendWaitingStatuses.includes(task.status);
};

const isShowedTask = (task: ImportTaskType) => {
  return Boolean(task.viewed_at);
};

const isServerTask = (
  task: ImportTaskType,
): task is TaskProcessingType | ServerTaskFailedType | TaskSuccessType => {
  return Boolean(task.uuid);
};

export const filters = {
  isShowedTask,
  isMatchingTask,
  isActiveTask,
  isFinishedTask,
  isHistoryTask,
  isServerTask,
  isFailedTask,
  isUploadingTask,
  isWaitingBackendTask,
};
