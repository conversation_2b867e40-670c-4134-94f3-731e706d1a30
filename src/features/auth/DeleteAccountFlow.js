import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom';
import EnterExitTransition from 'components/EnterExitTransition';
import DeleteAccountEmailPopup from 'features/auth/DeleteAccountEmailPopup';
import DeleteAccountPopup from 'features/auth/DeleteAccountPopup';
import DeleteAccountConfirmPopup from 'features/auth/DeleteAccountConfirmPopup';
import DeleteAccountWithoutEmailPopup from 'features/auth/DeleteAccountWithoutEmailPopup';
import DeleteAccountErrorPopup from 'features/auth/DeleteAccountErrorPopup';
import useQuery from 'utils/use-query';

import auth from 'modules/auth';
import useHistory from 'utils/use-history';

const DELETE_STEPS = {
  NOTIFICATION: 'NOTIFICATION',
  EMAIL: 'EMAIL',
  WITHOUT_EMAIL: 'WITHOUT_EMAIL',
  CONFIRM: 'CONFIRM',
  ERROR: 'ERROR',
};

const DeleteAccountFlow = ({ onClose }) => {
  const hasEmail = useSelector(auth.selectors.isValidEmail);
  const history = useHistory();
  const [step, setStep] = useState(DELETE_STEPS.NOTIFICATION);
  const location = useLocation();
  const [, mergeQuery] = useQuery();

  const resetHistoryState = () => {
    history.replace({
      ...location,
      state: {},
    });
    mergeQuery({ settings: undefined, step: undefined });
  };

  return (
    <>
      <EnterExitTransition variant='popup'>
        {step === DELETE_STEPS.NOTIFICATION && (
          <DeleteAccountPopup
            onClose={resetHistoryState}
            onNext={() =>
              setStep(
                hasEmail ? DELETE_STEPS.EMAIL : DELETE_STEPS.WITHOUT_EMAIL,
              )
            }
          />
        )}
      </EnterExitTransition>
      {step === DELETE_STEPS.EMAIL && (
        <EnterExitTransition variant='popup'>
          <DeleteAccountEmailPopup
            onClose={resetHistoryState}
            onNext={() => setStep(DELETE_STEPS.CONFIRM)}
          />
        </EnterExitTransition>
      )}
      {step === DELETE_STEPS.WITHOUT_EMAIL && (
        <EnterExitTransition variant='popup'>
          <DeleteAccountWithoutEmailPopup onClose={resetHistoryState} />
        </EnterExitTransition>
      )}
      {step === DELETE_STEPS.CONFIRM && (
        <EnterExitTransition variant='popup'>
          <DeleteAccountConfirmPopup onClose={resetHistoryState} />
        </EnterExitTransition>
      )}
      {step === DELETE_STEPS.ERROR && (
        <EnterExitTransition variant='popup'>
          <DeleteAccountErrorPopup
            code={location.state?.delete_account_error?.code}
            onClose={resetHistoryState}
          />
        </EnterExitTransition>
      )}
    </>
  );
};

export default DeleteAccountFlow;
