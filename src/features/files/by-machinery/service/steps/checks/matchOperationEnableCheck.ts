import RootState from 'types/rootState';

import { ApiDataFieldOperations } from '../../../types';
import { selectors } from '../../../redux/selectors';
import { StepValidationStatus } from '../types';

export const matchOperationEnableCheck = (state: RootState) => {
  const operations: ApiDataFieldOperations[] =
    selectors.getMatchingOperations(state);

  return operations.some(operation => operation.is_enabled)
    ? StepValidationStatus.valid
    : StepValidationStatus.needToEnable;
};
