import React, { forwardRef, useEffect, useState } from 'react';
import { Controller } from 'react-hook-form';

import { type EntityWithLimits } from 'types/multiaccount';
import { type ErrorData } from 'modules/entities/types';
import Icons from 'constants/icons';
// @ts-ignore
import TextInput from 'components/forms/TextInput';
import Switch from 'components/ui/Switch/Switch';
import {
  ClientHookForm,
  ClientForm,
} from 'features/multiaccount/dealer/modals/ClientForm/types';

import { useTranslate } from 'utils/use-translate';

import { styles } from './BaseTestLimitField.styles';

export type BaseTestLimitFieldProps = {
  client?: EntityWithLimits;
  errorKey: string;
  fieldName: keyof ClientForm;
  form: ClientHookForm;
  iconName: string;
  isPending: boolean;
  maxLimit: number;
  requestErrors: Nullable<ErrorData>;
  suffixText?: string;
  titleKey: string;
  usedLimitValue?: number;
};

const BaseTestLimitField = forwardRef<
  HTMLInputElement,
  BaseTestLimitFieldProps
>(
  (
    {
      client,
      errorKey,
      fieldName,
      form,
      iconName,
      isPending,
      maxLimit,
      requestErrors,
      suffixText = 'ha',
      titleKey,
      usedLimitValue = 0,
    },
    ref,
  ) => {
    const { t } = useTranslate();
    const { watch, errors: formErrors, trigger, control, setValue } = form;
    const [isFieldEnabled, setIsFieldEnabled] = useState(false);

    const hasLimits = client?.status === 'active' && !!client?.accountUuid;
    const commercialLimits = watch('commercial_limits');
    const testLimits = watch('test_limits');
    const unitType = watch('unit_type');

    const validateAtLeastOne = () => {
      return !!commercialLimits || !!testLimits;
    };

    useEffect(() => {
      // trigger validation for limits after changing unitType
      trigger(['test_limits', 'commercial_limits']);
    }, [unitType, testLimits, commercialLimits, trigger]);

    useEffect(() => {
      if (!isFieldEnabled) {
        setValue(fieldName, undefined);
      }
    }, [isFieldEnabled, fieldName, setValue]);

    const getErrorMessage = (error: unknown): string | undefined => {
      if (error && typeof error === 'object' && 'message' in error) {
        return (error as { message: string }).message;
      }
      return undefined;
    };

    const fieldError = formErrors[fieldName];
    const errorMessage = getErrorMessage(fieldError);

    const handleToggleChange = (checked: boolean) => {
      setIsFieldEnabled(checked);
    };

    return (
      <div className={styles.container}>
        <div className={styles.header}>
          <div className={styles.labelContainer}>
            <div className={styles.iconContainer}>
              {Icons[iconName as keyof typeof Icons]}
            </div>
            <label className={styles.label}>
              {
                // @ts-ignore
                t(titleKey, { max: maxLimit })
              }
            </label>
          </div>
          <Switch
            checkedColor='black'
            disabled={isPending}
            id={`${fieldName}-toggle`}
            onChange={handleToggleChange}
            size='small'
            value={isFieldEnabled}
          />
        </div>

        <Controller
          name={fieldName}
          control={control}
          defaultValue=''
          disabled={isPending || !isFieldEnabled}
          rules={{
            validate: isFieldEnabled ? validateAtLeastOne : undefined,
            required: isFieldEnabled && hasLimits,
            min: isFieldEnabled
              ? {
                  value: usedLimitValue,
                  // @ts-ignore
                  message: t(`${errorKey}.min`, { value: usedLimitValue }),
                }
              : undefined,
            max: isFieldEnabled
              ? {
                  value: maxLimit,
                  // @ts-ignore
                  message: t(`${errorKey}.max`, { max: maxLimit }),
                }
              : undefined,
          }}
          render={({ onChange, value }, { invalid }) => {
            return (
              <div className={styles.inputContainer}>
                <TextInput
                  className={styles.input}
                  disabled={isPending || !isFieldEnabled}
                  groupThousands={true}
                  hideArrows
                  id={fieldName}
                  isValid={!invalid}
                  numeric
                  onChange={onChange}
                  positiveOnly
                  precision={0}
                  ref={ref}
                  suffixText={suffixText}
                  tabIndex={1}
                  value={isFieldEnabled ? value : ''}
                />

                {isFieldEnabled && fieldError && errorMessage && (
                  <div className={styles.errorMessage}>{errorMessage}</div>
                )}

                {isFieldEnabled &&
                  requestErrors?.[fieldName] &&
                  requestErrors[fieldName]?.map((error: string) => (
                    <div key={error} className={styles.fieldError}>
                      {t(`multiaccount.dealer.error.workspace_title.${error}`)}
                    </div>
                  ))}
              </div>
            );
          }}
        />
      </div>
    );
  },
);

export default BaseTestLimitField;
