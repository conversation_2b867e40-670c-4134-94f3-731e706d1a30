import { useState } from 'react';
import { Modals } from './types';

export type MoveClientsManager = {
  moveClientIds: string[];
  showMoveModal: (uuids: string[]) => void;
  closeMoveModal: () => void;
};

export const useMoveClients = (
  setVisibleModal: (type: Modals) => void,
): MoveClientsManager => {
  const [moveClientIds, setMoveClientIds] = useState<string[]>([]);

  const showMoveModal = (uuids: string[]) => {
    setVisibleModal('move-client');
    setMoveClientIds(uuids);
  };
  const closeMoveModal = () => {
    setMoveClientIds([]);
    setVisibleModal(null);
  };

  return {
    moveClientIds,
    showMoveModal,
    closeMoveModal,
  };
};
