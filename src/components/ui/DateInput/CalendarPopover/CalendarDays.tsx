import React, { VFC } from 'react';
import cx from 'classnames';
import range from 'lodash/range';
import { Calendar, RenderProps } from 'dayzed';

export type CalendarDaysProps = {
  calendar: Calendar;
  getDateProps: RenderProps['getDateProps'];
  hideBottomPadding?: boolean;
  visibilitySelected?: false;
};

export const CalendarDays: VFC<CalendarDaysProps> = React.memo(
  ({
    calendar,
    hideBottomPadding,
    getDateProps,
    visibilitySelected = true,
  }) => {
    return (
      <tbody>
        {calendar.weeks.map((week, index) => (
          <tr key={index}>
            {week.map((day, index) => {
              return (
                <td
                  key={index}
                  className={cx('modal-calendar-dates__day', {
                    'modal-calendar-dates__othermonth':
                      typeof day === 'object' &&
                      (day.prevMonth || day.nextMonth),
                    'modal-calendar-dates__currentmonth':
                      typeof day === 'object' &&
                      !day.prevMonth &&
                      !day.nextMonth,
                    'modal-calendar-dates__currentday':
                      typeof day === 'object' &&
                      day.selected &&
                      visibilitySelected,
                    __disabled: typeof day === 'object' && !day.selectable,
                  })}
                >
                  {day && (
                    <div
                      className='modal-calendar-dates__item'
                      {...(day.selectable
                        ? getDateProps({ dateObj: day })
                        : {})}
                    >
                      {day.date.getDate()}
                    </div>
                  )}
                </td>
              );
            })}
          </tr>
        ))}
        {/* Add an empty row to make popup height consistent */}
        {!hideBottomPadding && calendar.weeks.length < 6 && (
          <tr>
            {range(7).map(index => (
              <td key={index} className='modal-calendar-dates__day __disabled'>
                <div className='modal-calendar-dates__item'>&nbsp;</div>
              </td>
            ))}
          </tr>
        )}
      </tbody>
    );
  },
);
