import { useDispatch, useSelector } from 'react-redux';

import entities from 'modules/entities';
import { useEffect } from 'react';
import alert from 'modules/alert';
import { useTranslate } from 'utils/use-translate';
import { EntityType } from 'constants/entities';

const REQUEST_ID = 'resent-invite';

export type ResendManager = {
  resendInvite: (uuid: string, entityType: EntityType) => void;
};

export const useResendInvite = (): ResendManager => {
  const dispatch = useDispatch();
  const { t } = useTranslate();

  const request = useSelector(state => {
    return entities.selectors.getRequestStatus(state, REQUEST_ID);
  });

  useEffect(() => {
    if (request.status === 'resolved') {
      dispatch(
        alert.actions.show({
          show: true,
          icon: 'modal-success',
          text: t(`multiaccount.dealer.resend.success`) as string,
          onSuccess: () => {
            dispatch(alert.actions.close());
          },
        }),
      );
      dispatch(entities.actions.clearRequest(REQUEST_ID));
    }
    if (request.status === 'rejected') {
      dispatch(
        alert.actions.show({
          show: true,
          icon: 'modal-error',
          text: t(`multiaccount.dealer.resend.fail`) as string,
          onSuccess: () => {
            dispatch(alert.actions.close());
          },
        }),
      );
      dispatch(entities.actions.clearRequest(REQUEST_ID));
    }
  }, [request.status, dispatch, t]);

  const resendInvite = (userId: string, entityType: EntityType) => {
    dispatch(
      entities.actions.request(REQUEST_ID, [
        {
          type: 'create',
          entityType: entityType,
          path: {
            operation: 'resend',
            urlId: userId,
          },
        },
      ]),
    );
  };

  return {
    resendInvite,
  };
};
