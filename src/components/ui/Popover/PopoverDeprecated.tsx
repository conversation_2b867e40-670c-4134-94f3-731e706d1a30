import React, {
  useRef,
  useState,
  DOMAttributes,
  useEffect,
  ReactNode,
  ReactElement,
  VFC,
  CSSProperties,
} from 'react';
import Measure from 'react-measure';

import RenderAboveEverything from '../../RenderAboveEverything';
import EnterExitTransition, { Variants } from '../../EnterExitTransition';

import useForceUpdate from 'utils/use-force-update';
import findScrollableParent from 'utils/find-scrollable-parent';

const PaddingWidth = 20;

/**
 * @deprecated
 */
export enum PopoverDeprecatedAlign {
  BottomRight = 'bottom-right',
  Overlap = 'overlap',
  BottomLeft = 'bottom-left',
  TopLeft = 'top-left',
  Centered = 'centered',
  DynamicLeft = 'dynamic-left',
  BottomMiddle = 'bottom-middle',
  TopRight = 'top-right',
  RightTop = 'right-top',
  RightBottom = 'right-bottom',
  MiddleTop = 'middle-top',
  MiddleLeft = 'middle-left',
  MiddleRight = 'middle-right',
}

/**
 * @deprecated
 */
export type PopoverDeprecatedProps = {
  renderTrigger: (
    args: { ref?: TEMPORARY_ANY } & DOMAttributes<any>, // eslint-disable-line @typescript-eslint/no-explicit-any
  ) => ReactNode;
  renderPopover: (args: {
    ref: (ref: Element | null) => void;
    style: CSSProperties;
  }) => ReactElement;
  active: boolean;
  anchor?: DOMRect;
  offset?: [number, number];
  align?: PopoverDeprecatedAlign;
  skipAutoAlign?: boolean;
  transitionVariant?: keyof typeof Variants;
  timeout?: number;
  className?: string;
};

/**
 * @deprecated
 */
export const PopoverDeprecated: VFC<PopoverDeprecatedProps> = ({
  active,
  anchor,
  offset = [0, 0],
  align,
  skipAutoAlign,
  transitionVariant,
  timeout,
  renderTrigger,
  renderPopover,
  className,
}) => {
  const trigger = useRef<HTMLDivElement>();
  const topPos = useRef(0);
  const leftPos = useRef(0);
  const [bounds, setBounds] = useState({ width: 0, height: 0 });
  const forceUpdate = useForceUpdate();
  useEffect(() => {
    if (!active || !trigger.current) {
      return;
    }

    if (align === PopoverDeprecatedAlign.Overlap) {
      const { top, left } = trigger.current.getBoundingClientRect();

      topPos.current = top;
      leftPos.current = left;
    }

    if (align === PopoverDeprecatedAlign.BottomLeft) {
      const { left } = trigger.current.getBoundingClientRect();
      leftPos.current = left;
    }

    const el = findScrollableParent(trigger.current);
    el.addEventListener('scroll', forceUpdate);
    return () => el.removeEventListener('scroll', forceUpdate);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [active]);

  const calculatePosition = () => {
    if (!trigger.current && !anchor) {
      return {};
    }

    const { top, left, width: triggerWidth, height: triggerHeight } =
      // @ts-ignore
      anchor || trigger.current.getBoundingClientRect();

    const { innerHeight, innerWidth } = window;
    const { width, height } = bounds;
    let [offsetX, offsetY] = offset;
    let fittingAlign = align;

    if (!skipAutoAlign && fittingAlign === PopoverDeprecatedAlign.BottomLeft) {
      if (top + triggerHeight + offsetY + height >= innerHeight) {
        fittingAlign = PopoverDeprecatedAlign.TopLeft;
        offsetY *= -1;
      }

      if (left + offsetX + width + PaddingWidth >= innerWidth) {
        const resX = left + offsetX + width + PaddingWidth - innerWidth;
        offsetX = resX + PaddingWidth;
      }
    }

    if (!skipAutoAlign && fittingAlign === PopoverDeprecatedAlign.TopLeft) {
      offsetX = offset[0];
    }

    if (!skipAutoAlign && fittingAlign === PopoverDeprecatedAlign.Overlap) {
      if (top + triggerHeight + offsetY + height >= innerHeight) {
        const resY = top + triggerHeight + offsetY + height - innerHeight;
        offsetY = -resY;
      }
      if (left + offsetX + width + PaddingWidth >= innerWidth) {
        const resX = left + offsetX + width + PaddingWidth - innerWidth;
        offsetX = resX;
      }
    }

    switch (fittingAlign) {
      case PopoverDeprecatedAlign.Centered:
        return {
          left: left + triggerWidth / 2 - width / 2 + offsetX,
          top: top + triggerHeight / 2 - height / 2 + offsetY,
        };
      case PopoverDeprecatedAlign.TopLeft:
        return {
          left: left + offsetX,
          top: top - height + offsetY,
        };
      case PopoverDeprecatedAlign.DynamicLeft:
        return {
          left: left + -width - 90,
          top: top - height + offsetY,
        };
      case PopoverDeprecatedAlign.BottomLeft:
        return {
          left: leftPos.current - offsetX,
          top: top + triggerHeight + offsetY,
        };
      case PopoverDeprecatedAlign.BottomRight:
        return {
          left: left + triggerWidth - width + offsetX,
          top: top + triggerHeight + offsetY,
        };
      case PopoverDeprecatedAlign.BottomMiddle:
        return {
          left: left + triggerWidth / 2 - width / 2 + offsetX,
          top: top + triggerHeight + offsetY,
        };
      case PopoverDeprecatedAlign.TopRight:
        return {
          left: left + triggerWidth + offsetX,
          top: top + triggerHeight / 2 - height + offsetY,
        };
      case PopoverDeprecatedAlign.RightTop:
        return {
          left: left + triggerWidth + offsetX,
          top: top + offsetY,
        };
      case PopoverDeprecatedAlign.RightBottom:
        return {
          left: left + triggerWidth + offsetX,
          top: top + triggerHeight - height + offsetY,
        };
      case PopoverDeprecatedAlign.MiddleTop:
        return {
          left: left + triggerWidth / 2 - width / 2 + offsetX,
          top: top - height + offsetY,
        };
      case PopoverDeprecatedAlign.MiddleLeft:
        return {
          left: left - width + offsetX,
          top: top + triggerHeight / 2 - height / 2 + offsetY,
        };
      case PopoverDeprecatedAlign.MiddleRight:
        return {
          left: left + triggerWidth + offsetX,
          top: top + triggerHeight / 2 - height / 2 + offsetY,
        };
      case PopoverDeprecatedAlign.Overlap:
        return {
          transform: `translate(${left - leftPos.current - offsetX}px, ${
            top - topPos.current + offsetY
          }px)`,
          left: leftPos.current,
          top: topPos.current,
        };

      default:
        throw new Error(`Bad alignment: ${fittingAlign}`);
    }
  };

  const position = calculatePosition();

  return (
    <>
      {renderTrigger({
        ref: trigger,
      })}
      <EnterExitTransition variant={transitionVariant} timeout={timeout}>
        {active && (
          <RenderAboveEverything className={className}>
            <Measure
              bounds
              onResize={({ bounds }) => {
                setBounds({
                  width: bounds?.width || 0,
                  height: bounds?.height || 0,
                });
              }}
            >
              {({ measureRef, contentRect }) =>
                renderPopover({
                  ref: measureRef,
                  style: {
                    ...position,
                    position: 'fixed',
                    // use clip
                    // because visibility removes focus from searchbox
                    // and opacity brokes animation
                    // https://allyjs.io/tutorials/hiding-elements.html
                    clip:
                      // @ts-ignore
                      'width' in contentRect.bounds ? 'auto' : 'rect(0 0 0 0)',
                  },
                })
              }
            </Measure>
          </RenderAboveEverything>
        )}
      </EnterExitTransition>
    </>
  );
};
