import React, { useCallback, useEffect, VFC } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import * as H from 'history';

import { actions } from 'features/fields/BulkUpload/redux/actions';
import { selectors } from 'features/fields/BulkUpload/redux/selectors';

import toasts from 'modules/toasts';

import { useTranslate } from 'utils/use-translate';

import { BulkUploadTitle } from './components/BulkUploadTitle';
import { BulkUploadDescription } from './components/BulkUploadDescription';
import { FloatingNavigation } from './components/FloatingNavigation';
import { NavProgress } from './components/NavProgress/NavProgress';

import { FileSelectionStep } from './steps/FileSelectionStep';
import { ProcessingStep } from './steps/ProcessingStep';
import { CheckDataStep } from './steps/CheckDataStep';
import { UploadingStep } from './steps/UploadingStep';
import { MatchColumnsStep } from './steps/MatchColumnsStep';
import { MatchCropsStep } from './steps/MatchCropsStep';
import { SaveFieldsStep } from './steps/SaveFieldsStep';

import { BULK_UPLOAD_TOAST_KEY } from './constants';
import { CentralSection, NavProgressHeader } from './styles';
import { BulkUploadStep, FileBundle, LocationState } from './types';
import { BulkUploadStepOrder } from './utils';

interface BulkUploadPageProps {
  history: H.History<LocationState>;
  location: H.Location<LocationState>;
}

export const BulkUploadPage: VFC<BulkUploadPageProps> = ({
  history,
  location,
}) => {
  const { t } = useTranslate();
  const dispatch = useDispatch();

  const currentStep: BulkUploadStep = useSelector(selectors.getCurrentStep);
  const reachedStep: BulkUploadStep = useSelector(selectors.getReachedStep);
  const selectedFiles = useSelector(selectors.getFiles);
  const isProcessingDisabled = useSelector(selectors.isProcessingDisabled);
  const isUploadingDisabled = useSelector(selectors.isUploadingDisabled);
  const uploadingInProgress = useSelector(selectors.getUploadingInProgress);
  const fieldsFetchingPending = useSelector(selectors.getFieldsFetchingPending);
  const validBundlesWithProps: FileBundle[] = useSelector(
    selectors.getValidFileBundlesWithProps,
  );

  useEffect(() => {
    dispatch(toasts.actions.removeGroup(BULK_UPLOAD_TOAST_KEY));
  }, [dispatch]);

  function isNextBtnDisabled() {
    return (
      (currentStep === BulkUploadStep.FILE_SELECTION && isProcessingDisabled) ||
      (currentStep === BulkUploadStep.CHECK_DATA && isUploadingDisabled)
    );
  }

  function isNextBtnPending() {
    if (currentStep === BulkUploadStep.PROCESSING) {
      return true;
    }

    return uploadingInProgress || fieldsFetchingPending;
  }

  function getNextBtnLabel() {
    switch (currentStep) {
      case BulkUploadStep.FILE_SELECTION:
        return t('fields.bulk_upload.floating_nav.process_files');
      case BulkUploadStep.PROCESSING:
        return t('fields.bulk_upload.floating_nav.import');
      case BulkUploadStep.CHECK_DATA:
        return t('fields.bulk_upload.floating_nav.import');
      case BulkUploadStep.UPLOADING:
        return t('fields.bulk_upload.floating_nav.try_again');
      default:
        return t('fields.bulk_upload.floating_nav.process_files');
    }
  }

  const onNavCancel = () => {
    dispatch(actions.resetBulkUploadState());
  };

  const continueFromCheckData = useCallback(() => {
    const filesAreNotUploaded =
      BulkUploadStepOrder[reachedStep] <
      BulkUploadStepOrder[BulkUploadStep.UPLOADING];

    if (filesAreNotUploaded) {
      dispatch(actions.importBundles());
      return;
    }

    const step =
      validBundlesWithProps.length === 1
        ? BulkUploadStep.COLUMN_MATCHING
        : BulkUploadStep.SAVING;

    dispatch(actions.setCurrentStep(step));
  }, [dispatch, reachedStep, validBundlesWithProps]);

  const onNavContinue = () => {
    switch (currentStep) {
      case BulkUploadStep.FILE_SELECTION:
        dispatch(actions.processFiles());
        break;
      case BulkUploadStep.CHECK_DATA:
        continueFromCheckData();
        break;
      case BulkUploadStep.UPLOADING:
        dispatch(actions.importBundles());
        break;
      default:
        break;
    }
  };

  function renderCurrentStep() {
    switch (currentStep) {
      case BulkUploadStep.FILE_SELECTION:
        return <FileSelectionStep />;
      case BulkUploadStep.PROCESSING:
        return <ProcessingStep />;
      case BulkUploadStep.CHECK_DATA:
        return <CheckDataStep />;
      case BulkUploadStep.UPLOADING:
        return <UploadingStep />;
      case BulkUploadStep.COLUMN_MATCHING:
        return <MatchColumnsStep />;
      case BulkUploadStep.CROP_MATCHING:
        return <MatchCropsStep />;
      default:
        return null;
    }
  }

  function renderCentralSection() {
    if (currentStep === BulkUploadStep.SAVING) {
      return <SaveFieldsStep history={history} />;
    }

    return (
      <CentralSection>
        <BulkUploadTitle />
        <BulkUploadDescription />
        {renderCurrentStep()}
      </CentralSection>
    );
  }

  function renderFloatingNavigation() {
    const noSelectedFiles =
      currentStep === BulkUploadStep.FILE_SELECTION &&
      selectedFiles.length === 0;

    if (
      noSelectedFiles ||
      currentStep === BulkUploadStep.COLUMN_MATCHING ||
      currentStep === BulkUploadStep.CROP_MATCHING ||
      currentStep === BulkUploadStep.SAVING
    ) {
      return null;
    }

    return (
      <FloatingNavigation
        nextLabel={getNextBtnLabel()}
        nextDisabled={isNextBtnDisabled()}
        nextPending={isNextBtnPending()}
        cancelLabel={t('fields.bulk_upload.floating_nav.cancel')}
        onCancel={onNavCancel}
        onContinue={onNavContinue}
      />
    );
  }

  function renderHeader() {
    return (
      <NavProgressHeader>
        <NavProgress />
      </NavProgressHeader>
    );
  }

  return (
    <div className='main-section' style={{ overflow: 'auto' }}>
      {renderHeader()}
      {renderFloatingNavigation()}
      {renderCentralSection()}
    </div>
  );
};
