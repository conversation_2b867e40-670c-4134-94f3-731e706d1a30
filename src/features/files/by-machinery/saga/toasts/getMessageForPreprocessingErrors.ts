import toasts from 'modules/toasts';

import { isImportFilePage } from '../../redux/utils';
import {
  BASE_ROUTE_MACHINERY_IMPORT,
  TOAST_KEY_TO_IMPORT,
} from '../../constants';
import { failedToastIcon } from '../../styles';

export const getMessageForPreprocessingErrors = () => {
  if (!isImportFilePage()) {
    const viewport = window.location.pathname.split('/')[1];

    return toasts.actions.push(
      TOAST_KEY_TO_IMPORT,
      'import.by-machinery.toast.import.error.title',
      {
        getLabel: ({ t, label }) => t(label) as string,
        content: 'import.by-machinery.toast.import.error.description',
        link: {
          text: 'import.by-machinery.toast.import.error.to',
          to: `/${viewport}/${BASE_ROUTE_MACHINERY_IMPORT}`,
        },
        position: 'bottom',
        icon: 'alert-circle',
        iconClassName: failedToastIcon,
        ttl: Infinity,
      },
    );
  }
};
