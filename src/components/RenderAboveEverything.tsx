import React, { Component } from 'react';
import { createPortal } from 'react-dom';

type RenderAboveEverythingProps = {
  className?: string;
};

class RenderAboveEverything extends Component<RenderAboveEverythingProps> {
  container: HTMLDivElement | null = null;
  getContainer() {
    if (!this.container) {
      this.container = document.createElement('div');
      document.body.appendChild(this.container);
    }

    const { className } = this.props;
    if (className) {
      this.container.className = className;
    }

    return this.container;
  }

  componentWillUnmount() {
    if (this.container) {
      document.body.removeChild(this.container);
    }
  }

  render() {
    return createPortal(this.props.children, this.getContainer());

    return <></>; // eslint-disable-line
  }
}

export default RenderAboveEverything;
