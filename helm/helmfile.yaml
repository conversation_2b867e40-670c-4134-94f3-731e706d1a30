---
helmDefaults:
  verify: false
  historyMax: 30
  createNamespace: true
  timeout: 36000

environments:
  default:
  development:
    kubeContext: arn:aws:eks:eu-central-1:398868223311:cluster/frontend
  staging:
    kubeContext: arn:aws:eks:eu-central-1:398868223311:cluster/frontend
  production:
    kubeContext: arn:aws:eks:eu-central-1:275509970230:cluster/frontend
commonLabels:
  origin: gitlab.com/onesoil-ag/backend/backend-core

releases:
  # Backend
  - name: web-scouting
    chart: onesoil/app
    version: 1.0.0
    namespace: {{env "HELM_NAMESPACE" | default "web-scouting"}}
    values:
      - values/web-scouting.yaml.gotmpl
      - values/{{.Environment.Name}}/web-scouting.yaml.gotmpl

repositories:
  - name: onesoil
    url: s3://onesoil-chart-museum
