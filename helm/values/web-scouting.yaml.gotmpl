deployment: true

replicaCount: 1

image:
  repository: 859247034219.dkr.ecr.eu-central-1.amazonaws.com/web-scouting
  pullPolicy: IfNotPresent
  tag: {{env "VERSION" | default ""}}

service:
  enable: true
  type: ClusterIP
  ports:
    - port: 80
      targetPort: 80
      protocol: TCP

resources:
  limits:
    memory: 256Mi
  requests:
    cpu: 300m
    memory: 256Mi

nodeSelector:
  purpose: application

ingressRoute:
  enable: true
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/tags: Environment=Development
    alb.ingress.kubernetes.io/group.name: "external"
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/ssl-redirect: '443'    
    alb.ingress.kubernetes.io/healthcheck-path: /
    alb.ingress.kubernetes.io/success-codes: "200"
    alb.ingress.kubernetes.io/target-group-attributes: stickiness.enabled=true,stickiness.lb_cookie.duration_seconds=60
  routes:
    - host: web.dev-fe.onesoil.ai
      http:
        paths:
        - backend:
            service:
              name: web-scouting
              port:
                number: 80
          path: /
          pathType: Prefix

volumesSecret:
  enable: true
  volumes:
    - name: basicauth
      files:
        - name: .htpasswd
          data: |
            onesoil:$2a$10$OZNglaLhGp8k4w8MSAIl6O0B91GI0o2M24tfJF8NEmWLbEJJU8z9G
            translators:$2a$10$uYuYVMIzIL5Zjb9IYKWDI.QYcqR0EujnPp6UtvKTGlha/t9WlCEKq

volumeMounts:
  - name: basicauth
    mountPath: /etc/nginx/.htpasswd
    subPath: .htpasswd
