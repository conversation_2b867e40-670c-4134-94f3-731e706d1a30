import React, { useState } from 'react';
import { call, take, takeEvery } from 'redux-saga/effects';
import { useSelector, useDispatch } from 'react-redux';
import { useForm } from 'react-hook-form';
import { styled } from 'linaria/react';
import groupBy from 'lodash/groupBy';

import Icon from 'components/Icon';
import Button from 'components/ui/Button/Button';
import TextInput from 'components/forms/TextInput';
import RenderAboveEverything from 'components/RenderAboveEverything';

import api from 'modules/api';
import auth from 'modules/auth';

import { resolved, rejected } from 'sagas/global/api';
import logEvent from 'sagas/global/logEvent';
import useSaga from 'utils/use-saga';
import { useTranslate } from 'utils/use-translate';

const ErrorLabel = styled.div`
  margin-top: 2px;
  font-size: 12px;
  color: #ff3b30;
`;

const DeleteAccountPopup = ({ onClose, onNext }) => {
  const { t } = useTranslate();
  const userID = useSelector(auth.selectors.getUserID);
  const dispatch = useDispatch();

  const [errors, setErrors] = useState([]);
  const errorsByField = groupBy(errors, 'field');

  const { register, handleSubmit } = useForm({
    mode: 'onChange',
  });

  const submitRequest = useSelector(state =>
    api.selectors.getRequestStatus(state, 'delete-account'),
  );

  const onSubmit = form => {
    dispatch(auth.actions.deleteAccount(userID, form.username));
  };

  useSaga(function*() {
    yield takeEvery(rejected(auth.actions.deleteAccount), action => {
      const { error_details = [] } = action.payload;
      setErrors(error_details);
    });

    const action = yield take(resolved(auth.actions.deleteAccount));

    const { success = false } = action.meta.response;

    if (success) {
      yield call(onNext);
    }

    yield call(logEvent, 'delete_account_request');
    yield call(onClose);
  }, []);

  return (
    <RenderAboveEverything>
      <div className='modal modal-editseasons'>
        <div className='modal-outer'>
          <div className='modal-editseasons-content'>
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className='modal-editseasons-content__header'>
                {t('delete_account.title')}
              </div>
              <div className='modal-editseasons-content__body'>
                <div className='form-group'>
                  {t(`delete_account.email.text`)}
                  <TextInput
                    tabIndex={1}
                    id='username'
                    name='username'
                    autoComplete='off'
                    type='text'
                    ref={register({ required: true })}
                    clearable
                    isValid={!errorsByField.username}
                  />

                  {errorsByField.username?.map(({ code }) => (
                    <ErrorLabel key={code}>
                      {t(`delete_account.email.error.${code}`)}
                    </ErrorLabel>
                  ))}
                </div>
              </div>
              <div className='modal-editseasons-content__actions'>
                <Button
                  className='btn btn-primary btn-lg'
                  onClick={() => {
                    onClose();
                  }}
                >
                  {t('forms.cancel')}
                </Button>
                <Button
                  className='btn btn-danger btn-lg'
                  type='submit'
                  pending={submitRequest.status === 'pending'}
                >
                  <span className='btn__ico'>
                    <Icon className='ico-trash-n-os' name='trashcan' />
                  </span>
                  {t(`delete_account.button.remove`)}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </RenderAboveEverything>
  );
};

export default DeleteAccountPopup;
