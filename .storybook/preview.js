import React from 'react';
import { Provider } from 'react-redux';
import { Router } from 'react-router-dom';
import { addDecorator } from '@storybook/react';

import history from '../src/history';

import 'normalize.css';
import 'react-virtualized/styles.css';
import 'assets/styles/index.css';
import 'assets/styles/fonts.css';
import 'assets/styles/onesoil.css';
import 'assets/styles/onesoil-updates.css';

// Break some import loops
import 'modules/auth';
import 'modules/stations';
import 'modules/fields';

import setupStore from 'utils/setup-store';

const { store } = setupStore();

addDecorator(storyFn => (
  <Provider store={store}>
    <Router history={history}>
      <div
        className='page page-app-welcome'
        style={{ minHeight: '100%', height: '100%' }}
      >
        <div className='page-container' style={{ display: 'flex' }}>
          {storyFn()}
        </div>
      </div>
    </Router>
  </Provider>
));
