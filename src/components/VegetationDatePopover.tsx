import React, { Fragment } from 'react';
import { connect } from 'react-redux';
import groupBy from 'lodash/groupBy';
import moment from 'moment';
import cx from 'classnames';

import LinkButton from 'components/LinkButton';
import ModernSpinner from 'components/ui/ModernSpinner/ModernSpinner';

//@ts-ignore
import fields from 'modules/fields';
import { fieldsSelectors } from 'modules/fields/selectors';

//@ts-ignore
import { MinNDVIDate } from 'sagas/local/field-info';
import { formatDate } from 'utils/format-date';
import { useTranslate } from 'utils/use-translate';
import { RGB } from 'types/rgb';

//Before TS typegen model use duck typing for ndvi
interface VegetationDatePopoverProps {
  fieldFillingType: string;
  style: React.CSSProperties;
  value: TEMPORARY_ANY;
  fieldId: string;
  ndvi: {
    info: { processed: number; total: number };
    items: {
      date: string;
      id: number;
      status: number;
      validity: number;
    }[];
    options: {
      source: string;
      startDate: string;
    };
  };
  rgb: RGB;
  logChangeDateEvent?: (event: string, index: number) => void;
  onLoadMoreNDVI: (fieldId: string) => {};
  onLoadMoreRGB: (fieldId: string) => {};
  getMenuProps: TEMPORARY_ANY;
  getItemProps: TEMPORARY_ANY;
  highlightedIndex: number | null;
}

const VegetationDatePopover = React.forwardRef<
  TEMPORARY_ANY,
  VegetationDatePopoverProps
>(
  (
    {
      style,
      value,
      fieldId,
      ndvi,
      rgb,
      logChangeDateEvent,
      onLoadMoreNDVI,
      onLoadMoreRGB,
      getMenuProps,
      getItemProps,
      highlightedIndex,
      fieldFillingType,
    },
    ref,
  ) => {
    const { t } = useTranslate();
    const { info, items = [] } = fieldFillingType === 'rgb' ? rgb : ndvi; // TODO AnalysisFieldSideBar
    const { options } = ndvi;

    const loading =
      !info || info.processed < info.total || rgb?.status === 'pending';
    let index = -1;
    const filteringItems =
      fieldFillingType === 'rgb'
        ? items
        : (items as []).filter(
            (item: {
              date: string;
              id: number;
              status: number;
              validity: number;
            }) => item.validity > 0 && item.status === 1,
          );

    let filteredItems = filteringItems.map(item => {
      index += 1;
      return { ...item, index };
    });
    const currentYear = new Date().getFullYear();
    const itemsByYear = groupBy(
      filteredItems,
      (item: { date: string; id: number; status: number; validity: number }) =>
        moment(item.date).year(),
    );
    const years = Object.keys(itemsByYear).map(Number).sort().reverse();

    return (
      <div ref={ref} style={{ ...style, width: 260 }} className='modal-select'>
        <div className='modal-select__inner' {...getMenuProps()}>
          {years.map(year => (
            <Fragment key={year}>
              {year !== currentYear && (
                <div className='modal-select__title'>
                  {/* @ts-ignore */}
                  {t('fields.ndvi.year', { year })}
                </div>
              )}
              <ul className='modal-select__list'>
                {itemsByYear?.[year]?.map((item: TEMPORARY_ANY) => (
                  <li
                    key={item.date}
                    className='modal-select__list-item'
                    {...getItemProps({
                      item,
                      index: item.index,
                      onClick: () => {
                        logChangeDateEvent?.(
                          'map-glance-change-date_list',
                          item.index,
                        );
                      },
                    })}
                  >
                    <LinkButton
                      className={cx('modal-select__item', {
                        __checked: value && value.id === item.id,
                        __highlighted: highlightedIndex === item.index,
                      })}
                    >
                      <span className='modal-select__value'>
                        {formatDate(
                          item.date,
                          // @ts-ignore
                          t(
                            moment(item.date).year() === currentYear
                              ? 'fields.ndvi.date_format'
                              : 'fields.ndvi.full_date_format',
                          ),
                        )}
                      </span>
                      {fieldFillingType === 'rgb' ? null : (
                        <span className='modal-select__abbr'>
                          {t(
                            // @ts-ignore
                            item.validity === 100
                              ? 'fertilizer.nitrogen.no_clouds'
                              : 'fertilizer.nitrogen.clouds',
                            { value: `${(100 - item.validity).toFixed()}%` },
                          )}
                        </span>
                      )}
                    </LinkButton>
                  </li>
                ))}
              </ul>
            </Fragment>
          ))}
          {!loading && options?.startDate > MinNDVIDate && (
            <ul className='modal-select__list'>
              <li className='modal-select__list-item'>
                <LinkButton
                  className='modal-select__item'
                  onClick={() => {
                    if (fieldFillingType === 'rgb') {
                      onLoadMoreRGB(fieldId);
                    } else {
                      onLoadMoreNDVI(fieldId);
                    }
                  }}
                >
                  <span className='modal-select__value'>
                    {t('fields.ndvi.load_more')}
                  </span>
                </LinkButton>
              </li>
            </ul>
          )}
        </div>
        {loading && (
          <div className='modal-select__loader'>
            <ModernSpinner /> {t('fertilizer.nitrogen.loading_dates')}
          </div>
        )}
      </div>
    );
  },
);

const mapStateToProps = (
  state: TEMPORARY_ANY,
  ownProps: VegetationDatePopoverProps,
) => ({
  fieldFillingType: fieldsSelectors.getFieldFillingType(state),
  ndvi: fieldsSelectors.getNDVIByID(state, ownProps.fieldId),
  rgb: fieldsSelectors.getRGBById(state, ownProps.fieldId),
});

const mapDispatchToProps = {
  onLoadMoreNDVI: fields.actions.requestNextNDVIPeriod,
  onLoadMoreRGB: fields.actions.requestNextRGBPeriod,
};

export default connect(mapStateToProps, mapDispatchToProps, null, {
  forwardRef: true,
})(VegetationDatePopover);
