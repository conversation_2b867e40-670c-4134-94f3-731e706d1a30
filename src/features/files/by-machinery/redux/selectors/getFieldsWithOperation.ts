import { createSelector } from 'reselect';
import { selectors } from './index';
import { fieldsSelectors } from 'modules/fields/selectors';
import { ApiDataFieldOperations } from '../../types';

export const getFieldsWithOperation = createSelector(
  selectors.getMatchingOperations,
  fieldsSelectors.getAllOwn,
  (operations: ApiDataFieldOperations[], fields) => {
    const fieldsWithDataIds = operations.map(record => record.field_uuid);

    return fields.filter(field =>
      fieldsWithDataIds.includes(field.fieldUserSeasonId),
    );
  },
);
