import React, { forwardRef } from 'react';
import { Controller } from 'react-hook-form';
import { cx } from 'linaria';
import type { Control } from 'react-hook-form';
import type { FieldErrors } from 'react-hook-form/dist/types/errors';

import ClearableInput from 'components/ClearableInput';
import {
  ClientForm,
  ClientFormType,
  EditNameFormType,
} from 'features/multiaccount/dealer/modals/ClientForm/types';
import { ClientFieldErrors } from 'features/multiaccount/dealer/modals/ClientForm/components/ClientField/ClientFieldErrors';

import { ErrorData } from 'modules/entities/types';
import { useTranslate } from 'utils/use-translate';

import { styles } from './ClientField.styles';

type ClientFieldProps = {
  autoFocus?: boolean;
  columnTranslationKey: string;
  control: Control<ClientForm | EditNameFormType>;
  disabled?: boolean;
  formErrors: FieldErrors<ClientForm | EditNameFormType>;
  inputType?: string;
  isPending: boolean;
  name: string;
  placeholderTranslationKey?: string;
  requestErrors: Nullable<ErrorData>;
  tabIndex?: number;
  type: ClientFormType;
  validationRules?: Record<string, unknown>;
};

const ClientField = forwardRef<HTMLInputElement, ClientFieldProps>(
  (
    {
      autoFocus = false,
      columnTranslationKey,
      control,
      disabled = false,
      formErrors,
      inputType = 'text',
      isPending,
      name,
      placeholderTranslationKey,
      requestErrors,
      tabIndex = 1,
      type,
      validationRules,
    },
    ref,
  ) => {
    const { t } = useTranslate();

    const defaultValidationRules = {
      required: t('multiaccount.dealer.error.required') as string,
      validate: (v: string) =>
        v.trim() === ''
          ? (t('multiaccount.dealer.error.required') as string)
          : true,
    };

    const rules = validationRules || defaultValidationRules;

    return (
      <div className='form-group'>
        <label className={cx('form-label', styles.label)}>
          {t(columnTranslationKey)}
        </label>

        <Controller
          name={name}
          control={control}
          rules={rules}
          render={({ onChange, value }, { invalid }) => {
            return (
              <>
                <ClearableInput
                  id={name}
                  disabled={isPending || disabled}
                  tabIndex={tabIndex}
                  autoFocus={autoFocus}
                  className={cx('form-input size-full', invalid && '__error')}
                  type={inputType}
                  ref={ref}
                  value={value || ''}
                  onChange={onChange}
                  placeholder={
                    placeholderTranslationKey
                      ? t(placeholderTranslationKey)
                      : ''
                  }
                />
                <ClientFieldErrors
                  name={name}
                  formErrors={formErrors}
                  requestErrors={requestErrors}
                />
              </>
            );
          }}
        />
      </div>
    );
  },
);

export default ClientField;
