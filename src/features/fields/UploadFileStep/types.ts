import {
  FieldsUsersFilesAuthorizationError,
  FieldsUsersFilesValidationError,
} from 'types/api';

export type UploadFileErrorType = {
  status:
    | 'filenames_not_same'
    | 'mixed_format'
    | 'too_many_files'
    | 'too_big_file'
    | 'same_format'
    | 'dbf_missing'
    | 'shp_missing'
    | 'unsupported_file'
    | 'unsupported_media_file'
    | null;
};

export type ServerErrorType =
  | FieldsUsersFilesValidationError
  | FieldsUsersFilesAuthorizationError;
