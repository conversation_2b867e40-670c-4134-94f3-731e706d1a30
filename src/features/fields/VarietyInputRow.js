import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';
import { createSelector } from 'reselect';
import uniq from 'lodash/uniq';

import CustomInputDropdown from './CustomInputDropdown';

import entities from 'modules/entities';

const collator = new Intl.Collator(undefined, {
  numeric: true,
  sensitivity: 'base',
});

export const getVarietyByCrop = createSelector(
  state => entities.selectors.getAll(state, 'fieldCrop'),
  (state, crop) => crop,
  (fieldCrops, crop) => {
    const varieties = fieldCrops
      .filter(
        item =>
          item.variety !== null && item.variety !== '' && item.crop === crop,
      )
      .map(item => item.variety)
      .sort((a, b) => collator.compare(a, b));
    return uniq(varieties);
  },
);

const VarietyInputRow = ({
  active = false,
  index,
  crop,
  value,
  placeholder,
  setValue,
  onChange,
  disabled,
  invalid = false,
}) => {
  const varietyList = useSelector(state => getVarietyByCrop(state, crop));

  useEffect(() => {
    if (setValue) setValue(`selectedFields[${index}].crops[0].variety`, '');
  }, [index, crop, setValue]);

  return (
    <CustomInputDropdown
      index={index}
      placeholder={placeholder}
      crop={crop}
      value={value}
      disabled={!active && (crop === null || crop === 'propose' || disabled)}
      invalid={invalid}
      varietyList={varietyList}
      onChange={onChange}
    />
  );
};

export default VarietyInputRow;
