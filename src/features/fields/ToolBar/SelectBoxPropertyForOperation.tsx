import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { useCurrentFieldDataOperationsLoader } from 'features/files/by-machinery/dataLoaders/useCurrentFieldDataOperationsLoader';
import MapDropdown from 'components/ui/mapToolBar/MapDropdown';
import { useTranslate } from 'utils/use-translate';
// @ts-ignore
import fields from 'modules/fields';
import { fieldsSelectors } from 'modules/fields/selectors';
import { ApiDataFieldOperations } from '../../files/by-machinery/types';
import { useLegendLoader } from 'features/files/by-machinery/dataLoaders/useLegendLoader';

export type SelectBoxPropertyForOperationProps = {
  operation: ApiDataFieldOperations;
};

export const SelectBoxPropertyForOperation = ({
  operation,
}: SelectBoxPropertyForOperationProps) => {
  const { t } = useTranslate();
  const [legends] = useLegendLoader(operation);

  const currentPropertyName = useSelector(
    fieldsSelectors.getFieldFillingProperty,
  );
  const dispatch = useDispatch();
  const [data] = useCurrentFieldDataOperationsLoader();
  const options = Object.keys(legends || {}).map(key => ({
    id: key as string,
    label: t(`import.visualisation.${key}.title` as string),
  }));
  const currentOption = options.find(v => v.id === currentPropertyName);

  useEffect(() => {
    if (!currentOption && options.length > 0) {
      dispatch(fields.actions.selectFieldFillingProperty(options[0]?.id));
    }
  }, [options, currentOption, dispatch]);

  if (!data || options.length === 0) {
    return null;
  }

  return (
    <MapDropdown
      value={currentPropertyName as string}
      label={
        t(`import.visualisation.${currentPropertyName || ''}.title`) as string
      }
      options={options}
      onChange={value => {
        dispatch(fields.actions.selectFieldFillingProperty(value));
      }}
    />
  );
};
