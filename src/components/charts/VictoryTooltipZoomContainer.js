import React from 'react';
import { VictoryZoomContainer } from 'victory';
import hoistNonReactStatics from 'hoist-non-react-statics';

import VictoryTooltipContainer from './VictoryTooltipContainer';

const VictoryTooltipZoomContainer = ({ children, ...otherProps }) => (
  <VictoryTooltipContainer
    containerComponent={<VictoryZoomContainer />}
    {...otherProps}
  >
    {children}
  </VictoryTooltipContainer>
);

// Victory is relying on some fields to be present on container components for shared events to work
hoistNonReactStatics(VictoryTooltipZoomContainer, VictoryZoomContainer);

VictoryTooltipZoomContainer.defaultProps = {
  ...VictoryZoomContainer.defaultProps,
};

export default VictoryTooltipZoomContainer;
