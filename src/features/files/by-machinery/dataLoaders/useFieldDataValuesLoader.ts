import { useDispatch, useSelector } from 'react-redux';
import { useCallback, useEffect, useMemo } from 'react';

import entities from 'modules/entities';
import { selectors } from 'features/files/by-machinery/redux/selectors';
import { FieldDataValues, DataLoader } from 'features/files/by-machinery/types';
import RootState from 'types/rootState';

export const DATA_VALUES_REQUEST_ID = 'useFieldDataValuesLoader';

export const useFieldDataValuesLoader = (): DataLoader<FieldDataValues[]> => {
  const dispatch = useDispatch();
  const uuids = useSelector((state: RootState) =>
    selectors.getMatchingTasksUuid(state),
  );
  const uuidsStr = uuids.join(',');

  const fetch = useCallback(() => {
    if (!uuidsStr) {
      return;
    }
    dispatch(
      entities.actions.request(DATA_VALUES_REQUEST_ID, [
        {
          type: 'fetchMany',
          entityType: 'fieldsDataLayerValue',
          query: {
            include: ['prop.layer.source.upload'],
            'filter{prop.layer.source.upload.in}': uuidsStr.split(','),
          },
        },
      ]),
    );
  }, [uuidsStr, dispatch]);

  const all: FieldDataValues[] = useSelector(state => {
    return entities.selectors.getAll(state, 'fieldsDataLayerValue');
  });

  const data = useMemo(() => {
    return all.filter(v => {
      if (v.prop?.layer?.source?.upload_uuid) {
        // backend has a bug. sometimes it sends inconsistent data and omit layer or source
        return uuids.includes(v.prop.layer.source.upload_uuid);
      } else {
        return false;
      }
    });
  }, [all, uuids]);

  const request = useSelector(state =>
    entities.selectors.getRequestStatus(state, DATA_VALUES_REQUEST_ID),
  );

  useEffect(fetch, [fetch]);

  return [data, request, fetch];
};
