import React, { memo } from 'react';
import { useSelector } from 'react-redux';

import settings from 'modules/settings';
import fields from 'modules/fields';

import AverageVegetationLabel from './AverageVegetationLabel';
import moment from 'moment';
import { formatYieldUnit } from 'utils/units';

import { formatDate } from 'utils/format-date';
import { useTranslate } from 'utils/use-translate';

const currentYear = new Date().getFullYear();

const SortedByLabel = ({ fieldId }) => {
  const { sortBy } = useSelector(settings.selectors.getFieldList);
  const units = useSelector(settings.selectors.getUnits);
  const yieldUnitsByCrop = useSelector(settings.selectors.getYieldUnitsByCrop);
  const { t } = useTranslate();

  const { crops } = useSelector(state =>
    fields.selectors.getByID(state, fieldId),
  );

  const hasValue = crops.length === 1 && crops[0]?.[sortBy];

  if (sortBy === 'yield_value' && hasValue) {
    return (
      <div className='soil-fields-list-meta__item soil-fields-list-meta__ndvi'>
        {`${crops[0]?.yield_value} ${formatYieldUnit(
          crops[0]?.yield_value_units,
          units,
          yieldUnitsByCrop[crops[0]?.crop],
          t,
        )}`}
      </div>
    );
  }

  if (['sowing_date', 'harvest_date'].includes(sortBy) && hasValue) {
    return (
      <div className='soil-fields-list-meta__item soil-fields-list-meta__ndvi'>
        {formatDate(
          crops[0]?.[sortBy],
          t(
            moment(crops[0]?.[sortBy]).year() === currentYear
              ? 'fields.ndvi.date_format'
              : 'fields.ndvi.full_date_format',
          ),
        )}
      </div>
    );
  }

  if (sortBy === 'vegetation_index' || sortBy === 'name' || sortBy === 'crop') {
    return <AverageVegetationLabel fieldId={fieldId} />;
  }

  return null;
};

export default memo(SortedByLabel);
