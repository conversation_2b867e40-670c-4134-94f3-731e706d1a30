import React, { VFC } from 'react';
import uniq from 'lodash/uniq';
import { useSelector } from 'react-redux';

import { useTranslate } from 'utils/use-translate';

import { ApiDataFieldOperations } from 'features/files/by-machinery/types';
import { selectors } from 'features/files/by-machinery/redux/selectors';

import { CellStyle, NotValueStyle } from './styles';
import { formatMeta } from '../../../utils/formatMeta';
import RootState from 'types/rootState';

export type CellProps = {
  hovered: boolean;
  propertyName: string;
  operations: ApiDataFieldOperations[];
};

export const CellText: VFC<CellProps> = ({ propertyName, operations }) => {
  const { t } = useTranslate();
  const meta = useSelector((state: RootState) => selectors.getMeta(state));
  if (!meta) {
    return null;
  }

  const formattedMeta = formatMeta(meta);

  const propertyMeta = formattedMeta.groups
    .map(i => i.properties)
    .flat(1)
    .find(v => v.name === propertyName);
  const dependentProperties = propertyMeta!.children
    .map(v => v.name)
    .concat(propertyName) as string[];

  const properties = operations
    .map(operation =>
      operation.data_source.layer.properties.filter(v =>
        dependentProperties.includes(v.target_name as string),
      ),
    )
    .flat(1)
    .filter(Boolean);

  const values = properties
    .map(property => property!.sample_values || [])
    .flat(1)
    .sort((a, b) =>
      // @ts-ignore
      typeof a === 'string' ? a.localeCompare(b || '') : a > b ? 1 : -1,
    );

  if (values.length > 0) {
    const units = uniq(properties.map(property => property.unit)).filter(
      Boolean,
    );

    const unitLabel =
      units.length === 1 ? t(`import.matching.units.${units[0]}`) : '';

    return (
      <CellStyle hovered={false}>
        {[values[0], values[values.length - 1]]
          .map(v => `${v} ${unitLabel}`)
          .join(' - ')}
      </CellStyle>
    );
  }

  return (
    <CellStyle hovered={false}>
      <NotValueStyle>{t('import.matching_operations.not_value')}</NotValueStyle>
    </CellStyle>
  );
};
