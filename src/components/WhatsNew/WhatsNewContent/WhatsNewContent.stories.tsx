import React from 'react';
import { Provider } from 'react-redux';
//@ts-ignore
import setupStore from 'utils/setup-store';
import { useTranslate } from 'utils/use-translate';

//@ts-ignore
import WhatsNewContent from './WhatsNewContent';
import { UserSettingsStatusMap } from 'modules/settings/types';

const MockStore = ({
  storyStore,
  children,
}: {
  storyStore: TEMPORARY_ANY;
  children: TEMPORARY_ANY;
}) => <Provider store={storyStore}>{children}</Provider>;

export default {
  title: 'common/WhatsNewContent',
  component: WhatsNewContent,
};

export const Simple = () => {
  const { store } = setupStore({
    settings: {
      status: UserSettingsStatusMap.resolved,
      featuresUsed: { id: '1', value: [] },
    },
  });

  const { t } = useTranslate();

  return (
    <MockStore storyStore={store}>
      <WhatsNewContent language={'en'} onClose={() => {}} pages={[]} t={t} />
    </MockStore>
  );
};
