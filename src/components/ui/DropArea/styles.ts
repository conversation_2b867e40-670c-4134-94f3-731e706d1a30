import { styled } from 'linaria/react';
import { css } from 'linaria';

export const DropAreaIcon = styled.div`
  margin-top: 26px;
  margin-bottom: 34px;
`;

export const DropAreaTitle = styled.div`
  font-weight: 500;
  font-size: 16px;
  line-height: 22px;
  text-align: center;
  color: #5e5e5e;
  margin-bottom: 4px;
`;

export const DropAreaDescription = styled.div`
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  text-align: center;
  color: #5e5e5e;
`;

export const fileInput = css`
  position: absolute;
  left: -99999rem;
  height: 0;
  width: 0;
`;
