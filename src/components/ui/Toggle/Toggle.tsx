import React, { CSSProperties, useMemo } from 'react';
import { nanoid } from 'nanoid';
import { cx } from 'linaria';

import { styles } from './Toggle.style';

export type Props = {
  className?: string;
  size?: 'normal' | 'small';
  options: Array<{ key: string; value: string; icon?: string }>;
  value: string;
  disabled?: boolean;
  onChange: Function;
};

// Differs from old Toggle component, that this one can show more than 2 items

const Toggle = ({
  className,
  size,
  options,
  value,
  disabled,
  onChange,
}: Props) => {
  const toggleId = useMemo(() => nanoid(), []);

  const activeIndex = useMemo(
    () => options.findIndex(option => option.key === value),
    [options, value],
  );

  return (
    <div
      className={cx(
        styles.wrapper,
        className,
        disabled && styles.disabledWrapper,
      )}
    >
      <div
        className={styles.content}
        style={{ '--item-count': options.length } as CSSProperties}
      >
        {options.map((option, index) => (
          <div className={styles.group} key={option.key}>
            <input
              checked={value === option.key}
              id={`${toggleId}-${index}`}
              type='radio'
              name={toggleId}
              disabled={disabled}
              onChange={event => {
                if (!event.target.checked) {
                  return;
                }
                onChange(option.key, option);
              }}
            />
            <label
              className={cx(
                styles.label,
                size === 'small' && styles.label__small,
              )}
              htmlFor={`${toggleId}-${index}`}
            >
              <span className={styles.value}>{option.value}</span>
            </label>
          </div>
        ))}
        {activeIndex !== -1 && (
          <div
            className={cx(
              styles.background,
              disabled && styles.disabledBackground,
            )}
            style={{
              width: `calc(100% / ${options.length})`,
              transform: `translateX(${100 * activeIndex}%)`,
            }}
          />
        )}
      </div>
    </div>
  );
};

export default Toggle;
