import { css } from 'linaria';
import { styled } from 'linaria/react';

import { SwitchSize } from './Switch';

export const styles = {
  label: css`
    display: flex;
    align-items: center;
    cursor: pointer;
  `,
  wrapper: css`
    display: inline-flex;
    overflow: hidden;
    box-sizing: border-box;
    position: relative;
    flex-shrink: 0;
    z-index: 0;
    vertical-align: middle;
    width: 40px;
    height: 20px;
    padding: 0px;
    margin: 8px;
  `,

  small: css`
    width: 32px;
    height: 16px;
    margin: 6px;
  `,

  medium: css`
    width: 40px;
    height: 20px;
    margin: 8px;
  `,

  large: css`
    width: 48px;
    height: 24px;
    margin: 10px;
  `,

  switchTrack: css`
    height: 100%;
    width: 100%;
    border-radius: 13px;
    opacity: 1;
    transition: background-color 500ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
    background-color: rgba(165, 178, 188, 1);
  `,

  checkbox: css`
    cursor: inherit;
    position: absolute;
    opacity: 0;
    width: 300%;
    height: 100%;
    top: 0px;
    left: -100%;
    margin: 0px;
    padding: 0px;
    z-index: 1;
  `,
};

type StyledSwitchRootProps = {
  className: string;
  size: SwitchSize;
};

type StyledSwitchTrackProps = {
  size: SwitchSize;
};

type StyledSwitchThumbProps = {
  size: SwitchSize;
};

const getSizeStyles = (size: SwitchSize) => {
  switch (size) {
    case 'small':
      return {
        thumbSize: '10px',
        borderRadius: '10px',
        translateX: '16px',
        margin: '3px',
        top: '3px',
        left: '3px',
      };
    case 'large':
      return {
        thumbSize: '18px',
        borderRadius: '16px',
        translateX: '24px',
        margin: '3px',
        top: '3px',
        left: '3px',
      };
    case 'medium':
    default:
      return {
        thumbSize: '14px',
        borderRadius: '13px',
        translateX: '20px',
        margin: '3px',
        top: '3px',
        left: '3px',
      };
  }
};

export const StyledSwitchTrack = styled.div<StyledSwitchTrackProps>`
  height: 100%;
  width: 100%;
  border-radius: ${props => getSizeStyles(props.size).borderRadius};
  opacity: 1;
  transition: background-color 500ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  background-color: rgba(165, 178, 188, 1);
`;

export const StyledSwitchRoot = styled.div<StyledSwitchRootProps>`
  display: inline-flex;
  -webkit-box-align: center;
  align-items: center;
  -webkit-box-pack: center;
  justify-content: center;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0px;
  border: 0px;
  cursor: pointer;
  user-select: none;
  vertical-align: middle;
  appearance: none;
  text-decoration: none;
  border-radius: 50%;
  position: absolute;
  top: ${props => getSizeStyles(props.size).top};
  left: ${props => getSizeStyles(props.size).left};
  z-index: 1;
  color: rgb(255, 255, 255);
  transition: left 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,
    transform 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  padding: 0px;
  margin: 0px;
  transition-duration: 300ms;

  &.checked {
    transform: translateX(${props => getSizeStyles(props.size).translateX});
  }

  &.checked + ${StyledSwitchTrack} {
    background-color: rgba(39, 174, 96, 1);
    opacity: 1;
    border: 0px;
  }
`;

export const StyledSwitchThumb = styled.div<StyledSwitchThumbProps>`
  background-color: currentcolor;
  width: ${props => getSizeStyles(props.size).thumbSize};
  height: ${props => getSizeStyles(props.size).thumbSize};
  border-radius: 50%;
  box-sizing: border-box;
`;
