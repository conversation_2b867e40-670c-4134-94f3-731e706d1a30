import React from 'react';
import { NavProgress } from 'components/ui/NavProgress/NavProgress';

const routes = [
  { to: '/', title: 'fields.upload.nav.upload_file' },
  { to: '/:fileId/match-columns', title: 'fields.upload.nav.check_data' },
  { to: '/:fileId/match-crops', title: 'fields.upload.nav.match_crops' },
  { to: '/:fileId/save-fields', title: 'fields.upload.nav.save_fields' },
];

const UploadFieldsNavBar = () => {
  return <NavProgress routes={routes} />;
};

export default UploadFieldsNavBar;
