import React, { FC, Fragment } from 'react';
import { connect, useDispatch } from 'react-redux';
import { RouteComponentProps, useLocation } from 'react-router-dom';
import { StringMap } from 'i18next';
import { Trans } from 'react-i18next';

import MapDropdown from 'components/ui/mapToolBar/MapDropdown';
import MapHint from 'components/ui/mapToolBar/MapHint';
import MapButton from 'components/ui/mapToolBar/MapButton';

import { parseQuery } from 'utils/query';
import { useTranslate } from 'utils/use-translate';

// @ts-ignore
import files from 'modules/files';
import RootState from 'types/rootState';
import { FileColumn, FileFullData } from 'types/filesState';
import { OperationTypesEnum } from 'constants/files';
import { getViewSchemeConfig } from './colorSchemes';
import { ColorSchemeEnum } from './colorSchemes/types';
import { PopoverDeprecatedAlign } from 'components/ui/Popover';

type FileToolbarComponentProps = {
  file: FileFullData;
};

export const FileToolbarComponent: FC<FileToolbarComponentProps> = ({
  file,
}) => {
  const { t } = useTranslate();
  const dispatch = useDispatch();
  const location = useLocation();

  if (!file) {
    return null;
  }
  const query = parseQuery(location);
  const columns = file.status === 'resolved' ? file.columns : [];
  const selectedColumn: FileColumn = columns
    .filter(column => file.properties.includes(column.name))
    .find(
      column => column.name === (query.column || file.default_column),
    ) as FileColumn;

  const viewSchemeConfig = getViewSchemeConfig(selectedColumn, file);
  const LegendComponent = viewSchemeConfig.legend;

  const link = (
    /* eslint-disable-next-line jsx-a11y/anchor-has-content */
    <a
      target='_blank'
      rel='noreferrer'
      href={t('soil_analysis.hint.link') as string}
    />
  );

  const isSoilAnalysis =
    file.type === OperationTypesEnum.soil_analysis ||
    getViewSchemeConfig(selectedColumn, file).type ===
      ColorSchemeEnum.soilAnalysis;

  return (
    <Fragment>
      <div className='top-toolbar'>
        <div style={{ display: 'flex' }}>
          <div className='top-toolbar-item __fixed'>
            <MapDropdown
              disabled={file.status !== 'resolved'}
              value={selectedColumn ? selectedColumn.name : null}
              label={
                file.status === 'pending'
                  ? (t('files.toolbar.pending') as string)
                  : (t('files.toolbar.data', {
                      column: selectedColumn
                        ? selectedColumn.name
                        : t('files.toolbar.no_columns'),
                    } as StringMap) as string)
              }
              options={columns
                .filter(column => (file.properties || []).includes(column.name))
                .map(column => ({
                  label: column.name,
                  id: column.name,
                }))}
              onChange={column => {
                dispatch(files.actions.setDefaultColumn(file.id, column));
              }}
            />
          </div>
          {isSoilAnalysis && (
            <div
              className='top-toolbar-item __fixed'
              style={{ position: 'relative' }}
            >
              <MapHint
                align={PopoverDeprecatedAlign.BottomLeft}
                renderTrigger={props => (
                  <MapButton
                    {...props}
                    iconName='alert-circle'
                    label={t('soil_analysis.hint.label') as string}
                  />
                )}
                popoverContent={
                  <div>
                    <Trans components={[link]}>
                      {'soil_analysis.hint.description'}
                    </Trans>
                  </div>
                }
              />
            </div>
          )}
        </div>
      </div>
      {file.status === 'resolved' && selectedColumn && (
        <LegendComponent file={file} column={selectedColumn} />
      )}
    </Fragment>
  );
};

type FileToolbarProps = RouteComponentProps<{
  fileId?: string;
}>;

const mapStateToProps = (state: RootState, ownProps: FileToolbarProps) => ({
  file: files.selectors.getByID(state, ownProps.match.params.fileId),
});

export default connect(mapStateToProps)(FileToolbarComponent);
