import React, { useState, VFC } from 'react';
import { useDispatch } from 'react-redux';

import { useTranslate } from 'utils/use-translate';
import Icon from 'components/Icon';
import Loader from 'components/progress/Loader';

import {
  cancelButtonIconClass,
  CancelButtonStyle,
  ActiveTaskInfoStyle,
  ActiveTaskSubTitleStyle,
  ActiveTaskTitleStyle,
  ActiveTaskStatusWrapper,
  ActiveTaskRowStyle,
  ActiveTaskRowContainer,
} from '../styles';

import {
  TaskProcessingType,
  TaskImportingType,
  TaskWaitingType,
} from 'features/files/by-machinery/types';
import { actions } from 'features/files/by-machinery/redux/actions';
import Alert from 'components/ui/Alert/Alert';

type TaskProcessingRowProps = {
  task: TaskProcessingType | TaskImportingType | TaskWaitingType;
};

const TaskProcessingRow: VFC<TaskProcessingRowProps> = ({ task }) => {
  const { t } = useTranslate();
  const dispatch = useDispatch();
  const [isShowRemoveAlert, setIsShowRemoveAlert] = useState(false);

  return (
    <ActiveTaskRowStyle disabled={task.disabled}>
      <ActiveTaskRowContainer>
        <CancelButtonStyle
          onClick={e => {
            e.preventDefault();
            setIsShowRemoveAlert(true);
          }}
          disabled={task.disabled}
        >
          <ActiveTaskStatusWrapper>
            <Icon name='close' className={cancelButtonIconClass} />
            <Loader inline />
          </ActiveTaskStatusWrapper>
        </CancelButtonStyle>
        <div>
          <ActiveTaskTitleStyle>{task.name}</ActiveTaskTitleStyle>
          <div>
            <ActiveTaskSubTitleStyle>
              {t('import.by-machinery.processing.title')}
            </ActiveTaskSubTitleStyle>
            <ActiveTaskInfoStyle>
              {t('import.by-machinery.processing.info')}
            </ActiveTaskInfoStyle>
          </div>
        </div>
        {isShowRemoveAlert && (
          <Alert
            show={true}
            text={t(`import.by-machinery.alert.remove.text`)}
            onDanger={() => {
              dispatch(actions.removeTask(task));
              setIsShowRemoveAlert(false);
            }}
            onCancel={() => setIsShowRemoveAlert(false)}
            danger={t('import.by-machinery.alert.remove.confirm') as string}
            success={t('import.by-machinery.alert.remove.cancel') as string}
          />
        )}
      </ActiveTaskRowContainer>
    </ActiveTaskRowStyle>
  );
};

export default TaskProcessingRow;
