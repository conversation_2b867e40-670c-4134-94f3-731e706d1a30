import React, { useState } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { ColumnInfo } from 'components/ui/DragAndDropTable/TableRow/types';
import { TableRow } from 'components/ui/DragAndDropTable/TableRow/TableRow';
import { Table } from './DragAndDropTable.style';
import { CustomDragLayer } from './CustomDragLayer/CustomDragLayer';
import { v4 as uuidv4 } from 'uuid';
import { DragAndDropTableContext } from './DragAndDropTableContext';
import { TableGroupsRow } from './TableRow/TableGroupsRow';

export interface DragAndDropTableProps extends React.PropsWithChildren<{}> {
  columnsInfo: ColumnInfo[];
  setSorted?: (columnId: string) => void;
}

const DragAndDropTable = ({
  columnsInfo,
  children,
  setSorted,
}: DragAndDropTableProps) => {
  const [dragUuid] = useState(uuidv4());
  const childrenWithProps = React.Children.map(children, child => {
    if (React.isValidElement(child)) {
      return React.cloneElement(child, { columnsInfo });
    }
    return child;
  });

  return (
    <DndProvider backend={HTML5Backend}>
      <DragAndDropTableContext.Provider value={dragUuid}>
        <table className={Table}>
          <tbody>
            <TableGroupsRow columnsInfo={columnsInfo} />
            <TableRow
              id='header'
              header
              columnsInfo={columnsInfo}
              onCellClick={setSorted}
            >
              {() =>
                columnsInfo.map(columnInfo => {
                  return (
                    <React.Fragment key={columnInfo.key}>
                      {columnInfo.headerLabel}
                    </React.Fragment>
                  );
                })
              }
            </TableRow>
            {childrenWithProps}
          </tbody>
        </table>
        <CustomDragLayer />
      </DragAndDropTableContext.Provider>
    </DndProvider>
  );
};

export default DragAndDropTable;
