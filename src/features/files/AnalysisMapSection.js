import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

/* eslint-disable-next-line */
import 'leaflet.sync';
import { getModel } from 'react-redux-form';
import cx from 'classnames';
import moment from 'moment';

import Saga from 'components/saga/Saga';
import ControlledMap from 'components/maps/ControlledMap';
import DirectGoogleLayer from 'components/maps/DirectGoogleLayer';
import AnalysisToolbar from './AnalysisToolbar';
import SideBySideMap from './SideBySideMap';
import AnalysisInteractiveTooltip from './AnalysisInteractiveTooltip';
import analyzeFieldSaga from 'sagas/local/analyze-field';

import SelectedFieldFiles from 'features/files/SelectedFieldFiles';
import fields from 'modules/fields';
import map from 'modules/map';
import forms from 'constants/forms';

import ZoomControls, {
  MinZoom,
  MaxZoom,
} from 'features/platform/ZoomControls/ZoomControls';
import RangeLegend from 'components/RangeLegend';
import { BarsCount, LegendColorsMap } from 'constants/style';
import { StyledBottomMapWidget } from 'components/ui/mapToolBar/styles';

import { formatDate } from 'utils/format-date';
import { useTranslate } from 'utils/use-translate';

const AnalysisMapSection = ({ match, history, location }) => {
  const MapA = useRef(null);
  const MapB = useRef(null);
  const [tooltipIndex, setTooltipIndex] = useState(null);
  const [hoveredValue, setHoveredValue] = useState(null);
  const dispatch = useDispatch();
  const { t } = useTranslate();
  const fieldFillingType = useSelector(fields.selectors.getFieldFillingType);
  const field = useSelector(state =>
    fields.selectors.getByID(state, match.params.fieldId),
  );
  const ndvi = useSelector(state =>
    fields.selectors.getNDVIByID(state, match.params.fieldId),
  );
  const rgb = useSelector(state =>
    fields.selectors.getRGBById(state, match.params.fieldId),
  );
  const zoom = useSelector(state => map.selectors.getZoomLevel(state));

  const { items = [] } = ndvi;
  const filteredItems = items.filter(
    item => item.validity > 0 && item.status === 1,
  );

  const selectedNDVIIndex = useSelector(state => {
    return getModel(state, `${forms.analysis}`);
  });
  const selectedRGBIndex = useSelector(state => {
    return getModel(state, `${forms.analysisRGB}`);
  });

  const mapSections = Object.keys(
    fieldFillingType === 'rgb' ? selectedRGBIndex : selectedNDVIIndex,
  );

  const currentYear = new Date().getFullYear();
  const renderValue = index => {
    const value =
      fieldFillingType === 'rgb'
        ? (rgb?.items ?? [])[index]
        : filteredItems[index];
    if (!value) {
      return '';
    }
    return formatDate(
      value.date,
      t(
        moment(value.date).year() === currentYear
          ? 'fields.ndvi.date_format'
          : 'fields.ndvi.full_date_format',
      ),
    );
  };

  const setView = () => {
    if (MapA.current?.map) {
      MapA.current.map._selfSetView();
    }
  };

  useEffect(() => {
    if (!field) {
      return;
    }

    if (MapA.current?.map && MapB.current?.map) {
      MapA.current.map.sync(MapB.current.map);
      MapB.current.map.sync(MapA.current.map);
    }
  }, [field, dispatch]);

  if (!field) {
    return null;
  }

  return (
    <div className='map-container'>
      <Saga
        saga={analyzeFieldSaga}
        field={field}
        mapID='grid-field'
        history={history}
      />
      <AnalysisToolbar match={match} history={history} location={location} />
      <div className='map-viewer-grid'>
        <div className='map-viewer-grid-hrow' style={{ flexBasis: '100%' }}>
          {mapSections.map((section, index) => (
            <React.Fragment key={section}>
              <div
                className='map-viewer-grid-cell'
                style={{ flexBasis: '50%' }}
              >
                <div
                  className='map-viewer-grid__header'
                  data-align={index === 0 ? 'left' : 'right'}
                  style={index === 1 ? { textAlign: 'right' } : null}
                >
                  {index + 1}.{' '}
                  {t(
                    `analysis.item.${
                      fieldFillingType === 'rgb' ? 'rgb' : 'ndvi'
                    }`,
                  )}
                  ,{' '}
                  {renderValue(
                    fieldFillingType === 'rgb'
                      ? selectedRGBIndex[`rgb${index + 1}`]
                      : selectedNDVIIndex[`ndvi${index + 1}`],
                  )}
                </div>
                <ControlledMap
                  ref={index === 0 ? MapA : MapB}
                  id='grid-field'
                  viewportHash='default'
                  onViewportHashChange={() => {}}
                  mapProps={{
                    className: 'map-viewer',
                    trackResize: true,
                    zoomControl: false,
                    worldCopyJump: true,
                    editable: false,
                    minZoom: MinZoom,
                    boxZoom: false,
                    doubleClickZoom: zoom < MaxZoom,
                  }}
                >
                  <DirectGoogleLayer />
                  {field.id && (
                    <SelectedFieldFiles
                      fieldId={field.id}
                      index={
                        fieldFillingType === 'rgb'
                          ? selectedRGBIndex[`rgb${index + 1}`]
                          : selectedNDVIIndex[`ndvi${index + 1}`]
                      }
                      changeTooltipIndex={setTooltipIndex}
                      mapIndex={index}
                      tooltipIndex={tooltipIndex}
                      updateTooltipValue={setHoveredValue}
                    />
                  )}
                </ControlledMap>
                {fieldFillingType === 'rgb' ? null : (
                  <StyledBottomMapWidget>
                    <RangeLegend
                      colorsRange={LegendColorsMap[
                        fieldFillingType === 'default'
                          ? 'field_ndvi'
                          : 'contrasted_ndvi'
                      ].colors(BarsCount)}
                      labels={[
                        t('fields.ndvi.legend.low'),
                        t('fields.ndvi.legend.high'),
                      ]}
                      withClouds
                    />
                  </StyledBottomMapWidget>
                )}
                {/*<VegetationLegend dataTheme="dark-mode" />*/}
                {index === tooltipIndex && (
                  <AnalysisInteractiveTooltip hoveredValue={hoveredValue} />
                )}
              </div>
              {index === 0 && (
                <div className='map-viewer-grid-vdivider matrix' />
              )}
            </React.Fragment>
          ))}
          <ZoomControls
            id='grid-field'
            showFieldLocation
            showGlance
            field={field}
            setView={setView}
          />
        </div>
      </div>

      <div
        className={cx('map-viewer-grid', {
          hidden: match.params.type !== 'side-by-side',
        })}
      >
        <SideBySideMap
          history={history}
          isVisible={match.params.type === 'side-by-side'}
          field={field}
          firstDateIndex={
            fieldFillingType === 'rgb'
              ? selectedRGBIndex.rgb1
              : selectedNDVIIndex.ndvi1
          }
          secondDateIndex={
            fieldFillingType === 'rgb'
              ? selectedRGBIndex.rgb2
              : selectedNDVIIndex.ndvi2
          }
          zoom={zoom}
          renderValue={renderValue}
        />
      </div>
    </div>
  );
};

export default AnalysisMapSection;
