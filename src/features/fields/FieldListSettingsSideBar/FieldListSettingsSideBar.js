import React, { useState, useCallback, useMemo } from 'react';
import { useSelector, useDispatch } from 'react-redux';

import Button from 'components/ui/Button/Button';
import Icon from 'components/Icon';
import RadioButton from 'components/ui/RadioButton/RadioButton';
import RadioGroup from 'components/ui/RadioGroup/RadioGroup';
import Sorter from 'components/ui/Sorter/Sorter';
import Checkbox from 'components/ui/Checkbox/Checkbox';

import getRootScoutingUrl from 'utils/get-root-scouting-url';
import { useTranslate } from 'utils/use-translate';

import settings from 'modules/settings';
import fields from 'modules/fields';

import { FieldListSettingsKeys, FieldListSortOptions } from 'constants/fields';

import logEvent from 'sagas/global/logEvent';
import { SortDirections } from 'constants/sortDirections';

import {
  groupName,
  formSection,
  StyledRadioGroupRow,
} from './FieldListSettingsSideBar.style';

const FieldListSettingsSideBar = ({ history }) => {
  const { t } = useTranslate();
  const fieldListSettings = useSelector(settings.selectors.getFieldList);

  const dispatch = useDispatch();

  const [localSettings, setLocalSettings] = useState(fieldListSettings);

  const rootUrl = useMemo(
    () => getRootScoutingUrl(history.location.pathname),
    [history.location.pathname],
  );

  const updateSettings = ({ key, value }) => {
    if (
      key === FieldListSettingsKeys.groupBy &&
      value === true &&
      localSettings.sortBy === 'crop'
    ) {
      setLocalSettings({
        ...localSettings,
        [key]: value,
        sortBy: 'name',
        orderBy: SortDirections.ASCENDING,
      });
    } else if (key === FieldListSettingsKeys.sortBy) {
      setLocalSettings({
        ...localSettings,
        [key]: value,
        orderBy: SortDirections.ASCENDING,
      });
    } else {
      setLocalSettings({ ...localSettings, [key]: value });
    }
  };

  const applySettings = useCallback(() => {
    const { groupBy, sortBy, orderBy } = localSettings;
    dispatch(
      settings.actions.updateLocal({ fieldList: { groupBy, sortBy, orderBy } }),
    );
    dispatch(fields.actions.resetExpandedCrops());

    logEvent('fields_list_settings_change', {
      grouped: groupBy,
      sort: sortBy,
      order: orderBy,
    });
    history.push(rootUrl);
  }, [dispatch, history, localSettings, rootUrl]);

  return (
    <div className='soil-sidebar-create'>
      <div className='soil-sidebar-header'>
        <Button
          className='app-nav-back'
          onClick={() => {
            history.push(rootUrl);
          }}
        >
          <Icon className='ico-arrow-long-left-os' name='arrow-left' />
        </Button>
        <div className='soil-sidebar-header__inner'>
          <h1 className='soil-sidebar-header__main'>
            {t('fields.list.settings.title')}
          </h1>
        </div>
      </div>
      <div className='soil-sidebar-body __scroll'>
        <div className={formSection}>
          <Checkbox
            checked={localSettings.groupBy}
            onChange={() =>
              updateSettings({
                key: FieldListSettingsKeys.groupBy,
                value: !localSettings.groupBy,
              })
            }
          >
            {t('fields.list.settings.group')}
          </Checkbox>
        </div>
        <div className={formSection}>
          <div className={groupName}>{t('fields.list.settings.sort')}</div>
          <RadioGroup
            itemsData={FieldListSortOptions}
            name={'sort-settings'}
            itemRenderer={item => {
              if (localSettings.groupBy && item.value === 'crop') {
                return null;
              }
              return (
                <StyledRadioGroupRow key={item.value}>
                  <RadioButton
                    value={item.value}
                    name={item.name}
                    onChange={event => {
                      updateSettings({
                        key: FieldListSettingsKeys.sortBy,
                        value: event.target.value,
                      });
                    }}
                    checked={item.value === localSettings.sortBy}
                  >
                    {t(`fields.list.settings.sort.${item.value}`)}
                  </RadioButton>
                  <Sorter
                    isHidden={item.value !== localSettings.sortBy}
                    label={t(
                      `fields.list.sort.${item.type}.${localSettings.orderBy}`,
                    )}
                    value={localSettings.orderBy}
                    onChange={value => {
                      updateSettings({
                        key: FieldListSettingsKeys.orderBy,
                        value,
                      });
                    }}
                  />
                </StyledRadioGroupRow>
              );
            }}
          />
        </div>
      </div>
      <div className='soil-sidebar-addnew'>
        <Button
          className='btn btn-success btn-lg size-full'
          data-shadow='true'
          onClick={applySettings}
        >
          {t('forms.apply')}
        </Button>
      </div>
    </div>
  );
};

export default FieldListSettingsSideBar;
