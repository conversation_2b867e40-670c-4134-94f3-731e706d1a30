import React, { ChangeEvent, useEffect, useRef } from 'react';
import cx from 'classnames';

import Button from './ui/Button/Button';

export type ClearableInputProps = {
  onChange: (value: string | ChangeEvent<HTMLInputElement>) => void;
  value: string;

  autoFocus?: boolean;
  maskedValue?: string;
  disabled?: Nullable<boolean>;
} & React.DetailedHTMLProps<
  React.InputHTMLAttributes<HTMLInputElement>,
  HTMLInputElement
>;

const ClearableInput = React.forwardRef<HTMLInputElement, ClearableInputProps>(
  (props, ref) => {
    const input = useRef<HTMLInputElement>(null);

    useEffect(() => {
      if (input.current && props.autoFocus) {
        input.current.focus();
      }
    }, [props.autoFocus]);

    return (
      <div
        className={cx('form-input-extended', {
          __filled:
            (Boolean(props.value) || Boolean(props?.maskedValue)) &&
            !props.disabled,
        })}
      >
        <input
          ref={el => {
            // @ts-ignore
            input.current = el;
            // @ts-ignore
            ref && ref(el);
          }}
          className='form-input size-full'
          type='text'
          {...props}
        />
        {!props.disabled && (
          <Button
            className='form-input-clear'
            type='button'
            onClick={() => {
              if (!props.disabled) {
                props.onChange('');
                input.current!.focus();
              }
            }}
          >
            ×
          </Button>
        )}
      </div>
    );
  },
);

export default ClearableInput;
