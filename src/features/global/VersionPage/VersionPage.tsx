import React from 'react';
import { VersionPageContainer, VersionParagraph } from './VersionPage.style';
import { Link } from 'react-router-dom';

const VersionPage = () => {
  return (
    <main className='page-container'>
      <VersionPageContainer>
        <div>
          <VersionParagraph>
            Version: {process.env.REACT_APP_VERSION || '0.0.0'} (
            {process.env.REACT_APP_CI_COMMIT_SHORT_SHA || '1'}) (
            {process.env.REACT_APP_CI_PIPELINE_IID || '1'})
          </VersionParagraph>
        </div>
        <div>
          <Link to={'/'}>Back to App</Link>
        </div>
      </VersionPageContainer>
    </main>
  );
};

export default VersionPage;
