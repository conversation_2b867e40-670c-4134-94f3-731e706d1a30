import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react';
import '@testing-library/jest-dom';

import SmartButton, { ButtonDataTestId } from './SmartButton';

test('can accept dataTestId', async () => {
  const customDataTestId = 'childrenDataTestId';
  render(<SmartButton dataTestId={customDataTestId} />);

  expect(screen.queryByTestId(customDataTestId)).toBeInTheDocument();
});

test('can render text', async () => {
  const textText = 'textText';
  render(<SmartButton>{textText}</SmartButton>);

  expect(screen.queryByTestId(ButtonDataTestId)).toHaveTextContent(textText);
});

test('can render children', async () => {
  const childrenDataTestId = 'childrenDataTestId';

  const children = <span data-testid={childrenDataTestId} />;
  render(<SmartButton>{children}</SmartButton>);

  expect(screen.queryByTestId(childrenDataTestId)).toBeInTheDocument();
});

test('show no content in pending state', async () => {
  const childrenDataTestId = 'childrenDataTestId';

  const children = <span data-testid={childrenDataTestId} />;
  const { rerender } = render(
    <SmartButton pending={true}>{children}</SmartButton>,
  );

  expect(screen.queryByTestId(ButtonDataTestId)).toBeInTheDocument();
  expect(screen.queryByTestId(childrenDataTestId)).not.toBeInTheDocument();

  rerender(<SmartButton pending={false}>{children}</SmartButton>);

  expect(screen.queryByTestId(ButtonDataTestId)).toBeInTheDocument();
  expect(screen.queryByTestId(childrenDataTestId)).toBeInTheDocument();
});

test('disable onClink in disabled state', async () => {
  const stub = jest.fn();
  const { rerender } = render(<SmartButton onClick={stub} disabled={false} />);

  fireEvent.click(screen.getByTestId(ButtonDataTestId));
  expect(stub).toBeCalledTimes(1);

  rerender(<SmartButton onClick={stub} disabled={true} />);

  fireEvent.click(screen.getByTestId(ButtonDataTestId));
  expect(stub).toBeCalledTimes(1);
});

test(' disable onClink in pending state', async () => {
  const stub = jest.fn();
  const { rerender } = render(<SmartButton onClick={stub} pending={false} />);

  fireEvent.click(screen.getByTestId(ButtonDataTestId));
  expect(stub).toBeCalledTimes(1);

  rerender(<SmartButton onClick={stub} pending={true} />);

  fireEvent.click(screen.getByTestId(ButtonDataTestId));
  expect(stub).toBeCalledTimes(1);
});
