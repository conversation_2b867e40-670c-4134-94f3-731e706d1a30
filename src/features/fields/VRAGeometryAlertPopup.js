import React from 'react';

import RenderAboveEverything from 'components/RenderAboveEverything';
import Button from 'components/ui/Button/Button';

import { paSuitableStatuses } from 'constants/fields';

const VRAGeometryAlertPopup = ({ t, processingStatus, onProceed, onClose }) => {
  return (
    <RenderAboveEverything>
      <div className='modal modal-editseasons'>
        <div className='modal-outer'>
          <div className='modal-editseasons-content'>
            <div className='modal-editseasons-content__header'></div>
            <div className='modal-editseasons-content__body'>
              <div className='form-group'>
                {processingStatus?.status === 'processing' &&
                  t('vra.geometry.alert.processing.text')}
                {paSuitableStatuses.includes(
                  processingStatus?.suitability_status,
                ) && t('vra.geometry.alert.map.text')}
              </div>
            </div>
            <div className='modal-editseasons-content__actions'>
              <Button className='btn btn-primary btn-lg' onClick={onProceed}>
                {t('vra.geomerty.alert.btn.proceed')}
              </Button>
              <Button className='btn btn-success btn-lg' onClick={onClose}>
                {t('vra.geomerty.alert.btn.cancel')}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </RenderAboveEverything>
  );
};

export default VRAGeometryAlertPopup;
