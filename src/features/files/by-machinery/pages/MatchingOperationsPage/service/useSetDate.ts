import { useCallback } from 'react';
import { useDispatch } from 'react-redux';
import moment from 'moment';

import entities from 'modules/entities';
import { EntityOperation } from 'modules/entities/types';

import { ApiDataFieldOperations } from '../../../types/server';

export type OnSeSetDateType = (
  operations: ApiDataFieldOperations[],
  date: string,
) => void;

export const useSetDate = (): OnSeSetDateType => {
  const dispatch = useDispatch();

  return useCallback(
    (operations, dateString) => {
      const date = moment(dateString);
      const formattedData = date.format('YYYY-MM-DD') + 'T12:00:00Z';

      const requests: EntityOperation[] = operations.map(item => ({
        type: 'patch',
        entityType: 'fieldsDataOperation',
        path: {
          id: item.uuid,
        },
        query: {
          include: ['data_source.layer.properties'],
        },
        body: {
          start_time: formattedData,
          end_time: formattedData,
        },
      }));

      dispatch(entities.actions.request('setRequestId', requests));
    },
    [dispatch],
  );
};
