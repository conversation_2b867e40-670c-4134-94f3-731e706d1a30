import React, { VFC } from 'react';

import { FileBundle } from 'features/fields/BulkUpload/types';

import { FileBundleView } from './FileBundleView';

interface FileBundleListProps {
  bundles: FileBundle[];
}

export const FileBundleList: VFC<FileBundleListProps> = ({ bundles }) => {
  return (
    <>
      {bundles.map((bundle, index) => (
        <FileBundleView
          bundle={bundle}
          key={`${bundle.name}_${String(index)}`}
        />
      ))}
    </>
  );
};
