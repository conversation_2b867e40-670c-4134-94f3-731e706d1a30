import { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import entities from 'modules/entities';

import { selectors } from 'features/files/by-machinery/redux/selectors';
import {
  DataLoader,
  ApiDataFieldOperations,
  TaskSuccessType,
} from 'features/files/by-machinery/types';
import RootState from 'types/rootState';

export const HISTORY_OPERATIONS_REQUEST_ID = 'useHistoryFieldDataLoader';

export const useHistoryFieldDataLoader = (): DataLoader<
  ApiDataFieldOperations[]
> => {
  const dispatch = useDispatch();
  const historyTasks: TaskSuccessType[] = useSelector((state: RootState) =>
    selectors.getHistoryTasks(state),
  );
  const uuidsStr = historyTasks.map(v => v.uuid).join(',');
  const sources = historyTasks
    .map(task => task.sources.map(v => v.uuid))
    .flat(1);

  const fetch = useCallback(() => {
    if (!uuidsStr) {
      return;
    }
    dispatch(
      entities.actions.request(HISTORY_OPERATIONS_REQUEST_ID, [
        {
          type: 'fetchMany',
          entityType: 'fieldsDataOperation',
          query: {
            include: ['data_source.layer'],
            'filter{data_source.layer.source.upload.in}': uuidsStr.split(','),
          },
        },
      ]),
    );
  }, [uuidsStr, dispatch]);

  const data: ApiDataFieldOperations[] = useSelector(state => {
    return entities.selectors
      .getAll(state, 'fieldsDataOperation')
      .filter(record => sources.includes(record.data_source.layer.source_uuid));
  });

  const request = useSelector(state =>
    entities.selectors.getRequestStatus(state, HISTORY_OPERATIONS_REQUEST_ID),
  );

  useEffect(fetch, [fetch]);

  return [data, request, fetch];
};
