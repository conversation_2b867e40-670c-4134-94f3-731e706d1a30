import React, { VFC } from 'react';
import { FileProcessingStatus } from 'utils/import-fields';

import { FileProcessingView } from './FileProcessingView';

interface FileProcessingListProps {
  files: File[];
  status: Record<string, FileProcessingStatus>;
}

export const FileProcessingList: VFC<FileProcessingListProps> = ({
  files,
  status,
}) => {
  return (
    <>
      {files.map((file, index) => (
        <FileProcessingView
          key={`${file.name}_${index}`}
          name={file.name}
          status={status[file.name]!}
        />
      ))}
    </>
  );
};
