import cx from 'classnames';
import React, {
  ChangeEvent,
  ChangeEventH<PERSON>ler,
  DragEvent,
  useState,
  VFC,
  useRef,
} from 'react';

import { useTranslate } from 'utils/use-translate';

type DropAreaChildren = ({
  openUploadDialog,
}: {
  openUploadDialog: () => void;
}) => React.ReactNode;

type DropAreaDrop = {
  accept?: string;
  onLoadFiles?: (files: File[]) => void;
  onDrop?: (e: DragEvent) => void;
  onInputChange?: ChangeEventHandler;
  descriptionEndLabel?: string;
  limitLabel?: string;
  linkLabel?: string;
  titleLabel?: string;
  descriptionStartLabel?: string;
  children?: DropAreaChildren;
};

const DropArea: VFC<DropAreaDrop> = props => {
  const {
    accept = '*',
    children,
    onDrop,
    onInputChange,
    onLoadFiles,
    descriptionEndLabel = 'fields.upload.upload_area.descr_end',
    limitLabel = 'fields.upload.upload_area.size_limit',
    linkLabel = 'fields.upload.upload_area.descr_link',
    titleLabel = 'fields.upload.upload_area.title',
    descriptionStartLabel = 'fields.upload.upload_area.descr_start',
  } = props;
  const inputFile = useRef<HTMLInputElement | null>(null);

  const { t } = useTranslate();
  const [dragging, setDragging] = useState(false);

  const handleInputChange = (event: ChangeEvent<HTMLInputElement>) => {
    onInputChange && onInputChange(event);
    onLoadFiles && onLoadFiles(Array.from(event.target.files || []));
    // @ts-ignore
    event.target.value = null;
  };

  const handleDrop = (event: DragEvent) => {
    event.preventDefault();
    setDragging(false);

    onDrop && onDrop(event);
    onLoadFiles && onLoadFiles(Array.from(event.dataTransfer.files));
  };

  const handleDragOver = (event: DragEvent) => {
    event.preventDefault();
    setDragging(true);
  };

  const handleDragLeave = (event: DragEvent) => {
    event.preventDefault();
    setDragging(false);
  };

  const openUploadDialog = () => {
    inputFile.current && inputFile.current.click();
  };

  return (
    <div
      onDrop={e => handleDrop(e)}
      onDragOver={e => handleDragOver(e)}
      onDragLeave={e => handleDragLeave(e)}
      className={cx('main-uploader-area', {
        __highlight: dragging,
      })}
    >
      <div className='main-uploader-area__inner'>
        <h3 className='main-uploader-area__title'>{t(titleLabel)}</h3>
        <div className='main-uploader-area__description'>
          {children && children({ openUploadDialog })}
          {t(descriptionStartLabel)}{' '}
          <span className='main-uploader-file'>
            <input
              accept={accept}
              ref={inputFile}
              className='main-uploader-file__emulate'
              type='file'
              multiple
              onChange={handleInputChange}
            />
            <span className='main-uploader-file__label'>{t(linkLabel)}</span>{' '}
          </span>{' '}
          {t(descriptionEndLabel)}
          <div>
            <span>{t(limitLabel)}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DropArea;
