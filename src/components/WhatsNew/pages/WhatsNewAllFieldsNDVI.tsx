import React, { VFC } from 'react';
import Button from 'components/ui/Button/Button';

import WhatsNewImg from 'assets/images/whatsnew/whatsnew_allndvi.jpg';
import WhatsNewImg2x from 'assets/images/whatsnew/<EMAIL>';

import { WhatsNewProps } from '../types';
import { FeaturesKeyEnum } from '../consts';
import {
  StyledPage,
  styles,
} from 'components/WhatsNew/WhatsNewContent/WhatsNewContent.style';

const AllFieldsNDVI: VFC<WhatsNewProps> = ({ style, onClick, isLast, t }) => {
  return (
    <StyledPage style={{ ...style }}>
      <div className={styles.semiPage}>
        <div className={styles.pageContent}>
          <div className={styles.textContent}>
            <h2 className={styles.title}>
              {t('whatsnew.all_fields_ndvi.title')}
            </h2>
            <p className={styles.text}>{t('whatsnew.all_fields_ndvi.text')}</p>
          </div>
          <Button className='btn btn-success fit-content' onClick={onClick}>
            {t(`whatsnew.${isLast ? 'end' : 'next'}.action_text`)}
          </Button>
        </div>
      </div>
      <div className={styles.semiPage}>
        <picture className={styles.pagePicture}>
          <source srcSet={`${WhatsNewImg} 1x, ${WhatsNewImg2x} 2x`} />
          <img
            src={WhatsNewImg}
            alt={t('whatsnew.all_fields_ndvi.title') as string}
          />
        </picture>
      </div>
    </StyledPage>
  );
};

const WhatsNewAllFieldsNDVI = {
  key: FeaturesKeyEnum.allFieldsNdvi,
  component: AllFieldsNDVI,
  filter: {},
};

export default WhatsNewAllFieldsNDVI;
