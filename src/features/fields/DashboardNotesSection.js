import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';

import ModernSpinner from 'components/ui/ModernSpinner/ModernSpinner';
import DashboardNoteItem from './DashboardNoteItem';

import { getViewportHash } from 'utils/get-root-scouting-url';
import { useTranslate } from 'utils/use-translate';

import notes from 'modules/notes';
import fields from 'modules/fields';
import entities from 'modules/entities';

import { FEATURES, FeatureDisabledTooltip } from 'features/permissions';
import { PopoverDeprecatedAlign } from 'components/ui/Popover';

const DashboardNotesSection = ({ fieldId, location }) => {
  const { t } = useTranslate();
  const dispatch = useDispatch();

  const field = useSelector(state => fields.selectors.getByID(state, fieldId));
  const fieldSeason = useSelector(state =>
    entities.selectors.getByID(state, 'fieldSeason', field?.fieldUserSeasonId),
  );

  const noteIDs = useSelector(state =>
    notes.selectors.getFieldSeasonNoteIDs(state, fieldSeason?.uuid),
  );

  const { status } = useSelector(state =>
    entities.selectors.getRequestStatus(state, 'fetchNotes'),
  );

  return (
    <div className='map-dashboard__section'>
      <div className='map-dashboard-header'>
        <h2 className='map-dashboard-header__title'>
          {t('fields.dashboard.notes')}
        </h2>
        <Link
          className='map-dashboard-header__goto'
          to={`/${getViewportHash(location.pathname)}/scouting`}
        >
          {t('fields.dashboard.notes.full')}
        </Link>
      </div>
      {status === 'pending' && (
        <div className='map-dashboard-weather __loader'>
          <div className='spinner-container'>
            <ModernSpinner large />
          </div>
          <div className='map-dashboard-weather__inner'>
            {t('fields.dashboard.notes.loading')}
          </div>
        </div>
      )}
      {status === 'empty' && (
        <div className='map-dashboard-weather __empty-state'>
          <div className='map-dashboard-weather__inner'>
            {t('fields.dashboard.notes.empty')}
            <br />
            <FeatureDisabledTooltip
              feature={FEATURES.NOTE_ADD}
              direction={PopoverDeprecatedAlign.MiddleTop}
            >
              <Link
                to={`/${getViewportHash(location.pathname)}/scouting?add_note`}
                onClick={() => {
                  dispatch(fields.actions.select(fieldId));
                }}
              >
                {t('fields.dashboard.notes.add')}
              </Link>
            </FeatureDisabledTooltip>
          </div>
        </div>
      )}
      {status === 'resolved' && (
        <div className='map-dashboard-notes'>
          <div className='map-dashboard-notes__area'>
            {noteIDs.map(noteId => (
              <DashboardNoteItem key={noteId} noteId={noteId} t={t} />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default DashboardNotesSection;
