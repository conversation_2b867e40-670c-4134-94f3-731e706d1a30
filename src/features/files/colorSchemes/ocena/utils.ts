import { FileColumn, FileFullData } from 'types/filesState';

const ocenaReg = /ocena_(?<elementName>[0-9a-z]+)/;

export const isOcenaColumn = (column: FileColumn) => {
  const name = column.name.toLowerCase();

  return ocenaReg.test(name);
};

export const getOcenaParentColumn = (
  file: FileFullData,
  column: FileColumn,
): FileColumn | undefined => {
  const name = column.name.toLowerCase();
  const match = name.match(/[a-z0-9]+/);
  const elementName = match ? match.shift() : false;
  const ocenaName = `ocena_${elementName}`;

  return file.columns.find(column => column.name.toLowerCase() === ocenaName);
};
