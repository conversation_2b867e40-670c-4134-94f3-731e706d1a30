import React, { VFC } from 'react';
import { css } from 'linaria';

const style = {
  badge: css`
    padding: 4px;
    display: inline-block;
    background: #d5dbdf;
    border-radius: 4px;

    font-weight: 500;
    font-size: 12px;
    line-height: 16px;
    color: #222222;
  `,
};

export type BadgeProps = {
  text: string;
  color: 'passive';
};

export const Badge: VFC<BadgeProps> = ({ text }) => {
  return <span className={style.badge}>{text}</span>;
};
