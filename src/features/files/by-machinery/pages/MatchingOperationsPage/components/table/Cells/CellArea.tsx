import React, { VFC } from 'react';

import { FieldEntityType } from 'types/fields';

import {
  SimpleTooltip,
  SimpleTooltipArrow,
  SimpleTooltipTheme,
} from 'components/ui/Tooltip/SimpleTooltip/SimpleTooltip';
import { PopoverDeprecatedAlign } from 'components/ui/Popover';
import { ModernTooltip } from 'components/ui/Tooltip/ModernTooltip/ModernTooltip';
import { ApiDataFieldOperations } from 'features/files/by-machinery/types';
import { AreaPercentageStyle, AreaStyle, CellStyle } from './styles';
import { OperationsImage } from './OperationsImage';

export type CellOperationProps = {
  hovered: boolean;
  operations: ApiDataFieldOperations[];
  field: FieldEntityType;
};

export const CellArea: VFC<CellOperationProps> = ({ operations, field }) => {
  const area = operations
    .map(i => i?.area)
    .filter(Boolean)
    .reduce((a, b) => a + b, 0);

  return (
    <CellStyle>
      <span className='form-select__value'>
        <ModernTooltip
          active
          arrow={SimpleTooltipArrow.LEFT}
          theme={SimpleTooltipTheme.DARK}
          align={PopoverDeprecatedAlign.MiddleRight}
          offset={[20, 0]}
          // @ts-ignore
          renderTrigger={props => (
            <span {...props}>
              <AreaStyle>{area.toFixed(1)}</AreaStyle>
            </span>
          )}
          renderTooltip={() => (
            <div>
              <SimpleTooltip.Description>
                <OperationsImage operations={operations} />
              </SimpleTooltip.Description>
            </div>
          )}
        />{' '}
        ha{' '}
        <AreaPercentageStyle>
          ({((area / field.area) * 100).toFixed(0)}%)
        </AreaPercentageStyle>
      </span>
    </CellStyle>
  );
};
