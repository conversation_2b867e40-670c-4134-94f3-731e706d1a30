import { createSelector } from 'reselect';

import { TaskUploadingType } from 'features/files/by-machinery/types';
import { filters } from '../filters';
import { selectors } from './index';
import RootState from 'types/rootState';

export const getUploadingProgress = createSelector(
  (root: RootState) => selectors.getActiveTasks(root),
  tasks => {
    const uploadingTasks = tasks.filter(
      filters.isUploadingTask,
    ) as TaskUploadingType[];

    if (uploadingTasks.length === 0) {
      return null;
    }

    const progress = uploadingTasks.reduce<number>((progress, task) => {
      return progress + task.progress / uploadingTasks.length;
    }, 0);

    if (progress === 1) {
      return null;
    }

    return progress;
  },
);
