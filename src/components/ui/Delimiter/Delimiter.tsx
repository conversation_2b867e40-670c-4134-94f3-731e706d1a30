import React from 'react';
import { StyledDelimiter } from './Delimiter.style';

const MARGIN_TOP_DEFAULT = 15;
const MARGIN_BOTTOM_DEFAULT = 20;
const COLOR_DEFAULT = '#E4E7EA';

export interface DelimiterProps {
  top?: number;
  bottom?: number;
  color?: string;
}

const Delimiter = ({
  top = MARGIN_TOP_DEFAULT,
  bottom = MARGIN_BOTTOM_DEFAULT,
  color = COLOR_DEFAULT,
}) => <StyledDelimiter top={top} bottom={bottom} color={color} />;

export default Delimiter;
