import React from 'react';
import EnterExitTransition from 'components/EnterExitTransition';

import Icon from 'components/Icon';
import Button from 'components/ui/Button/Button';
import RenderAboveEverything from 'components/RenderAboveEverything';

import useHistory from 'utils/use-history';
import { useTranslate } from 'utils/use-translate';

const DeleteAccountSuccessPopup = ({ onClose }) => {
  const { t } = useTranslate();
  const history = useHistory();

  return (
    <EnterExitTransition variant='popup'>
      <RenderAboveEverything>
        <div className='modal modal-editseasons'>
          <div className='modal-outer'>
            <div className='modal-editseasons-content'>
              <div className='modal-editseasons-content__header'>
                {t('delete_account.success.title')}
              </div>
              <div className='modal-editseasons-content__body'>
                <div className='form-group'>
                  {t('delete_account.success.text')}
                </div>
              </div>
              <div className='modal-editseasons-content__actions'>
                <Button
                  className='btn btn-success btn-lg'
                  onClick={() => history.replace('/internal/logout')}
                >
                  <span className='btn__ico'>
                    <Icon className='ico-check-os' name='check' />
                  </span>
                  {t(`delete_account.success.button.start`)}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </RenderAboveEverything>
    </EnterExitTransition>
  );
};

export default DeleteAccountSuccessPopup;
