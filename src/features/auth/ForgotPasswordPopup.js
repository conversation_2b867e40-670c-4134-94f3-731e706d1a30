import React from 'react';
import { actions as formActions } from 'react-redux-form';
import { spawn, put, take } from 'redux-saga/effects';
import { useSelector, useDispatch } from 'react-redux';

import Button from 'components/ui/Button/Button';
import RenderAboveEverything from 'components/RenderAboveEverything';

import api from 'modules/api';
import auth from 'modules/auth';

import forms from 'constants/forms';
import { resolved } from 'sagas/global/api';
import useHistory from 'utils/use-history';
import useSaga from 'utils/use-saga';
import { useTranslate } from 'utils/use-translate';

const ForgotPasswordPopup = () => {
  const { t } = useTranslate();
  const email = useSelector(auth.selectors.getUserEmail);
  const dispatch = useDispatch();
  const history = useHistory();

  const submitRequest = useSelector(state =>
    api.selectors.getRequestStatus(state, 'forgot-password'),
  );

  useSaga(function* () {
    yield take(resolved(auth.actions.forgot));

    // Logout will immediately redirect and cancel this saga, so we spawn here
    yield spawn(function* () {
      yield put(auth.actions.logout());

      // Auth forgot page expects email already filled in in form, we provide it here manually
      yield put(formActions.change(`${forms.login}.login`, email));
      history.replace('/auth/reset', {
        login: email,
      });
    });
  }, []);

  return (
    <RenderAboveEverything>
      <div className='modal modal-editseasons'>
        <div className='modal-outer'>
          <div className='modal-editseasons-content'>
            <div className='modal-editseasons-content__header'>
              {t('forgot_password.title')}
            </div>
            <div className='modal-editseasons-content__body'>
              <div className='form-group'>
                {t('forgot_password.text', { email })}
              </div>
            </div>
            <div className='modal-editseasons-content__actions'>
              <Button
                className='btn btn-primary btn-lg'
                onClick={() => {
                  history.goBack();
                }}
              >
                {t('forgot_password.back')}
              </Button>
              <Button
                className='btn btn-success btn-lg'
                pending={submitRequest.status === 'pending'}
                onClick={() => {
                  dispatch(auth.actions.forgot({ login: email }));
                }}
              >
                {t('forgot_password.submit')}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </RenderAboveEverything>
  );
};

export default ForgotPasswordPopup;
