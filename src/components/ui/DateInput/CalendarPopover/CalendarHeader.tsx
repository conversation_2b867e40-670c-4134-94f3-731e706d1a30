import React, { VFC } from 'react';
import { Calendar } from 'dayzed';
import moment from 'moment';
import cx from 'classnames';

import { capitalizeFirstLetter } from 'utils/capitalizeFirstLetter';

import Button from 'components/ui/Button/Button';
import SelectInput from 'components/SelectInput';
import { MonthFormat } from '../constants';

export type CalendarHeaderProps = {
  calendar: Calendar;
  yearRange: number[];

  onChangeDate: (newDate: Date) => void;
  onChangeMonth: (newMonth: number) => void;
  onChangeYear: (newYear: number) => void;
};

export const CalendarHeader: VFC<CalendarHeaderProps> = ({
  calendar,
  onChangeDate,
  onChangeMonth,
  onChangeYear,
  yearRange,
}) => {
  return (
    <div className='modal-calendar-header'>
      <div className='modal-calendar-header__inner'>
        <SelectInput
          options={moment.months(MonthFormat).map((item, i) => {
            return {
              id: i,
              name: item,
            };
          })}
          renderTrigger={({ ref, getToggleButtonProps, disabled, isOpen }) => (
            <Button
              ref={ref}
              className={cx('modal-calendar-header__item', {
                'form-select--active': isOpen,
              })}
              {...getToggleButtonProps({ disabled })}
            >
              {moment.months(MonthFormat)[calendar.month]}
            </Button>
          )}
          value={calendar.month}
          renderValue={({ value }) =>
            value && capitalizeFirstLetter(value.name)
          }
          onChange={id => {
            if (typeof id === 'number') {
              onChangeDate(new Date(calendar.year, id));
              onChangeMonth(id + 1);
            }
          }}
        />
        <SelectInput
          options={yearRange.map((item, i) => {
            return {
              id: i,
              name: item,
            };
          })}
          renderTrigger={({ ref, getToggleButtonProps, isOpen }) => (
            <Button
              ref={ref}
              className={cx('modal-calendar-header__item', {
                'form-select--active': isOpen,
              })}
              {...getToggleButtonProps()}
            >
              {calendar.year}
            </Button>
          )}
          value={yearRange.indexOf(calendar.year)}
          renderValue={result => result?.value?.name}
          onChange={id => {
            const year = yearRange[id];

            if (year) {
              onChangeDate(new Date(year, calendar.month));
              onChangeYear(year);
            }
          }}
        />
      </div>
    </div>
  );
};
