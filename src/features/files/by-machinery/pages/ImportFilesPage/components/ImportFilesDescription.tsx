import React from 'react';
import { Trans } from 'react-i18next';

import { Brands } from './Brands';
import { useTranslate } from 'utils/use-translate';
import { HintLink } from './styles';
import { ModernTooltip } from 'components/ui/Tooltip/ModernTooltip/ModernTooltip';
import {
  SimpleTooltip,
  SimpleTooltipArrow,
  SimpleTooltipTheme,
} from 'components/ui/Tooltip/SimpleTooltip/SimpleTooltip';
import { PopoverDeprecatedAlign } from 'components/ui/Popover';
import { ImportTaskDescription } from '../../styles';

const ImportFilesDescription = ({ dataType }: { dataType: string }) => {
  const { t } = useTranslate();
  const hint1 = (
    <ModernTooltip
      active
      arrow={SimpleTooltipArrow.TOP}
      theme={SimpleTooltipTheme.DARK}
      align={PopoverDeprecatedAlign.BottomMiddle}
      offset={[0, 10]}
      // @ts-ignore
      renderTrigger={props => (
        <HintLink {...props}>
          {t('import.by-machinery.import.hint1.link')}
        </HintLink>
      )}
      renderTooltip={() => (
        <div>
          <SimpleTooltip.Title>
            {t('import.by-machinery.import.hint1.title')}
          </SimpleTooltip.Title>
          <SimpleTooltip.Description>
            <strong>.shp, .gpkg, .geojson</strong>
          </SimpleTooltip.Description>
        </div>
      )}
    />
  );

  const hint2 = (
    <ModernTooltip
      active
      arrow={SimpleTooltipArrow.TOP}
      theme={SimpleTooltipTheme.DARK}
      align={PopoverDeprecatedAlign.BottomMiddle}
      offset={[0, 10]}
      maxWidth={250}
      // @ts-ignore
      renderTrigger={props => (
        <HintLink {...props}>
          {t('import.by-machinery.import.hint2.link')}
        </HintLink>
      )}
      renderTooltip={() => (
        <div>
          <SimpleTooltip.Title>
            {t('import.by-machinery.import.hint2.title')}
          </SimpleTooltip.Title>
          <Brands />
        </div>
      )}
    />
  );

  return (
    <ImportTaskDescription>
      <Trans
        i18nKey={`import.by-machinery.import.${dataType}.description`}
        components={[hint1, hint2]}
      />
    </ImportTaskDescription>
  );
};

export default ImportFilesDescription;
