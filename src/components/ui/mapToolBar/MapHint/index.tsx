import React, { FC, ReactElement, useEffect, useRef, useState } from 'react';
import cx from 'classnames';

import {
  PopoverDeprecated,
  PopoverDeprecatedAlign,
  PopoverDeprecatedProps,
} from 'components/ui/Popover';

export type MapDropdownProps = {
  align?: PopoverDeprecatedProps['align'];
  offset?: PopoverDeprecatedProps['offset'];
  active?: boolean;

  renderTrigger: PopoverDeprecatedProps['renderTrigger'];
  popoverContent: ReactElement;
};

const CloseDelay = 50;
const delay = 5000;

const MapHint: FC<MapDropdownProps> = props => {
  const {
    renderTrigger,
    popoverContent,
    align = PopoverDeprecatedAlign.MiddleRight,
    active: forcedActive,
  } = props;
  let { offset } = props;
  if (offset === undefined) {
    const alignParts = align.split('-');
    const isVertical = alignParts[0] === 'middle';

    offset = isVertical ? [10, 0] : [0, 10];
  }

  const timer = useRef<TEMPORARY_ANY>(null);
  const [active, setActive] = useState(Boolean(forcedActive));

  useEffect(() => {
    if (!forcedActive) {
      return setActive(false);
    }

    setActive(true);
    const timer = setTimeout(() => {
      setActive(false);
    }, delay);

    return () => clearTimeout(timer);
  }, [forcedActive]);

  const onMouseEnter = () => {
    clearTimeout(timer.current);
    setActive(true);
  };
  const onMouseLeave = () => {
    clearTimeout(timer.current);
    timer.current = setTimeout(setActive, CloseDelay, false);
  };

  return (
    <PopoverDeprecated
      active={active}
      offset={offset}
      align={align}
      transitionVariant='legacy'
      renderTrigger={props =>
        renderTrigger({
          ...props,
          onMouseEnter,
          onMouseLeave,
        })
      }
      renderPopover={({ style, ...props }) => (
        <div
          className={cx('simple-tooltip', {
            '__arrow-top': align === 'bottom-middle',
            '__arrow-left': !align,
          })}
          data-theme='dark'
          style={{ maxWidth: 338, ...style }}
          {...props}
          onMouseEnter={onMouseEnter}
          onMouseLeave={onMouseLeave}
          onClick={event => {
            event.stopPropagation();
          }}
        >
          <div className='simple-tooltip__body'>
            <div className='simple-tooltip__content'>{popoverContent}</div>
          </div>
        </div>
      )}
    />
  );
};

export default MapHint;
