import { FileType, FileFullData } from 'types/filesState';

import { isSoilAnalysisColorScheme } from '../colorSchemes/soilAnalysis/checker';
import { isOcenaColorScheme } from '../colorSchemes/ocena/checker';

function guard(arg: unknown): arg is object {
  return arg !== undefined;
}

export const isFileWithContent = (
  file?: FileFullData | FileType,
): file is FileFullData => {
  return guard(file) && 'columns' in file;
};

export const isFileSoilAnalysis = (file: FileFullData) => {
  return file.columns.some(column => {
    const dataSource = {
      column,
      file,
    };
    return (
      isSoilAnalysisColorScheme(dataSource) || isOcenaColorScheme(dataSource)
    );
  });
};
