import { ReactElement, useCallback, VFC } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getField, actions as formActions } from 'react-redux-form';

export type SubmitButtonProps = {
  model: string;
  native?: boolean;
  disabled?: boolean;
  neverDisable?: boolean;
  render: (config: {
    pending: boolean;
    disabled: boolean;
    onClick?: () => void;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  }) => ReactElement<any, any> | null;
};

const SubmitButton: VFC<SubmitButtonProps> = ({
  render,
  native,
  neverDisable,
  disabled,
  model,
}) => {
  const dispatch = useDispatch();
  // @ts-ignore
  const field = useSelector(state => getField(state, model) || {});

  const onClick = useCallback(
    () => dispatch(formActions.submit(model)),
    [model, dispatch],
  );
  const disabledState = disabled || field.pending || !field.valid;

  return render({
    pending: field.pending as boolean,
    disabled: !neverDisable && disabledState,
    onClick: native ? undefined : onClick,
  });
};

export default SubmitButton;
