import React from 'react';

import config from 'config';

type LogoProps = {
  href?: string;
};

function Logo({ href = '/' }: LogoProps) {
  const data = {
    className: config.whiteLabel?.logoClassName || 'logo',
    title: config.whiteLabel?.title || 'OneSoil',
  };

  return (
    /* eslint-disable-next-line jsx-a11y/anchor-has-content */
    <a className={data.className} title={data.title} href={href} />
  );
}

export default Logo;
