import React, { Component } from 'react';
import { eventChannel, buffers } from 'redux-saga';

import { SagaContext } from './SagaProvider';
import { SagaMiddleware, Saga as SagaType } from '@redux-saga/core';
import { Task } from '@redux-saga/types';

type SagaProps = {
  saga: SagaType<TEMPORARY_ANY[]>,
  middleware: SagaMiddleware,
  persist: boolean,
}

type EmitterType = (input: unknown) => void;

class Saga extends Component<SagaProps> {
  runningSaga: Task | null = null;
  propsEmitter: EmitterType | null = null;

  componentDidMount() {
    const { saga, middleware, ...sagaProps } = this.props;
    this.runningSaga = middleware.run(
      saga,
      this,
      eventChannel(emitter => {
        this.propsEmitter = emitter;
        emitter(sagaProps);
        return () => {};
      }, buffers.dropping(1)),
    );
  }

  componentDidUpdate() {
    if (this.propsEmitter) {
      this.propsEmitter(this.props);
    }
  }

  componentWillUnmount() {
    const { persist } = this.props;
    if (!persist && this.runningSaga) {
      this.runningSaga.cancel();
      this.runningSaga = null;
    }
  }

  render() {
    return null;
  }
}

const SagaWrapper = (props: TEMPORARY_ANY) => (
  <SagaContext.Consumer>
    {(middleware: SagaMiddleware) => <Saga middleware={middleware} {...props} />}
  </SagaContext.Consumer>
);

export default SagaWrapper;
