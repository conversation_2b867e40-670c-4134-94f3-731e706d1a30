import React from 'react';
import { connect } from 'react-redux';
import { Redirect } from 'react-router-dom';
import { Form, getModel, actions as formActions } from 'react-redux-form';
import cx from 'classnames';

import Icon from 'components/Icon';
import Button from 'components/ui/Button/Button';
import SubmitButton from 'components/forms/SubmitButton';
import FileListItem from '../FileListItem';
import ModernSpinner from 'components/ui/ModernSpinner/ModernSpinner';

import files from 'modules/files';

import forms from 'constants/forms';
import getRootScoutingUrl from 'utils/get-root-scouting-url';
import { useTranslate } from 'utils/use-translate';

const UploadFilesSideBar = ({
  status,
  uploadProgress,
  files,
  fileData,
  selectedFileId,
  basePath,
  history,
  location: { pathname, state = {} },
  onSubmit,
  onRemoveFile,
}) => {
  const { t } = useTranslate();
  const baseUrl = getRootScoutingUrl(pathname);
  return (
    <Form
      className='soil-sidebar-create'
      model={forms.addFiles}
      onSubmit={form => {
        const { files } = form;
        onSubmit(
          files.map(file => file.id),
          files.map(file => ({
            file_id: fileData[file.id].original_id,
            properties: fileData[file.id].properties,
            field_user_id: fileData[file.id].field_user_id,
            type: file.type || 'other',
          })),
        );
      }}
    >
      <div className='soil-sidebar-header'>
        <Button
          className='app-nav-back'
          onClick={() => {
            history.replace(baseUrl);
          }}
        >
          <Icon className='ico-arrow-long-left-os' name='arrow-left' />
        </Button>
        <div className='soil-sidebar-header__inner'>
          <h1 className='soil-sidebar-header__main'>
            {t('files.upload.title')}
          </h1>
        </div>
      </div>
      <div
        className={cx('soil-sidebar-body', '__scroll', {
          '__empty-state': files.length === 0,
        })}
      >
        {status.state === 'pending' && (
          <div className='soil-sidebar-empty'>
            <div className='soil-sidebar-empty__ico with_loader'>
              <ModernSpinner large />
            </div>
            <h2 className='soil-sidebar-empty__title'>
              {uploadProgress
                ? t('files.upload_pending.title_progress', {
                    progress: `${(uploadProgress * 100).toFixed()}%`,
                  })
                : t('files.upload_pending.title')}
            </h2>
            <p>{t('files.upload_pending.description')}</p>
          </div>
        )}
        {status.state === 'rejected' && (
          <div className='soil-sidebar-empty'>
            {!status.errorCode && (
              <Redirect to={getRootScoutingUrl(pathname)} />
            )}
            <div className='soil-sidebar-empty__ico'>
              <Icon name='warning' />
            </div>
            <h2 className='soil-sidebar-empty__title'>
              {t(`files.upload_error.${status.errorCode}.title`)}
            </h2>
            <p>
              {t(
                `files.upload_error.${status.errorCode}.description`,
                status.errorMeta,
              )}
            </p>
            <p>
              <Button
                className='btn btn-success'
                onClick={() => {
                  history.replace(baseUrl);
                }}
              >
                {t('files.error.try_again')}
              </Button>
            </p>
          </div>
        )}
        {status.state === 'resolved' && (
          <ul className='soil-fields-list' data-type='files'>
            {files.map((file, index) => (
              <FileListItem
                key={file.id}
                fileId={file.id}
                history={history}
                formModel={`.files[${index}]`}
                selected={selectedFileId === file.id}
                onClick={() => {
                  history.replace(`${basePath}/upload/${file.id}`);
                }}
                onRemove={() => {
                  if (files.length === 1) {
                    // The last file removed, get out of here
                    history.replace(basePath);
                  }
                  onRemoveFile(index);
                }}
              />
            ))}
          </ul>
        )}
      </div>
      {status.state !== 'rejected' && (
        <div className='soil-sidebar-addnew'>
          <ul className='soil-sidebar-addnew__list'>
            <li className='soil-sidebar-addnew__list-item'>
              <Button
                className='btn btn-lg btn-primary'
                onClick={() => {
                  history.replace(baseUrl);
                }}
              >
                {t('forms.cancel')}
              </Button>
            </li>
            <li className='soil-sidebar-addnew__list-item'>
              <SubmitButton
                model={forms.addFiles}
                neverDisable
                native
                render={props => (
                  <Button
                    className='btn size-full btn-lg btn-success __no-visual-disable'
                    disabled={files.length === 0}
                    type='submit'
                    {...props}
                    pending={status.state === 'pending'}
                  >
                    <span className='btn__ico'>
                      <Icon className='ico-check-os' name='check' />
                    </span>
                    {t('forms.save')}
                  </Button>
                )}
              />
            </li>
          </ul>
        </div>
      )}
    </Form>
  );
};

const mapStateToProps = state => ({
  files: getModel(state, `${forms.addFiles}.files`),
  status: files.selectors.getUploadStatus(state),
  uploadProgress: files.selectors.getUploadProgress(state),
  fileData: files.selectors.getData(state),
});

const mapDispatchToProps = {
  onSubmit: files.actions.createBatch,
  onRemoveFile: index => formActions.remove(`${forms.addFiles}.files`, index),
};

export default connect(mapStateToProps, mapDispatchToProps)(UploadFilesSideBar);
