import React, { VFC } from 'react';
import { Link, matchPath, useRouteMatch, useLocation } from 'react-router-dom';
import { useTranslate } from 'utils/use-translate';
import { parseQuery, stringifyQuery } from 'utils/query';
import {
  NawProgressListItemStyle,
  NawProgressListStyle,
  NawProgressStyle,
} from './NavProgress.style';

type Route = {
  to: string;
  title: string;
};

export enum NavProgressLinkMode {
  disabled = 'disabled',
  enabled = 'enabled',
  enabledBeforeActiveElement = 'enabledBeforeActiveElement',
}

type NavProgressProps = {
  routes: readonly Route[];
  linkMode?: NavProgressLinkMode;
};

export const NavProgress: VFC<NavProgressProps> = props => {
  const { routes, linkMode = NavProgressLinkMode.disabled } = props;
  const { t } = useTranslate();
  const match = useRouteMatch();
  const location = useLocation();

  const query = parseQuery(location);
  const strQuery = stringifyQuery(query);

  const indexOfActiveElement = routes.findIndex(route => {
    return matchPath(window.location.pathname, {
      path: `${match.url}${route.to}`,
      exact: true,
    });
  });

  return (
    <NawProgressStyle>
      <NawProgressListStyle>
        {routes.map((route, index) => {
          const title = t(route.title);

          let isLink = false;
          if (linkMode === NavProgressLinkMode.enabled) {
            isLink = true;
          }

          if (linkMode === NavProgressLinkMode.enabledBeforeActiveElement) {
            if (indexOfActiveElement > index) {
              isLink = true;
            }
          }

          return (
            <NawProgressListItemStyle
              key={route.to}
              data-status={indexOfActiveElement === index ? 'active' : null}
            >
              {isLink ? (
                <Link
                  to={
                    match.url +
                    (route.to !== '/' ? route.to : '') +
                    `?${strQuery}`
                  }
                >
                  {title}
                </Link>
              ) : (
                title
              )}
            </NawProgressListItemStyle>
          );
        })}
      </NawProgressListStyle>
    </NawProgressStyle>
  );
};
