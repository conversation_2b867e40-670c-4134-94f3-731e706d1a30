import React, { ChangeEventHandler } from 'react';

import {
  StyledEmail,
  StyledInput,
  StyledInputEl,
  StyledLabel,
  StyledType,
  StyledValueWrapper,
} from './RadioButton.styles';

type RadioButtonProps = {
  checked: boolean;
  colorClassName?: string;
  disabled?: boolean;
  email: string;
  name: string;
  onChange: ChangeEventHandler<HTMLInputElement>;
  type: string | null;
  value: string;
};

const RadioButton = ({
  checked,
  email,
  type,
  colorClassName,
  disabled,
  name,
  onChange,
  value,
}: RadioButtonProps) => (
  <StyledLabel data-legend={colorClassName} checked={checked}>
    <StyledValueWrapper>
      <StyledType>{type}</StyledType>
      <StyledEmail>{email}</StyledEmail>
    </StyledValueWrapper>
    <StyledInput
      type='radio'
      checked={checked}
      disabled={disabled}
      value={value}
      name={name}
      onChange={onChange}
    />
    <StyledInputEl />
  </StyledLabel>
);

export default RadioButton;
