import { ApiDataFieldOperations } from '../types';
import moment from 'moment';
import { DateFormatFunc } from 'utils/dates/dateFormatFactory';

type ArrayOneOrMore<T> = {
  0: T;
} & Array<T>;

export const getOperationsRange = (
  operations: ApiDataFieldOperations[],
  dateFormat: DateFormatFunc,
) => {
  const dates: moment.Moment[] = operations
    .map(operation => [
      operation.start_time && moment(operation.start_time),
      operation.end_time && moment(operation.end_time),
    ])
    .flat(1)
    .filter(Boolean) as moment.Moment[];

  const sortedDates: ArrayOneOrMore<moment.Moment> = [...dates].sort((a, b) =>
    a.isAfter(b) ? 1 : -1,
  ) as ArrayOneOrMore<moment.Moment>;

  const firstDate: moment.Moment = sortedDates[0];
  const lastDate: moment.Moment = sortedDates[
    sortedDates.length - 1
  ] as moment.Moment;

  const range: string[] = [];
  if (firstDate) {
    range.push(dateFormat(firstDate));

    if (dateFormat(lastDate) !== dateFormat(firstDate)) {
      range.push(dateFormat(lastDate));
    }
  }

  return range;
};
