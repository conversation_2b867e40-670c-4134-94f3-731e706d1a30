import React, { VFC, useEffect, useMemo } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Controller, useForm, useWatch } from 'react-hook-form';
import { orderBy, debounce } from 'lodash';

import Modal from 'components/ui/Modal/Modal';
import Button from 'components/ui/Button/Button';
import Icon from 'components/Icon';
import { CustomDropdownOption } from 'components/ui/CustomDropdown/types';
// @ts-ignore
import Toggle from 'components/Toggle';
import { SelectBox } from 'components/ui/CustomDropdown/SelectBox';
import { useActiveDataLayerPropertiesLoader } from 'features/files/by-machinery/dataLoaders';

import { useTranslate } from 'utils/use-translate';
import { useSingletonSelector } from 'utils/optimization/useSingletonSelector';

import entitiesModule from 'modules/entities';
import api from 'modules/api';
import { actions } from 'features/files/by-machinery/redux/actions';

import {
  MatchFieldsInfoSection,
  MatchFieldsInfoSectionText,
  selectBoxWrapper,
  IconInfo,
  Overlay,
  Fields,
} from '../styles';
import {
  ApiDataFieldLayer,
  MetaAttributesApi,
} from 'features/files/by-machinery/types';
import { deepEqualDecorator } from 'utils/optimization/deepEqualDecorator';

import { fieldsSelectors } from 'modules/fields/selectors';
import importTaskModule from 'features/files/by-machinery/redux';
import { useErrorMessage } from '../hooks/useErrorMessage';
import ErrorLineComponent from './ErrorLineComponent';
import { Trans } from 'react-i18next';

type MatchFieldsModalProps = {
  uuid: string | null;
  isVisible: boolean;
  onClose: () => void;
};

type MatchFieldsForm = {
  type: 'fields' | 'field';
  fieldNameId?: string;
  fieldId?: string;
  pointId: string;
};

export const REQUEST_ID = 'match-fields';

const optionsCalculation = deepEqualDecorator(
  ([properties]: [
    ApiDataFieldLayer['properties'],
    MetaAttributesApi['allowed_types'],
  ]) =>
    Object.values(properties)
      .map<CustomDropdownOption>(property => ({
        id: property.uuid,
        label: property.name,
        subLabelItems: (property.sample_values || []).filter(
          s => typeof s === 'string',
        ) as string[],
      }))
      .sort((a, b) => a.id?.localeCompare(b.id)),
);

export const MatchFieldsModal: VFC<MatchFieldsModalProps> = ({
  uuid,
  isVisible,
  onClose,
}) => {
  const { t } = useTranslate();
  const dispatch = useDispatch();
  const [properties] = useActiveDataLayerPropertiesLoader(uuid!);
  const [isOpened, setIsOpened] = React.useState(false);
  const [showSpinner, setShowSpinner] = React.useState(false);
  const [showErrors, setShowErrors] = React.useState(false);

  const link = (
    /* eslint-disable-next-line jsx-a11y/anchor-has-content */
    <a
      target='_blank'
      rel='noreferrer'
      href={t('import.by-machinery.fields-match-modal.info.link')}
    />
  );

  const layer = useSelector(state =>
    entitiesModule.selectors.getByID(state, 'fieldsDataLayer', uuid),
  );

  const layerMatchingTask = useSelector(state =>
    importTaskModule.selectors.getLayerMatchingTask(state, uuid),
  );
  const spatialMatchingRequest = useSelector(state =>
    api.selectors.getRequestStatus(state, `spatial-matching`),
  );

  const allFields = useSelector(state => fieldsSelectors.getAllFields(state));

  const options: CustomDropdownOption[] = useSingletonSelector(
    optionsCalculation,
    [properties],
  );

  const hideSpinnerDebounced = debounce(() => setShowSpinner(false), 1000);

  useEffect(() => {
    const isAnyRequestActive =
      spatialMatchingRequest?.status === 'pending' ||
      layerMatchingTask?.status === 'processing';

    if (isAnyRequestActive) {
      setShowSpinner(true);
      setShowErrors(true);
      hideSpinnerDebounced.cancel();
    } else if (!isAnyRequestActive) {
      hideSpinnerDebounced();
    }
  }, [spatialMatchingRequest, layerMatchingTask, hideSpinnerDebounced]);

  const sortedFields = useMemo(() => {
    return orderBy(allFields, 'title', 'asc');
  }, [allFields]);

  const { control, getValues, handleSubmit, reset, setValue } = useForm({
    defaultValues: {
      type: layer?.spatial_matching_params?.field_user_id ? 'field' : 'fields',
      fieldNameId: layer?.spatial_matching_params?.field_name_prop_id || '',
      fieldId: layer?.spatial_matching_params?.field_user_id || '',
      pointId: layer?.spatial_matching_params?.sampling_point_prop_id || '',
    },
    mode: 'onChange',
  });

  const type = useWatch({ control, name: 'type' });
  const fieldNameId = useWatch({ control, name: 'fieldNameId' });
  const fieldId = useWatch({ control, name: 'fieldId' });
  const pointId = useWatch({ control, name: 'pointId' });

  useEffect(() => {
    const { field_name_prop_id, field_user_id, sampling_point_prop_id } =
      layer?.spatial_matching_params || {};

    if (sampling_point_prop_id && (field_name_prop_id || field_user_id)) {
      const initialState = {
        ...(field_name_prop_id && { fieldNameId: field_name_prop_id }),
        ...(field_user_id && { fieldId: field_user_id + '' }),
        pointId: sampling_point_prop_id,
        type: field_name_prop_id ? 'fields' : 'field',
      };

      reset(initialState);
    }
  }, [layer, reset]);

  const isDisabled =
    (type === 'fields' && (!fieldNameId || !pointId)) ||
    (type === 'field' && (!fieldId || !pointId));

  useEffect(() => {
    const shouldCloseModal =
      spatialMatchingRequest.status === 'resolved' &&
      layerMatchingTask?.status === 'finished' &&
      layerMatchingTask?.is_spatial_matched;

    if (shouldCloseModal && isOpened) {
      onClose();
      dispatch(entitiesModule.actions.clearRequest(`spatial-matching`));
      setIsOpened(false);
    }
  }, [
    spatialMatchingRequest.status,
    layerMatchingTask,
    isOpened,
    onClose,
    dispatch,
  ]);

  const getFieldErrorText = useErrorMessage('fieldError', layerMatchingTask, t);
  const getPointErrorText = useErrorMessage('pointError', layerMatchingTask, t);

  const onSubmit = (form: MatchFieldsForm) => {
    const { fieldId, fieldNameId, pointId } = form;

    const data = {
      uuid,
      sampling_point_prop_id: pointId,
      [type === 'fields' ? 'field_name_prop_id' : 'field_user_id']:
        type === 'fields' ? fieldNameId : +fieldId!,
    };

    // @ts-ignore
    dispatch(actions.spatialMatching(data));
    setIsOpened(true);
  };

  if (!uuid || !type) {
    return null;
  }

  const showFailedError =
    !getPointErrorText &&
    !getFieldErrorText &&
    (layerMatchingTask.status === 'failed' ||
      !layerMatchingTask.is_spatial_matched) &&
    showErrors &&
    !showSpinner;

  return (
    <Modal
      show={isVisible}
      onClose={() => {
        onClose();
      }}
      width={520}
    >
      <Modal.Head>
        {t(`import.by-machinery.fields-match-modal.${type}.title`)}
      </Modal.Head>
      <Modal.Body>
        {t('import.by-machinery.fields-match-modal.text')}
        <MatchFieldsInfoSection>
          <Icon className={IconInfo} name='info-circle' />
          <MatchFieldsInfoSectionText>
            <Trans components={[link]}>
              {'import.by-machinery.fields-match-modal.info.withlink'}
            </Trans>
          </MatchFieldsInfoSectionText>
        </MatchFieldsInfoSection>
        <Fields>
          <div className='form-group'>
            <Controller
              name='type'
              control={control}
              render={({ onChange, value }, { invalid }) => (
                <Toggle
                  value={value}
                  titleKey='label'
                  options={['fields', 'field'].map(type => ({
                    label: t(`import.by-machinery.fields-match-modal.${type}`),
                    id: type,
                  }))}
                  onChange={(v: string) => {
                    setShowErrors(false);
                    onChange(v);
                  }}
                />
              )}
            />
          </div>
          <div className='form-group'>
            <div className='form-label'>
              {type === 'fields'
                ? t('import.by-machinery.fields-match-modal.label-field-name')
                : t('import.by-machinery.fields-match-modal.label-field')}
            </div>

            <Controller
              name='fieldNameId'
              control={control}
              rules={{
                required: type === 'fields',
              }}
              render={({ value, onChange }) => {
                return type === 'fields' ? (
                  <SelectBox
                    options={options}
                    value={value}
                    wrapperClassName={selectBoxWrapper}
                    onChange={value => {
                      if (type === 'fields') {
                        const { pointId } = getValues();
                        if (value === pointId) {
                          setValue('pointId', '', { shouldValidate: true });
                        }
                      }
                      setShowErrors(false);
                      onChange(value);
                    }}
                    noFoundedText='import.matching.attributes.select.empty'
                    withSearchBox
                    placeholder='import.matching.attributes.select'
                    searchPlaceholder='import.matching.attributes.select_search'
                  />
                ) : (
                  <div />
                );
              }}
            />

            <Controller
              name='fieldId'
              control={control}
              rules={{
                required: type === 'field',
              }}
              render={({ value, onChange }) =>
                type === 'field' ? (
                  <SelectBox
                    options={sortedFields.map(field => ({
                      id: field.id + '',
                      label: field.title,
                    }))}
                    value={value}
                    wrapperClassName={selectBoxWrapper}
                    onChange={value => {
                      setShowErrors(false);
                      onChange(value);
                    }}
                    noFoundedText='import.matching.attributes.select.empty'
                    withSearchBox
                    placeholder='import.matching.attributes.select'
                    searchPlaceholder='import.matching.attributes.select_search'
                  />
                ) : (
                  <div />
                )
              }
            />

            {!showSpinner && showErrors && getFieldErrorText}
          </div>
          <div className='form-group'>
            <div className='form-label'>
              {t('import.by-machinery.fields-match-modal.label-point')}
            </div>
            <Controller
              name='pointId'
              control={control}
              rules={{ required: true, validate: value => value.trim() !== '' }}
              render={({ value, onChange }) => (
                <SelectBox
                  options={options}
                  value={value}
                  wrapperClassName={selectBoxWrapper}
                  onChange={value => {
                    if (type === 'fields') {
                      const { fieldNameId } = getValues();
                      if (value === fieldNameId) {
                        setValue('fieldNameId', '', { shouldValidate: true });
                      }
                    }
                    setShowErrors(false);
                    onChange(value);
                  }}
                  noFoundedText='import.matching.attributes.select.empty'
                  withSearchBox
                  placeholder='import.matching.attributes.select'
                  searchPlaceholder='import.matching.attributes.select_search'
                />
              )}
            />
            {!showSpinner && showErrors && getPointErrorText}
            {showFailedError && (
              <ErrorLineComponent
                icon='modal-error'
                message={t(
                  'import.by-machinery.fields-match-modal.error.failed',
                )}
              />
            )}
          </div>
          {showSpinner && <Overlay />}
        </Fields>
      </Modal.Body>
      <Modal.Actions>
        <Button
          className='btn btn-primary btn-lg'
          onClick={() => {
            onClose();
          }}
        >
          {t('import.by-machinery.fields-match-modal.cancel')}
        </Button>
        <Button
          className='btn btn-success btn-lg'
          type='submit'
          disabled={isDisabled}
          pending={showSpinner}
          onClick={handleSubmit(onSubmit)}
        >
          <span className='btn__ico'>
            <Icon className='ico-check-os' name='check' />
          </span>
          {t('import.by-machinery.fields-match-modal.save')}
        </Button>
      </Modal.Actions>
    </Modal>
  );
};
