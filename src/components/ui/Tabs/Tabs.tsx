import React, { createContext, FC, ReactElement, useContext } from 'react';
import cx from 'classnames';

import {
  TabListProps,
  TabPanelProps,
  TabProps,
  TabsContextProps,
  TabsProps,
} from './types';
import classNames from './Tabs.module.css';

export const TabsContext = createContext<TabsContextProps>({
  activeIndex: 0,
  setActiveIndex: () => {},
});

export const TabList = ({ children, customStyleClassName }: TabListProps) => {
  if (!children) return null;
  return (
    <ul className={cx(classNames.list, customStyleClassName)}>
      {React.Children.map(children, (child: ReactElement, index: number) =>
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        React.cloneElement(child as React.ReactElement<any>, {
          tabIndex: index,
        }),
      )}
    </ul>
  );
};

export const Tabs = ({
  children,
  activeIndex,
  setActiveIndex,
  customStyleClassName,
}: TabsProps) => {
  return (
    <TabsContext.Provider value={{ activeIndex, setActiveIndex }}>
      <div className={cx(classNames.tabs, customStyleClassName)}>
        {children}
      </div>
    </TabsContext.Provider>
  );
};

export const Tab = ({
  children,
  baseClassName,
  activeClassName,
  onClick,
  tabIndex,
  disabled,
  customStyleClassName,
}: TabProps) => {
  const tabContext = useContext<TabsContextProps>(TabsContext);
  const isActive = tabIndex === tabContext.activeIndex;
  return (
    <li className={cx(classNames.item, customStyleClassName)}>
      <button
        disabled={disabled}
        onClick={
          isActive
            ? undefined
            : () => {
                onClick && onClick(tabIndex);
                tabContext.setActiveIndex(tabIndex!);
              }
        }
        className={cx(
          baseClassName || classNames.button,
          isActive && (activeClassName || classNames.button__active),
          disabled && classNames.button__disabled,
        )}
      >
        {children}
      </button>
    </li>
  );
};

export const TabPanelList: FC = ({ children }) => {
  if (children)
    return (
      <>
        {React.Children.map(children, (child, index) =>
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          React.cloneElement(child as React.ReactElement<any>, {
            panelIndex: index,
          }),
        )}
      </>
    );
  return null;
};

export const TabPanel = ({
  children,
  panelIndex,
  customStyleClassName,
}: TabPanelProps) => {
  const { activeIndex } = useContext<TabsContextProps>(TabsContext);

  if (activeIndex === panelIndex)
    return (
      <div className={cx(classNames.panel, customStyleClassName)}>
        {children}
      </div>
    );
  return null;
};
