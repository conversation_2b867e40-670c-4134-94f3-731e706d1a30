import React, { Component } from 'react';
import { createSelector } from 'reselect';
import { connect } from 'react-redux';
import { Map } from 'react-leaflet';
import Measure from 'react-measure';
import bboxPolygon from '@turf/bbox-polygon';
import debounce from 'lodash/debounce';
import identity from 'lodash/identity';

import 'leaflet/dist/leaflet.css';
import '@geoman-io/leaflet-geoman-free';
import 'assets/styles/leaflet.pm.css';
import 'leaflet-editable';

import map from 'modules/map';
import auth from 'modules/auth';
import seasons from 'modules/seasons';
import 'utils/fix-leaflet-blurriness';
import screenshot from 'utils/leaflet-screenshot';
import { MaxZoom } from 'features/platform/ZoomControls/ZoomControls';

import logEvent from 'sagas/global/logEvent';

const EuropeViewport = {
  center: [49.89, 22.76],
  zoom: 4,
};

const CameraIdleDebounce = 100;
const ResizeDebounce = 250;

const viewportToHash = viewport =>
  `@${viewport.center.map(i => i.toFixed(4)).join(',')},${viewport.zoom}z`;

const hashToViewport = hash => {
  if (!hash.startsWith('@') || !hash.endsWith('z')) {
    return EuropeViewport;
  }
  const parts = hash.slice(1, -1).split(',');
  if (parts.length !== 3 || parts.some(isNaN)) {
    return EuropeViewport;
  }
  return {
    center: parts.slice(0, 2).map(Number),
    zoom: +parts[2],
  };
};

class ControlledMap extends Component {
  state = {
    viewport: hashToViewport(this.props.viewportHash),
  };

  componentDidMount() {
    const { disableEvents, onCameraIdle, onViewportChanged } = this.props;
    if (!disableEvents) {
      const geojson = this.getBoundsGeojson();
      onViewportChanged(geojson);
      onCameraIdle(geojson);
    }
  }

  componentDidUpdate(prevProps) {
    if (prevProps.intents !== this.props.intents) {
      this.handleIntents();
    }
  }

  handleIntents() {
    const { intents, onClearIntents } = this.props;
    if (!this.map || intents.length === 0 || !this.sizeStable) {
      return;
    }

    for (const intent of intents) {
      switch (intent.type) {
        case 'zoom-in':
          this.map.zoomIn();
          break;
        case 'zoom-out':
          this.map.zoomOut();
          break;
        case 'set-view':
          this.map.setView(intent.options.coords, intent.options.zoom);
          break;
        case 'fly-to':
          this.map.flyTo(intent.options.coords, intent.options.zoom);
          break;
        case 'fit-bounds':
          this.map.fitBounds(intent.options.bounds);
          break;
        case 'fly-to-bounds':
          this.map.flyToBounds(intent.options.bounds);
          break;
        case 'invalidate-size':
          this.map.invalidateSize(true);
          break;
        case 'make-screenshot':
          screenshot(this.map, (err, canvas) => {
            let img = document.createElement('img');
            let dimensions = this.map.getSize();
            img.width = dimensions.x;
            img.height = dimensions.y;
            img.src = canvas.toDataURL();
            document.body.appendChild(img);
          });
          break;
        default:
          console.warn('Unknown intent type', intent);
          break;
      }
    }
    const { id } = this.props;
    onClearIntents(id);
  }

  handleViewportChange = debounce(() => {
    const { disableEvents, onCameraIdle } = this.props;
    if (!this.map || disableEvents) {
      return;
    }
    onCameraIdle(this.getBoundsGeojson());
  }, CameraIdleDebounce);

  handleViewportChanged = viewport => {
    if (!viewport.center || !viewport.zoom) {
      // Viewport may not have been populated yet, ignore this event
      return;
    }
    let boundsGeojson = this.getBoundsGeojson();

    const { onViewportChanged, onViewportHashChange } = this.props;
    onViewportChanged(boundsGeojson);
    onViewportHashChange(viewportToHash(viewport));

    logEvent('map_move', {
      bbox: boundsGeojson.bbox,
      zoom_level: viewport.zoom,
      user_id: this.props?.userId ?? null,
      season_id: this.props?.currentSeason?.id ?? null,
    });
  };

  handleResize = debounce(() => {
    if (!this.map) {
      return;
    }
    try {
      // This sometimes throws on map removal
      this.map.invalidateSize(!!this.sizeStable);
      this.sizeStable = true;
      this.handleIntents();
    } catch (error) {
      console.error('Failed to invalidate size', error);
    }
  }, ResizeDebounce);

  handleClick = mapEvent => {
    const { disableEvents, onClick } = this.props;
    if (disableEvents) {
      return;
    }
    onClick([mapEvent.latlng.lng, mapEvent.latlng.lat]);
  };

  getBoundsGeojson() {
    const bounds = this.map.getBounds();
    const zoom = this.map.getZoom();
    const sw = bounds.getSouthWest();
    const ne = bounds.getNorthEast();
    return bboxPolygon([sw.lng, sw.lat, ne.lng, ne.lat], {
      properties: { zoom },
    });
  }

  makeRefHandler = createSelector(identity, measureRef => el => {
    this.map = el && el.leafletElement;
    measureRef(el && el.container);
  });

  render() {
    const { children, mapProps } = this.props;
    const { viewport } = this.state;
    return (
      <Measure bounds onResize={this.handleResize}>
        {({ measureRef }) => (
          <Map
            ref={this.makeRefHandler(measureRef)}
            viewport={viewport}
            maxZoom={MaxZoom}
            onClick={this.handleClick}
            onViewportChange={this.handleViewportChange}
            onViewportChanged={this.handleViewportChanged}
            {...mapProps}
          >
            {children}
          </Map>
        )}
      </Measure>
    );
  }
}

ControlledMap.defaultProps = {
  mapProps: {},
};

const mapStateToProps = () => {
  return (state, ownProps) => ({
    intents: map.selectors.makeIntentGetter(state, ownProps.id),
    userId: auth.selectors.getUserID(state),
    currentSeason: seasons.selectors.getCurrent(state),
  });
};

const mapDispatchToProps = {
  onClick: map.actions.click,
  onCameraIdle: map.actions.cameraIdle,
  onViewportChanged: map.actions.viewportChanged,
  onClearIntents: map.actions.clearIntents,
};

export default connect(mapStateToProps, mapDispatchToProps, null, {
  forwardRef: true,
})(ControlledMap);
