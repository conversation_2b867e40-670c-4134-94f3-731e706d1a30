import React, {
  DOMAttributes,
  ReactNode,
  ReactElement,
  VFC,
  CSSProperties,
} from 'react';

import {
  useFloating,
  autoUpdate,
  offset as offsetMiddleware,
  flip,
  size,
  useInteractions,
  hide,
} from '@floating-ui/react';

import EnterExitTransition from 'components/EnterExitTransition';
import { Options } from '@floating-ui/core/src/middleware/offset';
import { Placement } from '@floating-ui/core/src/types';

export type PopoverProps = {
  open: boolean;
  offset?: Options;

  renderTrigger: (
    args: { ref?: TEMPORARY_ANY } & DOMAttributes<any>, // eslint-disable-line @typescript-eslint/no-explicit-any
  ) => ReactNode;
  renderPopover: (args: {
    ref: (ref: HTMLElement | null) => void;
    style: CSSProperties;
  }) => ReactElement;
  placement?: Placement;
  sizeByParent?: boolean;
};

const defaultOffset = {
  mainAxis: 10,
};

export const Popover: VFC<PopoverProps> = ({
  offset = defaultOffset,
  open,
  placement,
  renderTrigger,
  renderPopover,
  sizeByParent,
}) => {
  const { x, y, strategy, refs, middlewareData } = useFloating({
    open: open,
    placement: placement,
    middleware: [
      offsetMiddleware(offset),
      flip(),
      sizeByParent &&
        size({
          apply({ rects, elements }) {
            Object.assign(elements.floating.style, {
              width: `${rects.reference.width}px`,
            });
          },
        }),
      hide(),
    ],
    whileElementsMounted: autoUpdate,
  });

  // Merge all the interactions into prop getters
  const { getReferenceProps, getFloatingProps } = useInteractions([]);

  return (
    <>
      {renderTrigger({
        ref: refs.setReference,
        ...getReferenceProps(),
      })}
      <EnterExitTransition>
        {open &&
          renderPopover({
            ref: refs.setFloating,
            style: {
              position: strategy,
              top: y ?? 0,
              left: x ?? 0,
              width: 'max-content',
              visibility: middlewareData.hide?.referenceHidden
                ? 'hidden'
                : 'visible',
            },
            ...getFloatingProps(),
          })}
      </EnterExitTransition>
    </>
  );
};
