import React from 'react';
import Downshift from 'downshift';
import { css, cx } from 'linaria';

import { PopoverDeprecated } from 'components/ui/Popover';
import { useTranslate } from 'utils/use-translate';

const sectionTitle = css`
  padding: 0 15px;
  font-size: 10px;
  line-height: 16px;
  color: #a5b2bc;
  text-transform: uppercase;
  letter-spacing: 1px;

  .modal-select__list + & {
    margin-top: 20px;
  }
`;

const CustomInputDropdown = ({
  index,
  crop,
  value,
  varietyList,
  placeholder,
  disabled,
  invalid,
  onChange,
}) => {
  const { t } = useTranslate();

  const itemToString = i => (i ? i : '');

  const filterBy = string => {
    const stringLowerCase = string.toLowerCase().trim();
    return item => item.toLowerCase().includes(stringLowerCase);
  };

  return (
    <div>
      <Downshift
        itemToString={itemToString}
        selectedItem={value ? value : ''}
        onInputValueChange={inputValue => {
          onChange(inputValue);
        }}
      >
        {({
          getInputProps,
          getMenuProps,
          getItemProps,
          isOpen,
          inputValue,
          selectedItem,
          highlightedIndex,
          setHighlightedIndex,
          openMenu,
        }) => {
          const options = varietyList.filter(filterBy(inputValue));
          return (
            <div onMouseLeave={() => setHighlightedIndex(null)}>
              <PopoverDeprecated
                align='bottom-left'
                offset={[0, 10]}
                active={isOpen && options.length > 0}
                renderTrigger={props => (
                  <input
                    {...props}
                    type='text'
                    className={cx('form-input size-full', invalid && '__error')}
                    placeholder={placeholder}
                    disabled={disabled}
                    onClick={() => !isOpen && openMenu()}
                    {...getInputProps({
                      value: value ? value : '',
                    })}
                  />
                )}
                renderPopover={props => (
                  <div {...props} className='modal-select'>
                    <div className='modal-select__inner' {...getMenuProps()}>
                      <div className={sectionTitle}>
                        {t('croprotation.crops.sections.recent')}
                      </div>
                      <ul className='modal-select__list'>
                        {options.map((item, index) => (
                          <li
                            className='modal-select__list-item'
                            {...getItemProps({
                              key: item,
                              index,
                              item,
                            })}
                          >
                            <div
                              className={cx(
                                'modal-select__item',
                                selectedItem === item && '__checked',
                              )}
                            >
                              {item}
                            </div>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                )}
              />
            </div>
          );
        }}
      </Downshift>
    </div>
  );
};

export default CustomInputDropdown;
