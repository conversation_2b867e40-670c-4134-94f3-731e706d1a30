import React, { VFC } from 'react';

import SmartButton from 'components/ui/Button/SmartButton';
import { FloatingActionsStyle } from 'components/ui/FloatingActions/styles';

interface FloatingNavigationProps {
  nextLabel: string | React.ReactNode;
  nextDisabled?: boolean;
  nextPending?: boolean;
  cancelLabel: string | React.ReactNode;
  onCancel: VoidCallBack;
  onContinue: VoidCallBack;
}

export const FloatingNavigation: VFC<FloatingNavigationProps> = ({
  nextLabel,
  nextDisabled,
  nextPending,
  cancelLabel,
  onCancel,
  onContinue,
}) => {
  return (
    <FloatingActionsStyle>
      <SmartButton className='btn btn-dark btn-lg' onClick={onCancel}>
        {cancelLabel}
      </SmartButton>

      <SmartButton
        className='btn btn-success btn-lg'
        disabled={nextDisabled}
        pending={nextPending}
        onClick={onContinue}
      >
        {nextLabel}
      </SmartButton>
    </FloatingActionsStyle>
  );
};
