import { MutableRefObject, useCallback } from 'react';
import { useDispatch } from 'react-redux';

import entities from 'modules/entities';
import { DataStoreType } from 'features/files/by-machinery/pages/MatchingAttributesPage/service/useDataStore';
import { UpdatePropertyType } from 'modules/entities/actions';

export type OnSetUnitType = (
  newUnitValue: string | null,
  attributeName: string,
  layerUuid: string,
) => void;

export const useSetUnit = (
  dataStore: MutableRefObject<DataStoreType>,
): OnSetUnitType => {
  const dispatch = useDispatch();

  return useCallback(
    (newUnitValue: string | null, attributeName: string, layerUuid: string) => {
      const dataLayersForUpdate = dataStore.current.dataLayers.filter(item =>
        dataStore.current.layersLinks[layerUuid]?.includes(item.uuid),
      );
      const allProperties = dataLayersForUpdate
        .map(item => item.properties)
        .flat(1);

      const propertyForAttribute = allProperties.filter(
        property => property.target_name === attributeName,
      );

      let requests: UpdatePropertyType[] = [];

      if (propertyForAttribute.length > 0) {
        requests.push({
          payload: {
            target_name: propertyForAttribute[0]!.target_name,
            unit: newUnitValue,
          },
          properties: propertyForAttribute.map(v => v.uuid),
        });
        dispatch(entities.actions.updateFieldsDataLayerProperty(requests));
      }
    },
    [dataStore, dispatch],
  );
};
