import React from 'react';
import { useSelector } from 'react-redux';

import VegetationTypeDropdown from './VegetationTypeDropdown';

import {
  StyledMapTopPanel,
  StyledTopPanelItem,
} from 'components/ui/mapToolBar/styles';

import { fieldsSelectors } from 'modules/fields/selectors';
import { SelectBoxPropertyForOperation } from './SelectBoxPropertyForOperation';
import { OperationLegend } from './OperationLegend';
import {
  filterOperationsForField,
  getOperationsValue,
} from '../../files/by-machinery/redux/selectors';

export type MachineryFieldToolbarProps = {
  fieldId: string;
};

export const MachineryFieldToolbar = ({
  fieldId,
}: MachineryFieldToolbarProps) => {
  const fieldFillingType = useSelector(fieldsSelectors.getFieldFillingType);
  const fieldFillingOperation = useSelector(
    fieldsSelectors.getFieldFillingOperation,
  );
  const field = useSelector(state => fieldsSelectors.getByID(state, fieldId));

  const allOperations = useSelector(getOperationsValue);
  const operationsForField = allOperations.filter(operation =>
    filterOperationsForField(operation, field?.fieldUserSeasonId),
  );
  const selectedOperation = operationsForField.find(
    v => v.uuid === fieldFillingOperation,
  );

  return (
    <>
      <StyledMapTopPanel>
        <StyledTopPanelItem>
          <VegetationTypeDropdown fieldId={fieldId} />
        </StyledTopPanelItem>
        <StyledTopPanelItem isFluid={fieldFillingType !== 'operation'}>
          {selectedOperation && (
            <SelectBoxPropertyForOperation operation={selectedOperation} />
          )}
        </StyledTopPanelItem>
      </StyledMapTopPanel>
      {selectedOperation && <OperationLegend operation={selectedOperation} />}
    </>
  );
};
