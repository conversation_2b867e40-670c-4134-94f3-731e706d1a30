import React from 'react';
import { useSelector } from 'react-redux';

import BackgroundImage, {
  BackgroundImageProps,
} from 'components/ui/BackgroundImage/BackgroundImage';
import { useTranslate } from 'utils/use-translate';
// @ts-ignore
import { getUrlForPolygon } from 'utils/static-maps';
import fieldSeasonModule from '../../modules/fieldSeason';
import { FieldBySeason } from '../../types/fields';
import { JohnDeereFieldIcon } from 'features/johnDeere';

type FieldImageProps = {
  field: TEMPORARY_ANY;
} & Omit<BackgroundImageProps, 'src'>;

export const FieldImage = ({ field, ...rest }: FieldImageProps) => {
  const { i18n } = useTranslate();
  const lang = i18n.resolvedLanguage;

  const fieldBySeason = useSelector(state => {
    const all: FieldBySeason[] =
      fieldSeasonModule.selectors.getAllFieldSeason(state);

    return all.find(v => v.id === field.fieldUserSeasonId);
  });

  return (
    <BackgroundImage
      {...rest}
      src={getUrlForPolygon(field.geometry, {
        lang,
      })}
    >
      <JohnDeereFieldIcon fieldBySeason={fieldBySeason} />
    </BackgroundImage>
  );
};
