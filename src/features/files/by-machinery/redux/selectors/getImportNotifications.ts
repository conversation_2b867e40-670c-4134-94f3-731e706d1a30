import { createSelector } from 'reselect';
import { getImportStatusForNotifications } from './getImportStatusForNotifications';
import { ImportingTaskStatus } from '../../types/tasks';
import { getUploadingProgress } from './getUploadingProgress';
import { Notification } from 'features/platform/PlatformSideBar/types';

export const getImportNotifications = createSelector(
  getImportStatusForNotifications,
  getUploadingProgress,
  (importStatus, uploadingProgress): Notification => {
    if (importStatus === ImportingTaskStatus.failed) {
      return {
        icon: 'alert-circle-bordered',
      };
    }

    if (
      importStatus === ImportingTaskStatus.success ||
      importStatus === ImportingTaskStatus.partlySuccess
    ) {
      return {
        icon: 'success-circle-bordered',
      };
    }

    if (importStatus === ImportingTaskStatus.inProgress) {
      return {
        progress: uploadingProgress,
      };
    }

    return {};
  },
);
