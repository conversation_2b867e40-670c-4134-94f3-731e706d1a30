import React from 'react';
import { connect, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import { css } from 'linaria';
import area from '@turf/area';

import Saga from 'components/saga/Saga';
//@ts-ignore
import PredictedCropTooltip from 'features/fields/PredictedCropTooltip/PredictedCropTooltip';

//@ts-ignore
import seasons from 'modules/seasons';
import settings from 'modules/settings';

import { getViewportHash } from 'utils/get-root-scouting-url';
//@ts-ignore
import { formatTitle } from 'utils/fields-formatters';
import { formatTitle as formatSeasonTitle } from 'utils/seasons-formatters';
import { useTranslate } from 'utils/use-translate';
//@ts-ignore
import { fetchInfo } from 'sagas/local/field-info';
import logEvent from 'sagas/global/logEvent';
import getFieldArea from 'utils/get-field-area';
import { formatDate } from 'utils/format-date';
import { FeatureDisabledTooltip, FEATURES } from 'features/permissions';
import { PopoverDeprecatedAlign } from 'components/ui/Popover';
import RootState, { SeasonType } from 'types/rootState';
import { fieldsSelectors } from 'modules/fields/selectors';
import { FieldEntityType } from 'types/fields';
import fieldSeasonModule from '../../modules/fieldSeason';

const cropsList = css`
  list-style-type: none;
  padding: 0;
  margin: 8px 0 0;
`;

const cropsListItem = css`
  padding: 3px 0;
`;

const cropName = css`
  font-size: 14px;
  line-height: 20px;
`;

const cropDetails = css`
  display: flex;
  justify-content: space-between;
  color: #798288;
`;

const cropDetailsAside = css`
  flex: 0 0 auto;
`;
const cropDetailsTitle = css`
  flex: 1 1 auto;
  overflow: hidden;
  text-overflow: ellipsis;

  + .${cropDetailsAside} {
    padding-left: 15px;
  }
`;

type DashboardFieldSectionType = {
  currentSeason: SeasonType;
  fieldId: TEMPORARY_ANY;
  field: FieldEntityType;
  fieldSeasons: SeasonType[] | null | undefined;
  history: TEMPORARY_ANY;
  location: TEMPORARY_ANY;
  formatUnit: TEMPORARY_ANY;
};

const DashboardFieldSection = ({
  currentSeason,
  fieldId,
  field,
  fieldSeasons,
  history,
  location,
  formatUnit,
}: DashboardFieldSectionType) => {
  const { t } = useTranslate();

  const fieldBySeason = useSelector(state => {
    return fieldSeasonModule.selectors.getFieldBydSeason(
      state,
      currentSeason.id,
      field.originalId,
    );
  });

  return (
    <div className='map-dashboard__side'>
      <Saga saga={fetchInfo} fieldId={fieldId} />
      <div className='map-dashboard-card'>
        <div>
          <h2>
            {formatTitle(t, field)}, {getFieldArea({ field, formatUnit })}
          </h2>
          {field.crops.length !== 0 ? (
            <ul className={cropsList}>
              {field.crops.map(crop => (
                <li key={crop.id} className={cropsListItem}>
                  <div className={cropName}>{t(`crop.${crop.crop}`)}</div>
                  <div className={cropDetails}>
                    {crop.variety && (
                      <div className={cropDetailsTitle}>{crop.variety}</div>
                    )}
                    {crop.sowing_date && (
                      <div className={cropDetailsAside}>
                        {formatDate(
                          crop.sowing_date,
                          t('fields.crops.date_format') as string,
                        )}
                      </div>
                    )}
                  </div>
                </li>
              ))}
            </ul>
          ) : (
            <p>
              <em className='c-neutral'>
                {t('fields.no_crop')}
                &nbsp;
                <PredictedCropTooltip
                  align='bottom-middle'
                  field={field}
                  history={history}
                  onZoomToField={() => {}}
                />
              </em>
            </p>
          )}
        </div>
      </div>
      <div className='map-dashboard__hr-slim' />
      <div className='map-dashboard-card'>
        <h3>{t('fields.dashboard.info.actions')}</h3>
        <ul className='map-dashboard-actions'>
          <li className='map-dashboard-actions__list-item'>
            <FeatureDisabledTooltip
              data={fieldBySeason}
              feature={FEATURES.FIELD_EDIT}
              direction={PopoverDeprecatedAlign.MiddleLeft}
            >
              <Link
                onClick={() => {
                  logEvent('field_edit_dialog_from_weather');
                  logEvent('field_edit', {
                    field_id: field.originalId,
                    field_type: field.type,
                    field_size: area(field.geometry),
                    season: currentSeason.id,
                  });
                }}
                to={`/${getViewportHash(location.pathname)}/fields/${
                  field.id
                }/edit`}
              >
                {t('fields.dashboard.info.edit')}
              </Link>
            </FeatureDisabledTooltip>
          </li>
        </ul>
      </div>
      {fieldSeasons && (
        <>
          <div className='map-dashboard__hr-slim' />
          <div className='map-dashboard-card'>
            <h3>{t('fields.dashboard.info.crops')}</h3>
            <div className='crop-rotation-details'>
              {fieldSeasons.map(season => {
                const crops = field.all_crops.filter(
                  crop => crop.season_id === season.id,
                );
                return (
                  <div
                    key={season.id}
                    className='crop-rotation-details__row-item'
                  >
                    <h4>{formatSeasonTitle(t, season)}</h4>
                    <ul className='crop-rotation-details__list'>
                      {crops.length === 0 && (
                        <FeatureDisabledTooltip
                          data={fieldBySeason}
                          feature={FEATURES.FIELD_EDIT}
                          direction={PopoverDeprecatedAlign.MiddleLeft}
                        >
                          <Link
                            to={`/${getViewportHash(
                              location.pathname,
                            )}/fields/${field.id}/edit?season=${season.id}`}
                          >
                            {t('fields.crops.add_crop')}
                          </Link>
                        </FeatureDisabledTooltip>
                      )}
                      {crops.map(crop => (
                        <li
                          key={crop.id}
                          className='crop-rotation-details__list-item'
                        >
                          <div className='crop-rotation-details__title'>
                            {t(`crop.${crop.crop}`)}
                          </div>
                          <div className='crop-rotation-details__aside'>
                            {crop.sowing_date ? (
                              formatDate(
                                crop.sowing_date,
                                t('fields.crops.date_format') as string,
                              )
                            ) : (
                              <FeatureDisabledTooltip
                                data={fieldBySeason}
                                feature={FEATURES.FIELD_EDIT}
                                direction={PopoverDeprecatedAlign.MiddleLeft}
                              >
                                <Link
                                  to={`/${getViewportHash(
                                    location.pathname,
                                  )}/fields/${field.id}/edit?season=${
                                    season.id
                                  }`}
                                >
                                  {t('fields.crops.add_sowing_date')}
                                </Link>
                              </FeatureDisabledTooltip>
                            )}
                          </div>
                        </li>
                      ))}
                    </ul>
                  </div>
                );
              })}
            </div>
            {/* Add some padding to fit intercom button */}
            <div style={{ height: 80 }} />
          </div>
        </>
      )}
    </div>
  );
};

const mapStateToProps = (state: RootState, ownProps: TEMPORARY_ANY) => {
  const getFieldSeasons = fieldsSelectors.makeSeasonsByIDGetter();
  return {
    currentSeason: seasons.selectors.getCurrent(state),
    field: fieldsSelectors.getByID(state, ownProps.fieldId) as FieldEntityType,
    fieldSeasons: getFieldSeasons(state, ownProps.fieldId),
    formatUnit: settings.selectors.getUnitFormatter(state),
  };
};

export default connect(mapStateToProps)(DashboardFieldSection);
