import React, { useContext, useRef } from 'react';
import { useDragLayer, XYCoord } from 'react-dnd';
import { ItemTypes } from 'components/ui/DragAndDropTable/TableRow/types';
import { CustomDragLayerStyle } from './CustomDragLayer.style';
import { DragAndDropTableContext } from '../DragAndDropTableContext';

//This layer needs to show custom component when dragging a row
export function CustomDragLayer() {
  const dragUuid = useContext(DragAndDropTableContext);

  const ref = useRef<HTMLDivElement>(null);
  const { itemType, isDragging, item, initialOffset, currentOffset } =
    useDragLayer(monitor => ({
      item: monitor.getItem(),
      itemType: monitor.getItemType(),
      initialOffset: monitor.getInitialSourceClientOffset(),
      currentOffset: monitor.getSourceClientOffset(),
      isDragging: monitor.isDragging(),
    }));

  const getItemStyles = (
    initialOffset: XYCoord | null,
    currentOffset: XYCoord | null,
  ) => {
    if (!initialOffset || !currentOffset || !ref.current) {
      return {
        display: 'none',
      };
    }

    let { x, y } = currentOffset;
    let { x: parentX, y: parentY } = ref.current.getBoundingClientRect();

    const transform = `translate(${x - parentX}px, ${y - parentY}px)`;

    return {
      transform,
      WebkitTransform: transform,
    };
  };

  const renderItem = () => {
    switch (itemType) {
      case ItemTypes.ROW + dragUuid:
        return item?.dragPreview ?? null;
      default:
        return null;
    }
  };

  if (!isDragging) {
    return null;
  }

  return (
    <div className={CustomDragLayerStyle} ref={ref}>
      <div style={getItemStyles(initialOffset, currentOffset)}>
        {renderItem()}
      </div>
    </div>
  );
}
