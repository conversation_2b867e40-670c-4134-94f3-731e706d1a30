import merge from 'lodash/merge';
import { GlobalConfigType } from 'config/types';

let config;

if (process.env.REACT_APP_ENV === 'local') {
  config = require('./local.json');
} else if (process.env.REACT_APP_ENV === 'development') {
  config = require('./development.json');
} else if (process.env.REACT_APP_ENV === 'staging') {
  config = require('./staging.json');
} else {
  config = require('./production.json');
}

const domainOverrides = config.domainOverrides[window.location.host];
if (domainOverrides) {
  merge(config, domainOverrides);
}

if (localStorage.OneSoilConfig) {
  try {
    const overrides = JSON.parse(localStorage.OneSoilConfig);
    merge(config, overrides);
  } catch (error) {
    console.error('Failed to load config overrides', error);
  }
}

export default config as GlobalConfigType;
