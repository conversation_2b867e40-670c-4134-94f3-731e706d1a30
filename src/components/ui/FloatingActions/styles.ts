import { styled } from 'linaria/react';

export const FloatingActionsDescriptionStyle = styled.div`
  margin-right: 15px;

  & > small {
    display: inline-block;
    font-size: 14px;
    line-height: 1;
  }
`;

export const FloatingActionsStyle = styled.div`
  position: fixed;
  z-index: 59;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  width: fit-content;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);

  color: #fff;
  fill: #fff;
  margin-top: -95px;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.36);
  border-radius: 10px;
  background-color: rgba(34, 34, 34, 0.85);
  padding: 15px;

  & .btn {
    min-width: 170px;
  }

  & .btn:not(:first-child) {
    margin-left: 10px;
  }

  & .btn-lg {
    min-width: 190px;
  }

  & .btn-lg:not(:first-child) {
    margin-left: 15px;
  }
`;

export const FloatingActionsNameStyle = styled.strong`
  position: relative;
  display: block;
  font-size: 24px;
  line-height: 1.25;
  font-weight: bold;
  overflow: hidden;
  text-overflow: ellipsis;
`;
