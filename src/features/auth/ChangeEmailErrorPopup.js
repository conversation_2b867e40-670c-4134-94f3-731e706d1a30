import React from 'react';

import Icon from 'components/Icon';
import Button from 'components/ui/Button/Button';
import RenderAboveEverything from 'components/RenderAboveEverything';

import useQuery from 'utils/use-query';
import { useTranslate } from 'utils/use-translate';
import { closeButtonStyle, errorText } from 'features/auth/styled';

const ChangeEmailErrorPopup = ({ code, onClose }) => {
  const { t } = useTranslate();

  // eslint-disable-next-line no-unused-vars
  const [query, mergeQuery] = useQuery();

  return (
    <RenderAboveEverything>
      <div className='modal modal-editseasons'>
        <div className='modal-outer'>
          {code !== 'expired' && (
            <Button className={closeButtonStyle} onClick={onClose}>
              <Icon className='ico-close-os' name='close' />
            </Button>
          )}
          <div className='modal-editseasons-content'>
            <div className='modal-editseasons-content__header'>
              {t('change_email.title')}
            </div>
            <div className='modal-editseasons-content__body'>
              <div className={`form-group ${errorText}`}>
                {t(
                  code === 'expired'
                    ? 'change_email.token_expired'
                    : 'change_email.token_invalid',
                )}
              </div>
            </div>
            {code === 'expired' && (
              <div className='modal-editseasons-content__actions'>
                <Button
                  className='btn btn-primary btn-lg'
                  onClick={() => {
                    onClose();
                  }}
                >
                  {t('forms.cancel')}
                </Button>
                <Button
                  className='btn btn-success btn-lg'
                  onClick={() => {
                    mergeQuery({ settings: 'email' });
                    onClose();
                  }}
                >
                  {t(`change_email.retry`)}
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </RenderAboveEverything>
  );
};

export default ChangeEmailErrorPopup;
