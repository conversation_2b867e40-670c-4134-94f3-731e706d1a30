import { styled } from 'linaria/react';

export const BulkUploadDescriptionText = styled.div`
  font-size: 17px;
  font-weight: 400;
  line-height: 24px;
  text-align: left;
  margin: 10px 0 0;
`;

export const HintTitle = styled.div`
  font-weight: 700;
  font-size: 16px;
  line-height: 22px;
  padding-bottom: 5px;
  color: #ffffff;
`;

export const HintDescription = styled.div`
  font-weight: 500;
  font-size: 16px;
  line-height: 22px;
  color: #999999;
`;

export const HintLink = styled.span`
  border-bottom: 1px dashed #000000;
  color: inherit;
  cursor: pointer;
`;

export const HintTooltip = styled.div`
  max-width: 249.06px;
`;
