import React, { useMemo } from 'react';
import { isEmpty } from 'lodash';

import ErrorLineComponent from '../components/ErrorLineComponent';
import { ApiDataSourceLayer } from 'features/files/by-machinery/types';
import { TranslateFunction } from 'utils/use-translate';

export const useErrorMessage = (
  errorType: string,
  layerMatchingTask: ApiDataSourceLayer,
  t: TranslateFunction,
) =>
  useMemo(() => {
    if (!layerMatchingTask) return null;
    const { spatial_matching_meta } = layerMatchingTask;
    if (!spatial_matching_meta || isEmpty(spatial_matching_meta)) return null;

    if (errorType === 'fieldError') {
      if (spatial_matching_meta.total_fields === 0) {
        return (
          <ErrorLineComponent
            icon='modal-error'
            message={t(
              'import.by-machinery.fields-match-modal.error.no-existing-field',
            )}
          />
        );
      }

      if (spatial_matching_meta.total_fields_with_soil_sampling_maps === 0) {
        return (
          <ErrorLineComponent
            icon='modal-error'
            message={t('import.by-machinery.fields-match-modal.error.no-maps')}
          />
        );
      }

      if (
        spatial_matching_meta.total_fields_with_soil_sampling_maps <
        spatial_matching_meta.total_fields
      ) {
        return (
          <ErrorLineComponent
            icon='warning'
            message={t(
              'import.by-machinery.fields-match-modal.warning.some-fields-no-maps',
            )}
          />
        );
      }

      return null;
    }

    if (errorType === 'pointError') {
      if (
        spatial_matching_meta.matched_fields > 0 &&
        spatial_matching_meta.matched_fields <
          spatial_matching_meta.total_fields
      ) {
        return (
          <ErrorLineComponent
            icon='warning'
            message={t(
              'import.by-machinery.fields-match-modal.warning.some-fields-different-points',
            )}
          />
        );
      }

      if (
        spatial_matching_meta.total_soil_sampling_points > 0 &&
        spatial_matching_meta.matched_soil_sampling_points === 0
      ) {
        return (
          <ErrorLineComponent
            icon='modal-error'
            message={t(
              'import.by-machinery.fields-match-modal.error.not-correspond',
            )}
          />
        );
      }

      return null;
    }

    return null;
  }, [layerMatchingTask, t, errorType]);
