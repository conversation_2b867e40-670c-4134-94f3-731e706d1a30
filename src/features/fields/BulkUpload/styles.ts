import { styled } from 'linaria/react';
import { css } from 'linaria';

export const CentralSection = styled.div`
  margin: 40px auto;
  max-width: 670px;
`;

export const UploadingTasksArea = styled.div`
  width: 670px;
  background: #f5f7f9;
  padding: 20px;
  margin-top: 32px;
  border-radius: 24px;
`;

export const ErrorText = styled.span`
  color: #ff3b30;
`;

export const NavProgressHeader = styled.div`
  width: 100%;
  padding: 16px 16px 16px 32px;
  display: flex;
  justify-content: center;
`;

export const successToastIcon = css`
  color: #27ae60;
  height: 12px;
  padding-right: 12px;
`;

export const failedToastIcon = css`
  color: #ff3b30;
  margin-right: 5px;
  margin-top: -3px;
`;

export const BulkUploadItemRowStyle = styled.div`
  display: flex;
  margin-top: 22px;
  position: relative;
`;

export const BulkUploadItemStatusWrapper = styled.div`
  position: relative;
  padding: 0 7px 0 5px;
  height: 34px;
  width: 44px;
`;

export const BulkUploadItemTitleStyle = styled.div`
  font-weight: 500;
  font-size: 14px;
  line-height: 18px;
  margin: 2px 1px -2px 0;
  text-overflow: ellipsis;
  overflow: hidden;
  width: 400px;
`;

export const BulkUploadItemDescriptionStyle = styled.span`
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  text-overflow: ellipsis;
  overflow: hidden;
  width: 400px;
`;

export const BulkUploadItemErrorStyle = styled.span`
  color: #ff3b30;
`;

export const successButtonIconClass = css`
  color: #27ae60;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-top: -5px;
  margin-left: -8px;
  width: 12.59px;
  height: 9.41px;
`;

export const failedButtonIconClass = css`
  color: #ff3b30;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-top: -5px;
  margin-left: -10px;
  width: 14px;
  height: 14px;
`;
