import React, { VFC } from 'react';
import { useSelector } from 'react-redux';

import entities from 'modules/entities';
import { useTranslate } from 'utils/use-translate';
import { useDelayedState } from 'utils/optimization/useDelayedState';

import { SelectBox } from 'components/ui/CustomDropdown';

import {
  MetaAttributesApi,
  ApiDataFieldLayer,
} from 'features/files/by-machinery/types';
import { OnSetUnitType } from 'features/files/by-machinery/pages/MatchingAttributesPage/service/useSetUnit';

import { Skeleton, unitSelectBoxWrapper } from './styles';

type UnitSelectBoxProps = {
  attribute: MetaAttributesApi;
  layer: ApiDataFieldLayer;
  disabled: boolean;
  onSetUnit: OnSetUnitType;
};

export const UnitSelectBox: VFC<UnitSelectBoxProps> = ({
  attribute,
  layer,
  disabled,
  onSetUnit,
}) => {
  const { t } = useTranslate();
  const units = attribute.units || [];
  const currentProperty = layer.properties.find(
    property => property.target_name === attribute.name,
  );
  const value = currentProperty?.unit;

  const options = units.map(unit => ({
    id: unit,
    label: t('import.matching.units.' + unit) as string,
  }));

  const status = useSelector(state =>
    entities.selectors.getEntityStatus(
      state,
      'fieldsDataLayerProperty',
      currentProperty?.uuid,
      'unit',
    ),
  );
  const isLoading = useDelayedState(status === 'pending');
  if (isLoading) {
    return <Skeleton />;
  }

  return (
    <>
      <SelectBox
        sizeByParent={false}
        options={options}
        value={value}
        unClearable={true}
        disabled={disabled}
        wrapperClassName={unitSelectBoxWrapper}
        placeholder={t('import.matching.units.placeholder')}
        onChange={value => onSetUnit(value, attribute.name, layer.uuid)}
      />
    </>
  );
};
