import React from 'react';
import { StyledSorterButton } from './Sorter.style';
import { SortDirections } from 'constants/sortDirections';

export const SorterButtonDataTestId = 'sorter';
interface SorterProps {
  label: string;
  value: SortDirections;
  onChange: (value: SortDirections) => void;
  isHidden?: boolean;
}

const Sorter = ({ label, value, onChange, isHidden }: SorterProps) => {
  const onSorterClick = () => {
    onChange?.(
      (value =
        value === SortDirections.ASCENDING
          ? SortDirections.DESCENDING
          : SortDirections.ASCENDING),
    );
  };

  return (
    <StyledSorterButton
      data-testid={SorterButtonDataTestId}
      isHidden={isHidden}
      onClick={onSorterClick}
    >
      {label || '-'}
    </StyledSorterButton>
  );
};

export default Sorter;
