import React, { useState, useRef } from 'react';
import cx from 'classnames';

import Icon from 'components/Icon';
import Button from 'components/ui/Button/Button';
import { PopoverDeprecated } from 'components/ui/Popover';

import { getViewportHash } from 'utils/get-root-scouting-url';
import { useTranslate } from 'utils/use-translate';

const CloseDelay = 50;

const PredictedCropTooltip = ({ align, fieldSeason, history }) => {
  const [hovered, setHovered] = useState(false);
  const timer = useRef(null);
  const { t } = useTranslate();

  const onMouseEnter = () => {
    clearTimeout(timer.current);
    setHovered(true);
  };
  const onMouseLeave = () => {
    clearTimeout(timer.current);
    timer.current = setTimeout(setHovered, CloseDelay, false);
  };

  return (
    <PopoverDeprecated
      active={hovered}
      offset={align === 'bottom-middle' ? [0, 10] : [10, 0]}
      align={align || 'middle-right'}
      transitionVariant='legacy'
      renderTrigger={({ ref, ...otherProps }) => (
        <span
          className='soil-fields-list-meta__quest'
          onMouseEnter={onMouseEnter}
          onMouseLeave={onMouseLeave}
          {...otherProps}
        >
          <Icon innerRef={ref} name='question-circle' />
        </span>
      )}
      renderPopover={({ style, ...props }) => (
        <div
          className={cx('simple-tooltip', {
            '__arrow-top': align === 'bottom-middle',
            '__arrow-left': !align,
          })}
          style={{ maxWidth: 290, ...style }}
          {...props}
          onMouseEnter={onMouseEnter}
          onMouseLeave={onMouseLeave}
          onClick={event => {
            // Don't let clicks propagate to FieldListItem
            event.stopPropagation();
          }}
        >
          <div className='simple-tooltip-arrow'>
            <div className='simple-tooltip-arrow__inner' />
          </div>
          <div className='simple-tooltip__body'>
            <div className='simple-tooltip__content'>
              <p>
                {t('fields.suggest_predicted_crop', {
                  crop: t(`crop.${fieldSeason.predicted_crop}`).toLowerCase(),
                })}
              </p>
            </div>
            <div className='simple-tooltip-actions'>
              <div className='simple-tooltip-actions__item'>
                <Button
                  className='btn btn-sm btn-primary size-full'
                  onClick={() => {
                    const viewportHash = getViewportHash(
                      history.location.pathname,
                    );
                    history.push(
                      `/${viewportHash}/fields/${fieldSeason.id}/edit`,
                    );
                  }}
                >
                  <Icon className='ico-pencil-os' name='pencil' />{' '}
                  {t('fields.refine_crop.edit')}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    />
  );
};

export default PredictedCropTooltip;
