diff --git a/node_modules/moment/locale/ru.js b/node_modules/moment/locale/ru.js
index 7afb7b5..38b340f 100644
--- a/node_modules/moment/locale/ru.js
+++ b/node_modules/moment/locale/ru.js
@@ -40,8 +40,8 @@
         },
         monthsShort : {
             // по CLDR именно "июл." и "июн.", но какой смысл менять букву на точку ?
-            format: 'янв._февр._мар._апр._мая_июня_июля_авг._сент._окт._нояб._дек.'.split('_'),
-            standalone: 'янв._февр._март_апр._май_июнь_июль_авг._сент._окт._нояб._дек.'.split('_')
+            format: 'янв_февр_мар_апр_мая_июня_июля_авг_сент_окт_нояб_дек'.split('_'),
+            standalone: 'янв_февр_март_апр_май_июнь_июль_авг_сент_окт_нояб_дек'.split('_')
         },
         weekdays : {
             standalone: 'воскресенье_понедельник_вторник_среда_четверг_пятница_суббота'.split('_'),
