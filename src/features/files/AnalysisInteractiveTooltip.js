import React from 'react';
import { useSelector } from 'react-redux';
import { useTranslate } from 'utils/use-translate';

import ui from 'modules/ui';

import { FieldInteractiveTooltip } from 'components/FieldInteractiveTooltip/FieldInteractiveTooltip';

const AnalysisInteractiveTooltip = ({ hoveredValue }) => {
  const { isVisible, translateX, translateY } = useSelector(
    ui.selectors.interactiveTooltip,
  );
  const { t } = useTranslate();

  return (
    <FieldInteractiveTooltip
      isVisible={
        isVisible && hoveredValue !== null && hoveredValue !== undefined
      }
      position={[translateX, translateY]}
      withDot
    >
      <span>
        {hoveredValue?.key ? t(hoveredValue?.key) : hoveredValue?.toFixed(2)}
      </span>
    </FieldInteractiveTooltip>
  );
};

export default AnalysisInteractiveTooltip;
