import React, { useState, VFC } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import sumBy from 'lodash/sumBy';
import { cx } from 'linaria';

import useOutsideClick from 'utils/use-outside-click';
import {
  StyledHeaderCell,
  StyledHeaderCellProps,
  styles,
} from '../CropRotationView.style';
import { SeasonType } from 'types/rootState';
import { SeasonData } from '../types';
import settings from 'modules/settings';
// @ts-ignore
import entities from 'modules/entities';
import { formatTitle, formatDateRange } from 'utils/seasons-formatters';
import fieldSeason from 'modules/fieldSeason';
import EnterExitTransition from 'components/EnterExitTransition';
import ConfirmationPopup from '../../ConfirmationPopup';
import logEvent from 'sagas/global/logEvent';
import { PopoverDeprecated } from 'components/ui/Popover';
import Icon from 'components/Icon';
import { FormatUnit } from 'utils/functions';
import { formatDate } from 'utils/format-date';
import { TranslateFunction } from 'utils/use-translate';
import StackBar from 'components/ui/StackBar/StackBar';
import { OperationDelete } from 'modules/entities/types';
import { FeatureDisabledTooltip, FEATURES } from 'features/permissions';
import { PopoverDeprecatedAlign } from 'components/ui/Popover';
import { CropRotationTooltip } from 'features/crop-rotation/CropRotationTooltip/CropRotationTooltip';

type SeasonsHeaderCellProps = {
  columnIndex: number;
  season: SeasonType;
  seasonData: SeasonData;
  t: TranslateFunction;
  setShowSeasonPopup: (id?: number | boolean) => void;
  isSticky?: boolean;
} & StyledHeaderCellProps;

const SeasonsHeaderCell: VFC<SeasonsHeaderCellProps> = ({
  columnIndex,
  season,
  seasonData,
  t,
  setShowSeasonPopup,
  isSticky,
  ...props
}) => {
  const [opened, setOpened] = useState(false);
  const [showConfirmationPopup, setShowConfirmationPopup] = useState(false);
  const formatUnit: FormatUnit = useSelector(
    settings.selectors.getUnitFormatter,
  );
  const crops = useSelector(state =>
    fieldSeason.selectors.getCropStatistics(state, season?.id),
  );
  const onInsideClick = useOutsideClick(() => setOpened(false));
  const dispatch = useDispatch();

  const filledArea = sumBy(crops, 'area');
  const maxArea = Math.max(seasonData?.totalArea, filledArea);

  const removeCrops = () => {
    dispatch(
      entities.actions.request(
        'remove-all-crops',
        seasonData.cropsIds.map(
          cropId =>
            ({
              entityType: 'fieldCrop',
              path: { id: cropId + '' },
              type: 'delete',
            } as OperationDelete),
        ),
      ),
    );
  };

  return (
    <StyledHeaderCell columnIndex={columnIndex} {...props}>
      <EnterExitTransition variant='popup'>
        {showConfirmationPopup && (
          <ConfirmationPopup
            season={season}
            t={t}
            onConfirm={() => {
              removeCrops();
              setShowConfirmationPopup(false);
              logEvent('crop_rotation_remove_all_crops', {
                season_id: season.id,
              });
            }}
            onCancel={() => setShowConfirmationPopup(false)}
          />
        )}
      </EnterExitTransition>
      <div
        className={cx(styles.headerCell, isSticky && styles.headerCellSticky)}
      >
        <div className={styles.headerCellTop} onClickCapture={onInsideClick}>
          <PopoverDeprecated
            active={opened}
            offset={[0, 0]}
            align={PopoverDeprecatedAlign.Overlap}
            timeout={50}
            renderTrigger={({ ref, ...props }) => (
              <div
                onClick={() => {
                  setOpened(!opened);
                  if (!opened) {
                    logEvent('crop_rotation_season_click', {
                      season_id: season.id,
                    });
                  }
                }}
                {...props}
              >
                <div className={styles.headerCellTitle}>
                  <span className={styles.seasonNameContainer}>
                    <span
                      className={cx(
                        styles.seasonName,
                        isSticky && styles.seasonNameSticky,
                      )}
                    >
                      {formatTitle(t, season)}
                    </span>
                  </span>
                  <i
                    ref={ref}
                    className={cx(styles.editIcon, 'season-header-edit-icon')}
                  />
                </div>
                {!isSticky && (
                  <div className={styles.headerCellSeasonData}>
                    {formatDateRange(formatDate, t, season)}
                  </div>
                )}
              </div>
            )}
            renderPopover={({ style, ...props }) => (
              <CropRotationTooltip
                style={style}
                minWidth={211}
                onEscapeClickHandler={() => {
                  setOpened(false);
                }}
                {...props}
              >
                <div className='simple-tooltip__body'>
                  <ul className='popover-select__list'>
                    <FeatureDisabledTooltip
                      feature={FEATURES.SEASON_EDIT}
                      direction={PopoverDeprecatedAlign.MiddleRight}
                    >
                      <li className='popover-select__list-item'>
                        <div
                          className='popover-select__item'
                          onClick={() => {
                            setShowSeasonPopup(season?.id);
                            setOpened(false);
                          }}
                        >
                          {t('croprotation.season_edit_short')}
                        </div>
                      </li>
                    </FeatureDisabledTooltip>
                    <FeatureDisabledTooltip
                      feature={FEATURES.FIELD_ADD}
                      direction={PopoverDeprecatedAlign.MiddleRight}
                    >
                      <li
                        className={cx(
                          'popover-select__list-item',
                          !seasonData.cropsIds.length && '__disabled',
                        )}
                        onClick={() => {
                          setOpened(false);
                          setShowConfirmationPopup(true);
                        }}
                      >
                        <div className='popover-select__item popover-select__item-danger'>
                          <span className='btn__ico'>
                            <Icon className='ico-trash-n-os' name='trashcan' />
                          </span>
                          {t('croprotation.remove_all_crops')}
                        </div>
                      </li>
                    </FeatureDisabledTooltip>
                  </ul>
                </div>
              </CropRotationTooltip>
            )}
          />
        </div>
        {!isSticky && (
          <div className={styles.stackBarHolder}>
            <StackBar
              data={crops}
              total={maxArea}
              valueKey={'area'}
              t={t}
              // @ts-ignore
              legendBottomText={
                // @ts-ignore
                t('croprotation.stats.title.label', {
                  filledArea: formatUnit(filledArea, 'ha', 'area'),
                  totalArea: formatUnit(seasonData?.totalArea, 'ha', 'area'),
                }) as string
              }
            />
          </div>
        )}
      </div>
    </StyledHeaderCell>
  );
};

export default SeasonsHeaderCell;
