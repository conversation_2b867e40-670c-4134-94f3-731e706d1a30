import { actions as formActions } from 'react-redux-form';
import { spawn, put } from 'redux-saga/effects';

import auth from 'modules/auth';

import useHistory from 'utils/use-history';
import useSaga from 'utils/use-saga';

import forms from 'constants/forms';

const ResetPasswordRedirectPage = ({ location, match }) => {
  const history = useHistory();
  const { email } = match.params;
  useSaga(function* () {
    // Logout will immediately redirect and cancel this saga, so we spawn here
    yield spawn(function* () {
      yield put(auth.actions.logout());

      // Auth forgot page expects email already filled in in form, we provide it here manually
      yield put(formActions.change(`${forms.login}.login`, email));
      history.replace({
        pathname: '/auth/reset',
        search: location.search,
        state: {
          login: email,
        },
      });
    });
  }, []);
  return null;
};

export default ResetPasswordRedirectPage;
