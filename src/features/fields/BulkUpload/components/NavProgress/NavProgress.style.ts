import { styled } from 'linaria/react';

export const NavProgressStyle = styled.nav`
  flex: 0 0 auto;
  margin: 0;
  padding: 0;
  color: #a5b2bc;
  font-weight: 500;

  & a {
    color: #27ae60;
  }

  & a:hover {
    color: #239553;
  }
`;

export const NavProgressListStyle = styled.ul`
  list-style-type: none;
  margin: 0;
  padding: 0;
  display: flex;
`;

export const NavProgressListItemStyle = styled.li`
  margin-left: 8px;

  &:before {
    content: url('data:image/svg+xml,%3Csvg%20width%3D%2214%22%20height%3D%2212%22%20viewBox%3D%220%200%2014%2012%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M13.3438%206.06445C13.3438%205.89128%2013.2754%205.73861%2013.1387%205.60645L8.60645%201.08789C8.53353%201.01497%208.45833%200.962565%208.38086%200.930664C8.30794%200.898763%208.23047%200.882812%208.14844%200.882812C7.97982%200.882812%207.83854%200.939779%207.72461%201.05371C7.61068%201.16309%207.55371%201.30208%207.55371%201.4707C7.55371%201.55273%207.56738%201.63249%207.59473%201.70996C7.62207%201.78288%207.66309%201.84668%207.71777%201.90137L9.24902%203.45996L11.9629%205.93457L12.0996%205.59277L9.89844%205.45605H1.25098C1.07324%205.45605%200.927409%205.51302%200.813477%205.62695C0.704102%205.74089%200.649414%205.88672%200.649414%206.06445C0.649414%206.24219%200.704102%206.38802%200.813477%206.50195C0.927409%206.61589%201.07324%206.67285%201.25098%206.67285H9.89844L12.0996%206.53613L11.9629%206.20117L9.24902%208.66895L7.71777%2010.2275C7.66309%2010.2822%207.62207%2010.3483%207.59473%2010.4258C7.56738%2010.4987%207.55371%2010.5762%207.55371%2010.6582C7.55371%2010.8268%207.61068%2010.9658%207.72461%2011.0752C7.83854%2011.1891%207.97982%2011.2461%208.14844%2011.2461C8.3125%2011.2461%208.46061%2011.1823%208.59277%2011.0547L13.1387%206.52246C13.2754%206.3903%2013.3438%206.23763%2013.3438%206.06445Z%22%20fill%3D%22%23A5B2BC%22%2F%3E%3C%2Fsvg%3E');
    margin-right: 8px;
  }

  &:first-child:before {
    display: none;
  }

  &[data-status='active'] {
    color: #222;
  }

  &[data-status='clickable'] {
    color: var(--color-primary);
    cursor: pointer;
  }

  &[data-status='available'] {
    color: var(--color-primary);
  }
`;
