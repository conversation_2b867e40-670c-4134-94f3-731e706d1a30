import React, { useState, VFC } from 'react';
import { styled } from 'linaria/react';
import { css } from 'linaria';

import { useTranslate } from 'utils/use-translate';

import Checkbox from 'components/ui/Checkbox/Checkbox';
import { SelectBox } from 'components/ui/CustomDropdown';
import { CustomDropdownOption } from 'components/ui/CustomDropdown/types';
import {
  ApiDataFieldOperations,
  ApiDataFieldOperationType,
} from 'features/files/by-machinery/types';
import { useSelector } from 'react-redux';
import entities from '../../../../../../../../modules/entities';

const CellStyle = styled.div`
  display: flex;
  justify-content: start;
  align-items: center;
  min-height: 42px;
  min-width: 250px;

  .form-select__value {
    font-size: 14px;
  }
`;
const CellTextStyle = styled.span`
  font-size: 14px;
  padding-left: 14px;
  padding-right: 28px;
`;

const dropDown = css`
  width: 100%;
`;

export type CellOperationProps = {
  hovered: boolean;
  childrenOperations?: ApiDataFieldOperations[];
  operation: ApiDataFieldOperations;
  setType?: (
    operation: ApiDataFieldOperations,
    type: Nullable<ApiDataFieldOperationType>,
  ) => void;
  setChecked?: (operation: ApiDataFieldOperations, is_checked: boolean) => void;
};

export const CellOperation: VFC<CellOperationProps> = props => {
  const { t } = useTranslate();
  const {
    operation,
    setType,
    hovered,
    setChecked,
    childrenOperations = [],
  } = props;
  const layer = useSelector(state =>
    entities.selectors.getByID(
      state,
      'fieldsDataLayer',
      operation.data_source.layer.uuid,
    ),
  );
  const [isOpen, setOpen] = useState<boolean>(false);

  const options: CustomDropdownOption[] = Object.values(
    ApiDataFieldOperationType,
  ).map(str => ({
    id: str,
    label: t(
      `import.matching_operations.operation.${str.toLowerCase()}`,
    ) as string,
  }));
  const isChecked =
    operation.is_enabled || childrenOperations.some(i => i.is_enabled);

  const disabled = operation.type && layer.source.driver === 'ADAPT';

  if (disabled || (!hovered && !isOpen)) {
    return (
      <CellStyle>
        <Checkbox
          checked={isChecked}
          disabled={operation.type === null}
          onChange={() => setChecked?.(operation, !operation.is_enabled)}
        />
        <CellTextStyle>
          {' '}
          {t(
            `import.matching_operations.operation.${
              operation.type?.toLowerCase() || 'null'
            }`,
          ) || (t('files.types.select') as string)}
        </CellTextStyle>
      </CellStyle>
    );
  }

  return (
    <CellStyle>
      <Checkbox
        checked={isChecked}
        disabled={operation.type === null}
        onChange={() => setChecked?.(operation, !operation.is_enabled)}
      />
      <SelectBox
        options={options}
        value={operation.type}
        wrapperClassName={dropDown}
        onVisibilityToggle={setOpen}
        onChange={selectedValue => {
          setType?.(operation, selectedValue as ApiDataFieldOperationType);
        }}
        placeholder='import.matching_operations.operation.null'
        searchPlaceholder='import.matching.attributes.select_search'
      />
    </CellStyle>
  );
};
