import React, { VFC } from 'react';

type LegendBarProps = {
  from?: Nullable<string>;
  to?: Nullable<string>;
  colors: string[];
};

const LegendBar: VFC<LegendBarProps> = ({ from, to, colors }) => (
  <div className='map-legend-scale' data-from={from} data-to={to}>
    {colors.map(color => (
      <div
        key={color}
        className='map-legend-scale__item'
        style={{ backgroundColor: color }}
      />
    ))}
  </div>
);

export default LegendBar;
