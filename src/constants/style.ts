import chroma from 'chroma-js';
import range from 'lodash/range';

const ColorPalette = ['#E80606', '#FAF130', '#16C003'];

export const BarsCount = 18;

export const ColorScale = chroma.scale(ColorPalette);

export const colors = {
  ndviPlantsLegend: [
    '#422112',
    '#7F4020',
    '#B76135',
    '#C6974E',
    '#E6C957',
    '#FDFE03',
    '#E6EC06',
    '#D0DF00',
    '#B9CF02',
    '#A2C000',
    '#8AAF00',
    '#72A000',
    '#5B8E03',
    '#458100',
    '#2D7000',
    '#25602D',
    '#15542D',
    '#15442D',
  ],
  ndviWaterCloudsLegend: ['#D5D5D5', '#6DAAF6'],
  phosphorLegend: ['#97B5F3', '#5E8AE8', '#245DDD'],
  nitrogenLegend: ['#ebed8c', '#9dc674', '#56a05c'],
  cloudsLegend: ['#979797'],
  sowingLegend: ['#FFD5BC', '#FF6CAB', '#932A91'],
  overlapColor: '#9FB6CA',
  undercoverColor: '#EDEDED',
  colorPaletteGradient: range(0, 1, 0.05).map(v => ColorScale(v).hex()),
  cropColors: [
    '#FF3B30',
    '#007AFF',
    '#F4D359',
    '#27AE60',
    '#FF4FE9',
    '#FD9103',
    '#02BAFF',
    '#9B7939',
    '#6337FF',
    '#A5B2BC',
    '#FF8551',
    '#80B1FF',
    '#DDFF1A',
    '#53FF4A',
    '#C63867',
    '#44FFF0',
    '#B64FDF',
    '#DBDAB9',
    '#C09FFD',
    '#45436C',
  ],
  defaultBorder: '#FFE767',
  multipleValues: '#222222',
  noValues: '#ffffff',
  transparent: 'transparent',
  selectedBorder: '#ffffff',
};

export const LegendColorsMap = {
  operations: chroma
    .scale(['#CF2F1F', '#E78037', '#FEFB54', '#51B165', '#3C8575'])
    .mode('rgb'),
  yield_value: chroma
    .scale([
      '#8bb4ca',
      '#73a4bf',
      '#5792b3',
      '#4284a9',
      '#23709b',
      '#055d8d',
      '#026181',
      '#046878',
      '#05706c',
      '#077b5c',
      '#0a8451',
      '#0c8e44',
    ])
    .mode('rgb'),
  avg_ndvi: chroma
    .scale([
      '#422112',
      '#7F4020',
      '#B76135',
      '#C6974E',
      '#E6C957',
      '#FDFE03',
      '#E6EC06',
      '#D0DF00',
      '#B9CF02',
      '#A2C000',
      '#8AAF00',
      '#72A000',
      '#5B8E03',
      '#458100',
      '#2D7000',
      '#25602D',
      '#15542D',
      '#15442D',
    ])
    .mode('rgb'),
  std_ndvi: chroma
    .scale([
      '#0B8877',
      '#07B55E',
      '#3FE10E',
      '#BDF100',
      '#FEFF03',
      '#FFBD38',
      '#FF771C',
      '#EE0200',
      '#C50465',
    ])
    .mode('rgb'),
  last_ndvi_default: chroma.scale(),
  last_ndvi_contrasted: chroma.scale(),
  sowing_date: chroma.scale(['#FEFFC0', '#1B5800']).mode('rgb'),
  harvest_date: chroma.scale(['#FFF5DA', '#D24B1D']).mode('rgb'),
  field_ndvi: chroma
    .scale([
      '#350801',
      '#7E1805',
      '#AF3A03',
      '#ECB225',
      '#FCD731',
      '#FEE85F',
      '#FEFE00',
      '#F2F900',
      '#C4E700',
      '#97D500',
      '#5FC100',
      '#379F00',
      '#1C8300',
      '#0B6400',
      '#064B0A',
      '#033A0F',
      '#02310C',
      '#02310C',
    ])
    .mode('rgb'),
  contrasted_ndvi: chroma
    .scale([
      '#960850',
      '#EE0200',
      '#FF771C',
      '#FFBD38',
      '#FEFF03',
      '#BDF100',
      '#3FE10E',
      '#07B55E',
      '#0B8877',
    ])
    .mode('rgb'),
  brightness_map: chroma.scale(['#D2DC51', '#6FAA7B', '#402464']).mode('rgb'),
  elevation_map: chroma.scale(['#491E3C', '#D8572E', '#F0D893']).mode('rgb'),
};

export const LegendChartStylePadding = {
  left: 0,
  top: 20,
  right: 40,
  bottom: 15,
};

export const LegendChartVerticalAxisStyle = {
  axis: { strokeWidth: 0 },
  axisLabel: { fontSize: 0, padding: 0 },
  grid: {
    strokeWidth: 1,
    stroke: 'rgba(255, 255, 255, .1)',
    strokeDasharray: 'none',
  },
  ticks: { size: 0, strokeWidth: 0 },
  tickLabels: { fontSize: 10, padding: 15, fill: '#EBEBEB' },
};
export const LegendChartHorizontalAxisStyle = {
  axis: { strokeWidth: 0 },
  axisLabel: { fontSize: 0, padding: 0 },
  grid: { strokeWidth: 0 },
  ticks: { size: 0, strokeWidth: 0 },
  tickLabels: {
    fontSize: 10,
    padding: 6,
    fill: '#EBEBEB',
    fontFamily: 'Roboto, Arial, sans-serif',
  },
};

export const LegendChartTooltipStyle = {
  fill: '#ffffff',
  fontFamily: 'Roboto, Arial, sans-serif',
  fontSize: 10,
};

export const LegendChartFlyoutStyle = {
  fill: '#303437',
};
