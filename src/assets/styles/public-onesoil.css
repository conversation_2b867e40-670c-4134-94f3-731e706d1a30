.ico-arrow-left-os {
  width: 11px;
  height: 10px;
}
.ico-arrow-long-left-os {
  width: 14px;
  height: 12px;
}
.ico-arrow-right-os {
  width: 11px;
  height: 10px;
}
.ico-calendar-os {
  width: 15px;
  height: 12px;
}
.ico-check-os {
  width: 18px;
  height: 14px;
}
.ico-chevron-down-os {
  width: 16px;
  height: 9px;
}
.ico-chevron-down-small-os {
  width: 8px;
  height: 5px;
}
.ico-chevron-down-big-os {
  width: 14px;
  height: 8px;
}
.ico-clear-os {
  width: 16px;
  height: 16px;
}
.ico-close-os {
  width: 12px;
  height: 12px;
}
.ico-graph-os {
  width: 16px;
  height: 16px;
}
.ico-infomsg-os {
  width: 16px;
  height: 16px;
}
.ico-loader-os {
  width: 16px;
  height: 16px;
}
.ico-location-os {
  width: 15px;
  height: 15px;
}
.ico-minus-os {
  width: 14px;
  height: 2px;
}
.ico-pin-os {
  width: 15px;
  height: 18px;
}
.ico-plus-os {
  width: 12px;
  height: 12px;
}
.ico-plus-small-os {
  width: 8px;
  height: 8px;
}
.ico-attach-os {
  width: 12px;
  height: 12px;
}
.ico-chevron-left-os {
  width: 8px;
  height: 12px;
}
.ico-chevron-right-os {
  width: 8px;
  height: 12px;
}
.ico-download-os {
  width: 16px;
  height: 16px;
}
.ico-question-os {
  width: 14px;
  height: 14px;
}
.ico-pencil-os {
  width: 10px;
  height: 11px;
}
.ico-trash-os {
  width: 7px;
  height: 10px;
}
.ico-trash-n-os {
  width: 10px;
  height: 13px;
}
.ico-trash-light-os {
  width: 14px;
  height: 16px;
}
.ico-search-os {
  width: 15px;
  height: 15px;
}
.ico-refresh-os {
  width: 15px;
  height: 14px;
}
.ico-arrow-down-os {
  width: 8px;
  height: 9px;
}
.ico-arrow-up-os {
  width: 8px;
  height: 9px;
}
.ico-copy-os {
  width: 12px;
  height: 12px;
}
.ico-visible-os {
  width: 16px;
  height: 12px;
}
.ico-lock-os {
  width: 12px;
  height: 15px;
}
.ico-revert-os {
  width: 21px;
  height: 16px;
}
.ico-select-os {
  width: 16px;
  height: 20px;
}
.ico-cut-os {
  width: 18px;
  height: 16px;
}
.ico-ruler-os {
  width: 21px;
  height: 21px;
}
.ico-filters-os {
  width: 12px;
  height: 12px;
}
/*! normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css */

/* Document
   ========================================================================== */

/**
 * 1. Correct the line height in all browsers.
 * 2. Prevent adjustments of font size after orientation changes in iOS.
 */

html {
  line-height: 1.15; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
}

/* Sections
   ========================================================================== */

/**
 * Remove the margin in all browsers.
 */

body {
  margin: 0;
}

/**
 * Render the `main` element consistently in IE.
 */

main {
  display: block;
}

/**
 * Correct the font size and margin on `h1` elements within `section` and
 * `article` contexts in Chrome, Firefox, and Safari.
 */

h1 {
  font-size: 2em;
  margin: 0.67em 0;
}

/* Grouping content
   ========================================================================== */

/**
 * 1. Add the correct box sizing in Firefox.
 * 2. Show the overflow in Edge and IE.
 */

hr {
  box-sizing: content-box; /* 1 */
  height: 0; /* 1 */
  overflow: visible; /* 2 */
}

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */

pre {
  font-family: monospace, monospace; /* 1 */
  font-size: 1em; /* 2 */
}

/* Text-level semantics
   ========================================================================== */

/**
 * Remove the gray background on active links in IE 10.
 */

a {
  background-color: transparent;
}

/**
 * 1. Remove the bottom border in Chrome 57-
 * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.
 */

abbr[title] {
  border-bottom: none; /* 1 */
  text-decoration: underline; /* 2 */
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted; /* 2 */
}

/**
 * Add the correct font weight in Chrome, Edge, and Safari.
 */

b,
strong {
  font-weight: bolder;
}

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */

code,
kbd,
samp {
  font-family: monospace, monospace; /* 1 */
  font-size: 1em; /* 2 */
}

/**
 * Add the correct font size in all browsers.
 */

small {
  font-size: 80%;
}

/**
 * Prevent `sub` and `sup` elements from affecting the line height in
 * all browsers.
 */

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/* Embedded content
   ========================================================================== */

/**
 * Remove the border on images inside links in IE 10.
 */

img {
  border-style: none;
}

/* Forms
   ========================================================================== */

/**
 * 1. Change the font styles in all browsers.
 * 2. Remove the margin in Firefox and Safari.
 */

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-size: 100%; /* 1 */
  line-height: 1.15; /* 1 */
  margin: 0; /* 2 */
}

/**
 * Show the overflow in IE.
 * 1. Show the overflow in Edge.
 */

button,
input { /* 1 */
  overflow: visible;
}

/**
 * Remove the inheritance of text transform in Edge, Firefox, and IE.
 * 1. Remove the inheritance of text transform in Firefox.
 */

button,
select { /* 1 */
  text-transform: none;
}

/**
 * Correct the inability to style clickable types in iOS and Safari.
 */

button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
}

/**
 * Remove the inner border and padding in Firefox.
 */

button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

/**
 * Restore the focus styles unset by the previous rule.
 */

button:-moz-focusring,
[type="button"]:-moz-focusring,
[type="reset"]:-moz-focusring,
[type="submit"]:-moz-focusring {
  outline: 1px dotted ButtonText;
}

/**
 * Correct the padding in Firefox.
 */

fieldset {
  padding: 0.35em 0.75em 0.625em;
}

/**
 * 1. Correct the text wrapping in Edge and IE.
 * 2. Correct the color inheritance from `fieldset` elements in IE.
 * 3. Remove the padding so developers are not caught out when they zero out
 *    `fieldset` elements in all browsers.
 */

legend {
  box-sizing: border-box; /* 1 */
  color: inherit; /* 2 */
  display: table; /* 1 */
  max-width: 100%; /* 1 */
  padding: 0; /* 3 */
  white-space: normal; /* 1 */
}

/**
 * Add the correct vertical alignment in Chrome, Firefox, and Opera.
 */

progress {
  vertical-align: baseline;
}

/**
 * Remove the default vertical scrollbar in IE 10+.
 */

textarea {
  overflow: auto;
}

/**
 * 1. Add the correct box sizing in IE 10.
 * 2. Remove the padding in IE 10.
 */

[type="checkbox"],
[type="radio"] {
  box-sizing: border-box; /* 1 */
  padding: 0; /* 2 */
}

/**
 * Correct the cursor style of increment and decrement buttons in Chrome.
 */

[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
  height: auto;
}

/**
 * 1. Correct the odd appearance in Chrome and Safari.
 * 2. Correct the outline style in Safari.
 */

[type="search"] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/**
 * Remove the inner padding in Chrome and Safari on macOS.
 */

[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

/**
 * 1. Correct the inability to style clickable types in iOS and Safari.
 * 2. Change font properties to `inherit` in Safari.
 */

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/* Interactive
   ========================================================================== */

/*
 * Add the correct display in Edge, IE 10+, and Firefox.
 */

details {
  display: block;
}

/*
 * Add the correct display in all browsers.
 */

summary {
  display: list-item;
}

/* Misc
   ========================================================================== */

/**
 * Add the correct display in IE 10+.
 */

template {
  display: none;
}

/**
 * Add the correct display in IE 10.
 */

[hidden] {
  display: none;
}

html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
}
* {
  box-sizing: border-box;
}
*:after,
*:before {
  box-sizing: inherit;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
html {
  font-size: 16px;
}
body {
  color: #222;
  font: 14px/1.45 'Graphik', sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  background-color: white;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
td,
th {
  font: 1em/1.45 'Graphik', sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
}
input,
textarea,
option,
select,
button {
  color: #222;
  font: 1em 'Graphik', sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
}
strong,
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 500;
}
[role="button"],
button {
  cursor: pointer;
}
a {
  color: #007aff;
  text-decoration: none;
}
a:hover {
  color: #014e86;
}
.hs-title {
  font-size: 18px;
  font-weight: 700;
  line-height: 1.17;
  margin-top: 0;
  margin-bottom: 5px;
}
.page-container {
  position: relative;
  height: 100%;
}
.page-panels {
  display: flex;
  visibility: visible !important;
}
.page-panels .map-container {
  position: relative;
  z-index: 3;
  flex: 2 1 auto;
  -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
  will-change: auto;
}
.global-notification-true .soil-sidebar {
  margin-top: 50px;
}
.global-notification-true .map-controls {
  margin-top: 50px;
}
.global-notification-true .top-toolbar,
.global-notification-true .map-search {
  -webkit-transform: translate(0, 50px);
          transform: translate(0, 50px);
}
.global-notification-true .main-section {
  margin-top: 50px;
}
.main-container {
  display: flex;
  flex-direction: column;
  border-bottom-left-radius: 8px;
  border-top-left-radius: 8px;
  box-shadow: -2px 0 20px rgba(0,0,0,0.07);
  position: relative;
  overflow: hidden;
  flex: 1 1 auto;
  min-width: 0;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
}
.main-container__content {
  display: flex;
  flex: 1 1 auto;
  min-height: 0;
}
.main-container-notification {
  position: absolute;
  left: 0;
  right: 0;
  z-index: 93;
  height: 50px;
  color: #fff;
  background-color: #222;
  -webkit-transform: translate(0, -50px);
          transform: translate(0, -50px);
  transition: -webkit-transform 0.15s linear;
  transition: transform 0.15s linear;
  transition: transform 0.15s linear, -webkit-transform 0.15s linear;
  text-align: center;
  display: flex;
  border-top-left-radius: 8px;
}
.main-container-notification__content {
  margin: auto;
  width: 100%;
  padding: 5px 20px;
  font-weight: 500;
  font-size: 14px;
}
.main-container-notification[data-type="success"] {
  background-color: #27ae60;
}
.main-container-notification[data-type="warning"] {
  background-color: #eb924e;
}
.main-container-notification[data-type="error"] {
  background-color: #eb4e4e;
}
.main-container-notification[data-position="bottom"] {
  bottom: 0;
  -webkit-transform: translate(0, 50px);
          transform: translate(0, 50px);
  border-top-left-radius: 0;
  border-bottom-left-radius: 8px;
}
.main-container-notification[data-radius="none"] {
  border-radius: 0;
}
.main-container-notification.__state-normal {
  -webkit-transform: translate(0, 0);
          transform: translate(0, 0);
}
.main-container-notification-actions {
  display: flex;
  align-items: center;
  justify-content: center;
}
.main-container-notification-actions__ico {
  margin-right: 5px;
  flex: 0 0 auto;
}
.main-container-notification-actions__action {
  margin-left: 8px;
  flex: 0 0 auto;
}
.app-nav-back {
  width: 20px;
  height: 20px;
  border-width: 0;
  font-size: 0;
  line-height: 2px;
  vertical-align: top;
  color: #222;
  padding: 4px;
  background: none;
}
.app-nav-back .ico-arrow-left-os {
  width: 14px;
  height: 12px;
}
.app-nav-edit {
  width: 20px;
  border-width: 0;
  font-size: 0;
  line-height: 2px;
  vertical-align: top;
  color: #222;
  padding: 3px 0 4px;
  background: none;
}
.app-nav-edit svg {
  vertical-align: top;
  width: 13px;
  height: 13px;
}
.app-nav-action {
  width: 20px;
  height: 20px;
  border-width: 0;
  font-size: 0;
  line-height: 2px;
  vertical-align: top;
  color: #222;
  padding: 4px;
  background: none;
}
.app-nav-action__item {
  position: relative;
  display: block;
  width: 7px;
  height: 2px;
  background-color: currentColor;
  transition: width 0.15s;
  border-radius: 1px;
}
.app-nav-action__item:before {
  position: absolute;
  content: '';
  background-color: currentColor;
  height: 2px;
  left: 0;
  width: 12px;
  top: -4px;
  border-radius: 1px;
}
.app-nav-action__item:after {
  position: absolute;
  content: '';
  background-color: currentColor;
  height: 2px;
  left: 0;
  width: 10px;
  bottom: -4px;
  transition: width 0.15s;
  border-radius: 1px;
}
.app-nav-action:hover .app-nav-action__item {
  width: 12px;
}
.app-nav-action:hover .app-nav-action__item:after {
  width: 12px;
}
.app-side-filter-select__item {
  cursor: pointer;
  font-size: 15px;
  line-height: 1.34;
  font-weight: 500;
  white-space: nowrap;
}
.app-side-filter-select__item:after {
  display: inline-block;
  content: '';
  background-image: url("data:image/svg+xml,%3Csvg id='SVGDoc' width='8' height='6' xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:avocode='https://avocode.com/' viewBox='0 0 8 6'%3E%3Cdefs%3E%3Cpath d='M476.00871,26.47568l-3.02759,-2.97568l-0.98112,0.9948l4.00871,4.0052l3.82458,-4.0052l-1.02186,-0.9948z' id='Path-0'/%3E%3C/defs%3E%3Cdesc%3EGenerated with Avocode.%3C/desc%3E%3Cg transform='matrix%281,0,0,1,-472,-23%29'%3E%3Cg clip-path='url%28%23clip-BD5FD9A2-7769-4AAA-B3ED-01F4362C9393%29'%3E%3Ctitle%3EShape%3C/title%3E%3Cuse xlink:href='%23Path-0' fill='%23222222' fill-opacity='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  width: 8px;
  height: 6px;
  vertical-align: top;
  margin-top: 8px;
  margin-left: 5px;
  transition: -webkit-transform 0.2s;
  transition: transform 0.2s;
  transition: transform 0.2s, -webkit-transform 0.2s;
}
.offer-internal {
  position: relative;
  border-radius: 6px;
  background-color: #ffea72;
  min-height: 80px;
  display: flex;
  align-items: center;
}
.offer-internal__pic {
  flex: 0 0 auto;
  border-bottom-left-radius: 6px;
  border-top-left-radius: 6px;
}
.offer-internal__close {
  position: absolute;
  border: none;
  padding: 0;
  position: absolute;
  right: 10px;
  top: 10px;
  background: none;
  color: rgba(34,34,34,0.3);
  font-size: 1px;
  line-height: 1;
}
.offer-internal__close svg {
  width: 9px;
  height: 9px;
}
.offer-internal__content {
  font-size: 12px;
  line-height: 1.25;
  padding: 10px 10px 10px 0;
}
.offer-internal__content p {
  margin-top: 0;
  margin-bottom: 4px;
}
.offer-internal__link {
  display: inline-block;
  border-radius: 4px;
  background-color: #fff;
  color: #222;
  font-size: 10px;
  font-weight: bold;
  line-height: 12px;
  letter-spacing: 0.5px;
  padding: 6px 10px;
}
.g-loader {
  display: inline-block;
}
.g-loader__inner {
  display: inline-block;
  -webkit-animation: spin 1s infinite linear;
          animation: spin 1s infinite linear;
  -webkit-transform-origin: 50%;
          transform-origin: 50%;
  width: 16px;
  height: 16px;
  vertical-align: top;
  background: url("../images/ic_loader_24.png") no-repeat 50%/contain;
}
.g-loader.green .g-loader__inner {
  background-image: url('../images/ic_loader_24_green.png');
}
.g-loader.white .g-loader__inner {
  background-image: url('../images/ic_loader_24_white.png');
}
.g-loader.large .g-loader__inner {
  width: 24px;
  height: 24px;
}
.page-app-welcome {
  background-color: #fff;
}
.app-welcome {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}
.app-welcome-close {
  color: #d8d8d8;
  position: absolute;
  right: 5px;
  top: 5px;
  padding: 15px;
  border: none;
  background: none;
  font-size: 1px;
  line-height: 1;
}
.app-welcome-close:hover {
  color: #808f9b;
}
.app-welcome-close .ico-close-os {
  width: 12px;
  height: 12px;
  vertical-align: top;
}
.app-welcome__inner {
  height: 100%;
  max-height: 640px;
  padding: 23px 0 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.app-welcome .logo {
  margin: 0 auto 20px;
  flex: 0 0 auto;
}
.app-welcome .logo:before {
  width: 115px;
  height: 38px;
}
.app-welcome__content {
  width: 230px;
  margin: 0 auto 14px;
  text-align: center;
  color: rgba(0,0,0,0.95);
  font-size: 17px;
  line-height: 1.36;
  min-height: 4.059em;
  flex: 0 0 auto;
}
.app-welcome__content p {
  margin-top: 0;
  margin-bottom: 0;
}
.app-welcome__image {
  width: 267px;
  flex: 1 1 auto;
  background: url("../images/app-visual.png") no-repeat 50% 0/100% auto;
}
.app-welcome__download {
  position: relative;
  flex: 0 0 auto;
  min-height: 97px;
  width: 100%;
  display: flex;
  align-items: center;
  text-align: center;
  justify-content: center;
}
.app-welcome__download:before {
  position: absolute;
  content: '';
  top: -0.5px;
  left: 0;
  right: 0;
  height: 1px;
  border-top: 0.5px solid rgba(216,216,216,0.75);
}
.app-welcome__download .btn-success {
  box-shadow: 0 4px 8px rgba(23,73,44,0.4);
  border-radius: 25px;
  min-width: 270px;
  min-height: 50px;
  padding-top: 15px;
  padding-bottom: 15px;
  font-size: 15px;
}
.app-welcome__download .btn-success .ico-download-os {
  width: 18px;
  height: 18px;
}
.page-app-closed {
  background-color: #fff;
}
.app-closed {
  position: relative;
  min-height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}
.app-closed__inner {
  position: relative;
  z-index: 4;
  text-align: center;
  padding: 0 20px 30px;
  font-size: 18px;
  line-height: 23px;
}
.app-closed__plantain {
  font-size: 67px;
  line-height: 1;
  margin-bottom: 14px;
}
.app-closed__title {
  font-size: 40px;
  font-weight: bold;
  line-height: 44px;
  margin-bottom: 14px;
}
.app-closed-nav {
  position: relative;
  z-index: 3;
  margin: 0;
}
.app-closed-nav__list {
  flex: 0 0 auto;
  margin: 0;
  padding: 0 0 17px;
  list-style-type: none;
  display: flex;
  justify-content: center;
}
.app-closed-nav__list-item {
  margin: 0 15px;
}
.app-closed-visual {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  max-height: 480px;
  height: 100%;
  width: 100%;
}
.app-closed-visual__inner {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 625px;
  overflow: hidden;
}
.app-closed-visual__field {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: hidden;
  pointer-events: none;
}
.app-closed-visual__field:after {
  position: absolute;
  z-index: 1;
  content: '';
  width: 494px;
  height: 617px;
  top: -8px;
  left: -8px;
  background-image: repeating-linear-gradient(-45deg, transparent, transparent 16.6px, #27ae60 16.6px, #27ae60 25.6px);
  background-position: 0 0;
}
.app-closed-visual__field:before {
  position: absolute;
  z-index: 2;
  content: '';
  width: 900px;
  height: 478px;
  top: 620px;
  left: -150px;
  background-color: #fff;
  -webkit-transform: rotate(-45deg);
          transform: rotate(-45deg);
  -webkit-transform-origin: 0 0;
          transform-origin: 0 0;
}
.app-closed-visual-car {
  position: absolute;
  z-index: 3;
  left: 42px;
  top: 139px;
}
.app-closed-visual-car__el {
  position: absolute;
  z-index: 3;
  width: 232px;
  height: 200px;
  top: 0;
  left: 0;
  -webkit-transform: rotate(-45deg);
          transform: rotate(-45deg);
  -webkit-transform-origin: 0 0;
          transform-origin: 0 0;
  transition: -webkit-transform 0.5s;
  transition: transform 0.5s;
  transition: transform 0.5s, -webkit-transform 0.5s;
}
.app-closed-visual-car__el.__anim {
  -webkit-transform: translate(-440px, 440px) rotate(-45deg);
          transform: translate(-440px, 440px) rotate(-45deg);
}
.app-closed-visual-car__el.__grow .app-closed-visual-car__bg {
  opacity: 0;
}
.app-closed-visual-car__el:before {
  position: absolute;
  z-index: 3;
  content: '';
  width: 232px;
  height: 200px;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 232 200' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg transform='translate(-79 -146)' fill='%23222' fill-rule='nonzero'%3E%3Cg transform='rotate(45 293.6457 14.8213)'%3E%3Cg transform='translate(240 140)'%3E%3Cpath d='M176.495942,55.196143 L208.130013,23.316135 L230.725778,46.0875693 L199.091707,77.9675773 L212.649166,91.6304378 L153.900177,159.944741 L95.1511878,100.739012 L162.938483,41.5332824 L176.495942,55.196143 Z M162.034652,58.8395725 L113.2278,100.739012 L153.900177,141.727593 L195.476384,92.5412952 L162.034652,58.8395725 Z M183.274671,62.0275733 L192.312977,71.136147 L217.168319,46.0875693 L208.130013,36.9789956 L183.274671,62.0275733 Z M87.0167125,174.518459 L153.900177,241.921904 L144.861871,251.030478 L4.76812788,109.847585 L13.8064339,100.739012 L80.6898983,168.142457 L110.516308,138.084164 L102.381833,129.886447 L108.708647,123.510446 L131.304412,146.28188 L124.977598,152.657882 L116.843122,144.460165 L87.0167125,174.518459 Z M131.304412,36.9789956 L140.342718,46.0875693 L95.1511878,91.6304378 L86.1128819,82.5218641 L131.304412,36.9789956 Z M208.130013,114.401872 L217.168319,123.510446 L171.976789,169.053314 L162.938483,159.944741 L208.130013,114.401872 Z M203.61086,0.544700781 L212.649166,9.65327448 L185.534248,36.9789956 L176.495942,27.8704219 L203.61086,0.544700781 Z M244.283237,41.5332824 L253.321543,50.6418562 L226.206625,77.9675773 L217.168319,68.8590036 L244.283237,41.5332824 Z M135.823565,251.030478 L131.304412,255.584764 L0.248974879,123.510446 L4.76812788,118.956159 L135.823565,251.030478 Z'/%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E") no-repeat 50%/contain;
}
.app-closed-visual-car__el:after {
  position: absolute;
  content: '';
  width: 50px;
  height: 19px;
  top: 0;
  left: -10px;
  background-color: #fff;
}
.app-closed-visual-car__bg {
  position: absolute;
  z-index: 2;
  top: 17px;
  left: -10px;
  width: 1300px;
  height: 200px;
  background-color: #fff;
  transition: opacity 1s;
  pointer-events: none;
}
@-webkit-keyframes spin {
  100% {
    -webkit-transform: rotate(-360deg);
            transform: rotate(-360deg);
  }
}
@keyframes spin {
  100% {
    -webkit-transform: rotate(-360deg);
            transform: rotate(-360deg);
  }
}
.form-group {
  margin-bottom: 15px;
}
.form-group-subgroup {
  margin-bottom: 5px;
}
.form-title {
  position: relative;
  font-weight: 500;
  padding-bottom: 6px;
  margin-bottom: 15px;
}
.form-title:after {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  content: '';
  height: 1px;
  border-bottom: 0.5px solid #d6dce1;
}
.form-content {
  line-height: 1.3;
}
.form-content p {
  margin-top: 0;
  margin-bottom: 10px;
}
.form-hr {
  margin: 15px 0;
  height: 1px;
  border: none;
  border-bottom: 0.5px solid rgba(165,178,188,0.45);
}
.form-hr.__alt {
  margin-bottom: 10px;
}
.form-hr.__mg {
  margin-left: -20px;
  margin-right: -20px;
  margin-top: 20px;
}
.form-hr.__mg-short {
  margin-left: -20px;
  margin-right: -20px;
}
.form-hr.__big {
  position: relative;
  background-color: rgba(233,236,239,0.5);
  height: 10px;
  margin-top: 20px;
  margin-left: -20px;
  margin-right: -20px;
  border-bottom: 0;
}
.form-hr.__big:before {
  position: absolute;
  content: '';
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  border-top: 0.5px solid #d6dce1;
}
.form-label {
  display: block;
  margin-bottom: 1px;
  font-size: 14px;
  font-weight: 500;
}
.form-label__quest {
  display: inline-block;
  vertical-align: top;
  margin-top: 2px;
  margin-left: 2px;
  margin-bottom: -2px;
  color: rgba(165,178,188,0.3);
  cursor: pointer;
}
.form-label__quest:hover {
  color: rgba(165,178,188,0.5);
}
.form-label__side {
  font-weight: normal;
  float: right;
}
.form-input {
  border-radius: 8px;
  padding: 9px 13px;
  vertical-align: top;
  margin: 0;
  font-size: 1rem;
  line-height: 1.25rem;
  border: 2px solid rgba(165,178,188,0.3);
  resize: vertical;
  outline: none;
  transition: border-color 0.1s, border-width 0.1s;
}
.form-input:hover {
  border-color: rgba(165,178,188,0.5);
}
.form-input:focus,
.form-input:active {
  border-color: #27ae60;
}
.form-input.__error {
  border-color: #d80606;
}
.form-input::-webkit-input-placeholder {
  color: #a5b2bc;
}
.form-input::-moz-placeholder {
  color: #a5b2bc;
}
.form-input:-ms-input-placeholder {
  color: #a5b2bc;
}
.form-input:-moz-placeholder {
  color: #a5b2bc;
}
.form-input[disabled] {
  color: #b7c1c9;
  border-color: rgba(165,178,188,0.2);
}
.form-input[disabled]::-webkit-input-placeholder {
  color: rgba(165,178,188,0.5);
}
.form-input[disabled]::-moz-placeholder {
  color: rgba(165,178,188,0.5);
}
.form-input[disabled]:-ms-input-placeholder {
  color: rgba(165,178,188,0.5);
}
.form-input[disabled]:-moz-placeholder {
  color: rgba(165,178,188,0.5);
}
.form-input.form-size-medium {
  font-size: 14px;
  line-height: 20px;
  min-height: 34px;
  padding-top: 5px;
  padding-bottom: 5px;
  border-radius: 6px;
}
.form-checkbox {
  cursor: pointer;
  display: inline-flex;
  vertical-align: top;
  font-size: 1rem;
  line-height: 1.25rem;
}
.form-checkbox__chk {
  position: absolute;
  left: -9999px;
  opacity: 0;
}
.form-checkbox__chk[type="checkbox"]:checked + .form-checkbox__el {
  background-color: #27ae60;
  border-width: 0;
}
.form-checkbox__chk[type="checkbox"]:checked + .form-checkbox__el:before {
  opacity: 1;
  -webkit-transform: translate(0, 0);
          transform: translate(0, 0);
}
.form-checkbox__chk[type="checkbox"]:disabled + .form-checkbox__el {
  background-color: rgba(165,178,188,0.2) !important;
  border-width: 0;
}
.form-checkbox__chk[type="checkbox"]:disabled + .form-checkbox__el:before {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 13 10' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cdefs%3E%3Cpath d='M49.59,636.08l-3.17,-3.17l-1.42,1.41l4.59,4.59l8,-8l-1.41,-1.41z' id='a'/%3E%3C/defs%3E%3Cg transform='translate(-45 -629)'%3E%3Cuse xlink:href='%23a' fill='%23C5CDD4'/%3E%3C/g%3E%3C/svg%3E");
}
.form-checkbox__chk[type="checkbox"]:checked:disabled + .form-checkbox__el:before {
  opacity: 1;
  -webkit-transform: translate(0, 0);
          transform: translate(0, 0);
}
.form-checkbox__chk[type="radio"] + .form-checkbox__el {
  border-radius: 20px;
}
.form-checkbox__chk[type="radio"] + .form-checkbox__el:after {
  position: absolute;
  content: '';
  left: 50%;
  top: 50%;
  margin-left: -4px;
  margin-top: -4px;
  -webkit-transform-origin: center;
          transform-origin: center;
  background-color: #fff;
  width: 8px;
  height: 8px;
  opacity: 0;
  border-radius: 50%;
  -webkit-transform: scale(2.5);
          transform: scale(2.5);
  transition: opacity 0.25s, -webkit-transform 0.25s;
  transition: transform 0.25s, opacity 0.25s;
  transition: transform 0.25s, opacity 0.25s, -webkit-transform 0.25s;
}
.form-checkbox__chk[type="radio"]:disabled + .form-checkbox__el {
  background-color: rgba(165,178,188,0.2) !important;
  border-width: 0;
}
.form-checkbox__chk[type="radio"]:checked:disabled + .form-checkbox__el:after {
  background-color: #c5cdd4;
}
.form-checkbox__chk[type="radio"]:checked + .form-checkbox__el {
  background-color: #27ae60;
  border-width: 0;
}
.form-checkbox__chk[type="radio"]:checked + .form-checkbox__el:after {
  opacity: 1;
  -webkit-transform: scale(1);
          transform: scale(1);
}
.form-checkbox__el {
  position: relative;
  width: 20px;
  height: 20px;
  border-radius: 5px;
  border: 2px solid rgba(165,178,188,0.3);
  flex: 0 0 auto;
  background-color: rgba(39,174,96,0);
  transition: opacity 0.15s, border-width 0.15s, border-color 0.15s, background-color 0.15s;
  margin-top: -1.5px;
}
.form-checkbox__el:before {
  position: absolute;
  content: '';
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 13 10' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cdefs%3E%3Cpath d='M49.59,636.08l-3.17,-3.17l-1.42,1.41l4.59,4.59l8,-8l-1.41,-1.41z' id='a'/%3E%3C/defs%3E%3Cg transform='translate(-45 -629)'%3E%3Cuse xlink:href='%23a' fill='%23fff'/%3E%3C/g%3E%3C/svg%3E");
  background-size: contain;
  background-position: 50%;
  width: 13px;
  height: 10px;
  opacity: 0;
  left: 0;
  margin: 5px 0 0 4px;
  -webkit-transform: translate(-2px, -7px);
          transform: translate(-2px, -7px);
  transition: opacity 0.15s, -webkit-transform 0.15s;
  transition: transform 0.15s, opacity 0.15s;
  transition: transform 0.15s, opacity 0.15s, -webkit-transform 0.15s;
}
.form-checkbox__value {
  padding-left: 6px;
  flex: 1 1 auto;
}
.form-checkbox__emoji {
  display: inline-flex;
  vertical-align: top;
  align-items: center;
  justify-content: center;
  height: 24px;
  margin: -2px 0;
}
.form-checkbox__emoji img {
  vertical-align: top;
}
.form-checkbox:hover .form-checkbox__el {
  border-color: rgba(165,178,188,0.7);
}
.form-checkbox:hover .form-checkbox__chk:checked + .form-checkbox__el {
  background-color: #239553;
}
.form-checkbox[data-legend="color-1"] .form-checkbox__chk:checked + .form-checkbox__el {
  background-color: #f4d359;
}
.form-checkbox[data-legend="color-1"]:hover .form-checkbox__chk:checked + .form-checkbox__el {
  background-color: #d3b442;
}
.form-checkbox[data-legend="color-2"] .form-checkbox__chk:checked + .form-checkbox__el {
  background-color: #72bbf5;
}
.form-checkbox[data-legend="color-2"]:hover .form-checkbox__chk:checked + .form-checkbox__el {
  background-color: #5ca4de;
}
.form-checkbox[data-legend="color-3"] .form-checkbox__chk:checked + .form-checkbox__el {
  background-color: #a955b8;
}
.form-checkbox[data-legend="color-3"]:hover .form-checkbox__chk:checked + .form-checkbox__el {
  background-color: #9440a3;
}
.form-checkbox[data-color="inherit"] .form-checkbox__el {
  border-color: currentColor;
  opacity: 0.5;
}
.form-checkbox[data-color="inherit"]:hover .form-checkbox__el {
  border-color: currentColor;
  opacity: 1;
}
.form-checkbox[data-color="inherit"] .form-checkbox__chk:checked + .form-checkbox__el {
  background-color: currentColor;
  opacity: 1;
}
.form-checkbox[data-color="inherit"]:hover .form-checkbox__chk:checked + .form-checkbox__el {
  background-color: currentColor;
  opacity: 1;
}
.form-tooglebox {
  background-color: #e9ecef;
  border-radius: 8px;
  padding: 4px;
  color: #222;
  font-size: 14px;
}
.form-tooglebox__inner {
  position: relative;
  display: flex;
}
.form-tooglebox__bg {
  position: absolute;
  z-index: 1;
  left: 0;
  top: 0;
  bottom: 0;
  width: 50%;
  border-radius: 6px;
  background-color: #fff;
  transition: -webkit-transform 0.15s;
  transition: transform 0.15s;
  transition: transform 0.15s, -webkit-transform 0.15s;
}
.form-tooglebox__item {
  cursor: pointer;
  position: relative;
  z-index: 2;
  flex: 50%;
  display: flex;
  height: 34px;
  align-items: center;
  text-align: center;
  padding: 0 10px;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}
.form-tooglebox__value {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1rem;
  max-height: 32px;
}
.form-tooglebox__input {
  position: absolute;
  left: -9999px;
  opacity: 0;
}
.form-tooglebox__input:checked + .form-tooglebox__item {
  font-weight: 500;
}
.form-tooglebox__input[data-input="last"]:checked ~ .form-tooglebox__bg {
  -webkit-transform: translate(100%, 0);
          transform: translate(100%, 0);
}
.form-tooglebox__bg {
  position: absolute;
}
.form-tooglebox[data-theme="dark"] {
  box-shadow: 0 5px 10px rgba(0,0,0,0.25);
  background-color: #222;
  color: #fff;
}
.form-tooglebox[data-theme="dark"] .form-tooglebox__bg {
  background-color: #383838;
}
.form-total {
  display: flex;
  font-size: 16px;
  font-weight: 500;
}
.form-total__label {
  flex: 1 1 auto;
  min-width: 0;
}
.form-total__value {
  flex: 0 0 auto;
}
.list-checkboxes {
  list-style-type: none;
  padding: 0;
  margin: 0;
}
.list-checkboxes__list-item {
  margin: 0 0 12px;
}
.form-label + .list-checkboxes {
  margin-top: 12px;
}
.form-split-group {
  display: flex;
  align-items: flex-end;
}
.form-split-group .form-select,
.form-split-group .form-input {
  border-radius: 0;
}
.form-split-group__col {
  flex: 0 0 auto;
}
.form-split-group__col.__fluid {
  flex: 1 1 auto;
  min-width: 0;
}
.form-split-group__col:first-child .form-select,
.form-split-group__col:first-child .form-input {
  border-bottom-left-radius: 8px;
  border-top-left-radius: 8px;
}
.form-split-group__col:last-child .form-select,
.form-split-group__col:last-child .form-input {
  border-bottom-right-radius: 8px;
  border-top-right-radius: 8px;
}
.form-input-clear {
  position: absolute;
  z-index: 3;
  right: 11px;
  top: 50%;
  width: 20px;
  height: 20px;
  background: rgba(165,178,188,0.25);
  border: none;
  padding: 0;
  margin: -10px 0 0;
  border-radius: 20px;
  font-size: 0;
  line-height: 0;
  display: none;
}
.form-input-clear:hover {
  background-color: rgba(165,178,188,0.5);
}
.form-input-clear:before {
  position: absolute;
  content: '';
  left: 50%;
  top: 50%;
  margin-left: -1px;
  margin-top: -6px;
  width: 2px;
  height: 12px;
  background-color: #fff;
  -webkit-transform: rotate(-45deg);
          transform: rotate(-45deg);
}
.form-input-clear:after {
  position: absolute;
  content: '';
  left: 50%;
  top: 50%;
  margin-left: -1px;
  margin-top: -6px;
  width: 2px;
  height: 12px;
  background-color: #fff;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
}
.form-input-clear[data-size="small"] {
  width: 16px;
  height: 16px;
  margin-top: -8px;
}
.form-input-clear[data-size="small"]:after {
  -webkit-transform: rotate(0);
          transform: rotate(0);
  width: 8px;
  height: 8px;
  margin: -4px 0 0 -4px;
  background: url("data:image/svg+xml,%3Csvg width='8' height='8' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cpath d='M95.76408,13.236 L95.76408,13.236 C95.4517077,12.9236277 94.9452523,12.9236277 94.63288,13.236 L92.00008,15.8688 L89.36728,13.236 C89.0549077,12.9236277 88.5484523,12.9236277 88.23608,13.236 L88.23608,13.236 C87.9237077,13.5483723 87.9237077,14.0548277 88.23608,14.3672 L90.86888,17 L88.23608,19.6328 C87.9237077,19.9451723 87.9237077,20.4516277 88.23608,20.764 L88.23608,20.764 C88.5484523,21.0763723 89.0549077,21.0763723 89.36728,20.764 L92.00008,18.1312 L94.63288,20.764 C94.9452523,21.0763723 95.4517077,21.0763723 95.76408,20.764 L95.76408,20.764 C96.0764523,20.4516277 96.0764523,19.9451723 95.76408,19.6328 L93.13128,17 L95.76408,14.3672 C96.0764523,14.0548277 96.0764523,13.5483723 95.76408,13.236 Z' transform='translate(-88 -13)' fill='%23ffffff' fill-rule='evenodd'/%3E%3C/svg%3E");
}
.form-input-clear[data-size="small"]:before {
  display: none;
}
.form-input-clear[data-border="true"]:before {
  position: absolute;
  content: '';
  display: block;
  left: -11px;
  margin-top: -14px;
  pointer-events: none;
  width: 1px;
  height: 28px;
  -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
  background-color: rgba(214,220,225,0.5);
}
.form-photos {
  display: flex;
  flex-wrap: wrap;
  margin-left: -5px;
}
.form-photos__list-item {
  flex: 1.5;
  min-width: 33%;
  margin-bottom: 5px;
  height: 83px;
  display: flex;
  padding-left: 5px;
}
.form-photos__list-item:only-child {
  height: 146px;
}
.form-photos__pic {
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 6px;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: 50%;
}
.form-photos__item {
  position: relative;
  background-color: rgba(214,220,225,0.2);
  border-radius: 6px;
  width: 100%;
  cursor: pointer;
}
.form-photos__item:before {
  position: absolute;
  z-index: 2;
  content: '';
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.7);
  border-radius: 6px;
  opacity: 0;
}
.form-photos__item:hover:before {
  opacity: 1;
}
.form-photos__item:hover .form-photos__remove {
  opacity: 1;
}
.form-photos__item[data-status="loading"]:before {
  opacity: 1;
}
.form-photos__item[data-status="loading"] .form-photos__remove {
  opacity: 1;
}
.form-photos-progress {
  position: absolute;
  z-index: 3;
  width: 40px;
  height: 40px;
  top: 50%;
  left: 50%;
  margin-top: -20px;
  margin-left: -20px;
  vertical-align: top;
  -webkit-transform: rotate(-90deg);
          transform: rotate(-90deg);
}
.form-photos-progress__value {
  fill: none;
  stroke: #fff;
  stroke-linecap: round;
  -webkit-transform-origin: center;
          transform-origin: center;
}
.form-photos__remove {
  position: absolute;
  z-index: 4;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: none;
  color: #fff;
  font-size: 0;
  background: none;
  opacity: 0;
  width: 100%;
}
.form-photos__remove:before {
  position: absolute;
  width: 20px;
  height: 20px;
  top: 50%;
  left: 50%;
  margin-left: -10px;
  margin-top: -10px;
  content: '';
  background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10 20C4.477 20 0 15.523 0 10S4.477 0 10 0s10 4.477 10 10-4.477 10-10 10m5.412-13.998l-1.414-1.414L10 8.586 6.002 4.588 4.588 6.002 8.586 10l-3.998 3.998 1.414 1.414L10 11.414l3.998 3.998 1.414-1.414L11.414 10z' fill='%23fff'/%3E%3C/svg%3E");
}
.form-input-extended {
  position: relative;
}
.form-input-extended[data-prepend="ico"] .form-input {
  padding-left: 35px;
}
.form-input-extended__ico {
  position: absolute;
  top: 50%;
  left: 2px;
  margin-top: -19px;
  width: 35px;
  height: 38px;
  line-height: 38px;
  text-align: center;
  display: flex;
}
.form-input-extended__ico img {
  vertical-align: top;
  margin: auto;
}
.form-input-extended.__filled .form-input-clear {
  display: block;
}
.form-input-extended.__filled .form-input {
  padding-right: 40px;
}
.form-group-grid {
  display: flex;
}
.form-group-grid__col {
  flex: 0 0 auto;
}
.form-group-grid__col.__fluid {
  flex: 1 1 auto;
  min-width: 0;
}
.form-group-grid__col.__calendarlabel {
  width: 22px;
  align-self: center;
}
.form-group-grid.__sps-default {
  margin-left: -10px;
}
.form-group-grid.__sps-default .form-group-grid__col {
  padding-left: 10px;
}
.form-group-grid.__sps-medium {
  margin-left: -15px;
}
.form-group-grid.__sps-medium .form-group-grid__col {
  padding-left: 15px;
}
.form-append {
  position: relative;
}
.form-append:after {
  position: absolute;
  pointer-events: none;
  top: 2px;
  bottom: 2px;
  right: 2px;
  content: attr(data-appendix);
  color: #a5b2bc;
  white-space: nowrap;
  padding: 9px 13px 9px 3px;
  background-color: rgba(255,255,255,0.9);
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
  font-size: 1rem;
  line-height: 1.25rem;
}
.form-append--disabled:after {
  color: rgba(165,178,188,0.5);
}
.form-group-items-header {
  display: flex;
  padding-bottom: 6px;
}
.form-group-items-header__col {
  flex: 0 0 auto;
  font-size: 12px;
  font-weight: 500;
}
.form-group-items-header__col.__fluid {
  flex: 1 1 auto;
  min-width: 0;
}
.form-group-items-row {
  padding-bottom: 5px;
  display: flex;
}
.form-group-items-row__col {
  flex: 0 0 auto;
  border-left: 0.5px solid #c1cbd4;
}
.form-group-items-row__col.__fluid {
  flex: 1 1 auto;
  min-width: 0;
}
.form-group-items-row__col:first-child {
  border-left-width: 0;
}
.form-group-items-row__col:first-child .form-select,
.form-group-items-row__col:first-child .form-input {
  border-bottom-left-radius: 6px;
  border-top-left-radius: 6px;
}
.form-group-items-row__col:last-child .form-select,
.form-group-items-row__col:last-child .form-input {
  border-bottom-right-radius: 6px;
  border-top-right-radius: 6px;
}
.form-group-items .form-select,
.form-group-items .form-input {
  border-radius: 0;
}

.pagination {
  display: inline-flex;
  min-height: 42px;
}
.pagination__item {
  background-color: #e9ecef;
  box-sizing: content-box;
  min-width: 42px;
  text-align: center;
  flex: 0 0 auto;
  color: #222;
  display: flex;
  align-items: center;
  justify-content: center;
}
.pagination__item:first-child {
  border-bottom-left-radius: 6px;
  border-top-left-radius: 6px;
}
.pagination__item:last-child {
  border-bottom-right-radius: 6px;
  border-top-right-radius: 6px;
}
.pagination__item + .pagination__item {
  border-left: 1px solid #d2d4d9;
}
.pagination__item + .pagination__item.__disabled {
  border-left-color: rgba(210,212,217,0.75);
}
.pagination__item:hover {
  background-color: #d5dbdf;
}
.pagination__item.__disabled {
  color: #c5cdd4;
  pointer-events: none;
  background-color: #f5f7f9;
}
.pagination__item.__disabled:hover {
  background-color: #f5f7f9;
}
.pagination-arrow-back {
  margin: 0 auto;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  border-right: 4px solid currentColor;
  width: 0;
  height: 0;
  display: inline-block;
  vertical-align: top;
}
.pagination-arrow-forward {
  margin: 0 auto;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  border-left: 4px solid currentColor;
  width: 0;
  height: 0;
  display: inline-block;
  vertical-align: top;
}
.pagination-small {
  min-height: 34px;
}
.pagination-small .pagination-arrow-back,
.pagination-small .pagination-arrow-forward {
  min-width: 34px;
}
.tags-list {
  display: flex;
  flex-wrap: wrap;
  padding: 0;
  margin: 0 0 -5px -5px;
  list-style-type: none;
}
.tags-list__list-item {
  margin-left: 5px;
  margin-bottom: 5px;
  flex: 0 0 auto;
}
.tags-list__item {
  display: flex;
  border-radius: 17px;
  border: 1px solid rgba(165,178,188,0.5);
  line-height: 1.25rem;
  padding: 7px 8px 5px 14px;
  align-items: center;
  background: none;
}
.tags-list__item[data-action="add"] {
  padding-left: 30px;
  padding-right: 16px;
  font-weight: 500;
  position: relative;
}
.tags-list__item[data-action="add"]:hover {
  color: #27ae60;
}
.tags-list__item[data-action="add"]:after {
  position: absolute;
  content: '';
  left: 17px;
  width: 8px;
  height: 2px;
  top: 50%;
  margin-top: -1px;
  background-color: currentColor;
}
.tags-list__item[data-action="add"]:before {
  position: absolute;
  content: '';
  left: 20px;
  top: 50%;
  margin-top: -4px;
  height: 8px;
  width: 2px;
  background-color: currentColor;
}
.tags-list__item[disabled],
.tags-list__item.__disabled {
  color: rgba(165,178,188,0.7);
  pointer-events: none;
  background-color: #f5f7f9;
  border-color: transparent;
}
.tags-list__value {
  flex: 0 0 auto;
  max-width: 300px;
  overflow: hidden;
  white-space: nowrap;
  position: relative;
  text-overflow: ellipsis;
}
.tags-list .form-input-clear {
  position: relative;
  top: auto;
  margin: -2px 0 0 5px;
  width: 16px;
  height: 16px;
  flex: 0 0 auto;
  display: block;
  right: auto;
  background-color: rgba(165,178,188,0.35);
}
.tags-list .form-input-clear:after {
  display: none;
}
.tags-list .form-input-clear:before {
  -webkit-transform: rotate(0);
          transform: rotate(0);
  width: 8px;
  height: 8px;
  margin: -4px 0 0 -4px;
  background: url("data:image/svg+xml,%3Csvg width='8' height='8' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cpath d='M95.76408,13.236 L95.76408,13.236 C95.4517077,12.9236277 94.9452523,12.9236277 94.63288,13.236 L92.00008,15.8688 L89.36728,13.236 C89.0549077,12.9236277 88.5484523,12.9236277 88.23608,13.236 L88.23608,13.236 C87.9237077,13.5483723 87.9237077,14.0548277 88.23608,14.3672 L90.86888,17 L88.23608,19.6328 C87.9237077,19.9451723 87.9237077,20.4516277 88.23608,20.764 L88.23608,20.764 C88.5484523,21.0763723 89.0549077,21.0763723 89.36728,20.764 L92.00008,18.1312 L94.63288,20.764 C94.9452523,21.0763723 95.4517077,21.0763723 95.76408,20.764 L95.76408,20.764 C96.0764523,20.4516277 96.0764523,19.9451723 95.76408,19.6328 L93.13128,17 L95.76408,14.3672 C96.0764523,14.0548277 96.0764523,13.5483723 95.76408,13.236 Z' transform='translate(-88 -13)' fill='%23ffffff' fill-rule='evenodd'/%3E%3C/svg%3E");
}
.tags-list .form-input-clear:hover {
  background-color: rgba(165,178,188,0.55);
}
.form-upload {
  position: relative;
  overflow: hidden;
  color: #007aff;
}
.form-upload:hover {
  color: #014e86;
}
.form-upload__input {
  font-size: 80px;
  cursor: pointer;
  position: absolute;
  right: 0;
  top: 0;
  opacity: 0;
}
.form-upload__label {
  font-size: 12px;
  font-weight: 500;
}
.form-upload__label svg {
  margin-top: 2px;
  vertical-align: top;
}
.u-select {
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.u-select__virtualselect {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border: none;
  background: none;
  opacity: 0;
  font-size: 14px;
}
.u-select__virtualselect option {
  font-size: 14px;
}
.nitro-table {
  border-bottom: 0.5px solid rgba(165,178,188,0.45);
  margin-left: -20px;
  margin-right: -20px;
}
.nitro-table table {
  width: 100%;
}
.nitro-table th {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.3;
  padding: 0 4px 15px 16px;
  text-align: left;
}
.nitro-table td {
  padding: 14px 4px 14px 16px;
  vertical-align: top;
  border-top: 0.5px solid #d6dce0;
  text-align: left;
}
.nitro-table tr td:last-child,
.nitro-table tr th:last-child {
  padding-right: 20px;
}
.nitro-table .bgc-entity {
  padding: 0;
}
.fields-table {
  margin-left: -20px;
  margin-bottom: -10px;
}
.fields-table__quest {
  color: rgba(165,178,188,0.3);
  display: inline-block;
  vertical-align: top;
  margin-top: 0;
  margin-bottom: -3px;
  cursor: pointer;
}
.fields-table table {
  width: 100%;
}
.fields-table table[data-layout="fixed"] {
  table-layout: fixed;
}
.fields-table table[data-valign="top"] td {
  padding-top: 8px;
  vertical-align: top;
}
.fields-table td {
  padding: 0 0 10px 20px;
  height: 52px;
  font-size: 16px;
  line-height: 1.25rem;
  vertical-align: middle;
}
.fields-table th {
  padding: 0 0 2px 20px;
  text-align: left;
  line-height: 1.063rem;
  font-weight: 500;
}
.fields-table tfoot td {
  vertical-align: top;
  height: auto;
}
.fields-table tfoot small {
  display: inline-block;
  font-size: 14px;
  line-height: 0.9375rem;
}
.fields-table-area {
  display: flex;
  align-items: center;
}
.fields-table-area__int {
  flex: 0 0 auto;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 6px;
}
.fields-table-area__value {
  flex: 1 1 auto;
}
.form-checkboxes-with-options {
  display: inline-flex;
}
.form-checkboxes-with-options__checkbox {
  flex: 1 1 auto;
  min-width: 0;
}
.form-checkboxes-with-options__checkbox .form-checkbox {
  max-width: 100%;
}
.form-checkboxes-with-options .form-checkbox__value {
  white-space: nowrap;
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
}
.form-checkboxes-with-options__option {
  flex: 0 0 auto;
  padding-left: 10px;
  font-size: 12px;
  padding-top: 3px;
}
.form-enter-checkboxes {
  list-style-type: none;
  padding: 0;
  margin: 0 0 40px -15px;
  display: flex;
}
.form-enter-checkboxes__list-item {
  flex: 1;
  margin: 0 0 0 15px;
}
.form-enter-checkboxes__chk {
  position: absolute;
  left: -9999px;
}
.form-enter-checkboxes__item {
  cursor: pointer;
}
.form-enter-checkboxes__action {
  display: block;
  box-shadow: 0 0 6px rgba(236,236,239,0.5);
  border-radius: 12px;
  border: 1px solid rgba(214,220,225,0.5);
  background-color: #fff;
  padding: 19px 15px 17px;
  text-align: center;
}
.form-enter-checkboxes__action:hover {
  background-color: #fafafb;
  box-shadow: none;
}
.form-enter-checkboxes__symb {
  display: flex;
  margin: 0 auto 8px;
  width: 50px;
  height: 50px;
  background-color: rgba(165,178,188,0.15);
  border-radius: 50%;
  align-items: center;
  justify-content: center;
}
.form-enter-checkboxes__symb svg {
  vertical-align: top;
  margin: auto;
}
.form-enter-checkboxes__label {
  font-size: 16px;
  font-weight: 500;
}
.form-colorpicker {
  list-style-type: none;
  padding: 0;
  margin: 0 0 0 -13px;
  flex-wrap: wrap;
  display: flex;
}
.form-colorpicker__list-item {
  margin-left: 13px;
}
.form-colorpicker__radio {
  position: absolute;
  left: -9999px;
}
.form-colorpicker__radio:checked + .form-colorpicker__color:before {
  opacity: 1;
  -webkit-transform: scale(1);
          transform: scale(1);
}
.form-colorpicker__item {
  cursor: pointer;
}
.form-colorpicker__color {
  display: flex;
  width: 26px;
  height: 26px;
  border-radius: 50%;
  align-items: center;
  justify-content: center;
}
.form-colorpicker__color:before {
  opacity: 0;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #fff;
  content: '';
  -webkit-transform: scale(2.5);
          transform: scale(2.5);
  transition: opacity 0.25s, -webkit-transform 0.25s;
  transition: transform 0.25s, opacity 0.25s;
  transition: transform 0.25s, opacity 0.25s, -webkit-transform 0.25s;
}
.btn {
  display: inline-block;
  cursor: pointer;
  padding: 11px 15px 11px;
  border-width: 0;
  white-space: nowrap;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background: none;
  transition: color 0.1s linear, background-color 0.1s linear, box-shadow 0.1s linear;
  min-height: 42px;
  line-height: 1.45;
  border-radius: 8px;
  font-weight: 500;
  text-align: center;
}
.btn-primary {
  background-color: #e9ecef;
  color: #222;
  font-weight: 500;
}
.btn-primary:hover,
.btn-primary:focus {
  background-color: #d5dbe0;
  color: #222;
}
.btn-primary.disabled,
.btn-primary[disabled] {
  cursor: default;
  background-color: #f5f7f9 !important;
  color: rgba(165,178,188,0.7) !important;
}
.btn-primary[data-shadow="true"] {
  box-shadow: 0 4px 8px rgba(143,149,154,0.4);
}
.btn-primary[data-shadow="true"].disabled,
.btn-primary[data-shadow="true"][disabled] {
  box-shadow: 0 4px 8px rgba(162,169,176,0.4) !important;
}
.btn-default {
  background-color: transparent;
  color: #222;
  box-shadow: inset 0 0 0 1px #d2d8dc;
}
.btn-default:hover,
.btn-default:focus {
  background-color: transparent;
  color: #27ae60;
}
.btn-default.disabled,
.btn-default[disabled] {
  cursor: default;
  background-color: #f5f7f9 !important;
  color: rgba(165,178,188,0.7) !important;
  box-shadow: none !important;
}
.btn-default[data-style="error"] {
  color: #ff3b30;
}
.btn-default[data-style="error"]:hover,
.btn-default[data-style="error"]:focus {
  color: #a52c2c;
}
.btn-default[data-shadow="true"] {
  box-shadow: 0 4px 8px rgba(143,149,154,0.4);
}
.btn-default[data-shadow="true"].disabled,
.btn-default[data-shadow="true"][disabled] {
  box-shadow: 0 4px 8px rgba(162,169,176,0.4) !important;
}
.btn-success {
  background-color: #27ae60;
  color: #fff;
}
.btn-success.btn-xs {
  box-shadow: 0 4px 8px rgba(23,73,44,0.4);
}
.btn-success:hover,
.btn-success:focus {
  background-color: #239553;
  color: #fff;
}
.btn-success.disabled,
.btn-success[disabled] {
  cursor: default;
  background-color: #f5f7f9 !important;
  color: rgba(165,178,188,0.7) !important;
}
.btn-success[data-shadow="true"] {
  box-shadow: 0 4px 8px rgba(23,73,44,0.4);
}
.btn-success[data-shadow="true"].disabled,
.btn-success[data-shadow="true"][disabled] {
  box-shadow: 0 4px 8px rgba(162,169,176,0.4) !important;
}
.btn-danger {
  background-color: #ff3b30;
  color: #fff;
}
.btn-danger.btn-xs {
  box-shadow: 0 4px 8px rgba(23,73,44,0.4);
}
.btn-danger:hover,
.btn-danger:focus {
  background-color: #a52c2c;
  color: #fff;
}
.btn-danger.disabled,
.btn-danger[disabled] {
  cursor: default;
  background-color: #f5f7f9 !important;
  color: rgba(165,178,188,0.7) !important;
}
.btn-danger[data-shadow="true"] {
  box-shadow: 0 4px 8px rgba(143,149,154,0.4);
}
.btn-danger[data-shadow="true"].disabled,
.btn-danger[data-shadow="true"][disabled] {
  box-shadow: 0 4px 8px rgba(162,169,176,0.4) !important;
}
.btn-dark {
  background-color: #4d4d4d;
  color: #fff;
}
.btn-dark.btn-xs {
  box-shadow: 0 4px 8px rgba(0,0,0,0.4);
}
.btn-dark:hover,
.btn-dark:focus {
  background-color: #5e5e5e;
  color: #fff;
}
.btn-dark.disabled,
.btn-dark[disabled] {
  cursor: default;
  background-color: #f5f7f9 !important;
  color: rgba(165,178,188,0.7) !important;
}
.btn-dark[data-shadow="true"] {
  box-shadow: 0 4px 8px rgba(143,149,154,0.4);
}
.btn-dark[data-shadow="true"].disabled,
.btn-dark[data-shadow="true"][disabled] {
  box-shadow: 0 4px 8px rgba(162,169,176,0.4) !important;
}
.btn-lg {
  padding: 15.5px 20px 14.5px;
  min-height: 50px;
  border-radius: 10px;
  font-size: 1rem;
  line-height: 1.25rem;
}
.btn-lg .btn__ico {
  margin-top: -1px;
  margin-right: 5px;
}
.btn-lg .btn__arrow {
  margin-top: 8.5px;
}
.btn-sm {
  padding: 7.5px 10px 6.5px;
  min-height: 34px;
  border-radius: 6px;
  font-size: 14px;
  line-height: 1.25rem;
}
.btn-sm .btn__ico {
  margin-top: -1px;
  margin-right: 5px;
}
.btn-sm .btn__arrow {
  margin-top: 8px;
}
.btn-sm[data-radius="rounded"] {
  padding-left: 17px;
  padding-right: 17px;
}
.btn-icon {
  min-width: 56px;
}
.btn-icon .btn__ico {
  margin-right: 0;
}
.btn__ico {
  display: inline-flex;
  vertical-align: top;
  margin-right: 6px;
  font-size: 1px;
  min-height: 1.25rem;
  align-items: center;
  margin-top: -1px;
}
.btn__ico[data-size="large"] .ico-pencil-os {
  width: 13px;
  height: 13px;
}
.btn__ico[data-size="large"] .ico-attach-os {
  width: 16px;
  height: 15px;
}
.btn .ico-trash-n-os {
  margin-top: -1px;
}
.btn__arrow {
  display: inline-block;
  vertical-align: top;
  border-left: 3.5px solid transparent;
  border-right: 3.5px solid transparent;
  border-top: 4px solid currentColor;
  margin-top: 7.5px;
  -webkit-transform-origin: center;
          transform-origin: center;
}
.btn-warr {
  position: relative;
}
.btn--active .btn__arrow {
  -webkit-transform: rotate(-180deg);
          transform: rotate(-180deg);
}
.btn--loading .btn__ico {
  margin-right: 0;
}
.btn--loading .btn__ico .g-loader {
  line-height: 1px;
}
.btn[data-radius="rounded"] {
  border-radius: 30px;
}
.btn-link {
  color: #007aff;
  padding: 0;
  margin: 0;
  border: none;
  background: none;
}
.btn-link:hover {
  color: #014e86;
}
.btn-open {
  border-radius: 50%;
  min-width: 42px;
  padding-left: 0;
  padding-right: 0;
  text-align: center;
}
.btn-open .btn__ico {
  margin-right: 0;
  margin-top: 0;
}
.btn-open .ico-chevron-down-small-os {
  width: 14px;
  height: 8px;
}
.btn-open--active .btn__ico {
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}
.online-cells {
  display: flex;
}
.online-cells__item {
  width: 22px;
  height: 2px;
  background-color: rgba(175,175,175,0.5);
}
.online-cells__item + .online-cells__item {
  margin-left: 2px;
}
.online-cells__item.__filled {
  background-color: #26ae60;
}
.progress-bar {
  position: relative;
  width: 24px;
  height: 24px;
}
.progress-bar__ring {
  top: -1.5px;
  left: 0;
  position: absolute;
  -webkit-transform: rotate(90deg) translate3d(0, 0, 0) scaleX(-1);
          transform: rotate(90deg) translate3d(0, 0, 0) scaleX(-1);
  width: 24px;
  height: 24px;
  margin: 0;
  border: none;
}
.progress-bar__circle {
  stroke-dasharray: 76 76;
  stroke-dashoffset: 13;
  stroke-miterlimit: round;
}
.progress-bar-numbers {
  position: absolute;
  top: 2px;
  left: 0;
  width: 100%;
  height: 20px;
  overflow: hidden;
}
.progress-bar-numbers__count {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  text-align: center;
  line-height: 20px;
  font-size: 14px;
  font-weight: 500;
}
.progress-bar-numbers__count.__state-initial {
  -webkit-transform: translate(0, -20px);
          transform: translate(0, -20px);
  opacity: 0;
}
.progress-bar-numbers__count.__state-normal {
  -webkit-transform: translate(0, 0);
          transform: translate(0, 0);
  opacity: 1;
}
.progress-bar-numbers__count.__state-ending {
  -webkit-transform: translate(0, 20px);
          transform: translate(0, 20px);
  opacity: 0;
}
.progress-bar[data-sec="5"] .progress-bar__circle {
  transition: stroke-dashoffset 5s linear 0s;
}
.progress-bar.__state-normal .progress-bar__circle {
  stroke-dashoffset: 76;
}
.map-controls {
  position: absolute;
  z-index: 3;
  top: 50%;
  right: 10px;
  margin-right: env(safe-area-inset-right);
  -webkit-transform: translate(0, -50%);
          transform: translate(0, -50%);
  transition: margin-top 0.15s linear;
}
.map-controls-group {
  box-shadow: 0 5px 10px rgba(0,0,0,0.25);
  background-color: #222;
  list-style-type: none;
  padding: 0;
  margin: 10px 0;
  border-radius: 20px;
}
.map-controls-group__list-item {
  position: relative;
  margin-bottom: 2px;
}
.map-controls-group__list-item:first-child .map-controls-item {
  border-top-right-radius: 20px;
  border-top-left-radius: 20px;
}
.map-controls-group__list-item:first-child .map-controls-item:before {
  display: none;
}
.map-controls-group__list-item:last-child {
  margin-bottom: 0;
}
.map-controls-group__list-item:last-child .map-controls-item {
  border-bottom-right-radius: 20px;
  border-bottom-left-radius: 20px;
}
.map-controls-group__list-item:last-child .map-controls-item:after {
  display: none;
}
.map-controls-item {
  border-width: 0;
  margin: 0;
  padding: 0;
  width: 40px;
  height: 40px;
  background-color: rgba(17,17,17,0);
  transition: color 0.1s, background-color 0.1s;
  vertical-align: top;
  color: #ebebeb;
  display: flex;
}
.map-controls-item:hover:not([disabled]) {
  color: #fff;
  background-color: #000;
}
.map-controls-item:hover:not([disabled]):after {
  background-color: #000;
  left: 0;
  right: 0;
  height: 4px;
  border-radius: 0;
}
.map-controls-item:hover:not([disabled]):before {
  background-color: #000;
  left: 0;
  right: 0;
  border-radius: 0;
  opacity: 1;
}
.map-controls-item[disabled] {
  color: rgba(235,235,235,0.4);
}
.map-controls-item:after {
  position: absolute;
  content: '';
  bottom: -2px;
  left: 9px;
  right: 9px;
  height: 2px;
  background-color: rgba(255,255,255,0.05);
  border-radius: 2px;
  transition: all 0.1s;
}
.map-controls-item:before {
  position: absolute;
  content: '';
  top: -2px;
  left: 9px;
  right: 9px;
  height: 2px;
  background-color: rgba(255,255,255,0.05);
  border-radius: 2px;
  transition: all 0.1s;
  opacity: 0;
}
.map-controls-item svg {
  margin: auto;
}
.map-controls-item svg,
.map-controls-item use {
  vertical-align: top;
}
.map-controls-item[data-icon="location"] {
  padding-top: 1.5px;
  padding-right: 1px;
}
.map-controls-item[data-icon="ruler"] {
  padding-right: 0.5px;
}
.map-controls-item[data-icon="plus"] {
  padding-top: 2px;
}
.map-controls-item[data-icon="plus"]:after {
  bottom: -4px;
}
.map-controls-item--active {
  background-color: #fff;
  color: #222;
}
.map-controls-item--active.map-controls-item--active:hover {
  background-color: #fff;
  color: #222;
}
.rb-toolbar {
  position: absolute;
  z-index: 3;
  right: 0;
  bottom: 10px;
  display: flex;
}
.rb-toolbar-item {
  margin-right: 10px;
  border-radius: 20px;
  box-shadow: 0 5px 10px rgba(0,0,0,0.25);
  background-color: #222;
}
.top-toolbar {
  position: absolute;
  z-index: 3;
  right: 10px;
  top: 5px;
  left: 5px;
  display: flex;
  justify-content: space-between;
  transition: -webkit-transform 0.15s linear;
  transition: transform 0.15s linear;
  transition: transform 0.15s linear, -webkit-transform 0.15s linear;
}
.top-toolbar-item {
  margin-right: 10px;
  border-radius: 20px;
  box-shadow: 0 5px 10px rgba(0,0,0,0.25);
  background-color: #222;
}
.top-toolbar-item.__fixed {
  flex: 0 0 auto;
}
.top-toolbar-item.__fluid {
  flex: 1 1 auto;
  min-width: 0;
}
.top-toolbar-item:last-child {
  margin-right: 0;
}
.map-search {
  position: absolute;
  z-index: 3;
  top: 5px;
  left: 5px;
  border-radius: 20px;
  box-shadow: 0 5px 10px rgba(0,0,0,0.25);
  background-color: #222;
  width: 42px;
  color: #ebebeb;
  transition: width 0.2s, border-radius 0.2s, background-color 0.2s, translate 0.15s linear;
}
.map-search:hover {
  background-color: #101010;
}
.map-search__outer {
  position: relative;
  height: 42px;
}
.map-search__btn {
  position: absolute;
  z-index: 3;
  top: 0;
  left: 0;
  width: 42px;
  height: 42px;
  color: #ebebeb;
  border: none;
  background: none;
  margin: 0;
  padding: 0;
}
.map-search__btn svg {
  vertical-align: top;
}
.map-search__inner {
  visibility: hidden;
  pointer-events: none;
  opacity: 0;
}
.map-search__inner .g-loader {
  position: absolute;
  top: 13px;
  right: 15px;
  opacity: 0.6;
  display: none;
}
.map-search__input {
  color: #fff;
  background: none;
  padding: 11.5px 20px 10.5px 42px;
  margin: 0;
  font-size: 14px;
  vertical-align: top;
  width: 100%;
  line-height: 1.25rem;
  border: none;
  border-radius: 8px;
  height: 42px;
}
.map-search-results {
  display: none;
  position: relative;
  overflow-y: auto;
  max-height: 300px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}
.map-search-results__empty {
  color: #ebebeb;
  padding: 15px 20px 12px;
  text-align: center;
}
.map-search--opened {
  border-radius: 8px;
  width: 270px;
}
.map-search--opened:hover {
  background-color: #222;
}
.map-search--opened .map-search__inner {
  visibility: visible;
  opacity: 1;
  pointer-events: auto;
}
.map-search--loading .map-search__input {
  padding-right: 50px;
}
.map-search--loading .map-search__inner .g-loader {
  display: block;
}
.map-search--results .map-search__input {
  padding-right: 50px;
}
.map-search--results .map-search__outer:after {
  position: absolute;
  content: '';
  bottom: -1px;
  left: 0;
  right: 0;
  height: 1px;
  border-top: 0.5px solid #353535;
}
.map-search--results .map-search-results {
  display: block;
  line-height: 1.25rem;
}
.map-search--results .map-search-results__group {
  position: relative;
  padding: 15px 20px 8px;
}
.map-search--results .map-search-results__group:before {
  position: absolute;
  content: '';
  top: -1px;
  left: 0;
  right: 0;
  height: 1px;
  border-top: 0.5px solid #353535;
}
.map-search--results .map-search-results__group:first-child:before {
  display: none;
}
.map-search--results .map-search-results__label {
  color: rgba(255,255,255,0.6);
  font-size: 12px;
  margin-bottom: 8px;
}
.map-search--results .map-search-results__list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}
.map-search--results .map-search-results__list-item {
  margin-bottom: 1px;
}
.map-search--results .map-search-results__item {
  display: block;
  color: rgba(235,235,235,0.6);
  margin: 0 -20px;
  padding: 7px 20px;
}
.map-search--results .map-search-results__item:hover {
  background-color: #101010;
}
.map-search--results .map-search-results__hgh {
  color: #fff;
}
.map-view-type-item {
  padding: 0 20px;
  border-radius: 20px;
  border-width: 0;
  background-color: rgba(17,17,17,0);
  transition: color 0.1s, background-color 0.1s;
  color: #ebebeb;
  min-height: 40px;
  display: flex;
  align-items: center;
}
.map-view-type-item:after {
  content: '';
  width: 5px;
  height: 5px;
  border-right: 1px solid currentColor;
  border-bottom: 1px solid currentColor;
  -webkit-transform: rotate(-45deg);
          transform: rotate(-45deg);
  margin-left: 8px;
  margin-top: 1px;
  transition: -webkit-transform 0.1s;
  transition: transform 0.1s;
  transition: transform 0.1s, -webkit-transform 0.1s;
}
.map-view-type-item:hover {
  color: #fff;
  background-color: #000;
}
.map-view-type-item__ico {
  margin-right: 10px;
  line-height: 1px;
  margin-top: -2px;
}
.map-view-type-item__ico svg,
.map-view-type-item__ico use {
  vertical-align: top;
}
.map-view-type-item[data-arrow="bottom"]:after {
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
  margin-top: -1px;
}
.navigation-link {
  position: relative;
  vertical-align: top;
  border-radius: 20px;
  border-width: 0;
  background-color: rgba(17,17,17,0);
  transition: color 0.1s, background-color 0.1s;
  width: 40px;
  height: 40px;
  color: #ebebeb;
  font-size: 0;
  line-height: 0;
  padding: 0;
}
.navigation-link:hover {
  color: #fff;
  background-color: #000;
}
.navigation-link:before {
  position: absolute;
  content: '';
  background-color: currentColor;
  width: 14px;
  left: 13px;
  top: 16px;
  height: 2px;
  border-radius: 1px;
}
.navigation-link:after {
  position: absolute;
  content: '';
  background-color: currentColor;
  width: 14px;
  height: 2px;
  border-radius: 1px;
  left: 13px;
  top: 22px;
}
.map-drop-bar {
  position: relative;
}
.map-drop-bar-item {
  padding: 0 20px;
  border-radius: 20px;
  border-width: 0;
  background-color: rgba(17,17,17,0);
  transition: color 0.1s, background-color 0.1s;
  color: #ebebeb;
  min-height: 40px;
  display: flex;
  align-items: center;
  -webkit-transform: translate(0, 0);
          transform: translate(0, 0);
}
.map-drop-bar-item:after {
  content: '';
  width: 5px;
  height: 5px;
  border-left: 1px solid currentColor;
  border-bottom: 1px solid currentColor;
  -webkit-transform: rotate(-45deg);
          transform: rotate(-45deg);
  margin-left: 8px;
  margin-top: -2px;
  transition: -webkit-transform 0.1s;
  transition: transform 0.1s;
  transition: transform 0.1s, -webkit-transform 0.1s;
}
.map-drop-bar-item:hover {
  color: #fff;
  background-color: #000;
}
.map-drop-bar-nav {
  position: absolute;
  min-width: 100px;
  box-shadow: 0 5px 10px rgba(0,0,0,0.25);
  background-color: #222;
  border-radius: 4px;
  top: 50px;
  font-size: 10px;
  padding: 10px 0;
  left: 0;
  -webkit-transform: translate(0, 10px);
          transform: translate(0, 10px);
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.1s, visibility 0.1s, -webkit-transform 0.1s;
  transition: opacity 0.1s, visibility 0.1s, transform 0.1s;
  transition: opacity 0.1s, visibility 0.1s, transform 0.1s, -webkit-transform 0.1s;
  overflow-x: hidden;
  overflow-y: auto;
  max-height: 350px;
}
.map-drop-bar-nav__list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}
.map-drop-bar-nav__item {
  display: block;
  cursor: pointer;
  padding: 0 20px;
  min-height: 4em;
  display: flex;
  align-items: center;
  color: #ebebeb;
  background-color: rgba(17,17,17,0);
  transition: color 0.1s, background-color 0.1s;
}
.map-drop-bar-nav__item:hover {
  color: #fff;
  background-color: #000;
}
.map-drop-bar-nav__value {
  font-size: 1.4em;
}
.map-drop-bar.__opened .map-drop-bar-item:after {
  -webkit-transform: rotate(-225deg) translate(1px, -2px);
          transform: rotate(-225deg) translate(1px, -2px);
}
.map-drop-bar.__opened .map-drop-bar-nav {
  opacity: 1;
  visibility: visible;
  -webkit-transform: translate(0, 0);
          transform: translate(0, 0);
}
.map-drop-bar[data-size="large"] .map-drop-bar-item {
  min-height: 42px;
  box-shadow: 0 5px 10px rgba(0,0,0,0.25);
  border-radius: 8px;
  background-color: #222;
  font-size: 16px;
}
.map-drop-bar[data-size="large"] .map-drop-bar-item:after {
  -webkit-transform: rotate(0);
          transform: rotate(0);
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid currentColor;
  border-bottom-width: 0;
  margin: 0 0 0 5px;
}
.map-drop-bar[data-size="large"] .map-drop-bar-item:hover {
  color: #fff;
  background-color: #000;
}
.map-point {
  position: absolute;
  z-index: 4;
  width: 7px;
  height: 7px;
  background-color: #212121;
  border: 2px solid rgba(255,255,255,0.95);
  border-radius: 50%;
}
.map-tooltip {
  position: absolute;
  z-index: 995;
  max-width: 290px;
  box-shadow: 0 0 8px rgba(0,0,0,0.25);
  border-radius: 6px;
  background-color: #fff;
}
.map-tooltip-arrow {
  position: absolute;
  background-image: url("data:image/svg+xml,%3Csvg width='13' height='7' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cdefs%3E%3Cpath d='M10.9879413,11.9879413 C11.1363056,9.86001713 10.710565,8.45498783 9.71071965,7.77285344 C8.81298522,7.16038321 7.00847294,6.21384757 4.29718281,4.93324651 L4.29718921,4.93323296 C4.04750492,4.81530166 3.94069802,4.51729034 4.05862932,4.26760604 C4.1078526,4.16339044 4.19156935,4.07938098 4.2956125,4.02979423 C7.02466319,2.72913448 8.8296989,1.77845663 9.71071965,1.17776067 C10.6911208,0.509305353 11.1168613,-0.888847316 10.9879413,-3.01669734 L10.9879413,11.9879413 Z' id='a'/%3E%3C/defs%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg transform='translate(-738 -293)'%3E%3Cg transform='translate(737 292)'%3E%3Cg%3E%3Cg%3E%3Cuse fill='%23FFF' transform='rotate(-90 7.5137 4.4856)' xlink:href='%23a'/%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  width: 13px;
  height: 7px;
}
.map-tooltip-info {
  display: flex;
  margin-left: -13px;
}
.map-tooltip-item {
  margin-left: 13px;
}
.map-tooltip-item__value {
  font-weight: bold;
  font-size: 16px;
}
.map-tooltip-item__diff {
  color: rgba(34,34,34,0.46);
}
.map-tooltip-status {
  font-weight: 500;
  margin-bottom: 4px;
}
.map-tooltip.__arrow-down .map-tooltip-arrow {
  bottom: -7px;
  left: 50%;
  margin-left: -6px;
}
.map-tooltip.__arrow-top .map-tooltip-arrow {
  top: -7px;
  left: 50%;
  margin-left: -6px;
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}
.map-tooltip.__arrow-left .map-tooltip-arrow {
  left: -10px;
  top: 50%;
  margin-top: -3px;
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
}
.map-tooltip.__arrow-right .map-tooltip-arrow {
  right: -10px;
  top: 50%;
  margin-top: -3px;
  -webkit-transform: rotate(-90deg);
          transform: rotate(-90deg);
}
.map-popover {
  position: absolute;
  z-index: 11;
  background: rgba(255,255,255,0.97);
  box-shadow: 0 5px 10px rgba(0,0,0,0.25);
  border-radius: 6px;
}
.map-popover-arrow {
  position: absolute;
  z-index: 2;
  top: -19px;
  left: 50%;
  margin-left: -13px;
  overflow: hidden;
  width: 26px;
  height: 20px;
}
.map-popover-arrow:before {
  position: absolute;
  content: '';
  background-color: #fff;
  border-top: 1px solid geyser;
  border-right: 1px solid geyser;
  width: 8px;
  height: 8px;
  border-top-right-radius: 2px;
  -webkit-transform: rotate(-45deg);
          transform: rotate(-45deg);
  left: 6px;
  bottom: -7px;
  -webkit-transform-origin: 0 0;
          transform-origin: 0 0;
  box-shadow: 0 5px 10px rgba(0,0,0,0.25);
}
.map-popover.__arrow-bottom .map-popover-arrow {
  top: auto;
  bottom: -19px;
}
.map-popover.__arrow-bottom .map-popover-arrow:before {
  bottom: auto;
  top: 0;
}
.popover-place {
  min-width: 310px;
  font-size: 12px;
  line-height: 1.25;
}
.popover-place .map-popover__body {
  padding: 20px;
}
.popover-place-actions {
  margin-bottom: 15px;
}
.popover-place-actions__item {
  margin-bottom: 5px;
}
.popover-place-chars {
  display: flex;
  flex-wrap: wrap;
  margin-left: -15px;
}
.popover-place-chars__item {
  padding-left: 15px;
  padding-bottom: 15px;
  width: 50%;
}
.popover-place-chars__label {
  font-size: 10px;
}
.popover-place-chars__value {
  font-size: 18px;
}
.popover-place-chars__diff {
  font-size: 12px;
  color: rgba(34,34,34,0.46);
}
.popover-place-table {
  list-style-type: none;
  padding: 0;
  margin: 0 0 0 -15px;
}
.popover-place-table__list-item {
  margin-bottom: 7px;
  display: flex;
}
.popover-place-table__label {
  width: 50%;
  padding-left: 15px;
  color: rgba(34,34,34,0.46);
}
.popover-place-table__value {
  width: 50%;
  padding-left: 15px;
}
.popover-place-table__value .online-cells {
  margin-top: 7px;
}
.map-timeline {
  display: flex;
}
.map-timeline__arrow {
  position: relative;
  flex: 0 0 auto;
  width: 40px;
  height: 40px;
  border-width: 0;
  margin: 0 0 0 2px;
  padding: 0;
  background-color: rgba(34,34,34,0);
  transition: color 0.1s, background-color 0.1s;
  vertical-align: top;
  color: #ebebeb;
  display: flex;
  line-height: 1;
}
.map-timeline__arrow:hover:not([disabled]) {
  color: #fff;
  background-color: #000;
}
.map-timeline__arrow:hover:not([disabled]):before {
  background-color: #000;
  top: 0;
  height: 40px;
}
.map-timeline__arrow:hover:not([disabled]) + .map-timeline__arrow:before {
  background-color: #000;
  top: 0;
  height: 40px;
}
.map-timeline__arrow[disabled] {
  color: rgba(235,235,235,0.4);
}
.map-timeline__arrow svg,
.map-timeline__arrow use {
  vertical-align: top;
}
.map-timeline__arrow svg {
  margin: auto;
}
.map-timeline__arrow:before {
  position: absolute;
  content: '';
  left: -2px;
  top: 9px;
  height: 22px;
  width: 2px;
  background-color: rgba(255,255,255,0.05);
  border-radius: 2px;
  transition: background-color 0.1s, height 0.1s, top 0.1s;
}
.map-timeline__arrow:last-child {
  border-top-right-radius: 20px;
  border-bottom-right-radius: 20px;
}
.map-timeline-body {
  position: relative;
  flex: 1 1 auto;
  min-width: 0;
}
.map-timeline-body:first-child .map-timeline-scroll {
  border-top-left-radius: 20px;
  border-bottom-left-radius: 20px;
}
.map-timeline-body.__shd-left:before {
  position: absolute;
  z-index: 3;
  content: '';
  width: 55px;
  top: 0;
  bottom: 0;
  left: 0;
  background-image: linear-gradient(90deg, #222 10%, rgba(34,34,34,0) 100%);
}
.map-timeline-body.__shd-right:after {
  position: absolute;
  z-index: 3;
  content: '';
  width: 55px;
  top: 0;
  bottom: 0;
  right: 0;
  background-image: linear-gradient(-90deg, #222 10%, rgba(34,34,34,0) 100%);
}
.map-timeline-infostatus {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 4;
  padding: 0 5px 0 15px;
  background-color: #222;
  color: rgba(235,235,235,0.4);
  display: flex;
  align-items: center;
}
.map-timeline-infostatus[data-align="right"] {
  left: auto;
  right: 0;
}
.map-timeline-infostatus[data-align="right"]:after {
  position: absolute;
  z-index: 3;
  content: '';
  width: 55px;
  top: 0;
  bottom: 0;
  right: -55px;
  background-image: linear-gradient(-90deg, #222 10%, rgba(34,34,34,0) 100%);
}
.map-timeline-infostatus[data-align="left"]:after {
  position: absolute;
  z-index: 3;
  content: '';
  width: 55px;
  top: 0;
  bottom: 0;
  right: -55px;
  background-image: linear-gradient(90deg, #222 10%, rgba(34,34,34,0) 100%);
}
.map-timeline-infostatus .g-loader {
  opacity: 0.4;
  margin-right: 8px;
  vertical-align: top;
  margin-top: 1px;
  margin-bottom: -1px;
}
.map-timeline-scroll {
  position: relative;
  z-index: 1;
  overflow-x: auto;
  width: 100%;
}
.map-timeline-info {
  min-height: 34px;
  display: flex;
  color: #fff;
  align-items: center;
}
.map-timeline-info__ico {
  font-size: 1px;
  line-height: 16px;
}
.map-timeline-info__ico .g-loader,
.map-timeline-info__ico svg {
  vertical-align: top;
  font-size: 1px;
  margin-top: -1px;
}
.map-timeline-info__label {
  flex: 1 1 auto;
  margin-left: 5px;
}
.map-timeline-header {
  position: relative;
  color: #ebebeb;
  font-weight: 500;
  line-height: 40px;
  padding: 0 0 0 20px;
}
.map-timeline-area {
  display: table;
  margin-left: auto;
}
.map-timeline-area__list-item {
  display: table-cell;
  vertical-align: top;
}
.map-timeline-area__list-item:last-child {
  padding-right: 13px;
}
.map-timeline-area__loader-txt {
  margin-left: 5px;
}
.map-timeline-area__item {
  display: block;
  border-width: 0;
  background-color: rgba(17,17,17,0);
  transition: color 0.1s, background-color 0.1s;
  color: #ebebeb;
  white-space: nowrap;
  line-height: 40px;
  min-height: 40px;
  padding: 0 13px;
  vertical-align: top;
}
.map-timeline-area__item.__selected {
  color: #fff;
  box-shadow: inset 0 -2px 0 #27ae60;
  font-weight: 500;
}
.map-timeline-area__item.__selected:hover {
  background-color: rgba(17,17,17,0);
}
.map-timeline-area__item[data-icon-type="cloud"] {
  font-size: 1px;
  line-height: 1;
  padding-top: 3px;
}
.map-timeline-area__item[data-icon-type="cloud"]:after {
  content: '';
  display: inline-block;
  margin: 0 auto;
  width: 19px;
  height: 17px;
  background: url("../images/ico-cloud.png") no-repeat 50%/contain;
  vertical-align: top;
}
.map-timeline-area__item[data-icon-type="loader"] {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 62px;
}
.map-timeline-area__item[data-icon-type="loader"] .g-loader {
  line-height: 16px;
  margin-bottom: 2px;
}
.map-timeline-area__item:hover {
  color: #fff;
  background-color: #000;
}
.map-container {
  position: absolute;
  z-index: 1;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: hidden;
}
.map-legend {
  position: absolute;
  z-index: 3;
  bottom: 0;
  left: 0;
  right: 0;
  min-height: 40px;
  display: flex;
  align-items: flex-end;
  padding-bottom: 8px;
}
.map-legend:before {
  position: absolute;
  content: '';
  height: 40px;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: linear-gradient(180deg, rgba(34,34,34,0) 0%, #222 100%);
  opacity: 0.8;
}
.map-legend[data-theme="dark-mode"] {
  right: auto;
  left: 5px;
  bottom: 5px;
  padding: 0;
  border-radius: 8px;
  background-color: rgba(34,34,34,0.8);
  padding: 28px 1px 8px;
}
.map-legend[data-theme="dark-mode"]:before {
  display: none;
}
.map-legend__item {
  padding: 0 9px;
}
.map-legend__item.__full {
  flex: 1 1 auto;
}
.map-legend-scale {
  position: relative;
  display: flex;
  min-width: 100px;
}
.map-legend-scale.__mw-320 {
  min-width: 320px;
}
.map-legend-scale.__full {
  flex: 1 1 auto;
}
.map-legend-scale.__mw-160 {
  min-width: 160px;
}
.map-legend-scale.__mw-160 .map-legend-scale__item {
  min-width: 10px;
}
.map-legend-scale.__mw-60 {
  min-width: 60px;
}
.map-legend-scale.__mw-60 .map-legend-scale__item {
  min-width: 10px;
}
.map-legend-scale.__mw-300 {
  min-width: 300px;
}
.map-legend-scale:before {
  position: absolute;
  content: attr(data-from);
  color: #ebebeb;
  font-size: 10px;
  line-height: 1;
  top: -16px;
  left: 0;
}
.map-legend-scale:after {
  position: absolute;
  content: attr(data-to);
  color: #ebebeb;
  font-size: 10px;
  line-height: 1;
  top: -16px;
  right: 0;
}
.map-legend-scale__item {
  height: 4px;
  flex: 1.5;
  min-width: 18px;
}
.map-with-panels {
  display: flex;
  flex-direction: column;
  min-width: 0;
}
.map-with-panels__map {
  position: relative;
  flex: 0 0 auto;
  min-height: 100%;
  transition: min-height 0.15s;
}
.map-with-panels__content {
  flex: 1 1 auto;
  position: relative;
  overflow-y: auto;
  background-color: #f6f8f9;
}
.map-with-panels-message {
  background-color: #fdf3c4;
  padding: 12px 20px 11px;
}
.map-with-panels-message__ico {
  display: inline-block;
  vertical-align: top;
  font-size: 14px;
  line-height: 1;
  opacity: 0.75;
  margin: 1px 7px -2px 0;
}
.map-with-panels.__opened .map-with-panels__map {
  min-height: 312px;
}
.map-viewer {
  position: absolute;
  z-index: 1;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.map-viewer__inner {
  position: absolute;
  z-index: 1;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.map-viewer iframe {
  vertical-align: top;
}
.map-dashboard {
  position: relative;
  background-color: #fff;
  display: flex;
  min-height: 100%;
}
.map-dashboard__close {
  position: absolute;
  z-index: 3;
  right: 15px;
  top: 15px;
  border-width: 0;
  padding: 0;
  margin: 0;
  background: none;
  width: 13px;
  height: 12px;
  background-image: url("data:image/svg+xml,%3Csvg width='13' height='12' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:avocode='https://avocode.com/'%3E%3Cdefs%3E%3Cpath d='M1263.47413,367.3034c0.20096,-0.20096 0.52677,-0.20096 0.72773,0c0.20096,0.20096 0.20096,0.52677 0,0.72773l-4.97387,4.97387l4.97387,4.97387c0.20096,0.20096 0.20096,0.52677 0,0.72773c-0.20096,0.20096 -0.52677,0.20096 -0.72773,0l-4.97387,-4.97387l-4.97387,4.97387c-0.20096,0.20096 -0.52677,0.20096 -0.72773,0c-0.20096,-0.20096 -0.20096,-0.52677 0,-0.72773l4.97387,-4.97387l-4.97387,-4.97387c-0.20096,-0.20096 -0.20096,-0.52677 0,-0.72773c0.20096,-0.20096 0.52677,-0.20096 0.72773,0l4.97387,4.97387z' id='a'/%3E%3C/defs%3E%3Cg transform='matrix(1,0,0,1,-1252,-367)'%3E%3Cg%3E%3Cuse xlink:href='%23a' fill='%23a5b2bc'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}
.map-dashboard__close:hover {
  background-image: url("data:image/svg+xml,%3Csvg width='13' height='12' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:avocode='https://avocode.com/'%3E%3Cdefs%3E%3Cpath d='M1263.47413,367.3034c0.20096,-0.20096 0.52677,-0.20096 0.72773,0c0.20096,0.20096 0.20096,0.52677 0,0.72773l-4.97387,4.97387l4.97387,4.97387c0.20096,0.20096 0.20096,0.52677 0,0.72773c-0.20096,0.20096 -0.52677,0.20096 -0.72773,0l-4.97387,-4.97387l-4.97387,4.97387c-0.20096,0.20096 -0.52677,0.20096 -0.72773,0c-0.20096,-0.20096 -0.20096,-0.52677 0,-0.72773l4.97387,-4.97387l-4.97387,-4.97387c-0.20096,-0.20096 -0.20096,-0.52677 0,-0.72773c0.20096,-0.20096 0.52677,-0.20096 0.72773,0l4.97387,4.97387z' id='a'/%3E%3C/defs%3E%3Cg transform='matrix(1,0,0,1,-1252,-367)'%3E%3Cg%3E%3Cuse xlink:href='%23a' fill='%23808f9b'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}
.map-dashboard-actions {
  list-style-type: none;
  padding: 0;
  margin: 0;
  font-size: 13px;
}
.map-dashboard-actions__list-item {
  margin-bottom: 11px;
}
.map-dashboard__seeall {
  border: none;
  padding: 0;
  background: none;
  margin: 0;
  font-weight: 500;
  margin-bottom: 10px;
  font-size: 13px;
}
.map-dashboard__seeall:after {
  display: inline-block;
  content: '';
  width: 8px;
  height: 6px;
  margin-left: 3px;
  vertical-align: top;
  margin-top: 4px;
  background-image: url("data:image/svg+xml,%3Csvg width='8' height='6' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' %3E%3Cdefs%3E%3Cpath d='M1154.00871,786.72568l-3.02759,-2.97568l-0.98112,0.9948l4.00871,4.0052l3.82458,-4.0052l-1.02186,-0.9948z' id='a'/%3E%3C/defs%3E%3Cg transform='matrix(1,0,0,1,-1150,-783)'%3E%3Cg%3E%3Cuse xlink:href='%23a' fill='%23222'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}
.map-dashboard-chars {
  width: 100%;
}
.map-dashboard-chars td {
  vertical-align: top;
  padding-bottom: 6px;
  padding-left: 10px;
  font-size: 13px;
}
.map-dashboard-chars tr > td:first-child {
  padding-left: 0;
}
.map-dashboard-chars__date {
  color: #a5b2bc;
  white-space: nowrap;
}
.map-dashboard__hr {
  height: 10px;
  position: relative;
  background-color: #f6f8f9;
}
.map-dashboard__hr:before {
  position: absolute;
  content: '';
  height: 1px;
  border-top: 0.5px solid #d6dce1;
  top: 0;
  left: 0;
  right: 0;
}
.map-dashboard__hr-slim {
  height: 1px;
  border-top: 0.5px solid rgba(165,178,188,0.45);
}
.map-dashboard__main {
  flex: 1 1 auto;
  min-width: 0;
}
.map-dashboard__side {
  position: relative;
  flex: 0 0 auto;
  width: 240px;
}
.map-dashboard__side:before {
  position: absolute;
  content: '';
  width: 1px;
  top: 0;
  left: -1px;
  bottom: 0;
  border-right: 0.5px solid rgba(165,178,188,0.45);
}
.map-dashboard__section {
  padding: 20px 20px 0 20px;
}
.map-dashboard-header {
  display: flex;
  align-items: baseline;
  margin-bottom: 14px;
}
.map-dashboard-header__title {
  font-size: 19px;
  font-weight: 500;
  line-height: 1.12;
  letter-spacing: -0.4px;
  margin-top: 0;
  margin-bottom: 0;
  flex: 1 1 auto;
}
.map-dashboard-header__title small {
  font-size: 13px;
  font-weight: normal;
}
.map-dashboard-header__goto {
  flex: 0 0 auto;
}
.map-dashboard-header__goto:after {
  display: inline-block;
  vertical-align: top;
  margin-left: 2px;
  margin-top: 7px;
  width: 5px;
  height: 8px;
  content: '';
  background-image: url("data:image/svg+xml,%3Csvg width='5' height='8' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cdefs%3E%3Cpath d='M1014.99796,343.70907l0.70703,-0.70703l2.82813,2.83594l0.70703,0.69922l-0.70703,0.70703l-2.82813,2.83593l-0.70703,-0.70703l2.83203,-2.83203z' id='a'/%3E%3C/defs%3E%3Cg transform='matrix(1,0,0,1,-1015,-343)'%3E%3Cg%3E%3Cuse xlink:href='%23a' fill='%2307c'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}
.crop-rotation-details {
  font-size: 13px;
}
.crop-rotation-details h4 {
  margin-top: 0;
  margin-bottom: 2px;
  font-size: 1em;
  font-weight: normal;
}
.crop-rotation-details__list {
  color: #a5b2bc;
  list-style-type: none;
  padding: 0;
  margin: 0;
}
.crop-rotation-details__list-item {
  display: flex;
  padding-bottom: 1px;
}
.crop-rotation-details__title {
  flex: 1 1 auto;
  overflow: hidden;
  text-overflow: ellipsis;
}
.crop-rotation-details__aside {
  flex: 0 0 auto;
  padding-left: 15px;
}
.crop-rotation-details__row-item {
  border-bottom: 0.5px solid rgba(165,178,188,0.45);
  margin-right: -20px;
  padding-right: 20px;
  padding-bottom: 12px;
  margin-bottom: 12px;
}
.crop-rotation-details__row-item:last-child {
  border-bottom-width: 0;
  padding-bottom: 0;
}
.map-dashboard-weather {
  display: flex;
  padding-bottom: 20px;
  min-height: 110px;
}
.map-dashboard-weather.__empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #a5b2bc;
  text-align: center;
}
.map-dashboard-weather.__loader {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #a5b2bc;
  text-align: center;
  flex-direction: column;
}
.map-dashboard-weather.__loader .g-loader {
  margin-bottom: 10px;
}
.map-dashboard-weather .weather-info-quickmetrics {
  border-left-width: 0;
}
.map-dashboard-weather .weather-info-quickmetrics__inner {
  margin: 1px 0 -12px -15px;
}
.map-dashboard-weather .weather-info-quickmetrics__item {
  width: 105px;
  padding-left: 15px;
  padding-bottom: 12px;
}
.map-dashboard-weather .weather-info-quickmetrics__label {
  position: relative;
  overflow: hidden;
  font-size: 13px;
  line-height: 1.4;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.map-dashboard-weather .weather-info-quickmetrics__value {
  line-height: 1.37;
}
.map-dashboard-weather .weather-info-current {
  flex: 0 0 auto;
  width: 190px;
  padding-right: 15px;
}
.map-dashboard-weather .weather-info-current-metrics {
  align-items: flex-start;
}
.map-dashboard-weather .weather-info-current-metrics__ico {
  margin-top: -3px;
}
.map-dashboard-weather .weather-info-current-metrics__temp {
  font-size: 36px;
  font-weight: 500;
  line-height: 43px;
}
.map-dashboard-weather .weather-info-current-predict {
  margin-top: 7px;
  color: #a5b2bc;
  font-size: 13px;
  line-height: 18px;
  margin-bottom: 0;
}
@supports (display: grid) {
  .map-dashboard-weather .weather-info-quickmetrics__inner {
    display: grid;
    grid-template-rows: auto;
    grid-template-columns: repeat(auto-fit, minmax(105px, 1fr));
    grid-gap: 0;
  }
  .weather-info-quickmetrics__item {
    width: auto;
  }
}
.map-dashboard-notes {
  position: relative;
  z-index: 2;
  margin: 0 -20px;
  padding-bottom: 20px;
  overflow-x: auto;
}
.map-dashboard-notes.__loader {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #a5b2bc;
  text-align: center;
  flex-direction: column;
  min-height: 144px;
}
.map-dashboard-notes.__loader .spinner-container {
  margin-bottom: 10px;
}
.map-dashboard-notes-wrapper {
  position: relative;
}
.map-dashboard-notes-wrapper.__shadow-right:after {
  position: absolute;
  z-index: 4;
  content: '';
  right: -20px;
  top: 0;
  bottom: 0;
  width: 46px;
  border-right: 20px solid #fff;
  background-image: linear-gradient(90deg, rgba(255,255,255,0) 0%, #fff 100%);
}
.map-dashboard-notes-wrapper.__shadow-left:before {
  position: absolute;
  z-index: 4;
  content: '';
  left: -20px;
  top: 0;
  bottom: 0;
  width: 46px;
  border-left: 20px solid #fff;
  background-image: linear-gradient(-90deg, rgba(255,255,255,0) 0%, #fff 100%);
}
.map-dashboard-notes__area {
  display: flex;
}
.map-dashboard-notes__item {
  position: relative;
  flex: 0 0 auto;
  padding: 0 20px 23px;
  max-width: 300px;
}
.map-dashboard-notes__item:before {
  position: absolute;
  content: '';
  width: 1px;
  top: 0;
  left: -1px;
  bottom: 0;
  border-right: 0.5px solid rgba(165,178,188,0.45);
}
.map-dashboard-notes__item:first-child:before {
  display: none;
}
.map-dashboard-notes__see {
  position: absolute;
  bottom: 1px;
  left: 20px;
  font-size: 13px;
  line-height: 1.32;
}
.map-dashboard-notes .soil-sidebar-gallery__area {
  border-left-width: 0;
  border-right-width: 0;
  overflow: hidden;
}
.map-dashboard-notes .soil-sidebar-gallery {
  margin-bottom: 0;
  margin-left: -5px;
  margin-right: -5px;
  min-width: 260px;
}
.map-dashboard-card {
  padding: 15px 20px 5px;
}
.map-dashboard-card h2 {
  font-size: 19px;
  font-weight: 500;
  line-height: 1.12;
  letter-spacing: -0.4px;
  padding-top: 5px;
  margin-top: 0;
  margin-bottom: 2px;
}
.map-dashboard-card h3 {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.37;
  margin-top: 0;
  margin-bottom: 0;
  padding-bottom: 10px;
}
.map-dashboard-card__code {
  font-size: 13px;
  color: #a5b2bc;
  text-transform: uppercase;
}
.map-dashboard-card__char {
  font-style: italic;
}
.map-dashboard-card__quest {
  display: inline-block;
  color: rgba(165,178,188,0.5);
  margin: 2px 0 -2px 3px;
  vertical-align: top;
  font-size: 14px;
  line-height: 1;
}
.map-dashboard-card__quest svg {
  vertical-align: top;
}
.map-dashboard-card__content {
  margin-bottom: 11px;
}
.map-dashboard-card__content p {
  margin-top: 0;
  margin-bottom: 0;
}
.map-dashboard-card__action {
  margin-bottom: 10px;
}
.map-view-type-control {
  position: absolute;
  z-index: 4;
  width: 226px;
  margin-left: -113px;
  left: 50%;
  top: 5px;
}
.map-view-type-control + .map-viewer-grid .map-viewer-grid__header[data-align="left"] {
  padding-right: 130px;
}
.map-view-type-control + .map-viewer-grid .map-viewer-grid__header[data-align="right"] {
  padding-left: 130px;
}
.map-viewer-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  background-color: #d8d8d8;
  display: flex;
  flex-direction: column;
}
.map-viewer-grid .map-legend {
  visibility: hidden;
  opacity: 0;
}
.map-viewer-grid__header {
  position: absolute;
  z-index: 5;
  top: 15px;
  text-shadow: 0 1px 0 rgba(0,0,0,0.52);
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.25rem;
}
.map-viewer-grid__header[data-align="left"] {
  left: 15px;
  right: 15px;
}
.map-viewer-grid__header[data-align="right"] {
  right: 15px;
  left: 15px;
}
.map-viewer-grid__header[data-align="center"] {
  left: 5px;
  right: 5px;
  text-align: center;
}
.map-viewer-grid-hrow {
  display: flex;
}
.map-viewer-grid-cell {
  position: relative;
  z-index: 1;
  overflow: hidden;
}
.map-viewer-grid-cell:hover .map-legend {
  visibility: visible;
  opacity: 1;
}
.map-viewer-grid-subgrid {
  display: flex;
}
.map-viewer-grid-subgrid-cell {
  display: flex;
  flex-direction: column;
}
.map-viewer-grid-vdivider {
  position: relative;
  z-index: 3;
  width: 2px;
  background-color: #222;
  cursor: col-resize;
}
.map-viewer-grid-vdivider__button {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -17px;
  margin-left: -17px;
  width: 34px;
  height: 34px;
  box-shadow: 0 5px 10px rgba(0,0,0,0.25);
  border-radius: 50%;
  background-color: #222;
  border: none;
  cursor: col-resize;
  color: #fff;
}
.map-viewer-grid-vdivider__button:before {
  position: absolute;
  left: 10px;
  top: 50%;
  margin-top: -4px;
  content: '';
  width: 0;
  height: 0;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  border-right: 4px solid currentColor;
}
.map-viewer-grid-vdivider__button:after {
  position: absolute;
  right: 10px;
  top: 50%;
  margin-top: -4px;
  content: '';
  width: 0;
  height: 0;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  border-left: 4px solid currentColor;
}
.map-viewer-grid-hdivider {
  position: relative;
  z-index: 3;
  height: 2px;
  background-color: #222;
  cursor: row-resize;
}
.soil-sidebar {
  width: 360px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  margin-left: env(safe-area-inset-left);
}
.header {
  position: relative;
  z-index: 2;
  flex: 0 0 auto;
  padding: 25px 30px 0;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}
.header:after {
  position: absolute;
  content: '';
  bottom: -20px;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(180deg, #fff 30%, rgba(255,255,255,0));
}
.header-bar {
  list-style-type: none;
  padding: 0;
  margin: 0 0 -5px;
  display: flex;
  font-weight: 500;
}
.header-bar__list-item {
  margin-left: 25px;
}
.logo {
  position: relative;
  min-height: 35px;
  display: block;
  width: 105px;
}
.logo:before {
  display: block;
  content: '';
  width: 105px;
  height: 35px;
  background: url("../images/logo.svg") no-repeat 50%/contain;
  transition: opacity 0.2s, -webkit-transform 0.2s;
  transition: opacity 0.2s, transform 0.2s;
  transition: opacity 0.2s, transform 0.2s, -webkit-transform 0.2s;
}
.logo:after {
  position: absolute;
  top: 4px;
  left: 0;
  display: block;
  content: '';
  width: 32px;
  height: 32px;
  background: url("../images/logo-short.svg") no-repeat 50%/contain;
  transition: opacity 0.2s, -webkit-transform 0.2s;
  transition: opacity 0.2s, transform 0.2s;
  transition: opacity 0.2s, transform 0.2s, -webkit-transform 0.2s;
  opacity: 0;
}
.hide-on-mobile {
  overflow: auto;
  height: 100%;
}
.main-content {
  flex: 1 1 auto;
  min-height: 0;
  position: relative;
  z-index: 1;
  overflow-y: auto;
  padding: 20px 30px 30px;
  font-size: 16px;
  line-height: 1.38;
}
.main-content.sidebar {
  height: calc(100% - 65px);
}
.main-content h1 {
  font-size: 36px;
  line-height: 1.2;
  font-weight: bold;
  margin-top: 0;
  margin-bottom: 25px;
}
.main-content h2 {
  margin-top: 0;
  margin-bottom: 1px;
  font-weight: bold;
  font-size: 1em;
  line-height: 1.03;
}
.main-content p {
  margin-bottom: 18px;
}
.main-content p + h2 {
  margin-top: 28px;
}
.call-to-action {
  margin-bottom: 9px;
}
.call-to-action-secondary {
  font-size: 14px;
}
.map-topbar {
  position: absolute;
  z-index: 91;
  box-shadow: 0 0 8px rgba(0,0,0,0.2);
  border-radius: 10px;
  background-color: #161616;
  top: 10px;
  right: 10px;
  left: 10px;
  padding: 8px 10px 10px;
  margin-right: env(safe-area-inset-right);
  will-change: transform;
}
.map-topbar .map-legend {
  position: relative;
  bottom: auto;
  left: auto;
  right: auto;
  padding-bottom: 0;
  min-height: 24px;
  margin: 0 -5px 10px;
}
.map-topbar .map-legend:before {
  display: none;
}
.map-topbar .map-legend__item {
  padding: 0 5px;
}
.map-topbar .map-legend-scale:before,
.map-topbar .map-legend-scale:after {
  font-size: 12px;
  line-height: 16px;
  top: -20px;
}
.map-topbar .map-legend-scale__item {
  min-width: 1px;
}
.map-topbar .map-timeline-infostatus {
  background-color: #161616;
}
.map-topbar .map-timeline-infostatus[data-align="left"]:after {
  background-image: linear-gradient(90deg, #161616 10%, rgba(34,34,34,0) 100%);
}
.map-topbar .map-timeline-scroll {
  border-radius: 0 !important;
}
.map-topbar .map-timeline-body.__shd-left:before {
  background-image: linear-gradient(90deg, #161616 10%, rgba(34,34,34,0) 100%);
}
.map-topbar .map-timeline-body.__shd-right:after {
  background-image: linear-gradient(-90deg, #161616 10%, rgba(34,34,34,0) 100%);
}
.map-topbar .map-timeline__arrow {
  background-color: #303437;
  width: 42px;
  height: 34px;
  border-radius: 6px;
  margin-left: 5px;
}
.map-topbar .map-timeline__arrow:hover:not([disabled]) {
  background-color: #42464b;
}
.map-topbar .map-timeline__arrow[disabled] {
  background-color: #252628;
  color: rgba(255,255,255,0.18);
}
.map-topbar .map-timeline-area__list-item {
  padding-left: 5px;
}
.map-topbar .map-timeline-area__list-item:first-child {
  padding-left: 0;
}
.map-topbar .map-timeline-area__list-item:last-child {
  padding-right: 0;
}
.map-topbar .map-timeline-area__item {
  background-color: #303437;
  border-radius: 6px;
  min-height: 34px;
  line-height: 34px;
}
.map-topbar .map-timeline-area__item:hover {
  background-color: #42464b;
}
.map-topbar .map-timeline-area__item.__selected {
  background-color: #037aff;
  color: #fff;
  box-shadow: none;
}
.map-topbar .map-timeline-area__item.__selected:hover {
  background-color: #037aff;
}
.map-topbar .map-timeline-area__item[data-icon-type="cloud"] {
  line-height: 1;
}
.side-gallery {
  position: relative;
  overflow: hidden;
  border-radius: 6px;
  height: 190px;
  margin-top: 10px;
  max-width: 345px;
}
.side-gallery__list {
  margin-left: -2px;
  display: flex;
  height: 192px;
}
.side-gallery__item {
  position: relative;
  flex: 1.5;
  padding-left: 2px;
  display: flex;
  padding-bottom: 2px;
}
.side-gallery__pic {
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  width: 100%;
  background-color: rgba(0,0,0,0.1);
}
.side-gallery[data-photos="3"] .side-gallery__list {
  display: block;
}
.side-gallery[data-photos="3"] .side-gallery__item {
  width: 50%;
  height: 96px;
}
.side-gallery[data-photos="3"] .side-gallery__item:first-child {
  height: 192px;
  float: left;
}
.side-gallery[data-photos="4"] .side-gallery__list {
  flex-wrap: wrap;
}
.side-gallery[data-photos="4"] .side-gallery__item {
  height: 96px;
  flex: 0 0 auto;
  width: 50%;
}
.side-gallery-count {
  position: absolute;
  top: 0;
  left: 2px;
  right: 0;
  bottom: 2px;
  color: #fff;
  background-color: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}
.side-gallery-count__item {
  font-size: 20px;
  font-weight: 700;
  letter-spacing: 0.19px;
}
.mobile-hr {
  background-color: #f2f6f8;
  box-shadow: inset 0 1px 0 rgba(214,220,225,0.5);
  height: 12px;
  margin: 0 -15px 15px;
}
.mobile-note-header {
  border-bottom: 1px solid rgba(214,220,225,0.5);
  margin: 0 -15px 15px;
  padding: 0 15px 21px;
}
.mobile-note-header h2 {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 20px;
  line-height: 1.2;
  font-weight: 500;
  letter-spacing: 0.19px;
}
.mobile-note-body {
  font-size: 16px;
  line-height: 1.4;
  letter-spacing: -0.2px;
}
.mobile-note-body p {
  margin-top: 0;
  margin-bottom: 15px;
}
.mobile-note .side-gallery {
  margin-top: 20px;
  margin-bottom: 15px;
}
.mobile-note-footer {
  border-top: 1px solid rgba(214,220,225,0.5);
  margin-right: -15px;
  padding: 14px 15px 15px 0;
  display: flex;
  justify-content: space-between;
  font-size: 16px;
  line-height: 1.4;
  letter-spacing: -0.2px;
}
.mobile-chars {
  padding: 0 0 0 15px;
  font-size: 16px;
  line-height: 1.4;
  letter-spacing: -0.2px;
}
.mobile-chars__title {
  margin: 0 -15px 15px -15px;
  padding: 0 15px 15px 0;
  font-size: 17px;
  font-weight: 500;
  letter-spacing: -0.41px;
  line-height: 22px;
  border-bottom: 1px solid rgba(214,220,225,0.5);
}
.mobile-chars h4 {
  font-size: 1em;
  margin-top: 0;
  margin-bottom: 0;
}
.mobile-chars p {
  margin-top: 0;
  margin-bottom: 15px;
}
.hide {
  display: none !important;
}
.text-muted {
  color: #afafaf;
}
.text-right {
  text-align: right;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center !important;
}
.fl-r {
  float: right;
}
.fl-l {
  float: right;
}
.nowrap {
  white-space: nowrap;
}
.text-wrap {
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
  -webkit-hyphens: auto;
      -ms-hyphens: auto;
          hyphens: auto;
}
.h-ellipsis {
  display: inline-block;
  max-width: 100%;
  position: relative;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.text-nowrap {
  white-space: nowrap;
}
.text-link {
  cursor: pointer;
  color: #007aff;
}
.text-link:hover {
  color: #014e86;
}
.c-error {
  color: #a52c2c;
}
.c-danger {
  color: #e00;
}
.c-neutral {
  color: #a5b2bc;
}
.size-full {
  width: 100%;
}
.size-half {
  width: 50%;
}
.size-quarter {
  width: 25%;
}
.size-short {
  width: 95px;
}
.size-normal {
  width: 110px;
}
.size-medium {
  width: 210px;
}
.size-xnormal {
  width: 150px;
}
.size-sx {
  width: 75px;
}
.shake-error {
  -webkit-animation: shake 0.82s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
          animation: shake 0.82s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
  -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
}
.mb-0 {
  margin-bottom: 0 !important;
}
.mt-0 {
  margin-top: 0 !important;
}
.mr-0 {
  margin-right: 0 !important;
}
.ml-0 {
  margin-left: 0 !important;
}
@-webkit-keyframes shake {
  10%, 90% {
    -webkit-transform: translate3d(-1px, 0, 0);
            transform: translate3d(-1px, 0, 0);
  }
  20%, 80% {
    -webkit-transform: translate3d(2px, 0, 0);
            transform: translate3d(2px, 0, 0);
  }
  30%, 50%, 70% {
    -webkit-transform: translate3d(-4px, 0, 0);
            transform: translate3d(-4px, 0, 0);
  }
  40%, 60% {
    -webkit-transform: translate3d(4px, 0, 0);
            transform: translate3d(4px, 0, 0);
  }
}
@keyframes shake {
  10%, 90% {
    -webkit-transform: translate3d(-1px, 0, 0);
            transform: translate3d(-1px, 0, 0);
  }
  20%, 80% {
    -webkit-transform: translate3d(2px, 0, 0);
            transform: translate3d(2px, 0, 0);
  }
  30%, 50%, 70% {
    -webkit-transform: translate3d(-4px, 0, 0);
            transform: translate3d(-4px, 0, 0);
  }
  40%, 60% {
    -webkit-transform: translate3d(4px, 0, 0);
            transform: translate3d(4px, 0, 0);
  }
}
@media screen and (min-width: 768px) {
  .show-on-mobile {
    display: none !important;
  }
}
@media screen and (max-width: 767px) {
  .hide-on-mobile {
    display: none !important;
  }
  .page-container {
    flex-direction: column;
    height: auto;
    min-height: 100%;
  }
  .soil-sidebar {
    z-index: 5;
    order: 1;
    flex: 0 0 auto;
    min-height: 230px;
    width: 100%;
    border-top-right-radius: 12px;
    border-top-left-radius: 12px;
    margin-top: -12px;
    padding: 20px 15px;
    display: block;
  }
  .header {
    padding: 0 0 19px;
  }
  .header:after {
    display: none;
  }
  .map-container {
    order: 0;
    height: calc(100vh - 218px);
    height: calc((var(--vh, 1vh) * 100) - 218px);
    flex: 1 1 auto;
    width: 100%;
  }
  .map-topbar {
    top: auto;
    bottom: 22px;
  }

  .logo {
    margin: 0 auto;
  }

  .mobile-content {
    font-size: 16px;
    line-height: 1.38;
    text-align: center;
  }

  .mobile-content__desc {
    margin-top: 0;
    margin-bottom: 20px;
    text-align: center;
  }
}

