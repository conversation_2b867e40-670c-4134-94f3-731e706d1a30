import React, { useState, VFC } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import Icon from 'components/Icon';

import {
  ApiDataSourceLayer,
  DataTypesKey,
  TaskMatchingType,
  TaskSuccessType,
} from 'features/files/by-machinery/types';
import { actions } from 'features/files/by-machinery/redux/actions';
import { selectors } from 'features/files/by-machinery/redux/selectors';

import { useTranslate } from 'utils/use-translate';

import {
  ActiveTaskTitleStyle,
  attentionButtonIconClass,
  ActiveTaskStatusWrapper,
  ActiveTaskRowStyle,
  ActiveTaskSubTitleStyle,
  ActiveTaskInfoStyle,
  ActiveTaskRowLine,
  ActiveTaskRowHead,
  ActiveTaskLayersList,
  ActiveTaskLayerContainer,
  ActiveTaskLayerRow,
  ActiveTaskRowTitleContainer,
  CancelButtonStyle,
  clearButtonIconClass,
  ActiveTaskRowContainer,
  SelectButtonStyle,
  ActiveTaskLayerIcon,
} from '../styles';
import { MatchFieldsModal } from '../MatchFieldsModal';
import ModernSpinner from 'components/ui/ModernSpinner/ModernSpinner';
import Alert from 'components/ui/Alert/Alert';

type SuccessTaskRowRowProps = {
  task: TaskSuccessType | TaskMatchingType;
};

const TaskSuccessRow: VFC<SuccessTaskRowRowProps> = ({ task }) => {
  const { t } = useTranslate();
  const dispatch = useDispatch();

  const [modalUuid, setModalUuid] = useState<string | null>(null);
  const [isShowRemoveAlert, setIsShowRemoveAlert] = useState(false);

  const hasUnfinishedActiveTask = useSelector(
    selectors.hasUnfinishedActiveTask,
  );

  const hasUnmatchedFiles = task.sources[0]?.layers!.some(layer => {
    return !layer.is_spatial_matched;
  });
  const hasXlsFiles = task.sources.some(source => source.driver === 'PANDAS');

  const getTaskIcon = (layer: ApiDataSourceLayer) => {
    if (layer.status === 'processing') return <ModernSpinner />;
    if (layer.is_spatial_matched)
      return <Icon name='checkSuccess' width={16} height={16} />;
    if (layer.status === 'failed' || !layer.is_spatial_matched)
      return (
        <Icon name='alert-circle' color='#FFC93E' width={16} height={16} />
      );
  };

  const isSoilSamplingXls =
    task.data_type === DataTypesKey.soilSampling && hasXlsFiles;

  return (
    <ActiveTaskRowStyle disabled={task.disabled}>
      <ActiveTaskRowContainer>
        <ActiveTaskRowHead>
          <ActiveTaskRowLine>
            <ActiveTaskStatusWrapper>
              {hasUnfinishedActiveTask || hasUnmatchedFiles ? (
                <Icon
                  name='alert-circle'
                  color='#FFC93E'
                  className={attentionButtonIconClass}
                />
              ) : (
                <Icon
                  name='checkSuccess'
                  className={attentionButtonIconClass}
                  width={20}
                  height={20}
                />
              )}
            </ActiveTaskStatusWrapper>
            <ActiveTaskRowTitleContainer>
              <ActiveTaskTitleStyle>{task.name}</ActiveTaskTitleStyle>

              <ActiveTaskSubTitleStyle>
                {task.data_type !== DataTypesKey.soilSampling &&
                  t('import.by-machinery.success.title')}
                {!hasUnmatchedFiles &&
                  task.data_type === DataTypesKey.soilSampling &&
                  t('fields.bulk_upload.processing.success')}
              </ActiveTaskSubTitleStyle>
              {hasUnmatchedFiles && (
                <ActiveTaskInfoStyle>
                  {t('import.by-machinery.task.help-info')}
                </ActiveTaskInfoStyle>
              )}
            </ActiveTaskRowTitleContainer>
          </ActiveTaskRowLine>

          {task.data_type !== DataTypesKey.soilSampling ? (
            <CancelButtonStyle
              onClick={e => {
                e.preventDefault();
                setIsShowRemoveAlert(true);
              }}
              disabled={task.disabled}
            >
              <Icon name='close' className={clearButtonIconClass} />
            </CancelButtonStyle>
          ) : (
            <SelectButtonStyle
              type='button'
              onClick={e => {
                e.preventDefault();
                setIsShowRemoveAlert(true);
              }}
              disabled={task.disabled}
            >
              <Icon width={10} height={12} name='trashcan' />
            </SelectButtonStyle>
          )}
        </ActiveTaskRowHead>
        {isShowRemoveAlert && (
          <Alert
            show={true}
            text={t(`import.by-machinery.alert.remove.text`)}
            onDanger={() => {
              dispatch(actions.removeTask(task));
              setIsShowRemoveAlert(false);
            }}
            onCancel={() => setIsShowRemoveAlert(false)}
            danger={t('import.by-machinery.alert.remove.confirm') as string}
            success={t('import.by-machinery.alert.remove.cancel') as string}
          />
        )}

        {isSoilSamplingXls && (
          <ActiveTaskLayersList>
            {task.sources[0]?.layers!.map(layer => (
              <ActiveTaskLayerRow>
                <ActiveTaskLayerIcon>{getTaskIcon(layer)}</ActiveTaskLayerIcon>
                <ActiveTaskLayerContainer>
                  {task.sources[0]?.path} - {layer.name}
                  <SelectButtonStyle
                    type='button'
                    onClick={() => {
                      setModalUuid(layer.uuid);
                    }}
                    disabled={task.disabled}
                  >
                    {layer.is_spatial_matched
                      ? t('import.by-machinery.task.button.edit')
                      : t('import.by-machinery.task.button.procced')}
                  </SelectButtonStyle>
                </ActiveTaskLayerContainer>
              </ActiveTaskLayerRow>
            ))}
          </ActiveTaskLayersList>
        )}
      </ActiveTaskRowContainer>

      <MatchFieldsModal
        uuid={modalUuid}
        isVisible={Boolean(modalUuid)}
        onClose={() => setModalUuid(null)}
      />
    </ActiveTaskRowStyle>
  );
};

export default TaskSuccessRow;
