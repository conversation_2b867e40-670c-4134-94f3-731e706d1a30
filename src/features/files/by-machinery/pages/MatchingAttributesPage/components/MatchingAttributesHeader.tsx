import React from 'react';
import {
  ImportTaskDescription,
  ImportTaskHeader,
  ImportTaskTitle,
} from '../../styles';
import { useTranslate } from 'utils/use-translate';

export type MatchingAttributesHeaderProps = {
  isTableTooWide: boolean;
  dataType: string;
};

export const MatchingAttributesHeader = ({
  isTableTooWide,
  dataType,
}: MatchingAttributesHeaderProps) => {
  const { t } = useTranslate();
  return (
    <ImportTaskHeader isTableTooWide={isTableTooWide}>
      <div>
        <ImportTaskTitle>
          {t(`import.matching_attributes.${dataType}.title`)}
        </ImportTaskTitle>
        <ImportTaskDescription>
          {t(`import.matching_attributes.${dataType}.description`)}
        </ImportTaskDescription>
      </div>
    </ImportTaskHeader>
  );
};
