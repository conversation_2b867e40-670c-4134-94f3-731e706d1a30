import React, { VFC } from 'react';
import cx from 'classnames';

import Icon from 'components/Icon';
import SmartButton from 'components/ui/Button/SmartButton';

import {
  FloatingActionsStyle,
  FloatingActionsDescriptionStyle,
  FloatingActionsNameStyle,
} from './styles';

type FloatingActionsProps = {
  title: string;
  entityName: string;
  cancelLabel: string;
  submitLabel: string;
  onSubmit: VoidCallBack;
  onCancel: VoidCallBack;
  pending?: boolean;
  submitDisabled?: boolean;
};

const FloatingActions: VFC<FloatingActionsProps> = ({
  title,
  entityName,
  pending,
  cancelLabel,
  submitLabel,
  submitDisabled,
  onSubmit,
  onCancel,
}) => {
  return (
    <FloatingActionsStyle>
      <FloatingActionsDescriptionStyle>
        <small>{title}</small>
        <br />
        <FloatingActionsNameStyle>{entityName}</FloatingActionsNameStyle>
      </FloatingActionsDescriptionStyle>
      <SmartButton className='btn btn-dark btn-lg' onClick={onCancel}>
        {cancelLabel}
      </SmartButton>
      <SmartButton
        className={cx('btn btn-success btn-lg', {
          '__no-visual-disable': !submitDisabled,
        })}
        disabled={submitDisabled}
        pending={pending}
        onClick={onSubmit}
      >
        <span className='btn__ico'>
          <Icon className='ico-check-os' name='check' />
        </span>
        {submitLabel}
      </SmartButton>
    </FloatingActionsStyle>
  );
};

export default FloatingActions;
