import React, { useEffect, useState, FC, CSSProperties } from 'react';
import Dayzed from 'dayzed';
import moment from 'moment';
import noop from 'lodash/noop';

import { DateFormat } from 'components/ui/DateInput/constants';
import {
  CalendarNavigation,
  CalendarNavigationProps,
} from './CalendarNavigation';
import { CalendarHeader } from './CalendarHeader';
import { CalendarWeekdays } from './CalendarWeekdays';
import { CalendarDays, CalendarDaysProps } from './CalendarDays';

const toDate = (value: moment.MomentInput): Date | undefined => {
  if (!value) {
    return;
  }

  return moment(value, DateFormat).toDate();
};

const toStringDate = (value: moment.MomentInput) =>
  moment(value).format(DateFormat);

// TODO remove string variant of calendar
export type CalendarPopoverDeprecatedProps<
  T extends [moment.Moment, moment.Moment] | moment.Moment | string,
> = {
  style?: CSSProperties;
  value: T;
  minDate?: string;
  maxDate?: string;
  minYear?: number;
  maxYear?: string;
  visibilitySelected?: CalendarDaysProps['visibilitySelected'];
  renderFooter?: FC<{
    value: T;
    onChange: (newDate: string) => void;
  }>;
  hideBottomPadding?: CalendarDaysProps['hideBottomPadding'];
  openToDate?: moment.Moment;
  onChange: (newDate: string) => void;
  validRange?: string[];
  onMonthClick?: (newMonth: number) => void;
  onYearClick?: (newYear: number) => void;
  onBackArrowClick?: CalendarNavigationProps['onBackArrowClick'];
  onNextArrowClick?: CalendarNavigationProps['onNextArrowClick'];
};

function CalendarPopover<
  T extends [moment.Moment, moment.Moment] | moment.Moment | string,
>(
  {
    style,
    value,
    minDate,
    maxDate,
    minYear = 2000,
    maxYear = moment().add(5, 'years').format('YYYY'),
    renderFooter,
    hideBottomPadding,
    openToDate,
    onChange,
    validRange,
    onMonthClick = noop,
    onYearClick = noop,
    onBackArrowClick = noop,
    onNextArrowClick = noop,
    visibilitySelected,
  }: CalendarPopoverDeprecatedProps<T>,
  ref: React.ForwardedRef<HTMLDivElement>,
) {
  const lastValue: moment.Moment | string = Array.isArray(value)
    ? value[0]
    : value;

  const [date, setDate] = useState(
    (lastValue ? moment(lastValue, DateFormat) : moment(openToDate)).toDate(),
  );
  const [offset, setOffset] = useState(0);

  useEffect(() => {
    setOffset(0);
  }, [date]);

  return (
    <Dayzed
      offset={offset}
      onOffsetChanged={setOffset}
      showOutsideDays
      selected={toDate(lastValue)}
      minDate={toDate(minDate)}
      maxDate={toDate(maxDate)}
      date={date}
      onDateSelected={({ date }) => {
        onChange(moment(date).format(DateFormat));
      }}
      render={({ calendars, getBackProps, getForwardProps, getDateProps }) => {
        if (calendars.length === 0) {
          return null;
        }
        const [calendar] = calendars;

        const yearRange: number[] = [];
        let startDate = moment().year(minYear);
        const endDate = moment().year(parseInt(maxYear));
        while (startDate.isSameOrBefore(endDate)) {
          startDate = startDate.add(1, 'year');
          yearRange.push(+startDate.format('YYYY'));
        }
        if (!calendar) {
          return null;
        }

        if (validRange) {
          calendar.weeks.map(week =>
            week.map(day => {
              if (
                typeof day === 'object' &&
                !validRange.includes(toStringDate(day.date))
              ) {
                day.selectable = false;
              }

              return day;
            }),
          );
        }

        return (
          <div
            ref={ref}
            className='modal-calendar'
            style={{ ...style, zIndex: 1000 }}
          >
            <div className='modal-calendar__inner'>
              <CalendarNavigation
                getBackProps={getBackProps}
                getForwardProps={getForwardProps}
                calendars={calendars}
                calendar={calendar}
                onBackArrowClick={onBackArrowClick}
                onNextArrowClick={onNextArrowClick}
              />
              <CalendarHeader
                calendar={calendar}
                yearRange={yearRange}
                onChangeDate={setDate}
                onChangeMonth={onMonthClick}
                onChangeYear={onYearClick}
              />

              <div className='modal-calendar-area'>
                <div className='modal-calendar-items __anim-allow'>
                  <div className='modal-calendar-items__item'>
                    <div className='modal-calendar-dates'>
                      <table className='modal-calendar-dates__table'>
                        <CalendarWeekdays />
                        <CalendarDays
                          calendar={calendar}
                          hideBottomPadding={hideBottomPadding}
                          getDateProps={getDateProps}
                          visibilitySelected={visibilitySelected}
                        />
                      </table>
                    </div>
                  </div>
                </div>
              </div>
              {renderFooter && renderFooter({ value, onChange })}
            </div>
          </div>
        );
      }}
    />
  );
}

export default React.forwardRef(CalendarPopover);
