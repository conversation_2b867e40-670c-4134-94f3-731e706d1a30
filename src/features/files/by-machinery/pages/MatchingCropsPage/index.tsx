import React, { VFC } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import entities from 'modules/entities';

import {
  cropsMatchMapStepProps,
  MatchCrops,
} from 'components/matching/MatchCrops';
import {
  useFieldDataValuesLoader,
  useActiveDataLayerLoader,
} from 'features/files/by-machinery/dataLoaders';
import { FieldDataValues } from 'features/files/by-machinery/types/server';
import { EntityOperation, StatusType } from 'modules/entities/types';
import { PageStyle } from '../styles';
import { mergeStatuses } from 'modules/entities/utils/mergeStatuses';
import { selectCropsProperties } from '../../redux/selectors';

const REQUEST_ID = 'fieldsDataLayerValueUpdate';

export const MatchingCropsPage: VFC = () => {
  const dispatch = useDispatch();
  const [dataValues, requestValues] = useFieldDataValuesLoader();
  const [, requestLayers] = useActiveDataLayerLoader();
  const cropsPropertyIsReady = useSelector(selectCropsProperties).every(
    v => v.is_values_imported,
  );

  const status = mergeStatuses(requestValues, requestLayers);
  if (
    (status !== StatusType.updating && status !== StatusType.resolved) ||
    !cropsPropertyIsReady
  ) {
    return null;
  }

  const cropDataValues = dataValues.filter(v => v.prop.target_name === 'crop');
  const uniqueMatching = cropDataValues.reduce<Record<string, FieldDataValues>>(
    (acc, value) => {
      acc[`${value.value}:${value.target_value}`] = value;

      return acc;
    },
    {},
  );

  const cropsFromFile = Object.values(uniqueMatching).map(i => i.value);

  const cropsMatchMap = cropDataValues.reduce<Record<string, string>>(
    (acc, value) => {
      if (value.target_value) {
        acc[`${value.value}`] = value.target_value;
      }

      return acc;
    },
    {},
  );

  const setCropsMatchMap: cropsMatchMapStepProps['setCropsMatchMap'] =
    mapForUpdate => {
      const valuesForUpdate = Object.keys(mapForUpdate)
        .map(cropNameInFile =>
          dataValues.filter(
            v =>
              v.value === cropNameInFile &&
              v.target_value !== mapForUpdate[cropNameInFile],
          ),
        )
        .flat(1);

      const requests: EntityOperation[] = valuesForUpdate.map(value => ({
        type: 'patch',
        entityType: 'fieldsDataLayerValue',
        path: {
          id: value.uuid,
        },
        query: {
          include: ['prop.layer.source.upload'],
        },
        body: {
          target_value: mapForUpdate[value.value],
        },
      }));
      dispatch(entities.actions.request(REQUEST_ID, requests));
    };

  return (
    <PageStyle>
      <MatchCrops
        cropsForMatch={cropsFromFile}
        cropsMatchMap={cropsMatchMap}
        setCropsMatchMap={setCropsMatchMap}
      />
    </PageStyle>
  );
};
