import qs from 'qs';
import { BBox } from '@turf/helpers';

import { ServerErrorType } from 'features/fields/UploadFileStep/types';
import { createActions } from 'utils/redux';
import {
  BulkUploadStep,
  FieldsUsersFilesFeature,
  FileBundle,
  FileBundleStatus,
} from 'features/fields/BulkUpload/types';

// @ts-ignore
import { withAPICall } from 'sagas/global/api';

import { FieldsUsersFilesResponse } from 'types/api';
import { FileProcessingStatus } from 'utils/import-fields';

export const actionsConfig = {
  processFiles: () => {},
  setFiles: (files: File[]) => ({ files }),
  setCurrentStep: (step: BulkUploadStep) => ({ step }),
  setReachedStep: (step: BulkUploadStep) => ({ step }),
  setProcessingProgress: (progress: number) => ({ progress }),
  updateProcessingStatus: (status: Record<string, FileProcessingStatus>) => ({
    status,
  }),
  setFileBundles: (bundles: FileBundle[]) => ({
    bundles,
  }),
  importBundles: () => {},
  setBundleStatus: (name: string, status: FileBundleStatus) => ({
    name,
    status,
  }),
  setBundlesStatuses: (statuses: Record<string, FileBundleStatus>) => ({
    statuses,
  }),
  resetBundlesUploadStatus: () => {},
  setBundleUploadingError: (name: string, error: ServerErrorType) => ({
    name,
    error,
  }),
  setBundleUploadingResult: (
    name: string,
    result: FieldsUsersFilesResponse,
  ) => ({
    name,
    result,
  }),
  resetImportedFields: () => {},
  setCropMatchingAvailable: (available: boolean) => ({ available }),
  resetBulkUploadState: () => {},
  setCheckedIDs: (ids: number[]) => ({ ids }),
  setBbox: (bbox: BBox | null) => ({ bbox }),
  fetchFieldsFromFiles: [
    (fileIds: number[]) => ({ fileIds }),
    (fileIds: number[]) => [
      withAPICall('batch', 'POST', {
        body: {
          requests: fileIds.map(fileId => {
            const rootRequestId = 'fetchFieldsFromFiles';

            const query = {
              'exclude[]': ['geom', 'properties'],
              'filter{field_user_file_id}': fileId,
            };

            const serializedQuery = qs.stringify(query, {
              arrayFormat: 'brackets',
            });

            return {
              request_id: `${rootRequestId}-${fileId}`,
              method: 'GET',
              path: `/en/v2/fields-users-files-features?${serializedQuery}`,
              related: [],
            };
          }),
        },
        v: 2,
      }),
    ],
  ] as const,
  createFieldsFromFiles: [
    (seasonId: number, features: FieldsUsersFilesFeature[]) => ({
      seasonId,
      features,
    }),
    (seasonId: number, features: FieldsUsersFilesFeature[]) => {
      const featuresMap = features.reduce<
        Record<number, FieldsUsersFilesFeature[]>
      >((record, feature) => {
        if (!record[feature.file_id]) {
          record[feature.file_id] = [feature];
        } else {
          record[feature.file_id]!.push(feature);
        }
        return record;
      }, {});

      const rootRequestId = 'create-fields-from-files';

      const requests = [];
      for (const [fileId, features] of Object.entries(featuresMap)) {
        requests.push({
          request_id: `${rootRequestId}-${fileId}`,
          method: 'POST',
          path: `/en/v2/fields-users-files/${fileId}/fields`,
          body: { season_id: seasonId, features },
          related: [],
        });
      }

      return [
        withAPICall('batch', 'POST', {
          body: { requests },
          v: 2,
        }),
      ];
    },
  ] as const,
};

export const actions = createActions('bulk-upload', actionsConfig);
