import {
  MetaAttributesWithChildren,
  MetaAttributes<PERSON>pi,
  TasksByMachineryMeta,
} from 'features/files/by-machinery/types';

type MetaPropertiesMap = Record<string, MetaAttributesApi[]>;

const dictionary: Record<string, string> = {
  vr_app_rate_mass_actual: 'osl_rate_actual',
  vr_app_rate_volume_actual: 'osl_rate_actual',
  vr_seed_rate_seeds_actual: 'osl_rate_actual',
  vr_seed_rate_mass_actual: 'osl_rate_actual',

  vr_app_rate_mass_set_point: 'osl_rate_set_point',
  vr_app_rate_volume_set_point: 'osl_rate_set_point',
  vr_seed_rate_seeds_set_point: 'osl_rate_set_point',
  vr_seed_rate_mass_set_point: 'osl_rate_set_point',

  // 'vr_total_quantity_applied_mass': 'osl_rate_set_point',
  // 'vr_total_quantity_applied_volume': 'osl_rate_set_point',

  vr_yield_mass_per_area: 'osl_yield_dry',
  vr_yield_mass: 'osl_yield_dry',
  vr_yield_volume_per_area: 'osl_yield_dry',
  vr_yield_volume: 'osl_yield_dry',

  vr_yield_wet_mass_per_area: 'osl_yield_wet',
  vr_yield_wet_mass: 'osl_yield_wet',

  vr_tillage_depth_control: 'vr_tillage_depth_actual',
};

export const formatMeta = (
  meta: TasksByMachineryMeta,
): TasksByMachineryMeta<MetaAttributesWithChildren> => {
  const properties: MetaAttributesApi[] = meta.groups
    .map(group => group.properties)
    .flat(1);

  const propertiesByParentGroup: MetaPropertiesMap = properties
    .filter(property => !property.match)
    .reduce<MetaPropertiesMap>((acc, property) => {
      const parent = dictionary[property.name];
      if (parent) {
        if (!acc[parent]) {
          acc[parent] = [];
        }
        acc[parent]!.push(property);
      } else {
        // console.error('Not found parent attribute for', property);
      }

      return acc;
    }, {});

  return {
    ...meta,
    groups: meta.groups.map(group => ({
      ...group,
      properties: group.properties
        .filter(property => property.match)
        .map(property => ({
          ...property,
          children: propertiesByParentGroup[property.name] || [],
        })),
    })),
  };
};
