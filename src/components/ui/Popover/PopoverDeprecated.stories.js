import React, { useState, useEffect } from 'react';

import { PopoverDeprecated, PopoverDeprecatedAlign } from './index';
import {
  SimpleTooltip,
  SimpleTooltipTheme,
} from 'components/ui/Tooltip/SimpleTooltip/SimpleTooltip';

export default {
  title: 'common/PopoverDeprecated',
  component: PopoverDeprecated,
  decorators: [storyFn => <div style={{ padding: 16 }}>{storyFn()}</div>],
};

const Box = React.forwardRef(
  ({ style, width, height, color, children, ...otherProps }, ref) => (
    <div
      ref={ref}
      style={{
        display: 'inline-block',
        width: `${width}px`,
        height: `${height}px`,
        border: `2px dashed ${color}`,
        textAlign: 'center',
        ...style,
      }}
      {...otherProps}
    >
      {children}
    </div>
  ),
);

export const About = () => (
  <>
    <p>
      This is a base component for anything that should be aligned and rendered
      above everything on the page. It works for tooltips, modals, popovers,
      popups, chart labels, practically anything.
    </p>
    <p>
      It currently uses <code>fixed</code> positioning, so may lag a bit when
      scrolling
    </p>
  </>
);

export const Simple = () => (
  <PopoverDeprecated
    active
    offset={[0, 10]}
    align={PopoverDeprecatedAlign.BottomMiddle}
    renderTrigger={props => (
      <span {...props}>
        This is a trigger, it is used as a base for popover position, and may
        also trigger popover itself (for tooltips / generic popovers)
      </span>
    )}
    renderPopover={props => (
      <span {...props} style={{ border: '1px dotted black', ...props.style }}>
        This is popover content, currently it's aligned at the bottom of the
        trigger, and at the middle of the content
      </span>
    )}
  />
);

export const Animated = () => {
  const [active, setActive] = useState(false);
  useEffect(() => {
    const timer = setInterval(() => setActive(v => !v), 1000);
    return () => clearInterval(timer);
  }, []);

  return (
    <PopoverDeprecated
      active={active}
      align={PopoverDeprecatedAlign.BottomLeft}
      renderTrigger={props => (
        <span {...props}>
          PopoverDeprecated is adding some css classes to it's content div, so
          you can have nice css in and out transitions
        </span>
      )}
      renderPopover={({ style, ...props }) => (
        <SimpleTooltip
          small
          theme={SimpleTooltipTheme.DARK}
          style={style}
          {...props}
        >
          Cool!
        </SimpleTooltip>
      )}
    />
  );
};

export const WithOffset = () => (
  <PopoverDeprecated
    active
    offset={[25, -25]}
    align={PopoverDeprecatedAlign.BottomRight}
    renderTrigger={props => (
      <Box {...props} width={100} height={100} color='red'>
        You can have different offsets, including negative
      </Box>
    )}
    renderPopover={props => (
      <Box {...props} width={50} height={50} color='green' />
    )}
  />
);

export const Alignments = () => (
  <div style={{ alignItems: 'flex', flexDirection: 'row', flexWrap: 'wrap' }}>
    {[
      PopoverDeprecatedAlign.Centered,
      PopoverDeprecatedAlign.TopLeft,
      PopoverDeprecatedAlign.BottomLeft,
      PopoverDeprecatedAlign.TopRight,
      PopoverDeprecatedAlign.BottomRight,
      PopoverDeprecatedAlign.BottomMiddle,
      PopoverDeprecatedAlign.RightBottom,
      PopoverDeprecatedAlign.MiddleLeft,
      PopoverDeprecatedAlign.MiddleRight,
      PopoverDeprecatedAlign.MiddleTop,
    ].map(align => (
      <PopoverDeprecated
        key={align}
        active
        align={align}
        renderTrigger={props => (
          <Box
            {...props}
            style={{ marginRight: 64, marginBottom: 64, ...props.style }}
            width={100}
            height={100}
            color='red'
          >
            {align}
          </Box>
        )}
        renderPopover={props => (
          <Box {...props} width={50} height={50} color='green' />
        )}
      />
    ))}
  </div>
);
