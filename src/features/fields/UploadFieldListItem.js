import React from 'react';
import cx from 'classnames';

// import { useDispatch, useSelector } from 'react-redux';
import { Controller } from 'react-hook-form';

import Checkbox from 'components/ui/Checkbox/Checkbox';
import BackgroundImage from 'components/ui/BackgroundImage/BackgroundImage';

// import fields from 'modules/fields';

const UploadFieldListItem = ({
  fieldId,
  previewUrl,
  fieldTitle,
  style,
  highlighted,
  index,
  checkedIDs,
  handleCheck,
  onFlyTo,
  onHighlight,
  onRemove,
  onUndoRemove,
}) => {
  // const field = useSelector(state => fields.selectors.getImportedFieldByID(state, fieldId));
  // const dispatch = useDispatch();

  const isChecked = checkedIDs && checkedIDs.includes(fieldId);
  // const onHighlight = () => fields.actions.highlight;
  return (
    <div
      style={style}
      className={cx('soil-fields-list__list-item', {
        __updated: highlighted === fieldId,
      })}
      data-status={!isChecked ? 'noselected' : null}
    >
      {checkedIDs && (
        <div className='soil-fields-list__edit-chk'>
          <Checkbox
            checked={isChecked}
            onChange={event => {
              handleCheck(event, fieldId);
            }}
          />
        </div>
      )}

      <div
        style={{ cursor: 'pointer' }}
        className='soil-fields-list__item'
        onMouseEnter={() => {
          // onHighlight(field.id);
          // dispatch(fields.actions.highlight(fieldId));
        }}
        onMouseLeave={() => {
          // onHighlight(null);
          // dispatch(fields.actions.highlight(null));
        }}
      >
        <BackgroundImage className='soil-fields-list__pic' src={previewUrl} />
        <div className='soil-fields-list__content'>
          <Controller
            as={
              <input
                type='text'
                className='form-input size-full form-size-medium'
              />
            }
            name={`[${index}].title`}
            defaultValue={fieldTitle}
          />
        </div>
      </div>
    </div>
  );
};

export default UploadFieldListItem;
