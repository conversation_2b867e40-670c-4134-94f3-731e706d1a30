import { css } from 'linaria';

export const styles = {
  container: css`
    background-color: #f2f2f7;
    border-radius: 8px;
    box-sizing: border-box;
    padding: 15px 16px 12px;
    width: 100%;
  `,
  header: css`
    align-items: center;
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    width: 100%;
  `,
  labelContainer: css`
    display: flex;
  `,
  iconContainer: css`
    align-items: center;
    display: flex;
    height: 16px;
    margin-right: 7px;
    width: 16px;
  `,
  label: css`
    color: #1c1c1e;
    font-size: 13px;
    font-weight: 600;
    letter-spacing: 0.01em;
    word-break: break-all;
  `,
  inputContainer: css`
    box-sizing: border-box;
    position: relative;
    width: 100%;
  `,
  input: css`
    background-color: #ffffff;
    box-sizing: border-box;
    width: 100%;

    &:disabled {
      background-color: #fafafa;
    }
  `,
  errorMessage: css`
    color: #ff3b30;
    font-size: 11px;
    font-weight: 500;
    margin-top: 8px;
    overflow-wrap: break-word;
    word-break: break-all;
  `,
  commonError: css`
    color: #a52c2c;
    font-size: 11px;
    font-weight: 500;
    margin-top: 8px;
    word-break: break-all;
  `,
  fieldError: css`
    color: #a52c2c;
    word-break: break-all;
  `,
  description: css`
    color: #636366;
    margin-top: 8px;
    word-break: break-all;
  `,
};
