import React, { useEffect } from 'react';
import { Route, matchPath } from 'react-router-dom';
import cx from 'classnames';
import { useDispatch } from 'react-redux';

import TransitionStateMachine from 'components/TransitionStateMachine';
import { TOAST_KEY_TO_FIELDS } from 'features/files/by-machinery/constants';
import toasts from 'modules/toasts';

import FieldListSideBar from './FieldListSideBar';
import EditListSideBar from './EditListSideBar';
import AddFieldsSideBar from './AddFieldsSideBar';
import FieldListSettingsSideBar from './FieldListSettingsSideBar/FieldListSettingsSideBar';

import { useHistoryFieldDataLoader } from '../files/by-machinery/dataLoaders';

const FieldsSideBar = ({ match, location, history }) => {
  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(toasts.actions.removeGroup(TOAST_KEY_TO_FIELDS));
  }, [dispatch]);

  const editFieldsMatch = matchPath(location.pathname, {
    path: `${match.url}/edit`,
  });
  const selectFieldsMatch = matchPath(location.pathname, {
    path: `${match.url}/add/:mode(select|create)`,
  });
  const settingsFieldsMatch = matchPath(location.pathname, {
    path: `${match.url}/list/settings`,
  });
  useHistoryFieldDataLoader();

  return (
    <TransitionStateMachine
      enterTime={0}
      exitTime={300}
      active={[editFieldsMatch, selectFieldsMatch, settingsFieldsMatch].some(
        Boolean,
      )}
      render={({ state, wrapActiveChild }) => (
        <div className={cx('soil-sidebar', { __addnew: state === 'entered' })}>
          <Route
            path={`${match.url}/:fieldId(\\w\\d+)?`}
            exact
            component={FieldListSideBar}
          />
          {editFieldsMatch &&
            wrapActiveChild(
              <EditListSideBar
                history={history}
                location={location}
                match={editFieldsMatch}
              />,
            )}
          {selectFieldsMatch &&
            wrapActiveChild(
              <AddFieldsSideBar
                history={history}
                location={location}
                match={selectFieldsMatch}
              />,
            )}
          {settingsFieldsMatch &&
            wrapActiveChild(
              <FieldListSettingsSideBar
                history={history}
                location={location}
                match={settingsFieldsMatch}
              />,
            )}
        </div>
      )}
    />
  );
};

export default FieldsSideBar;
