import React from 'react';
import { useSelector } from 'react-redux';

import { selectors } from 'features/fields/BulkUpload/redux/selectors';

import { UploadingTasksArea } from 'features/fields/BulkUpload/styles';
import { FileBundleList } from 'features/fields/BulkUpload/components';

export const CheckDataStep = () => {
  const bundles = useSelector(selectors.getFileBundles);

  return (
    <UploadingTasksArea>
      <FileBundleList bundles={bundles} />
    </UploadingTasksArea>
  );
};
