import React, { ReactNode } from 'react';
import ModernSpinner, { SpinnerTheme } from './ModernSpinner';

export default {
  title: 'ui/ModernSpinner',
  component: ModernSpinner,
  decorators: [
    (storyFn: () => ReactNode) => (
      <div style={{ padding: 16 }}>{storyFn()}</div>
    ),
  ],
};

export const Basic = () => <ModernSpinner />;

export const ThemeBlack = () => <ModernSpinner theme={SpinnerTheme.Black} />;

export const ThemeGreen = () => <ModernSpinner theme={SpinnerTheme.Green} />;

export const ThemeWhite = () => <ModernSpinner theme={SpinnerTheme.White} />;

export const Large = () => <ModernSpinner large />;
