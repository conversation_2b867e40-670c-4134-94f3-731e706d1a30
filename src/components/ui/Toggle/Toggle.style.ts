import { css } from 'linaria';

export const styles = {
  wrapper: css`
    background-color: rgba(228, 231, 234, 1);
    border-radius: 8px;
    padding: 2px;
    color: rgba(34, 34, 34, 1);
    font-size: 14px;
    flex-grow: 1;
    display: flex;

    input[type='radio'] {
      box-sizing: border-box;
      padding: 0;
      position: absolute;
      left: -9999px;
      opacity: 0;

      &:checked + label {
        font-weight: 500
      }
    },
  `,

  disabledWrapper: css`
    background-color: rgba(245, 247, 249, 1);
    color: rgba(165, 178, 188, 1);

    input[type='radio']:checked + label {
      color: rgba(94, 94, 94, 1);
    }
  `,

  content: css`
    position: relative;
    display: grid;
    flex-grow: 1;
    grid-template-columns: repeat(var(--item-count), 1fr);
  `,

  group: css`
    display: flex;
    flex-direction: column;
    justify-content: center;
  `,

  label: css`
    cursor: pointer;
    position: relative;
    z-index: 2;
    display: flex;
    min-height: 38px;
    align-items: center;
    text-align: center;
    padding: 0 4px;
    user-select: none;
    font-weight: normal;
    flex-grow: 1;
  `,

  label__small: css`
    min-height: 34px;
  `,

  value: css`
    flex-grow: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 18px;
    gap: 8px;
    white-space: pre;
    margin: 0 4px;
  `,

  background: css`
    position: absolute;
    z-index: 1;
    left: 0;
    top: 0;
    bottom: 0;
    border-radius: 6px;
    background-color: #fff;
    transition: transform 0.15s;
  `,

  disabledBackground: css`
    background-color: rgba(245, 247, 249, 1);
    border: 2px solid rgba(228, 231, 234, 1);
  `,
};
