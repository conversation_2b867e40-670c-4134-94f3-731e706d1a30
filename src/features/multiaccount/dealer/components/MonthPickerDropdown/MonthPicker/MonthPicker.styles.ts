import { css } from 'linaria';

export const styles = {
  monthPickerContainer: css`
    background: white;
    border-radius: 8px;
    box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.1),
      0px 16px 32px 0px rgba(0, 0, 0, 0.15);
    min-width: 268px;
  `,
  yearHeader: css`
    align-items: flex-start;
    align-self: stretch;
    border-bottom: 1px solid #f2f2f7;
    display: flex;
    padding: 4px 4px 3px 4px;
  `,
  yearNavButton: css`
    align-items: center !important;
    all: unset;
    display: flex;
    height: 40px;
    justify-content: center;
    width: 40px;

    &:hover:not(:disabled) {
      background-color: #f5f5f5;
      color: #333;
    }

    &:disabled {
      color: #ccc;
      cursor: not-allowed;
    }
  `,
  yearLabelWrap: css`
    align-items: center;
    align-self: stretch;
    display: flex;
    flex: 1 0 0;
    gap: 4px;
    justify-content: center;
  `,
  yearLabel: css`
    color: #1c1c1e;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 130%;
  `,
  monthGrid: css`
    display: grid;
    gap: 8px;
    grid-template-columns: repeat(3, 1fr);
    padding: 12px 11px;
  `,
  monthButton: css`
    align-items: center;
    align-self: stretch;
    all: unset;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    gap: 8px;
    justify-content: center;
    padding: 14px 8px;

    color: #1c1c1e;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 130%;
    text-align: center;

    &:hover:not(:disabled) {
      background: #f2f2f7;
    }

    &:focus {
      border: 1px solid #1c1c1e;
    }
  `,
  monthButtonSelected: css`
    border: 1px solid #1c1c1e;
    font-weight: 500;

    &:hover {
      background: #f2f2f7;
    }
  `,
  monthButtonDisabled: css`
    background-color: #f9f9f9;
    border-color: #f0f0f0;
    color: #ccc;
    cursor: not-allowed;

    &:hover {
      background-color: #f9f9f9;
      border-color: #f0f0f0;
    }
  `,
};
