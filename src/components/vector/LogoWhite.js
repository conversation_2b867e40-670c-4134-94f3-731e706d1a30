import React from 'react';

const LogoWhite = () => (
  <svg>
    <path d='M99.74305,21.66506h4.16574v22.98652h-4.16574zM93.2636,28.18716h4.16574v16.49136h-4.16574zM97.17307,21.84254c0.97408,0.96862 0.97408,2.72328 0,3.7225c-0.97425,0.99822 -2.6855,0.99822 -3.65958,0c-0.97425,-0.99922 -0.97425,-2.75388 0,-3.7225c0.97408,-0.99823 2.68533,-0.99823 3.65958,0zM86.18792,39.7601c0.85251,-0.93803 1.29468,-2.05747 1.29468,-3.35867c0,-1.30103 -0.44217,-2.42048 -1.29468,-3.3289c-0.85874,-0.90743 -1.89058,-1.36123 -3.1595,-1.36123c-1.26908,0 -2.33291,0.4538 -3.19165,1.36123c-0.85251,0.90842 -1.26908,2.02787 -1.26908,3.3289c0,1.3012 0.41657,2.42064 1.26908,3.35867c0.85874,0.9076 1.92257,1.3614 3.19165,1.3614c1.26892,0 2.30076,-0.4538 3.1595,-1.3614zM83.01064,27.74702c2.51078,0 4.57965,0.8474 6.20022,2.54186c1.62056,1.66421 2.42125,3.72168 2.42125,6.14232c0,2.45108 -0.80069,4.50871 -2.42125,6.17275c-1.62057,1.6642 -3.68944,2.51144 -6.20022,2.51144c-2.50455,0 -4.57342,-0.84724 -6.19399,-2.51144c-1.62696,-1.66404 -2.42109,-3.72167 -2.42109,-6.17275c0,-2.42064 0.79413,-4.47811 2.42109,-6.14232c1.62057,-1.69446 3.68944,-2.54186 6.19399,-2.54186zM59.93033,37.72269c1.41642,2.02705 3.54424,3.11689 5.7874,3.11689c1.80084,0 3.1275,-0.96862 3.1275,-2.39088c0,-0.90759 -0.64725,-1.69463 -1.98032,-2.32985c-0.26268,-0.13339 -0.83955,-0.36152 -1.76884,-0.72519l-0.38458,-0.15181c-1.12158,-0.45462 -1.98032,-0.87783 -2.56999,-1.21041c-2.30075,-1.36205 -3.45449,-3.20767 -3.45449,-5.56795c0,-1.87605 0.67941,-3.41887 2.07007,-4.6597c1.38442,-1.24084 3.18542,-1.87622 5.37083,-1.87622c2.83923,0 5.22973,0.99839 7.17806,3.02626l-2.33291,3.26787c-1.21133,-1.33162 -3.15966,-2.08806 -4.78756,-2.08806c-1.73685,0 -2.80068,0.84723 -2.80068,2.14826c0,0.87783 0.58967,1.60384 1.76884,2.20863l2.15982,0.96862c1.20493,0.51482 2.09583,0.90743 2.65334,1.17981c2.48024,1.24067 3.72373,3.08629 3.72373,5.56796c0,1.99661 -0.73717,3.63088 -2.21757,4.90149c-1.47401,1.27126 -3.39674,1.90664 -5.78724,1.90664c-3.3969,0 -6.46665,-1.48262 -8.26765,-4.17547zM53.52782,34.76634c-0.38458,-2.14826 -1.89057,-3.44946 -4.012,-3.44946c-2.09583,0 -3.66598,1.3012 -4.1659,3.44946zM45.31793,37.90794c0.58967,2.3285 2.15342,3.50845 4.69142,3.50845c1.74324,0 3.81331,-0.696 5.19757,-1.6639l1.6535,2.99377c-2.03792,1.51233 -4.36443,2.26834 -7.02417,2.26834c-2.83283,0 -4.99265,-0.84674 -6.49865,-2.50982c-1.50616,-1.66324 -2.24316,-3.71952 -2.24316,-6.10901c0,-2.50998 0.769,-4.56643 2.33291,-6.19892c1.56375,-1.63349 3.57607,-2.44982 6.05648,-2.44982c2.33275,0 4.28108,0.75617 5.78724,2.23792c1.50616,1.4816 2.26876,3.47788 2.26876,6.04803c0,0.57469 -0.05759,1.20954 -0.14734,1.87496zM28.49436,35.8514v8.83079h-4.1659v-16.48163h4.07615v2.29872c0.85235,-1.60289 2.77508,-2.75226 5.05024,-2.75226c1.74325,0 3.24941,0.60525 4.45434,1.78437c1.21133,1.17911 1.83299,2.87258 1.83299,5.08073v10.07007h-4.1659v-8.92152c0,-2.63111 -1.35227,-4.08227 -3.39035,-4.08227c-2.21756,0 -3.69157,1.72338 -3.69157,4.17301zM11.37095,26.33677c-1.91489,0 -3.50955,0.69624 -4.83523,2.05764c-1.33209,1.36123 -1.97894,3.08629 -1.97894,5.1135c0,2.02786 0.64685,3.72167 1.9469,5.08373c1.3257,1.36123 2.95238,2.02704 4.86726,2.02704c1.92129,0 3.54157,-0.66581 4.87367,-2.02704c1.32562,-1.36206 1.97256,-3.05587 1.94698,-5.08373c0,-2.05764 -0.65334,-3.75227 -1.979,-5.1135c-1.3257,-1.3614 -2.92036,-2.05764 -4.84165,-2.05764zM0,33.57699c0,-3.23745 1.08953,-5.99133 3.27498,-8.26016c2.21752,-2.26965 4.90289,-3.38926 8.09456,-3.38926c3.19167,0 5.877,1.11961 8.06258,3.38926c2.21101,2.26883 3.30699,4.99212 3.30699,8.19979c0,3.20768 -1.09598,5.93113 -3.30699,8.17019c-2.18558,2.23922 -4.87091,3.35867 -8.09462,3.35867c-3.21732,0 -5.93473,-1.08984 -8.08814,-3.29847c-2.15983,-2.20847 -3.24936,-4.93192 -3.24936,-8.17002zM107.61973,15.37095c0,-8.38941 3.94145,-14.12932 3.94145,-14.12932l0.95489,-1.24163c0.46153,0.2313 1.69205,0.57182 3.24941,1.00069c2.42265,0.66659 5.6207,1.54841 7.92146,2.57481c1.88434,0.84408 3.79412,1.99496 5.22973,3.78431c1.44201,1.79016 2.33931,4.22683 2.33931,7.12772c0,3.12246 -1.34587,5.34556 -2.70453,6.6234c-0.65382,0.61515 -1.32027,1.04083 -1.85219,1.32423c0.6023,2.98036 0.62166,5.88211 0.62166,8.26344h-3.94161v-0.76861c0.01279,-5.29986 0.03215,-10.77949 -5.72326,-15.44246l2.39707,-3.21651c2.68533,2.17565 4.40938,4.62035 5.4796,7.12765l0.10895,-0.08504c0.06415,-0.04655 0.12174,-0.08997 0.19869,-0.16711c0.7306,-0.69394 1.48056,-1.66404 1.48056,-3.65899c0,-2.08892 -0.58966,-3.42935 -1.48056,-4.54087c-0.8909,-1.11232 -2.15342,-2.02708 -3.69158,-2.71294c-1.53815,-0.68908 -3.15966,-1.15328 -4.72981,-1.60222c-1.08959,-0.31161 -2.15342,-0.616 -3.15326,-0.98384c-0.7498,1.24565 -2.70453,4.80188 -2.70453,10.72329c0,3.84781 1.01904,5.49573 1.90338,6.30776c0.8909,0.81187 1.93553,0.94609 2.33931,0.94609c0.51272,0 1.38442,-0.39607 2.4996,-0.90118c0.42938,-0.1936 0.89074,-0.40314 1.38426,-0.61269l1.47417,3.72168c-0.32699,0.14046 -0.67301,0.3204 -1.0384,0.51153c-1.15357,0.60068 -2.53143,1.31715 -4.31963,1.31715c-1.07663,0 -3.1467,-0.32287 -4.98609,-2.01833c-1.83299,-1.69529 -3.19805,-4.6597 -3.19805,-9.27201z' />
  </svg>
);

export default LogoWhite;
