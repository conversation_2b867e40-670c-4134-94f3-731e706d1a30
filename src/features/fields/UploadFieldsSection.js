import React from 'react';

import { FEATURES, ProtectedRouteHOC } from 'features/permissions';
import { BulkUploadPage } from 'features/fields/BulkUpload/BulkUploadPage';

const UploadFieldsSection = ({ history, location }) => {
  return <BulkUploadPage history={history} location={location} />;
};

export default ProtectedRouteHOC({
  Component: UploadFieldsSection,
  feature: FEATURES.FIELD_ADD,
  redirect: '/fields',
});
