import { ColorScale } from 'constants/style';
import { FileColumn, FileFullData } from 'types/filesState';
import { OcenaColorMap } from './constants';
import { getOcenaParentColumn } from './utils';
import { ColorSchemeGetter } from '../types';

export const getOcenaColorScheme: ColorSchemeGetter = (
  column: FileColumn,
  file: FileFullData,
) => {
  const parentColumn = getOcenaParentColumn(file, column);
  const columnForColor = parentColumn ? parentColumn.name : column.name;

  return (properties: TEMPORARY_ANY, zoom: number, type: number) => {
    const baseColor = ColorScale(
      (properties[column.name] - column.min) / (column.max - column.min),
    ).hex();

    return {
      color: type === 1 ? baseColor : 'gray',
      fillColor: OcenaColorMap[properties[columnForColor]] as string,
      fillOpacity: 1,
      weight: 0.5,
      radius: 3.75,
    };
  };
};
