import React, { useEffect } from 'react';
import { Controller, useWatch } from 'react-hook-form';
import { useSelector } from 'react-redux';
import { cx } from 'linaria';

// @ts-ignore
import Toggle from 'components/Toggle';
// @ts-ignore
import TextInput from 'components/forms/TextInput';

import settings from 'modules/settings';
import { ErrorData } from 'modules/entities/types';

import { LimitTypes, EntityWithLimits } from 'types/multiaccount';
import { UnitSystemType } from 'types/units';

import { ClientHookForm } from 'features/multiaccount/dealer/modals/ClientForm/types';
import { getTranslateKey } from 'features/multiaccount/dealer/utils';

// @ts-ignore
import { convert } from 'utils/units';
import { roundUp } from 'utils/format-number';
import { useTranslate } from 'utils/use-translate';

import { styles } from './ClientCommercialLimitField.styles';

export type ClientCommercialLimitFieldProps = {
  form: ClientHookForm;

  requestErrors: Nullable<ErrorData>;
  isPending: boolean;
  client?: EntityWithLimits;
};

const getCommercialLimits = (
  unitType: LimitTypes,
  units: UnitSystemType,
  client?: EntityWithLimits,
) => {
  if (!client) {
    return 0;
  }
  if (unitType === LimitTypes.fields) {
    return client.used_fields || 0;
  }
  return roundUp(
    Number(convert(Number(client.used_ha)).from('ha').to(units.area.unit)),
    1,
  );
};

const ClientCommercialLimitField = ({
  client,
  form,
  isPending,
}: ClientCommercialLimitFieldProps) => {
  const { t } = useTranslate();
  const { errors, watch, control, getValues, trigger } = form;
  const unitType = useWatch<LimitTypes>({
    control,
    name: 'unit_type',
    defaultValue: LimitTypes.hectares,
  });
  const units: UnitSystemType = useSelector(settings.selectors.getUnits);
  const usedCommercialLimits = getCommercialLimits(unitType, units, client);
  const hasLimits = client?.status === 'active' && !!client?.accountUuid;
  const commercialLimits = watch('commercial_limits');
  const testLimits = watch('test_limits');
  const validateAtLeastOne = () => {
    return !!commercialLimits || !!testLimits;
  };

  useEffect(() => {
    // trigger validation for limits after changing unitType
    trigger(['test_limits', 'commercial_limits']);
  }, [unitType, testLimits, commercialLimits, trigger]);

  let unitKey = getTranslateKey(getValues('unit_type'));
  if (unitType === LimitTypes.hectares && units.area.unit === 'ac') {
    unitKey = 'acres';
  }

  return (
    <div className='form-group'>
      <label className='form-label'>
        {t('multiaccount.invite-client-modal.column.commercial-limits.title')}
      </label>
      <label className='form-label-descr'>
        {t(
          'multiaccount.invite-client-modal.column.commercial-limits.subtitle',
        )}
      </label>
      <Controller
        name='commercial_limits'
        control={control}
        disabled={isPending}
        rules={{
          validate: validateAtLeastOne,
          required: hasLimits,
          min: {
            value: usedCommercialLimits,
            // @ts-ignore
            message: t(
              // @ts-ignore
              `multiaccount.dealer.error.paid.min.${unitKey}`,
              {
                value: usedCommercialLimits,
              },
            ),
          },
        }}
        render={({ onChange, value }, { invalid }) => {
          return (
            <>
              <div className='form-group-grid __sps-default'>
                <div className='form-group-grid__col size-half'>
                  <TextInput
                    id='commercial_limits'
                    disabled={isPending}
                    tabIndex={1}
                    isValid={!invalid}
                    className={cx('size-full __fluid', styles.field)}
                    numeric
                    positiveOnly
                    precision={1}
                    value={value}
                    onChange={onChange}
                    hideArrows
                    groupThousands={false}
                  />
                </div>
                <div className='form-group-grid__col size-half'>
                  <Controller
                    name='unit_type'
                    control={control}
                    disabled={isPending}
                    render={({ onChange: onChangeUnit, value: unitValue }) => (
                      <Toggle
                        titleKey='label'
                        value={unitValue}
                        options={(
                          ['hectares', 'fields'] as (keyof typeof LimitTypes)[]
                        ).map(key => {
                          let labelKey = `multiaccount.invite-client-modal.column.commercial-limits.${key}`;
                          if (key === 'hectares' && units.area.unit === 'ac') {
                            labelKey = `multiaccount.invite-client-modal.column.commercial-limits.acres`;
                          }
                          return {
                            id: LimitTypes[key],
                            label: t(labelKey),
                          };
                        })}
                        onChange={(v: LimitTypes) => {
                          onChangeUnit(v);
                          if (v === LimitTypes.fields) {
                            if (value) {
                              onChange(parseInt(value));
                            }
                          }
                        }}
                      />
                    )}
                  />
                </div>
              </div>
              {errors.commercial_limits && (
                <div className='error-message c-error'>
                  {errors.commercial_limits.message}
                </div>
              )}
            </>
          );
        }}
      />
    </div>
  );
};

export default ClientCommercialLimitField;
