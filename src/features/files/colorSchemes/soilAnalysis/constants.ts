import { SoilElementParameter } from './types';

const defaultPattern = ['#FF3B30', '#FE9800', '#FFEA30', '#27AE60', '#6337FF'];
const pHKClPattern = [
  '#8A3200',
  '#FF3B30',
  '#FE9800',
  '#FFEA30',
  '#3AAE27',
  '#007531',
];

const parameters: SoilElementParameter[] = [
  {
    names: ['pH (H2O)', 'ph_H20'],
    breakPoints: [5.4, 5.5, 5.8, 6.3, 7.4],
    colorsPattern: defaultPattern,
  },
  {
    names: ['pH (KCl)', 'ph_KC'],
    breakPoints: [4, 4.1, 4.6, 5.1, 5.6, 6.1],
    colorsPattern: pHKClPattern,
  },
  {
    names: ['OM', 'ORG'],
    breakPoints: [0, 1.1, 2.1, 4.1, 6.0],
    colorsPattern: defaultPattern,
  },
  {
    names: ['NO3', 'N03'],
    breakPoints: [0, 2.6, 5.1, 10.1, 15],
    colorsPattern: defaultPattern,
  },
  {
    names: ['N'],
    breakPoints: [0, 11, 16, 25, 31],
    colorsPattern: defaultPattern,
  },
  {
    names: ['P2O5_Chir'],
    breakPoints: [0, 21, 51, 101, 151],
    colorsPattern: defaultPattern,
  },
  {
    names: ['P2O5_Mach'],
    breakPoints: [0, 11, 16, 30, 60],
    colorsPattern: defaultPattern,
  },
  {
    names: ['P_Bray1'],
    breakPoints: [0, 6, 16, 36, 50],
    colorsPattern: defaultPattern,
  },
  {
    names: ['P_Mehl_3'],
    breakPoints: [0, 6, 16, 36, 50],
    colorsPattern: defaultPattern,
  },
  {
    names: ['P (Olsen)', 'P_Olsen'],
    breakPoints: [0, 4, 10, 17, 30],
    colorsPattern: defaultPattern,
  },
  {
    names: ['K2O_Chir'],
    breakPoints: [0, 21, 41, 81, 181],
    colorsPattern: defaultPattern,
  },
  {
    names: ['K2O_Mach'],
    breakPoints: [0, 11, 16, 31, 60],
    colorsPattern: defaultPattern,
  },
  {
    names: ['K2O_Kirs'],
    breakPoints: [0, 26, 51, 101, 250],
    colorsPattern: defaultPattern,
  },
  {
    names: ['K_NH4Ac'],
    breakPoints: [0, 51, 101, 151, 200],
    colorsPattern: defaultPattern,
  },
  {
    names: ['Ca_NH4Ac'],
    breakPoints: [0, 501, 1001, 3001, 5000],
    colorsPattern: defaultPattern,
  },
  {
    names: ['Mg_NH4Ac'],
    breakPoints: [0, 26, 51, 251, 500],
    colorsPattern: defaultPattern,
  },
  {
    names: ['SO4_P500'],
    breakPoints: [0, 2.6, 5.1, 10.1, 15.0],
    colorsPattern: defaultPattern,
  },
  {
    names: ['Zn_DTPA'],
    breakPoints: [0, 0.26, 0.51, 1.0],
    colorsPattern: defaultPattern,
  },
  {
    names: ['Mn_DTPA'],
    breakPoints: [0, 1.1, 3.1, 5.0],
    colorsPattern: defaultPattern,
  },
  {
    names: ['Cu_DTPA'],
    breakPoints: [0, 0.21, 0.25, 0.65],
    colorsPattern: defaultPattern,
  },
  {
    names: ['Fe_DTPA'],
    breakPoints: [0, 2.1, 4.6, 10.0],
    colorsPattern: defaultPattern,
  },
  {
    names: ['B_HW'],
    breakPoints: [0, 0.11, 0.41, 1.5],
    colorsPattern: defaultPattern,
  },
  {
    names: ['Al_KCl'],
    breakPoints: [0, 2, 6, 21],
    colorsPattern: defaultPattern,
  },
  {
    names: ['CEC'],
    breakPoints: [0, 6, 10, 16, 22],
    colorsPattern: defaultPattern,
  },
  {
    names: ['SS'],
    breakPoints: [0.1, 0.11, 0.51, 1.0, 1.5],
    colorsPattern: defaultPattern,
  },
];

export const normalizeColumnName = (str: string): string => {
  return str.toLowerCase().replace(/[^a-zA-Z0-9]/g, '');
};

export const normalizedParametersMap = parameters.reduce<
  Record<string, SoilElementParameter>
>((acc, parameter) => {
  parameter.names.forEach(parameterName => {
    acc[normalizeColumnName(parameterName)] = parameter;
  });

  return acc;
}, {});
