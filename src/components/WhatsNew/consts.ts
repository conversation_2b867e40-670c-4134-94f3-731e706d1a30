export const FeatureWebIdentifier = 'whatsnew-web-';

export enum FeaturesKeyEnum {
  allFieldsNdvi = 'whatsnew-web-all-fields-ndvi',
  cloudFree = 'whatsnew-web-cloudfree',
  colors = 'whatsnew-web-colors',
  contrastNdvi = 'whatsnew-web-contrast-ndvi',
  cropRotation = 'whatsnew-web-crop-rotation',
  cropRotationV2 = 'whatsnew-web-crop-rotation-v2',
  crops140 = 'CROPS-140',
  fieldBoundaries = 'whatsnew-web-field-boundaries',
  rgbLayer = 'whatsnew-web-rgb-layer',
  multiAccountHint = 'hint_about_multi_account',
  hintAboutRgbLayer = 'hint_about_rgb_layer',
  mapColors = 'MAP-COLORS',

  hungarian = 'whatsnew-web-hungarian',
  import = 'whatsnew-web-import',
  polish = 'whatsnew-web-polish',
  turkish = 'whatsnew-web-turkish',
  ukrainian = 'whatsnew-web-ukrainian',
  analysis = 'whatsnew-web-analysis',
  sharedFile = 'whatsnew-web-shared-file',
  multiAccount = 'whatsnew-web-multi-account',

  yieldFunnelShown = 'yield_funnel_shown',
  vraPreviewShown = 'vra_preview_shown',

  // for test!
  testKey = 'key',
  testWebKey = 'whatsnew-web-key',
  vraModalPopup = 'vra-modal-popup',
}
