import React, { VFC } from 'react';

import { TaskSuccessType } from 'features/files/by-machinery/types';

import TaskHistoryRow from './row/TaskHistoryRow';
import { HistoryTasksListStyle } from './styles';
import { useHistoryFieldDataLoader } from 'features/files/by-machinery/dataLoaders';

type ActiveTasksListProps = {
  tasks: TaskSuccessType[];
};

const HistoryTasksList: VFC<ActiveTasksListProps> = ({ tasks }) => {
  useHistoryFieldDataLoader();

  return (
    <HistoryTasksListStyle>
      {tasks.map(task => (
        <TaskHistoryRow task={task} key={task.uuid} />
      ))}
    </HistoryTasksListStyle>
  );
};

export default HistoryTasksList;
