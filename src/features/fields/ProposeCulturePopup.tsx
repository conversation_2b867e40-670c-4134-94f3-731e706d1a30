import React, { VFC } from 'react';
import { connect } from 'react-redux';
import { Form, Control } from 'react-redux-form';

import Icon from 'components/Icon';
import Saga from 'components/saga/Saga';
import Button from 'components/ui/Button/Button';
import Checkbox from 'components/ui/Checkbox/Checkbox';
import SubmitButton from 'components/forms/SubmitButton';
import ClearableInput from 'components/ClearableInput';
import RenderAboveEverything from 'components/RenderAboveEverything';

import auth from 'modules/auth';
// @ts-ignore
import fields from 'modules/fields';

import forms from 'constants/forms';
// @ts-ignore
import proposeCropSaga from 'sagas/local/propose-crop';
import { isEmail, required } from 'utils/validators';
import { useTranslate } from 'utils/use-translate';

type ProposeCulturePopupProps = {
  isValidEmail: boolean;
  onCancel: () => void;
  onSubmit: () => void;
};

const ProposeCulturePopup: VFC<ProposeCulturePopupProps> = ({
  isValidEmail,
  onCancel,
  onSubmit,
}) => {
  const { t } = useTranslate();
  return (
    <RenderAboveEverything>
      <Saga saga={proposeCropSaga} onDone={onCancel} />
      <div className='modal modal-editseasons'>
        <div className='modal-outer'>
          <div className='modal-editseasons-content'>
            <Form model={forms.propose} onSubmit={onSubmit}>
              <div className='modal-editseasons-content__header'>
                {t(`propose.title`)}
              </div>
              <div className='modal-editseasons-content__description'>
                {t(`propose.description`)}
              </div>
              <div className='modal-editseasons-content__body'>
                <div className='form-group'>
                  <label className='form-label'>
                    {t(`propose.crop.label`)}
                  </label>
                  <Control.text
                    model='.crop'
                    component={ClearableInput}
                    placeholder={t('propose.crop.placeholder') as string}
                    validators={{ required }}
                  />
                </div>
                <div className='form-group'>
                  <Control.checkbox
                    model='.notify'
                    component={Checkbox}
                    children={t('propose.send_email')}
                  />
                </div>
                {!isValidEmail && (
                  <div className='form-group'>
                    <div className='form-label'>{t('propose.email.label')}</div>
                    <Control.text
                      tabIndex={1}
                      model='.email'
                      component={ClearableInput}
                      validators={{ required, isEmail }}
                      placeholder={t('propose.email.placeholder') as string}
                    />
                  </div>
                )}
              </div>
              <div className='modal-content__actions'>
                <Button className='btn btn-primary btn-lg' onClick={onCancel}>
                  {t('forms.cancel')}
                </Button>
                <SubmitButton
                  native
                  model={forms.propose}
                  render={props => (
                    <Button
                      className='btn btn-success btn-lg'
                      type='submit'
                      {...props}
                    >
                      <span className='btn__ico'>
                        <Icon className='ico-check-os' name='check' />
                      </span>
                      {t(`forms.send`)}
                    </Button>
                  )}
                />
              </div>
            </Form>
          </div>
        </div>
      </div>
    </RenderAboveEverything>
  );
};

const mapStateToProps = (state: TEMPORARY_ANY, ownProps: TEMPORARY_ANY) => ({
  isValidEmail: auth.selectors.isValidEmail(state),
});

const mapDispatchToProps = {
  onSubmit: fields.actions.proposeCrop,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(ProposeCulturePopup);
