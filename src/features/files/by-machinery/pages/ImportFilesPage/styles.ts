import { styled } from 'linaria/react';
import { css } from 'linaria';

export const ImportTaskSection = styled.div``;

export const DropAreaStyle = styled.div`
  height: 200px;
  width: 670px;
  border: 2px dashed rgba(165, 178, 188, 0.4);
  border-radius: 10px;
  margin: 0 0 38px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
`;

export const DropAreaTitleStyle = styled.span`
  font-style: normal;
  font-weight: 700;
  font-size: 24px;
  line-height: 32px;
`;

export const DropAreaDescriptionStyle = styled.span`
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  text-align: center;
`;

export const UploadingTasksArea = styled.div`
  width: 670px;
  background: #f5f7f9;
  padding: 20px;
  margin-top: 20px;
  border-radius: 10px;
`;

export const successToastIcon = css`
  color: #27ae60;
  height: 12px;
  padding-right: 12px;
`;

export const failedToastIcon = css`
  color: #ff3b30;
  margin-right: 5px;
  margin-top: -3px;
`;

export const MatchFieldsInfoSection = styled.div`
  display: flex;
  background-color: #f5f7f9;
  border-radius: 8px;
  padding: 12px;
  gap: 12px;
  font-size: 14px;
  line-height: 18px;
  font-weight: 400;
  margin: 12px 0 24px 0;
`;

export const MatchFieldsInfoSectionText = styled.div`
  & > a {
    text-decoration: underline;
    color: #1c1c1e;
    cursor: pointer;
  }
`;

export const selectBoxWrapper = css`
  .form-select {
    max-height: 42px;
  }
`;

export const IconInfo = css`
  width: 60px;
`;

export const Overlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.4;
  background-color: #fff;
  z-index: 2;
`;

export const Fields = styled.div`
  position: relative;
`;
