import React, { VFC } from 'react';
import { useSelector } from 'react-redux';

import { useTranslate } from 'utils/use-translate';

import entities from 'modules/entities';
import { useDelayedState } from 'utils/optimization/useDelayedState';

import { SelectBox } from 'components/ui/CustomDropdown';

import {
  MetaAttributesApi,
  ApiDataFieldLayer,
  DataTypesKey,
} from 'features/files/by-machinery/types';
import { OnSetMethodType } from 'features/files/by-machinery/pages/MatchingAttributesPage/service/useSetMethod';

import { Skeleton, unitSelectBoxWrapper } from './styles';

type MethodSelectBoxProps = {
  attribute: MetaAttributesApi;
  layer: ApiDataFieldLayer;
  onSetMethod: OnSetMethodType;
};

export const MethodSelectBox: VFC<MethodSelectBoxProps> = ({
  attribute,
  layer,
  onSetMethod,
}) => {
  const { t } = useTranslate();
  const methods = attribute.extra_fields.method.values || [];
  const currentProperty = layer.properties.find(
    property => property.target_name === attribute.name,
  );
  const value =
    currentProperty?.extra_fields?.[DataTypesKey.soilSampling].method;

  const options = methods.map(method => ({
    id: method,
    label: method,
  }));

  const status = useSelector(state =>
    entities.selectors.getEntityStatus(
      state,
      'fieldsDataLayerProperty',
      currentProperty?.uuid,
      'extra_fields',
    ),
  );
  const isLoading = useDelayedState(status === 'pending');
  if (isLoading) {
    return <Skeleton />;
  }

  return (
    <SelectBox
      sizeByParent={false}
      options={options}
      value={value}
      unClearable={true}
      wrapperClassName={unitSelectBoxWrapper}
      placeholder={t('import.matching.methods.placeholder')}
      onChange={value => onSetMethod(value, attribute.name, layer.uuid)}
    />
  );
};
