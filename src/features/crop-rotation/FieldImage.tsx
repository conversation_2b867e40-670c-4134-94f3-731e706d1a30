import React from 'react';

import { FieldImageStyles } from './FieldImage.style';
import { FieldBySeason } from 'types/fields';
import { JohnDeereFieldIcon } from 'features/johnDeere';

type FieldImageProps = {
  src?: string;
  title: string;
  fieldBySeason: FieldBySeason;
};

const FieldImage = ({ src, title, fieldBySeason }: FieldImageProps) => {
  if (!src) {
    return <div className={FieldImageStyles.fieldPreview} />;
  }

  return (
    <div className={FieldImageStyles.fieldPreview}>
      <img src={src} alt={title} />
      <JohnDeereFieldIcon fieldBySeason={fieldBySeason} />
    </div>
  );
};

export default FieldImage;
