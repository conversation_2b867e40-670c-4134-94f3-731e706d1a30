import React, { useState } from 'react';
import Sorter from './Sorter';
import { SortDirections } from 'constants/sortDirections';

const LABEL_MOCK = 'sorter';
const ON_CHANGE_MOCK = () => {};

export default {
  title: 'ui/Sorter',
  component: Sorter,
  decorators: [
    (Story: React.FC) => (
      <div style={{ padding: 16 }}>
        <Story />
      </div>
    ),
  ],
};

export const Simple = () => {
  return (
    <Sorter
      label={LABEL_MOCK}
      value={SortDirections.ASCENDING}
      onChange={ON_CHANGE_MOCK}
    />
  );
};

export const WithEmptyLabel = () => {
  return (
    <Sorter
      label={''}
      value={SortDirections.ASCENDING}
      onChange={ON_CHANGE_MOCK}
    />
  );
};

export const WithChangingValue = () => {
  const [value, setValue] = useState(SortDirections.ASCENDING);

  const onChange = () => {
    setValue(
      value === SortDirections.ASCENDING
        ? SortDirections.DESCENDING
        : SortDirections.ASCENDING,
    );
  };

  return (
    <Sorter
      label={`${LABEL_MOCK}.${value}`}
      value={value}
      onChange={onChange}
    />
  );
};

export const WithoutOnChange = () => {
  // eslint-disable-next-line
  const onChange: any = null;

  return (
    <Sorter
      label={LABEL_MOCK}
      value={SortDirections.ASCENDING}
      onChange={onChange}
    />
  );
};

export const Hidden = () => {
  return (
    <Sorter
      label={LABEL_MOCK}
      value={SortDirections.ASCENDING}
      onChange={ON_CHANGE_MOCK}
      isHidden
    />
  );
};
