import { useState } from 'react';
import { Modals } from './types';

export type EditClientManager = {
  editedClientIds: string[];
  showEditNameModal: (uuid: string) => void;
  showEditLimitsModal: (uuids: string[]) => void;
  closeEditModal: () => void;
};

export const useEditClient = (
  setVisibleModal: (type: Modals) => void,
): EditClientManager => {
  const [editedClientIds, setEditedClientIds] = useState<string[]>([]);

  const showEditNameModal = (uuid: string) => {
    setVisibleModal('edit-name');
    setEditedClientIds([uuid]);
  };
  const showEditLimitsModal = (uuids: string[]) => {
    setVisibleModal('edit-limits');
    setEditedClientIds(uuids);
  };
  const closeEditModal = () => {
    setEditedClientIds([]);
    setVisibleModal(null);
  };

  return {
    editedClientIds,
    showEditNameModal,
    showEditLimitsModal,
    closeEditModal,
  };
};
