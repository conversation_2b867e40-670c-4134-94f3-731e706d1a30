import { createSelector } from 'reselect';
import moment from 'moment';

import RootState from 'types/rootState';
import entities from 'modules/entities';

import {
  ApiDataFieldLayer,
  ApiDataFieldOperations,
  FieldDataValues,
  TaskStatus,
  TaskSuccessType,
} from 'features/files/by-machinery/types';
import { filters } from '../filters';

const getAllTasks = (state: RootState) => state.tasksByMachinery.tasks;
const getUpdatingStatus = (state: RootState) => state.tasksByMachinery.status;
const getMeta = (state: RootState) => state.tasksByMachinery.meta;
const getLegend = (
  state: RootState,
  operation: Nullable<ApiDataFieldOperations>,
) => state.tasksByMachinery.legends[operation?.uuid || ''];
const getAllLayerMatchingTasks = (state: RootState) =>
  state.tasksByMachinery.layerTasks;

const getHistoryTasks = createSelector(
  (state: RootState) => getAllTasks(state),
  (tasks): TaskSuccessType[] => {
    const historyTasks = tasks.filter(filters.isHistoryTask);

    historyTasks.sort((a, b) => {
      return moment(b.created_at).isAfter(a.created_at) ? 1 : -1;
    });

    return historyTasks;
  },
);

const getActiveTasks = createSelector(
  (state: RootState) => getAllTasks(state),
  tasks => {
    return tasks.filter(task => task.status !== TaskStatus.done);
  },
);

const getMatchingTasksUuid = createSelector(
  (state: RootState) => getAllTasks(state),
  (tasks): string[] => {
    return tasks.filter(filters.isMatchingTask).map(v => v.uuid);
  },
);

const getMatchingTaskSourcesUuid = createSelector(
  (state: RootState) => getAllTasks(state),
  tasks => {
    return tasks
      .filter(filters.isMatchingTask)
      .map(task => task.sources.map(v => v.uuid))
      .flat(1);
  },
);

const hasUnfinishedActiveTask = createSelector(
  (state: RootState) => getAllTasks(state),
  tasks => {
    return tasks.some(filters.isWaitingBackendTask);
  },
);

export const selectCropsProperties = createSelector(
  getMatchingTasksUuid,
  state => entities.selectors.getAll(state, 'fieldsDataLayer'),
  (uuids, allLayers) => {
    const layers: ApiDataFieldLayer[] = allLayers.filter(record =>
      uuids.includes(record.source?.upload_uuid),
    ) as ApiDataFieldLayer[];
    return layers
      .map(v => v.properties)
      .flat(1)
      .filter(v => v.target_name === 'crop');
  },
);

export const getActiveDataLayers = createSelector(
  getMatchingTaskSourcesUuid,
  state =>
    entities.selectors.getAll(state, 'fieldsDataLayer') as ApiDataFieldLayer[],
  (sources, layers) => {
    return layers.filter(v => sources.includes(v.source_uuid));
  },
);

export const getActiveDataLayerProperties = createSelector(
  (state: RootState, uuid: string) =>
    entities.selectors.getByID(state, 'fieldsDataLayer', uuid),
  (layer: ApiDataFieldLayer) => {
    return layer?.properties || [];
  },
);

export const getMatchingOperations = createSelector(
  (state: RootState): ApiDataFieldOperations[] =>
    entities.selectors.getAll(state, 'fieldsDataOperation'),
  (state: RootState) => getMatchingTaskSourcesUuid(state),
  (operations, sources): ApiDataFieldOperations[] => {
    return operations.filter(record =>
      sources.includes(record.data_source.layer.source_uuid),
    );
  },
);

export const getMatchingElectroconductivity = createSelector(
  (state: RootState): ApiDataFieldOperations[] =>
    entities.selectors.getAll(state, 'fieldsDataElectroconductivity'),
  (state: RootState) => getMatchingTaskSourcesUuid(state),
  (electroconductivityData, sources): ApiDataFieldOperations[] => {
    return electroconductivityData.filter(record =>
      sources.includes(record.data_source.layer.source_uuid),
    );
  },
);

export const getFieldsDataLayerValue = createSelector(
  getActiveDataLayers,
  state =>
    entities.selectors.getAll(
      state,
      'fieldsDataLayerValue',
    ) as FieldDataValues[],
  (layers, dataValues) => {
    const layersUuid = layers.map(v => v.uuid);

    return dataValues
      .filter(v => layersUuid.includes(v.prop.layer_uuid))
      .filter(v => v.prop.target_name === 'crop');
  },
);

export const getOperationsValue = createSelector(
  (state: RootState) =>
    entities.selectors.getAll(
      state,
      'fieldsDataOperation',
    ) as ApiDataFieldOperations[],
  operations => operations.filter(v => v.parent_operation_uuid === null),
);

export const filterOperationsForField = (
  operation: ApiDataFieldOperations,
  fieldUserSeasonId: Nullable<number>,
) => {
  return (
    operation.type !== null &&
    operation.field_uuid === fieldUserSeasonId &&
    operation.is_imported &&
    operation.is_enabled
  );
};

export const getLayerMatchingTask = createSelector(
  [
    (state: RootState) => getAllLayerMatchingTasks(state),
    (state: RootState, uuid: string) => uuid,
  ],
  (layerTasks, uuid) => {
    const layerTask = layerTasks.find(layerTask => layerTask.uuid === uuid);
    return layerTask || null;
  },
);

export const getLayerTasks = createSelector(
  [(state: RootState) => getAllLayerMatchingTasks(state)],
  layerTasks => {
    return layerTasks;
  },
);

export const selectors: TEMPORARY_ANY = {
  getAllTasks,

  getActiveTasks,
  getHistoryTasks,

  getUpdatingStatus,
  getMatchingOperations,
  getMatchingElectroconductivity,

  hasUnfinishedActiveTask,
  getMatchingTasksUuid,
  getMatchingTaskSourcesUuid,
  getMeta,
  getLegend,
  getOperationsValue,
  filterOperationsForField,

  getLayerMatchingTask,

  selectCropsProperties,
  getLayerTasks,
};
