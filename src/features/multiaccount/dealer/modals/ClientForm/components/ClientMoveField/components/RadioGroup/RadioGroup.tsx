import React, { type ReactNode } from 'react';

import { StyledGroup } from './RadioGroup.styles';

type RadioGroupProps<T> = {
  children?: ReactNode[];
  isHorizontal?: boolean;
  itemRenderer?: (props: T & { name: string }) => ReactNode;
  itemsData?: T[];
  name: string;
};

const RadioGroup = <T extends object>({
  children,
  isHorizontal,
  itemRenderer,
  itemsData,
  name,
}: RadioGroupProps<T>) => {
  return (
    <StyledGroup isHorizontal={isHorizontal}>
      {itemRenderer
        ? itemsData?.map(item => itemRenderer({ ...item, name }))
        : children}
    </StyledGroup>
  );
};

export default RadioGroup;
