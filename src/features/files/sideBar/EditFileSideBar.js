import React from 'react';
import { connect } from 'react-redux';
import { Form, Control } from 'react-redux-form';

import Icon from 'components/Icon';
import Button from 'components/ui/Button/Button';
import Saga from 'components/saga/Saga';
import DateInput from 'components/ui/DateInput/DateInput';
import SubmitButton from 'components/forms/SubmitButton';
import MapNavigationLock from 'features/scouting/MapNavigationLock';
import RRFUnsavedPrompt from 'features/platform/RRFUnsavedPrompt';
import CheckboxList from '../CheckboxList';

import forms from 'constants/forms';
import { OperationTypes } from 'constants/files';
import { fileFormSaga } from 'sagas/local/files-main';
import { required } from 'utils/validators';

import files from 'modules/files';
import fields from 'modules/fields';
import { formatTitle } from 'utils/fields-formatters';
import { formatDate } from 'utils/format-date';
import { useTranslate } from 'utils/use-translate';
import { CustomDropdown } from 'components/ui/CustomDropdown/CustomDropdown';

const EditFileSideBar = ({ history, match, file, ownFields, onUpdateFile }) => {
  const { t } = useTranslate();
  if (!file || file.status !== 'resolved') {
    return null;
  }
  return (
    <Form
      className='soil-sidebar-create'
      model={forms.file}
      onSubmit={form => {
        onUpdateFile(form);
      }}
    >
      <Saga
        saga={fileFormSaga}
        fileId={match.params.fileId}
        history={history}
      />
      <MapNavigationLock />
      <RRFUnsavedPrompt
        message={t('toasts.unsaved_prompt')}
        model={forms.file}
      />
      <div className='soil-sidebar-header'>
        <Button
          className='app-nav-back'
          onClick={() => {
            history.goBack();
          }}
        >
          <Icon className='ico-arrow-long-left-os' name='arrow-left' />
        </Button>
        <div className='soil-sidebar-header__inner'>
          <h1 className='soil-sidebar-header__main'>{t(`files.edit.title`)}</h1>
        </div>
      </div>

      <div className='soil-sidebar-create__form'>
        <div className='form-group'>
          <label className='form-label'>{t('files.type.label')}</label>
          <Control.select
            className='form-select'
            component={CustomDropdown}
            model='.type'
            controlProps={{
              placeholder: t('files.types.select'),
              options: OperationTypes.map(type => ({
                label: t(`files.types.${type}`),
                id: type,
              })),
              renderValue: ({ value }) => (
                <div className='form-select__value u-select__value'>
                  {t(value ? `files.types.${value}` : 'files.types.select')}
                </div>
              ),
            }}
          />
        </div>
        <div className='form-group'>
          <label className='form-label'>{t('files.description.label')}</label>
          <Control.textarea
            className='form-input size-full'
            model='.description'
            maxLength='100'
            rows={3}
          />
        </div>
        <div className='form-group'>
          <label className='form-label'>{t('files.field.label')}</label>
          <Control.select
            className='form-select'
            component={CustomDropdown}
            model='.field_user_id'
            controlProps={{
              options: [
                {
                  label: t('files.field.none'),
                  id: null,
                },
                ...ownFields.map(field => ({
                  label: formatTitle(t, field),
                  id: field.originalId,
                })),
              ],
              renderValue: ({ value }) => {
                const selectedField = ownFields.find(
                  f => f.originalId === value,
                );
                return (
                  <div className='form-select__value u-select__value'>
                    {selectedField
                      ? formatTitle(t, selectedField)
                      : t('files.field.none')}
                  </div>
                );
              },
            }}
          />
        </div>

        <div className='form-group'>
          <label className='form-label'>
            {t('files.operation_date.label')}
          </label>
          <Control.select
            model={`.operation_date`}
            component={DateInput}
            placeholder={t('files.operation_date.placeholder')}
            renderValue={value =>
              formatDate(value, t('files.operation_date.date_format'))
            }
          />
        </div>

        <div className='form-group'>
          <label className='form-label'>{t('files.properties.label')}</label>
          <Control.select
            component={CheckboxList}
            model={`.properties`}
            validators={{ required }}
            options={file.columns.map(column => ({
              label: column.name,
              id: column.name,
            }))}
          />
        </div>

        {/* Fix bottom padding for non-chrome browsers */}
        <div style={{ height: 100 }} />
      </div>

      <div className='soil-sidebar-addnew'>
        <ul className='soil-sidebar-addnew__list'>
          <li className='soil-sidebar-addnew__list-item'>
            <Button
              className='btn btn-lg btn-primary'
              onClick={() => {
                history.goBack();
              }}
            >
              {t('forms.cancel')}
            </Button>
          </li>
          <li className='soil-sidebar-addnew__list-item'>
            <SubmitButton
              native
              model={forms.file}
              render={props => (
                <Button
                  className='btn size-full btn-warr btn-lg btn-success'
                  type='submit'
                  {...props}
                >
                  <span className='btn__ico'>
                    <Icon className='ico-check-os' name='check' />
                  </span>
                  {t('forms.save')}
                </Button>
              )}
            />
          </li>
        </ul>
      </div>
    </Form>
  );
};

const mapStateToProps = (state, ownProps) => ({
  file: files.selectors.getByID(state, ownProps.match.params.fileId),
  ownFields: fields.selectors.getAllOwn(state),
});

const mapDispatchToProps = {
  onUpdateFile: files.actions.update,
};

export default connect(mapStateToProps, mapDispatchToProps)(EditFileSideBar);
