import React from 'react';
import { connect } from 'react-redux';
import { Redirect, withRouter } from 'react-router-dom';
import flowRight from 'lodash/flowRight';

import Saga from 'components/saga/Saga';

import fields from 'modules/fields';

import zoomToFields from 'sagas/local/zoom-to-fields';
import getRootScoutingUrl from 'utils/get-root-scouting-url';

const MissingFieldRedirector = ({ location, match, hasField }) => {
  if (hasField) {
    return null;
  }

  const hasParamsFieldId = match?.params?.fieldId;

  // Prevent endless redirect loops
  if (location.pathname === getRootScoutingUrl(location.pathname)) {
    return null;
  }
  return (
    <>
      <Redirect
        to={{
          pathname: getRootScoutingUrl(location.pathname),
          // As there is already no field, no point to prevent this transition
          ignorePrompt: true,
        }}
      />

      {//remove the zoom after deleting the field
      !hasParamsFieldId && <Saga saga={zoomToFields} persist />}
    </>
  );
};

const mapStateToProps = (state, ownProps) => ({
  hasField: fields.selectors.getOwnIDs(state).includes(ownProps.fieldId),
  hasLocationFieldId: ownProps?.match?.params?.fieldId,
});

const enhance = flowRight(
  connect(mapStateToProps),
  withRouter,
);

export default enhance(MissingFieldRedirector);
