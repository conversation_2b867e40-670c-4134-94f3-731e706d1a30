import React from 'react';

import Checkbox from 'components/ui/Checkbox/Checkbox';

export default {
  title: 'ui/Checkbox',
  component: Checkbox,
  decorators: [
    (Story: React.FC) => (
      <div style={{ padding: 16 }}>
        <Story />
      </div>
    ),
  ],
};

export const CheckboxSimple = () => {
  return <Checkbox>simple</Checkbox>;
};

export const CheckboxDisabled = () => {
  return <Checkbox disabled>disabled</Checkbox>;
};

export const CheckboxSmall = () => {
  return <Checkbox isSmall>small</Checkbox>;
};

export const CheckboxOneLine = () => {
  return <Checkbox oneLine>text text text text text text</Checkbox>;
};

export const CheckboxColor = () => {
  return (
    <div style={{ display: 'flex', flexDirection: 'column' }}>
      <Checkbox colorClassName='color-1'>text</Checkbox>
      <Checkbox colorClassName='color-2'>text</Checkbox>
      <Checkbox colorClassName='color-3'>text</Checkbox>
    </div>
  );
};

export const CheckboxPartly = () => {
  return <Checkbox partly>text</Checkbox>;
};
