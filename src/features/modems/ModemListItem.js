import React from 'react';
import { useSelector } from 'react-redux';
import moment from 'moment';
import cx from 'classnames';
import { css } from 'linaria';

import LinkButton from 'components/LinkButton';
import {
  ModernTooltip,
  ModernTooltipDirection,
} from 'components/ui/Tooltip/ModernTooltip/ModernTooltip';
import { SimpleTooltipTheme } from 'components/ui/Tooltip/SimpleTooltip/SimpleTooltip';

import auth from 'modules/auth';
import modems from 'modules/modems';

import { formatTitle } from 'utils/modems';
import { convertBytes } from 'utils/units';
import { formatDate } from 'utils/format-date';
import { useTranslate } from 'utils/use-translate';

const SignalLevels = ['marginal', 'ok', 'good', 'excellent'];

const getSignalStrength = v => {
  // From https://www.metageek.com/training/resources/understanding-rssi.html
  if (v <= -94) {
    return 'marginal';
  }
  if (v <= -84) {
    return 'ok';
  }
  if (v <= -74) {
    return 'good';
  }
  return 'excellent';
};

const header = css`
  display: flex;
  justify-content: space-between;
`;

const ModemListItem = ({ style, modemId, selected, onClick }) => {
  const userID = useSelector(auth.selectors.getUserID);
  const modem = useSelector(state => modems.selectors.getByID(state, modemId));
  const { t } = useTranslate();

  if (!modem) {
    return null;
  }
  const modemUser = modem.modems_users.find(user => user.user_id === userID);

  const renderSignal = () => {
    if (!modem.rssi) {
      return null;
    }
    const signalStrength = getSignalStrength(modem.rssi);
    const signalStrengthIndex = SignalLevels.indexOf(signalStrength);
    return (
      <ModernTooltip
        active
        offset={[0, 10]}
        animate={ModernTooltipDirection.FromTop}
        align='bottom-right'
        small
        theme={SimpleTooltipTheme.DARK}
        renderTrigger={props => (
          <div className='soil-fields-list-toolbar__item' {...props}>
            <span className='ico-level-signal'>
              {['low', 'medium', 'high'].map((level, index) => (
                <span
                  key={level}
                  className={cx(
                    'ico-level-signal__item',
                    signalStrengthIndex - 1 >= index && '__selected',
                  )}
                  data-level={level}
                />
              ))}
            </span>
          </div>
        )}
        renderTooltip={() => `${signalStrength}, ${modem.rssi} dbm`}
      />
    );
  };
  const renderBattery = () => {
    if (!modem.battery_level_percent) {
      return null;
    }
    return (
      <ModernTooltip
        active
        offset={[0, 10]}
        animate={ModernTooltipDirection.FromTop}
        align='bottom-right'
        small
        theme={SimpleTooltipTheme.DARK}
        renderTrigger={props => (
          <div className='soil-fields-list-toolbar__item' {...props}>
            <span
              className='ico-level-battery'
              data-error-level={
                modem.battery_level_percent <= 25
                  ? 'low'
                  : modem.battery_level_percent <= 35
                  ? 'medium'
                  : ''
              }
            >
              <span
                className='ico-level-battery__item'
                style={{ width: `${modem.battery_level_percent}%` }}
              />
            </span>
          </div>
        )}
        renderTooltip={() => `${modem.battery_level_percent}%`}
      />
    );
  };
  const renderLastConnection = () => {
    if (!modem.last_connection_at) {
      return null;
    }
    const lastConnectionAt = moment(modem.last_connection_at);
    const lastConnectionToday = lastConnectionAt.isSame(moment(), 'day');
    return (
      <ModernTooltip
        active
        offset={[0, 10]}
        animate={ModernTooltipDirection.FromTop}
        align='bottom-left'
        small
        theme={SimpleTooltipTheme.DARK}
        renderTrigger={props => (
          <div
            className='soil-fields-list-meta__item soil-modems-list-meta__date'
            {...props}
          >
            {formatDate(
              lastConnectionAt,
              lastConnectionToday
                ? t('stations.list.date_format.today')
                : t('stations.list.date_format'),
            )}
          </div>
        )}
        renderTooltip={() => t('modems.table.date')}
      />
    );
  };
  const renderStorage = () => {
    if (!modem.storage_size || !modem.storage_usage_size) {
      return null;
    }
    return (
      <div className='soil-fields-list-meta__item soil-modems-list-meta__storage'>
        {`${convertBytes(modem.storage_usage_size * 1024, t)} / ${convertBytes(
          modem.storage_size * 1024,
          t,
        )}`}
      </div>
    );
  };

  return (
    <li className='soil-fields-list__list-item' style={style}>
      <LinkButton
        className={cx('soil-fields-list__item', {
          __selected: selected,
        })}
        onClick={() => {
          onClick();
        }}
      >
        <div className='soil-fields-list__content'>
          <div className={header}>
            <h2 className='soil-fields-list__header'>
              {formatTitle(t, modemUser, modem)}
            </h2>
            <div className='soil-fields-list-toolbar'>
              {renderSignal()}
              {renderBattery()}
            </div>
          </div>
          <div className='soil-fields-list-meta'>
            {renderLastConnection()}
            {renderStorage()}
          </div>
        </div>
      </LinkButton>
    </li>
  );
};

export default ModemListItem;
