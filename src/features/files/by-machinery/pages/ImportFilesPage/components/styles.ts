import { styled } from 'linaria/react';
import { css } from 'linaria';

type ActiveTaskRowStyleProps = {
  disabled?: boolean;
};

export const ActiveTaskRowStyle = styled.div<ActiveTaskRowStyleProps>`
  display: flex;
  justify-content: space-between;
  margin-top: 22px;
  position: relative;
  user-select: none;
  opacity: ${props => (props.disabled ? 0.5 : 1)};
`;

export const ActiveTaskRowContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
`;

export const ActiveTaskRowHead = styled.div`
  display: flex;
  width: 100%;
  justify-content: space-between;
`;

export const ActiveTaskStatusWrapper = styled.div`
  position: relative;
  padding: 0 7px 0 3px;
  height: 34px;
  width: 40px;
`;

export const ActiveTaskTitleStyle = styled.div`
  font-weight: 500;
  font-size: 14px;
  line-height: 18px;
  margin: 2px 1px -2px 0;
  text-overflow: ellipsis;
  overflow: hidden;
  width: 400px;
`;

export const ActiveTaskSubTitleStyle = styled.span`
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  text-overflow: ellipsis;
  overflow: hidden;
  color: #5e5e5e;
  width: 400px;
`;

export const ActiveTaskInfoStyle = styled.span`
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  color: #5e5e5e;
`;

export const CancelButtonStyle = styled.button`
  background: none;
  color: inherit;
  border: none;
  padding: 0;
  font: inherit;
  cursor: pointer;
  outline: inherit;
`;

export const cancelButtonIconClass = css`
  color: #5e5e5e;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-top: -5px;
  margin-left: -5px;
  width: 8px;
  height: 8px;
`;

export const SelectButtonStyle = styled.button`
  display: flex;
  align-self: center;
  border: none;
  border-radius: 6px;
  padding: 8px 15px 8px 15px;
  background-color: rgba(228, 231, 234, 1);
  color: var(--text-color-primary);
  font-weight: 500;
  font-size: 14px;
  line-height: 18px;
`;

export const attentionButtonIconClass = css`
  position: absolute;
  left: 50%;
  top: 50%;
  margin-top: -10px;
  margin-left: -10px;
  width: 20px;
  height: 20px;
`;

export const successButtonIconClass = css`
  color: var(--color-primary);
  position: absolute;
  left: 50%;
  top: 50%;
  margin-top: -5px;
  margin-left: -8px;
  width: 12.59px;
  height: 9.41px;
`;

export const failedButtonIconClass = css`
  color: #ff3b30;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-top: -5px;
  margin-left: -10px;
  width: 14px;
  height: 14px;
`;

export const clearButtonIconClass = css`
  color: #222222;
  position: absolute;
  top: 50%;
  margin-top: -5px;
  right: 3px;
  width: 11px;
  height: 11px;
`;

export const tooltipDotsClass = css`
  color: #808f9b;
  right: 17px;
  cursor: pointer;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border-width: 0;
  background-color: rgba(240, 240, 240, 0);
  line-height: 4px;
  transition: background-color 0.15s;
  display: flex;
  justify-content: center;
  flex-direction: column;
`;

export const HistoryTasksListStyle = styled.div`
  margin-top: 30px;
`;

type HistoryTaskRowStyleProps = {
  disabled?: boolean;
};
export const HistoryTaskRowStyle = styled.div<HistoryTaskRowStyleProps>`
  position: relative;
  background: #f5f7f9;
  border-radius: 10px;
  width: 100%;
  margin-bottom: 10px;
  padding: 10px;
  height: 60px;
  display: flex;
  justify-content: space-between;
  opacity: ${props => (props.disabled ? 0.5 : 1)};
`;

export const HistoryTaskRowBlockStyle = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  padding-right: 8px;
`;

export const HistoryTaskLogoStyle = styled.div`
  width: 40px;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
`;

export const HistoryTaskTitleStyle = styled.div`
  font-weight: 500;
  font-size: 16px;
  line-height: 22px;
  padding-left: 11px;
`;

export const HistoryTaskSubTitleStyle = styled.div`
  font-weight: 400;
  font-size: 16px;
  line-height: 22px;
  padding-left: 10px;
`;

export const HistoryTaskSubInfoStyle = styled.div`
  font-weight: 400;
  font-size: 16px;
  line-height: 22px;
  padding-right: 14px;
`;

export const HistoryTaskTooltipBlockStyle = styled.div`
  position: relative;
  display: flex;
  align-items: center;
`;

export const HintLink = styled.span`
  border-bottom: 1px dashed #000000;
  color: inherit;
  cursor: pointer;
`;

export const ActiveTaskLayerRow = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
`;

export const ActiveTaskLayerIcon = styled.div`
  display: flex;
`;

export const ActiveTaskLayerContainer = styled.div`
  display: flex;
  flex-grow: 1;
  gap: 8px;
  justify-content: space-between;
  align-items: center;
`;

export const ActiveTaskLayersList = styled.div`
  padding-left: 48px;
  padding-top: 10px;
`;

export const ActiveTaskRowTitleContainer = styled.div`
  display: flex;
  flex: 1;
  flex-direction: column;
`;

export const ActiveTaskRowLine = styled.div`
  display: flex;
  gap: 8px;
`;

export const ErrorLine = styled.div`
  display: flex;
  align-items: center;
  margin-top: 8px;
  gap: 8px;
`;

export const IconError = css`
  width: 14px;
  height: 14px;
`;
