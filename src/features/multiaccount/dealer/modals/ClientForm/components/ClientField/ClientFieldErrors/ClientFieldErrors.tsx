import React from 'react';
import type { FieldErrors } from 'react-hook-form/dist/types/errors';

import { ClientForm } from 'features/multiaccount/dealer/modals/ClientForm/types';

import { ErrorData } from 'modules/entities/types';
import { useTranslate } from 'utils/use-translate';

import { styles } from './ClientFieldErrors.styles';

type ClientFieldErrorsProps = {
  name: string;
  formErrors: FieldErrors<ClientForm>;
  requestErrors: Nullable<ErrorData>;
};

const ClientFieldErrors = ({
  name,
  formErrors,
  requestErrors,
}: ClientFieldErrorsProps) => {
  const { t } = useTranslate();

  const getErrorMessage = () => {
    const fieldPath = name.split('.');
    let error: unknown = formErrors;

    for (const key of fieldPath) {
      if (error && typeof error === 'object' && error !== null) {
        if (/^\d+$/.test(key)) {
          error = (error as Record<string, unknown>)[parseInt(key, 10)];
        } else {
          error = (error as Record<string, unknown>)[key];
        }
      } else {
        error = null;
        break;
      }
    }

    if (!error) return null;

    if (typeof error === 'object' && error !== null && 'message' in error) {
      return (error as { message: string }).message;
    }

    return null;
  };

  const errorMessage = getErrorMessage();

  // Extract the base field name for request errors (e.g., "email" from "clients.0.email")
  const baseFieldName = name.split('.').pop() || name;

  return (
    <>
      {errorMessage && (
        <div className='error-message c-error'>{errorMessage}</div>
      )}
      {requestErrors?.[baseFieldName] &&
        requestErrors[baseFieldName]?.map(error => (
          <div key={error} className={styles.fieldError}>
            {t(`multiaccount.dealer.error.${baseFieldName}.${error}`)}
          </div>
        ))}
    </>
  );
};

export default ClientFieldErrors;
