export const ChartTypes = [
  'accumulated_temperature',
  'accumulated_precipitation',
  'vegetation',
  'annotations',
];

export const Charts = {
  annotations: {
    source: 'annotations',
    color: '#27AE60',
    hideAxis: true,
  },
  accumulated_temperature: {
    source: 'temperatures',
    type: 'line',
    color: '#F4D359',
    labels: ['last'],
    axisProps: {
      minDomain: 0,
    },
    chartProps: {
      x: 'date',
      y: 'value',
      style: {
        data: {
          fill: 'url(#weatherChartFillTemp)',
          stroke: '#F4D359',
          strokeWidth: 2,
        },
      },
    },
    category: 'temp',
    unit: 'C',
  },
  accumulated_precipitation: {
    source: 'precipitations',
    type: 'line',
    color: '#72BBF5',
    labels: ['last'],
    axisProps: {
      minDomain: 0,
    },
    chartProps: {
      x: 'date',
      y: 'value',
      style: {
        data: {
          fill: 'url(#weatherChartFillPrecip)',
          stroke: '#72BBF5',
          strokeWidth: 2,
        },
      },
    },
    category: 'precip',
    unit: 'mm',
  },
  accumulated_temperature_forecast: {
    label: 'weather.chart.tooltip.accumulated_temperature',
    type: 'line',
    color: '#F4D359',
    chartProps: {
      x: 'date',
      y: 'value',
      style: {
        data: {
          stroke: '#F4D359',
          strokeWidth: 2,
          strokeDasharray: 5,
        },
      },
    },
    category: 'temp',
    unit: 'C',
  },
  accumulated_precipitation_forecast: {
    label: 'weather.chart.tooltip.accumulated_precipitation',
    type: 'line',
    color: '#72BBF5',
    chartProps: {
      x: 'date',
      y: 'value',
      style: {
        data: {
          stroke: '#72BBF5',
          strokeWidth: 2,
          strokeDasharray: 5,
        },
      },
    },
    category: 'precip',
    unit: 'mm',
  },
  vegetation: {
    source: 'ndvi',
    type: 'line',
    color: '#A955B8',
    minColor: '#D8A6E1',
    maxColor: '#E659FF',
    labels: ['min', 'max'],
    incremental: true,
    axisProps: {
      domain: [0, 1],
    },
    chartProps: {
      x: 'date',
      y: 'avg',
      style: {
        data: {
          stroke: '#A955B8',
          strokeWidth: 2,
        },
      },
    },
    scatterProps: {
      x: 'date',
      y: 'avg',
      size: 3,
      style: {
        data: {
          fill: '#A955B8',
          stroke: 'none',
        },
      },
    },
    category: 'ndvi',
    unit: 'm',
  },
  vegetation_range: {
    type: 'range',
    chartProps: {
      x: 'date',
      y: 'max',
      y0: 'min',
      style: {
        data: {
          fill: 'rgba(169,85,184,0.15)',
          stroke: 'none',
        },
      },
    },
    category: 'ndvi',
    unit: 'm',
    minColor: '#D8A6E1',
    maxColor: '#E659FF',
  },
};
