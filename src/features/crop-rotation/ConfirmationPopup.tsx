import React, { VFC } from 'react';
import { css } from 'linaria';

import Modal from 'components/ui/Modal/Modal';
import Button from 'components/ui/Button/Button';

import { formatTitle as formatSeasonTitle } from 'utils/seasons-formatters';
import { TranslateFunction } from 'utils/use-translate';

const popupText = css`
  font-size: 16px;
  line-height: 22px;
  text-align: center;
`;

type ConfirmationPopupProps = {
  season: TEMPORARY_ANY;
  t: TranslateFunction;
  onConfirm: () => void;
  onCancel: () => void;
};

const ConfirmationPopup: VFC<ConfirmationPopupProps> = ({
  season,
  t,
  onConfirm,
  onCancel,
}) => {
  return (
    <Modal
      show
      width={400}
      onClose={onCancel}
      ModalHeader={() => (
        <div style={{ textAlign: 'center' }}>
          {t('croprotation.autofill.warning.popup.title')}
        </div>
      )}
      ModalBody={() => (
        <div className={popupText}>
          {// @ts-ignore
          t('croprotation.cropsremove.confirmation.popup.title', {
            seasonTitle: formatSeasonTitle(t, season) as string,
          })}
        </div>
      )}
      ModalActions={() => (
        <>
          <Button className='btn btn-primary btn-lg' onClick={onCancel}>
            {t('forms.cancel')}
          </Button>
          <Button className='btn btn-danger btn-lg' onClick={onConfirm}>
            {t('forms.remove')}
          </Button>
        </>
      )}
    />
  );
};

export default ConfirmationPopup;
