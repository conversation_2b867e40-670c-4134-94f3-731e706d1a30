import React, { useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { useHistory, useRouteMatch } from 'react-router-dom';

import CreateFieldDialogDropDown from 'features/fields/CreateFieldDialogDropDown/CreateFieldDialogDropDown';
import { getViewportHash } from 'utils/get-root-scouting-url';
import { useTranslate } from 'utils/use-translate';
// @ts-ignore
import fields from 'modules/fields';
import {
  AdvertisingBlock,
  AdvertisingTitle,
  AdvertisingDescriptionBlock,
  CreateFieldSelect,
  AdvertisingDescription,
} from './styles';
import {
  ApiDataFieldLayer,
  ApiDataFieldOperations,
} from 'features/files/by-machinery/types';
import uniq from 'lodash/uniq';

type CreateFieldDialogProps = {
  operations: ApiDataFieldOperations[];
  dataLayers: ApiDataFieldLayer[];
};

export const CreateFieldDialog = ({
  operations,
  dataLayers,
}: CreateFieldDialogProps) => {
  const { t } = useTranslate();
  const dispatch = useDispatch();
  const match = useRouteMatch();
  const history = useHistory();
  const viewport = getViewportHash(history.location.pathname);

  const onChange = useCallback(
    option => {
      if (option === 'upload') {
        // @ts-ignore
        dispatch(fields.actions.setImportStatus({ state: 'resolved' }));
      }

      history.push({
        pathname: `/${viewport}/fields/add/${option}`,
        state: { from: match.url },
      });
    },
    [viewport, history, dispatch, match],
  );
  const layersWithField = uniq(
    operations.map(operation => operation.data_source.layer.uuid),
  );
  const layersWithoutField = dataLayers.length - layersWithField.length;

  if (layersWithoutField === 0) {
    return null;
  }

  return (
    <AdvertisingBlock>
      <AdvertisingDescriptionBlock>
        <AdvertisingTitle>
          {t('import.matching_operations.create_field.title')}
        </AdvertisingTitle>
        <AdvertisingDescription>
          {
            //@ts-ignore
            t('import.matching_operations.create_field.description', {
              value: layersWithoutField,
            })
          }
        </AdvertisingDescription>
        <CreateFieldSelect>
          <CreateFieldDialogDropDown
            className='form-select form-select-white'
            renderValue={() => (
              <span className='form-select__value'>
                {t('croprotation.button.addfield')}
              </span>
            )}
            onChange={onChange}
          />
        </CreateFieldSelect>
      </AdvertisingDescriptionBlock>
    </AdvertisingBlock>
  );
};
