import { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import entities from 'modules/entities';

import { getActiveDataLayerProperties } from 'features/files/by-machinery/redux/selectors';
import {
  ApiDataFieldLayerProperties,
  DataLoader,
} from 'features/files/by-machinery/types';

export const ACTIVE_LAYERS_PROPERTIES_REQUEST_ID =
  'useActiveDataLayerPropertiesLoader';

export const useFetchActiveDataLayerProperties = (
  uuid: string,
): VoidFunction => {
  const dispatch = useDispatch();

  return useCallback(() => {
    if (!uuid) {
      return;
    }

    dispatch(
      entities.actions.request(ACTIVE_LAYERS_PROPERTIES_REQUEST_ID, [
        {
          type: 'fetchOne',
          entityType: 'fieldsDataLayer',
          path: { id: uuid },
          query: {
            include: ['source', 'properties'],
          },
        },
      ]),
    );
  }, [uuid, dispatch]);
};

export const useActiveDataLayerPropertiesLoader = (
  uuid: string,
): DataLoader<ApiDataFieldLayerProperties[]> => {
  const fetch = useFetchActiveDataLayerProperties(uuid);
  useEffect(fetch, [fetch]);

  const data: ApiDataFieldLayerProperties[] = useSelector(state =>
    getActiveDataLayerProperties(state, uuid),
  );

  const request = useSelector(state =>
    entities.selectors.getRequestStatus(
      state,
      ACTIVE_LAYERS_PROPERTIES_REQUEST_ID,
    ),
  );

  return [data, request, fetch];
};
