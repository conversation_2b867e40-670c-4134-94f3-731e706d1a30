import React, { useEffect, VFC } from 'react';
import { RouteComponentProps } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';

// @ts-ignore
import BodyClassName from 'components/BodyClassName';
// @ts-ignore
import Logo from 'components/vector/Logo';
// @ts-ignore
import YieldLogo from 'components/vector/YieldLogo';
// @ts-ignore
import AuthorizePage from './AuthorizePage';

import auth from 'modules/auth';

import { styles } from 'features/auth/InvitationSignupPage.styles';

import { useTranslate, TranslateFunction } from 'utils/use-translate';
import { parseJsonBase64 } from 'utils/encoding/parseBase64';
import { INVITATION_TYPES } from 'features/multiaccount/constants';
import { parseQuery } from 'utils/query';
import useApplyWorkspaceColor from 'utils/use-apply-workspace-color';

import config from 'config';

type InvitationSignupPageProps = RouteComponentProps<{
  invitationType: INVITATION_TYPES;
}>;

const getInvitationLogo = (fromYield: boolean, dealerLogo: string) => {
  if (config.whiteLabel?.logoClassName) {
    if (dealerLogo) {
      return <img alt='' className={styles.dealerLogo} src={dealerLogo} />;
    }
    return <div className={config.whiteLabel.logoClassName} />;
  }

  if (fromYield) {
    if (dealerLogo) {
      return (
        <div className={styles.invitationLogo}>
          <img alt='' className={styles.dealerLogo} src={dealerLogo} />
          <div className={styles.logoDivider}></div>
          <YieldLogo width={118} color='#000000' />
        </div>
      );
    }
    return <YieldLogo />;
  }

  return <Logo />;
};

const getInvitationTitle = (
  t: TranslateFunction,
  fromYield: boolean,
  name: string,
) => {
  if (fromYield) {
    if (config.whiteLabel?.title) {
      if (name) {
        // @ts-ignore
        return t(`partner.invitation.title`, { name });
      }
      // hardcode for agrilever whitelabel
      // @ts-ignore
      return t(`partner.invitation.title`, { name: config.whiteLabel.title });
    }
    return t('partner.yield.title');
  }
  // @ts-ignore
  return t(`partner.invitation.title`, { name });
};

const InvitationSignupPage: VFC<InvitationSignupPageProps> = props => {
  const { t } = useTranslate();
  const dispatch = useDispatch();
  const partner = props.match.params.invitationType;
  const query = parseQuery(props.location);
  const userEmail = useSelector(auth.selectors.getUserEmail);
  const isAuthorized = useSelector(auth.selectors.isAuthorized);
  const invitation: TEMPORARY_ANY = parseJsonBase64(query.data as string);
  const name = invitation.workspace_name;
  const email = invitation.email;
  const isSameUser = email === userEmail;
  const renderForm = !(!isSameUser && isAuthorized);
  const accentColor = invitation?.dealer_accent_color;
  const dealerLogo = invitation?.dealer_logo_url;
  const dealerWorkspaceName = invitation?.dealer_workspace_name;
  const fromYield =
    partner === INVITATION_TYPES.DEALER ||
    partner === INVITATION_TYPES.CONSULTANT;

  useApplyWorkspaceColor(accentColor);

  useEffect(() => {
    if (!isSameUser) {
      dispatch(auth.actions.logout());
    }
  }, [dispatch, isSameUser]);

  return (
    <>
      <BodyClassName className='onesoil-hello-page' />
      <div className={styles.container}>
        <div className={styles.about}>
          <div className={styles.content}>
            <div
              className={styles.logo}
              title={fromYield ? 'OneSoil Yield' : 'OneSoil'}
            >
              {getInvitationLogo(fromYield, dealerLogo)}
            </div>
            <h1 className={styles.title}>
              {getInvitationTitle(t, fromYield, name || dealerWorkspaceName)}
            </h1>
            <p className={styles.text}>{t(`partner.invitation.terms`)}</p>
          </div>

          <div className={styles.disclaimer}>
            {t(`partner.invitation.disclaimer`)}
          </div>
        </div>

        {renderForm && (
          <AuthorizePage
            hideHeader
            disableEmail={!!email}
            partner={partner}
            authLoginSagaProps={{
              email: email,
            }}
            {...props}
          />
        )}
      </div>
    </>
  );
};

export default InvitationSignupPage;
