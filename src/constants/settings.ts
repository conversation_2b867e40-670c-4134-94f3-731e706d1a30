import firebase from 'firebase/app';
import 'firebase/remote-config';
import { VFC } from 'react';

//import WhatsNewColors from 'components/WhatsNew/pages/WhatsNewColors';
//import WhatsNewImport from 'components/WhatsNew/pages/WhatsNewImport';
//import WhatsNewGuide from 'components/WhatsNew/pages/WhatsNewGuide';
//import WhatsNewCrops from 'components/WhatsNew/pages/WhatsNewCrops';
// import WhatsNewCropRotation from 'components/WhatsNew/pages/WhatsNewCropRotation';
//@ts-ignore
import WhatsNewHungarian from 'components/WhatsNew/pages/WhatsNewHungarian';
//@ts-ignore
import WhatsNewCloudFree from 'components/WhatsNew/pages/WhatsNewCloudFree';
//@ts-ignore
import WhatsNewUkrainian from 'components/WhatsNew/pages/WhatsNewUkrainian';
//@ts-ignore
import WhatsNewCropRotationV2 from 'components/WhatsNew/pages/WhatsNewCropRotationV2';
//@ts-ignore
import WhatsNewContrastNDVI from '../components/WhatsNew/pages/WhatsNewContrastNDVI';
//@ts-ignore
import WhatsNewAllFieldsNDVI from '../components/WhatsNew/pages/WhatsNewAllFieldsNDVI';
//@ts-ignore
import WhatsNewPolish from '../components/WhatsNew/pages/WhatsNewPolish';
//@ts-ignore
import WhatsNewTurkish from '../components/WhatsNew/pages/WhatsNewTurkish';
//@ts-ignore
import WhatsNewFieldBoundaries from '../components/WhatsNew/pages/WhatsNewFieldBoundaries';

//@ts-ignore
import WhatsNewRGBLayer from '../components/WhatsNew/pages/WhatsNewRGBLayer';
import { FeaturesKeyEnum } from '../components/WhatsNew/consts';
import { WhatsNewProps } from '../components/WhatsNew/types';
import WhatsNewSoilAnalysisConfig from '../components/WhatsNew/pages/WhatsNewSoilAnalysis';
import WhatsNewSharedFileConfig from '../components/WhatsNew/pages/WhatsNewSharedFile';
import WhatsNewMultiAccountConfig from 'components/WhatsNew/pages/WhatsNewMultiAccount';

export type NewFeatureComponent = VFC<WhatsNewProps>;
interface NewFeatureFilter {
  locale?: string | string[];
  deviceLocale?: string | string[];
}

export interface NewFeature {
  key: FeaturesKeyEnum;
  component: NewFeatureComponent;
  filter: NewFeatureFilter;
}

export const getNewFeatures = async (): Promise<NewFeature[]> => {
  const NewFeatures: NewFeature[] = [
    //  WhatsNewColors,
    //  WhatsNewImport,
    //  WhatsNewGuide,
    //  WhatsNewCrops,
    WhatsNewUkrainian,
    WhatsNewHungarian,
    WhatsNewCloudFree,
    WhatsNewContrastNDVI,
    // WhatsNewCropRotation,
    WhatsNewCropRotationV2,
    WhatsNewAllFieldsNDVI,
    WhatsNewPolish,
    WhatsNewTurkish,
    WhatsNewFieldBoundaries,
    WhatsNewSoilAnalysisConfig,
    WhatsNewSharedFileConfig,
    WhatsNewMultiAccountConfig,
  ];

  const remoteConfig = firebase.remoteConfig();

  const isRGBLayerActive = remoteConfig.getValue('rgb_layer').asBoolean();

  if (isRGBLayerActive) {
    const previousFeatureIndex = NewFeatures.findIndex(
      feature => feature.key === WhatsNewSharedFileConfig.key,
    );
    if (previousFeatureIndex > -1) {
      NewFeatures.splice(previousFeatureIndex + 1, 0, WhatsNewRGBLayer);
    }
  }

  return NewFeatures;
};

export const FeatureWebIdentifier = 'whatsnew-web-';

export const HistoryLinkEn =
  'https://www.notion.so/onesoil/What-s-new-in-the-OneSoil-web-app-4a5867cc512f4f4ca4152f16273a19ff';
