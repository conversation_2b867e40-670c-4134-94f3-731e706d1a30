import React from 'react';
import { Link } from 'react-router-dom';

import { useTranslate } from 'utils/use-translate';

import Icon from 'components/Icon';
import Logo from 'components/vector/Logo';
import BodyClassName from 'components/BodyClassName';
import CopyrightBar from 'features/auth/CopyrightBar';
import RequireAuthorization from 'features/auth/RequireAuthorization';

import config from 'config';

const WelcomePage = () => {
  const { t } = useTranslate();
  return (
    <div className='b-welcome'>
      <RequireAuthorization />
      <BodyClassName className='welcome-mode' />
      <div className='main-menu'>
        <Logo className='logo' width={145} height={50} />
        <Link className='item' to='/scouting'>
          {t('welcome.link.scouting')}
          <Icon name='arrow-right' />
        </Link>
        <a className='item' href='/nitrogen'>
          {t('welcome.link.nitrogen')}
          <Icon name='arrow-right' />
        </a>
        {config.features.weather ? (
          <Link className='item' to='/weather'>
            {t('welcome.link.weather')}
            <Icon name='arrow-right' />
          </Link>
        ) : (
          <div className='item disabled'>
            {t('welcome.link.weather')}
            <span className='badge'>{t('welcome.soon_badge')}</span>
          </div>
        )}
      </div>
      <CopyrightBar />
    </div>
  );
};

export default WelcomePage;
