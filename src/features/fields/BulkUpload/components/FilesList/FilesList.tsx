import React, { VFC } from 'react';

import Button from 'components/ui/Button/Button';
import Icon from 'components/Icon';

import { ErrorText } from 'features/fields/BulkUpload/styles';

import { isExtensionValid } from 'utils/import-fields';
import { useTranslate } from 'utils/use-translate';

// @ts-ignore
import { convertBytes } from 'utils/units';

import {
  FileListItemDescription,
  FileListItemIconWrapper,
  FileListItem,
  FileListItemContent,
  fileName,
} from './styles';

interface FilesListProps {
  files: File[];
  onFileDelete: (index: Number) => void;
}

export const FilesList: VFC<FilesListProps> = ({ files, onFileDelete }) => {
  const { t } = useTranslate();

  function renderFileDescription(file: File) {
    if (!isExtensionValid(file)) {
      return (
        <FileListItemDescription>
          <ErrorText>
            {t('fields.bulk_upload.invalid_file_extension')}
          </ErrorText>
        </FileListItemDescription>
      );
    }

    return (
      <FileListItemDescription>
        {convertBytes(file.size, t)}
      </FileListItemDescription>
    );
  }

  function renderFile(file: File, index: Number) {
    return (
      <FileListItem key={`${file.name}_${index}`}>
        <FileListItemContent>
          <FileListItemIconWrapper>
            <Icon name='archive-file' />
          </FileListItemIconWrapper>
          <div>
            <div className={fileName}>{file.name}</div>
            {renderFileDescription(file)}
          </div>
        </FileListItemContent>
        <Button className='btn btn-primary' onClick={() => onFileDelete(index)}>
          <Icon width={10} height={12} name='trashcan' />
        </Button>
      </FileListItem>
    );
  }

  return <>{files.map((file, index) => renderFile(file, index))}</>;
};
