import React, { Fragment } from 'react';
import { Route } from 'react-router-dom';

import Saga from 'components/saga/Saga';
import MapNavigationLock from 'features/scouting/MapNavigationLock';
import FilesFieldsLayer from './FilesFieldsLayer';
import FileLayer from './FileLayer';

import mapClickSaga from 'sagas/local/map-click';

const FilesLayer = props => (
  <Fragment>
    <MapNavigationLock />
    <Saga saga={mapClickSaga} history={props.history} match={props.match} />
    <Route
      path={[`${props.match.url}/by-field/:fieldId`, `${props.match.url}`]}
      component={FilesFieldsLayer}
    />
    <Route
      path={[
        `${props.match.url}/upload/:fileId`,
        `${props.match.url}/by-field/:fieldId/:fileId`,
        `${props.match.url}/:fileId`,
      ]}
      component={FileLayer}
    />
  </Fragment>
);

export default FilesLayer;
