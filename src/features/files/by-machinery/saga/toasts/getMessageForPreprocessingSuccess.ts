import toasts from 'modules/toasts';

import { isImportFilePage } from '../../redux/utils';
import {
  BASE_ROUTE_MACHINERY_IMPORT,
  TOAST_KEY_TO_IMPORT,
} from '../../constants';
import { successToastIcon } from '../../styles';

export const getMessageForPreprocessingSuccess = () => {
  const viewport = window.location.pathname.split('/')[1];

  if (!isImportFilePage()) {
    return toasts.actions.push(
      TOAST_KEY_TO_IMPORT,
      'import.by-machinery.toast.import.success.title',
      {
        getLabel: ({ t, label }) => t(label) as string,
        content: 'import.by-machinery.toast.import.success.description',
        position: 'bottom',
        icon: 'check',
        link: {
          text: 'import.by-machinery.toast.import.success.to',
          to: `/${viewport}/${BASE_ROUTE_MACHINERY_IMPORT}`,
        },
        iconClassName: successToastIcon,
        ttl: Infinity,
      },
    );
  }
};
