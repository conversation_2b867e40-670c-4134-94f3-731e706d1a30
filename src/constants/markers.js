import React from 'react';

export const WarningIconHTML = `
<svg width="14" height="14" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M7.003 14C10.842 14 14 10.848 14 7.003 14 3.158 10.842 0 6.997 0 3.15 0 0 3.158 0 7.003S3.158 14 7.003 14zm0-6.138c-.462 0-.726-.257-.753-.72l-.099-2.616c-.026-.49.33-.826.846-.826.515 0 .872.343.845.832l-.105 2.603c-.027.476-.284.727-.734.727zm0 2.55c-.548 0-.925-.323-.925-.825 0-.496.37-.813.925-.813.535 0 .912.317.912.813 0 .508-.377.825-.912.825z" fill="#F4D359"/>
</svg>
`;

export const SelectedNoteHTML = `
<svg width="41" height="55">
  <defs>
    <path d="M16.5 0C7.387 0 0 7.484 0 16.716c0 10.993 10.728 16.256 12.072 17.48 1.372 1.251 2.812 2.035 3.27 3.625.221.772.54 1.162 1.158 1.179.62-.018.945-.409 1.167-1.18.457-1.589 1.889-2.371 3.261-3.623C22.272 32.972 33 27.709 33 16.717 33 7.483 25.612 0 16.5 0z" id="selMarkerB"/>
    <filter x="-18.2%" y="-15.4%" width="136.4%" height="130.8%" filterUnits="objectBoundingBox" id="selMarkerA">
      <feOffset in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"/>
      <feColorMatrix values="0 0 0 0 0.666666667 0 0 0 0 0.164705882 0 0 0 0 0.164705882 0 0 0 0.8 0" in="shadowBlurOuter1"/>
    </filter>
    <circle id="selMarkerD" cx="3" cy="3" r="2.5"/>
    <filter x="-180%" y="-180%" width="460%" height="460%" filterUnits="objectBoundingBox" id="selMarkerC">
      <feOffset in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"/>
      <feColorMatrix values="0 0 0 0 0.666560374 0 0 0 0 0.164654404 0 0 0 0 0.164654404 0 0 0 1 0" in="shadowBlurOuter1"/>
    </filter>
  </defs>
  <g fill="none" fill-rule="evenodd">
    <g fill-rule="nonzero" transform="translate(4 4)">
      <use fill="#000" filter="url(#selMarkerA)" xlink:href="#selMarkerB"/>
      <use fill="#EF3E3E" fill-rule="evenodd" xlink:href="#selMarkerB"/>
    </g>
    <g transform="translate(17.5 44)">
      <use fill="#000" filter="url(#selMarkerC)" xlink:href="#selMarkerD"/>
      <use fill="#FFF" xlink:href="#selMarkerD"/>
      <rect fill="#EF3E3E" x="1.5" y="1.5" width="3" height="3" rx="1.5"/>
    </g>
    <g fill="#FFF">
      <path d="M23.405 16.73l1.31 1.31c.946.946 1.19 1.387 1.352 1.918.16.53.16 1.073 0 1.604-.161.53-.406.972-1.351 1.917l-2.584 2.584a3.41 3.41 0 0 1-4.821 0l3.798-5.727-5.727 3.799a3.41 3.41 0 0 1 0-4.821l2.584-2.584c.946-.946 1.387-1.19 1.918-1.351.53-.161 1.073-.161 1.603 0 .53.16.972.405 1.918 1.35zM26.598 18.898c-.18-.394-.495-.82-1.207-1.532l-1.311-1.311c-.71-.71-1.135-1.024-1.527-1.205l.015-.027a2.937 2.937 0 0 1 4.651-.607 2.944 2.944 0 0 1-.592 4.665l-.029.017zM25.261 24.283l.13-.129c.9-.9 1.165-1.344 1.327-1.843a1.364 1.364 0 1 1-1.457 1.972zm-4.006 3.344a3.393 3.393 0 0 0 1.552-.889l.438-.438a1.363 1.363 0 1 1-1.99 1.327zM19.134 14.727c-.499.162-.942.427-1.843 1.328l-.13.129a1.363 1.363 0 1 1 1.973-1.457zm-3.989 3.474l-.438.438a3.393 3.393 0 0 0-.889 1.551 1.364 1.364 0 1 1 1.327-1.99z"/>
    </g>
  </g>
</svg>
`;

export const DraggableNoteHTML = `
<svg width="41" height="55">
  <defs>
    <path d="M16.5 0C7.387 0 0 7.484 0 16.716c0 10.993 10.728 16.256 12.072 17.48 1.372 1.251 2.812 2.035 3.27 3.625.221.772.54 1.162 1.158 1.179.62-.018.945-.409 1.167-1.18.457-1.589 1.889-2.371 3.261-3.623C22.272 32.972 33 27.709 33 16.717 33 7.483 25.612 0 16.5 0z" id="dragMarkerB"/>
    <filter x="-18.2%" y="-15.4%" width="136.4%" height="130.8%" filterUnits="objectBoundingBox" id="dragMarkerA">
      <feOffset in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"/>
      <feColorMatrix values="0 0 0 0 0.666666667 0 0 0 0 0.164705882 0 0 0 0 0.164705882 0 0 0 0.8 0" in="shadowBlurOuter1"/>
    </filter>
    <circle id="dragMarkerD" cx="3" cy="3" r="2.5"/>
    <filter x="-180%" y="-180%" width="460%" height="460%" filterUnits="objectBoundingBox" id="dragMarkerC">
      <feOffset in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"/>
      <feColorMatrix values="0 0 0 0 0.666560374 0 0 0 0 0.164654404 0 0 0 0 0.164654404 0 0 0 1 0" in="shadowBlurOuter1"/>
    </filter>
  </defs>
  <g fill="none" fill-rule="evenodd">
    <g fill-rule="nonzero" transform="translate(4 4)">
      <use fill="#000" filter="url(#dragMarkerA)" xlink:href="#dragMarkerB"/>
      <use fill="#EF3E3E" fill-rule="evenodd" xlink:href="#dragMarkerB"/>
    </g>
    <path d="M19.717 19.826V15.13h-1.956L20.5 12l2.74 3.13h-1.957v4.696h5.087V17.87l3.13 2.739-3.13 2.739V21.39h-5.087v4.696h1.956l-2.739 3.13-2.74-3.13h1.957v-4.696H14.63v1.957l-3.13-2.74 3.13-2.738v1.956h5.087z" fill="#FFF"/>
    <g transform="translate(17.5 44)">
      <use fill="#000" filter="url(#dragMarkerC)" xlink:href="#dragMarkerD"/>
      <use fill="#FFF" xlink:href="#dragMarkerD"/>
      <rect fill="#EF3E3E" x="1.5" y="1.5" width="3" height="3" rx="1.5"/>
    </g>
  </g>
</svg>
`;

export const DraggableStationHTML = `
<svg width="41" height="55">
  <defs>
    <path d="M16.5 0C7.387 0 0 7.484 0 16.716c0 10.993 10.728 16.256 12.072 17.48 1.372 1.251 2.812 2.035 3.27 3.625.221.772.54 1.162 1.158 1.179.62-.018.945-.409 1.167-1.18.457-1.589 1.889-2.371 3.261-3.623C22.272 32.972 33 27.709 33 16.717 33 7.483 25.612 0 16.5 0z" id="dragMarkerB"/>
    <filter x="-18.2%" y="-15.4%" width="136.4%" height="130.8%" filterUnits="objectBoundingBox" id="dragMarkerA">
      <feOffset in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"/>
      <feColorMatrix values="0 0 0 0 0.666666667 0 0 0 0 0.164705882 0 0 0 0 0.164705882 0 0 0 0.8 0" in="shadowBlurOuter1"/>
    </filter>
    <circle id="dragMarkerD" cx="3" cy="3" r="2.5"/>
    <filter x="-180%" y="-180%" width="460%" height="460%" filterUnits="objectBoundingBox" id="dragMarkerC">
      <feOffset in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"/>
      <feColorMatrix values="0 0 0 0 0.666560374 0 0 0 0 0.164654404 0 0 0 0 0.164654404 0 0 0 1 0" in="shadowBlurOuter1"/>
    </filter>
  </defs>
  <g fill="none" fill-rule="evenodd">
    <g fill-rule="nonzero" transform="translate(4 4)">
      <use fill="#000" filter="url(#dragMarkerA)" xlink:href="#dragMarkerB"/>
      <use fill="#007AFF" fill-rule="evenodd" xlink:href="#dragMarkerB"/>
    </g>
    <path d="M19.717 19.826V15.13h-1.956L20.5 12l2.74 3.13h-1.957v4.696h5.087V17.87l3.13 2.739-3.13 2.739V21.39h-5.087v4.696h1.956l-2.739 3.13-2.74-3.13h1.957v-4.696H14.63v1.957l-3.13-2.74 3.13-2.738v1.956h5.087z" fill="#FFF"/>
    <g transform="translate(17.5 44)">
      <use fill="#000" filter="url(#dragMarkerC)" xlink:href="#dragMarkerD"/>
      <use fill="#FFF" xlink:href="#dragMarkerD"/>
      <rect fill="#007AFF" x="1.5" y="1.5" width="3" height="3" rx="1.5"/>
    </g>
  </g>
</svg>
`;

export const NoteTypeIcons = {
  note: <circle fill='#222' cx={9} cy={9} r={3} />,
  disease: <circle fill='#222' cx={9} cy={9} r={3} />,
  pests: (
    <path
      d='M11.46 6.63l.173.174c.548.548.858 1.056 1.02 1.586a2.74 2.74 0 0 1 0 1.604c-.162.53-.472 1.038-1.02 1.586l-1.501 1.501a2.5 2.5 0 0 1-3.536 0l2.786-4.2-4.2 2.786a2.5 2.5 0 0 1 0-3.535l1.501-1.501c.548-.548 1.056-.859 1.586-1.02a2.74 2.74 0 0 1 1.604 0c.53.161 1.038.472 1.586 1.02zm1.707 1.334a2.909 2.909 0 0 0-.02-.068c-.16-.531-.471-1.039-1.02-1.587l-.173-.173c-.548-.548-1.056-.859-1.586-1.02a2.91 2.91 0 0 0-.063-.018l.147-.26a2.154 2.154 0 0 1 3.498-.353 2.159 2.159 0 0 1-.522 3.33l-.26.15zm-.747 3.798c.37-.432.598-.845.727-1.273a2.88 2.88 0 0 0 .046-.168 1 1 0 1 1-.773 1.44zm-2.931 2.466a2.488 2.488 0 0 0 1.138-.652l.32-.32a1 1 0 1 1-1.458.972zM7.942 5.07a2.88 2.88 0 0 0-.168.046c-.428.13-.84.357-1.272.728a.999.999 0 0 1-.26-1.601 1 1 0 0 1 1.7.828zM5.008 7.316l-.321.32a2.49 2.49 0 0 0-.652 1.138 1 1 0 1 1 .973-1.459z'
      fill='#CE3D57'
      fillRule='evenodd'
    />
  ),
  weeds: <circle fill='#222' cx={9} cy={9} r={3} />,
  lying: <circle fill='#222' cx={9} cy={9} r={3} />,
  flooding: <circle fill='#222' cx={9} cy={9} r={3} />,
};
