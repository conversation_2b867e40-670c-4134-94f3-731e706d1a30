import { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import entities from 'modules/entities';

import { selectors } from 'features/files/by-machinery/redux/selectors';
import {
  DataLoader,
  ApiDataFieldElectroconductivity,
} from 'features/files/by-machinery/types';
import RootState from 'types/rootState';

export const ELECTROCONDUCTIVITY_REQUEST_ID =
  'useFieldsElectroconductivityLoader';

export const useFieldsElectroconductivityLoader = (): DataLoader<
  ApiDataFieldElectroconductivity[]
> => {
  const dispatch = useDispatch();
  const uuids = useSelector((state: RootState) =>
    selectors.getMatchingTasksUuid(state),
  );
  const uuidsStr = uuids.join(',');

  const fetch = useCallback(() => {
    if (!uuidsStr) {
      return;
    }
    dispatch(
      entities.actions.request(ELECTROCONDUCTIVITY_REQUEST_ID, [
        {
          type: 'fetchMany',
          entityType: 'fieldsDataElectroconductivity',
          query: {
            exclude: ['field_user_season.*'],
            include: ['data_source.layer.properties', 'field_user_season.id'],
            'filter{data_source.layer.source.upload.in}': uuidsStr.split(','),
          },
        },
      ]),
    );
  }, [uuidsStr, dispatch]);

  const data: ApiDataFieldElectroconductivity[] = useSelector(
    selectors.getMatchingElectroconductivity,
  );
  const request = useSelector(state =>
    entities.selectors.getRequestStatus(state, ELECTROCONDUCTIVITY_REQUEST_ID),
  );

  useEffect(fetch, [fetch]);

  return [data, request, fetch];
};
