import { VFC } from 'react';

import { FileColumn, FileFullData } from 'types/filesState';

export type ColorSchemeChecker = (dataSource: FileDataSourceType) => boolean;

export type ColorSchemeFunction = (
  properties: TEMPORARY_ANY,
  zoom: number,
  type: number,
) => {
  color: string;
  fillColor: string;
  fillOpacity: number;
  weight: number;
  radius: number;
};

export type ColorSchemeGetter = (
  column: FileColumn,
  file: FileFullData,
) => ColorSchemeFunction;

export type FileDataSourceType = {
  file: FileFullData;
  column: FileColumn;
};

export type ViewSchemeConfig = {
  type: ColorSchemeEnum;
  checker: ColorSchemeChecker;
  legend: VFC<FileDataSourceType>;
  colorSchemeGetter: ColorSchemeGetter;
};

export enum ColorSchemeEnum {
  'default',
  'ocena',
  'soilAnalysis',
}
