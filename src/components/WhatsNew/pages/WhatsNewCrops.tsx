import React, { VFC } from 'react';
import {
  StyledPage,
  styles,
} from 'components/WhatsNew/WhatsNewContent/WhatsNewContent.style';
import Button from 'components/ui/Button/Button';

import WhatsNewCropsImg from 'assets/images/whatsnew/whatsnew_crops.png';
import WhatsNewCropsImg2x from 'assets/images/whatsnew/<EMAIL>';
import { WhatsNewProps } from '../types';
import { FeaturesKeyEnum } from '../consts';

const Crops: VFC<WhatsNewProps> = ({ style, onClick, isLast, t }) => (
  <StyledPage style={{ ...style }}>
    <div className={styles.semiPage}>
      <div className={styles.pageContent}>
        <div className={styles.textContent}>
          <h2 className={styles.title}>{t('whatsnew.crops.title')}</h2>
          <p className={styles.text}>{t('whatsnew.crops.text')}</p>
        </div>
        <Button className='btn btn-success fit-content' onClick={onClick}>
          {t(`whatsnew.${isLast ? 'end' : 'next'}.action_text`)}
        </Button>
      </div>
    </div>
    <div className={styles.semiPage}>
      <picture className={styles.pagePicture} style={{ marginRight: 40 }}>
        <source srcSet={`${WhatsNewCropsImg} 1x, ${WhatsNewCropsImg2x} 2x`} />
        <img src={WhatsNewCropsImg} alt={t('whatsnew.crops.title') as string} />
      </picture>
    </div>
  </StyledPage>
);

const WhatsNewCrops = {
  // this key was used for back compability with mobile app
  // we have CROPS-140 key on mobile too
  key: FeaturesKeyEnum.crops140,
  component: Crops,
  filter: {},
};

export default WhatsNewCrops;
