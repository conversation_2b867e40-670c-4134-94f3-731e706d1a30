import React, { VFC } from 'react';
import { Redirect, matchPath, RouteComponentProps } from 'react-router-dom';
import { useSelector } from 'react-redux';
import auth from 'modules/auth';
import { INVITATION_TYPES } from 'features/multiaccount/constants';
import { parseQuery } from 'utils/query';

type UrlMapResolver = (
  // eslint-disable-next-line  @typescript-eslint/no-explicit-any
  params: RouteComponentProps<any>,
  isAuthorized: boolean,
) => string;
type UrlMapType = Record<string, string | UrlMapResolver>;

const UrlMap: UrlMapType = {
  '/fields': '/fields',
  '/fields/sorting': '/fields/list/settings',
  '/crop-rotation': '/fields/crop-rotation',
  '/add-fields': '/fields/add/select',
  '/seasons': '/fields?seasons',
  '/seasons/add': '/fields?seasons=create',
  // Duplication here is due to root app router ignoring first /scouting
  '/notes': '/scouting/scouting',
  '/add-note': '/scouting/scouting?add_note',
  '/confirm/:token': ({ match }) =>
    `/internal/confirm-email/${match.params.token}`,

  '/delete/:token': ({ match }) => `/internal/delete/${match.params.token}`,
  '/switch-workspace/:workspaceUuid': ({ match }) =>
    `/internal/switch-workspace/${match.params.workspaceUuid}`,
  '/accept-client-invitation': ({ location }, isAuthorized) => {
    const query = parseQuery(location);
    const target = `/fields/invitation/${INVITATION_TYPES.DEALER}/${query.data}`;

    if (!isAuthorized) {
      return `/auth/login/${INVITATION_TYPES.DEALER}${location.search}&next=${target}`;
    }

    return target;
  },
  '/accept-consultant-invitation': ({ location }, isAuthorized) => {
    const query = parseQuery(location);
    const target = `/fields/invitation/${INVITATION_TYPES.CONSULTANT}/${query.data}`;

    if (!isAuthorized) {
      return `/auth/login/${INVITATION_TYPES.CONSULTANT}${location.search}&next=${target}`;
    }

    return target;
  },
  '/accept-workspace-invitation': ({ location }, isAuthorized) => {
    const query = parseQuery(location);
    const target = `/fields/invitation/${INVITATION_TYPES.WORKSPACE}/${query.data}`;

    if (!isAuthorized) {
      return `/auth/login/${INVITATION_TYPES.WORKSPACE}${location.search}&next=${target}`;
    }

    return target;
  },
};

const matchRedirect = (props: RouteComponentProps, isAuthorized = false) => {
  const path = props.location.pathname.slice(props.match.url.length);

  for (let [from, to] of Object.entries(UrlMap)) {
    const match = matchPath(path, { path: from });
    if (match) {
      return typeof to === 'function'
        ? to({ ...props, match }, isAuthorized)
        : to;
    }
  }
  return null;
};

const DeepLinkPage: VFC<RouteComponentProps> = props => {
  const isAuthorized = useSelector(auth.selectors.isAuthorized);
  const target = matchRedirect(props, isAuthorized);

  if (target) {
    if (target.startsWith('http')) {
      window.location.replace(target);
      return null;
    }
    return <Redirect to={target} />;
  }

  return <Redirect to='/' />;
};

export default DeepLinkPage;
