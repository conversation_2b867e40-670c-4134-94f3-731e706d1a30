import React, { useEffect, useState } from 'react';
import {
  StyledCropRotationStickyHeader,
  styles,
} from '../CropRotationView.style';
import { AutoSizer, MultiGrid } from 'react-virtualized';
import {
  COLUMN_WIDTH,
  FIELDS_STICKY_ROW_HEIGHT,
  FIRST_COLUMN_WIDTH,
  SEASONS_STICKY_ROW_HEIGHT,
} from 'constants/crop-rotation';

type CropRotationStickyHeaderProps = {
  scrollLeft: number;
  scrollTop: number;
  cellRenderer: TEMPORARY_ANY;
  columnCount: number;
};

export const CropRotationStickyHeader = ({
  scrollLeft,
  scrollTop,
  cellRenderer,
  columnCount,
}: CropRotationStickyHeaderProps) => {
  const [isSticky, setIsSticky] = useState(false);
  useEffect(() => {
    if (scrollTop >= 152) setIsSticky(true);
    else setIsSticky(false);
  }, [scrollTop]);

  return (
    <StyledCropRotationStickyHeader isSticky={isSticky}>
      <AutoSizer disableHeight>
        {({ width }) => (
          <MultiGrid
            className={styles.cropRotationGrid}
            classNameBottomLeftGrid={styles.bottomLeft}
            classNameBottomRightGrid={styles.bottomRight}
            rowCount={2}
            rowHeight={({ index }) =>
              index === 0 ? SEASONS_STICKY_ROW_HEIGHT : FIELDS_STICKY_ROW_HEIGHT
            }
            columnCount={columnCount}
            columnWidth={({ index }) =>
              index ? COLUMN_WIDTH : FIRST_COLUMN_WIDTH
            }
            fixedColumnCount={1}
            height={SEASONS_STICKY_ROW_HEIGHT + FIELDS_STICKY_ROW_HEIGHT}
            width={width}
            cellRenderer={cellRenderer}
            scrollLeft={scrollLeft}
          />
        )}
      </AutoSizer>
    </StyledCropRotationStickyHeader>
  );
};
