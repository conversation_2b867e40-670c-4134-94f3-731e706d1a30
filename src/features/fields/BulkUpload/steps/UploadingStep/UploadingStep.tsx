import React, { VFC } from 'react';
import { useSelector } from 'react-redux';

import { selectors } from 'features/fields/BulkUpload/redux/selectors';

import { UploadingTasksArea } from 'features/fields/BulkUpload/styles';
import { FileBundleList } from 'features/fields/BulkUpload/components';

interface UploadingStepProps {}

export const UploadingStep: VFC<UploadingStepProps> = () => {
  const validBundles = useSelector(selectors.getValidFileBundles);

  return (
    <UploadingTasksArea>
      <FileBundleList bundles={validBundles} />
    </UploadingTasksArea>
  );
};
