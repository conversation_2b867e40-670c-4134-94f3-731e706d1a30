import React, { CSSProperties, VFC } from 'react';
import { useSelector } from 'react-redux';

import fieldSeason from 'modules/fieldSeason';
import { StyledCropsTableCell, styles } from '../CropRotationView.style';
import settings from 'modules/settings';
import { SeasonType } from 'types/rootState';
import { FormatUnit } from 'utils/functions';
import { CropStatisticType } from '../types';

type CropsTableCellProps = {
  season: SeasonType
  cropCode: string,
  columnIndex: number
  rowIndex: number
  style: CSSProperties
}

const CropsTableCell: VFC<CropsTableCellProps> = ({ season, cropCode, columnIndex, rowIndex, style }) => {
  const formatUnit: FormatUnit = useSelector(settings.selectors.getUnitFormatter);
  const crops: CropStatisticType[] = useSelector(state => fieldSeason.selectors.getCropStatistics(state, season.id));
  const crop = crops.find(crop => crop.crop === cropCode);

  return (
    <StyledCropsTableCell
      columnIndex={columnIndex}
      rowIndex={rowIndex}
      style={style}
    >
      <span className={styles.cropName}>
        {crop?.area ? formatUnit(crop.area, 'ha', 'area') : '—'}
      </span>
    </StyledCropsTableCell>
  );
};

export default CropsTableCell;
