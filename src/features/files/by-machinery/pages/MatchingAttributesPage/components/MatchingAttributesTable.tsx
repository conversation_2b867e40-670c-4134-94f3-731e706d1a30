import React, { FC } from 'react';
import { useLocation } from 'react-router-dom';

import { useTranslate } from 'utils/use-translate';
import { parseQuery } from 'utils/query';

import { MatchingCustomAttributesTable } from './MatchingCustomAttributesTable';
import {
  ApiDataFieldLayer,
  DataTypesKey,
  TasksByMachineryMeta,
} from 'features/files/by-machinery/types';
import { AttributeGroup } from './table/AttributeGroup';
import { createLayersLinks } from 'features/files/by-machinery/pages/MatchingAttributesPage/utils/createLayersLinks';

import {
  useDataStore,
  useSetAttribute,
  useSetUnit,
  useSetMethod,
} from 'features/files/by-machinery/pages/MatchingAttributesPage/service';

import {
  ColumnHeadCellStyle,
  ColumnSubTitleStyle,
  ColumnTitleStyle,
  MatchingTableStyle,
} from './table/styles';

export type MatchingAttributesTableProps = {
  dataLayers: ApiDataFieldLayer[];
  meta: TasksByMachineryMeta;
};

export const MatchingAttributesTable: FC<MatchingAttributesTableProps> = ({
  dataLayers,
  meta,
}) => {
  const location = useLocation();
  const { t } = useTranslate();

  const layersLinks = createLayersLinks(dataLayers);
  const dataLayersWithoutDuplications = dataLayers.filter(
    layer => layer.uuid in layersLinks,
  );

  const dataStore = useDataStore({
    layersLinks,
    dataLayers,
    meta,
  });

  const query = parseQuery(location);
  const dataType = (query['data-type'] || DataTypesKey.machinery) as string;

  const onSetAttributeValue = useSetAttribute(dataStore);
  const onSetUnit = useSetUnit(dataStore);
  const onSetMethod = useSetMethod(dataStore);

  return (
    <>
      <MatchingTableStyle
        equalColumns={dataType !== DataTypesKey.soilSampling}
        columns={dataLayersWithoutDuplications.length}
      >
        <ColumnHeadCellStyle>
          <ColumnTitleStyle>
            {t(`import.matching_attributes.${dataType}.our`) as string}
          </ColumnTitleStyle>
        </ColumnHeadCellStyle>
        {dataLayersWithoutDuplications.map(layer => (
          <ColumnHeadCellStyle key={layer.uuid}>
            <ColumnTitleStyle>
              {
                // @ts-ignore
                t(`import.matching_attributes.${dataType}.file_title`, {
                  name: layer.name,
                }) as string
              }
            </ColumnTitleStyle>

            {layersLinks[layer.uuid]!.length > 1 && (
              <ColumnSubTitleStyle>
                {
                  t('import.matching_attributes.files_subtitle', {
                    count: layersLinks[layer.uuid]!.length,
                  }) as string
                }
              </ColumnSubTitleStyle>
            )}
          </ColumnHeadCellStyle>
        ))}
        {meta.groups.map(group => {
          return (
            <AttributeGroup
              key={group.name}
              group={group}
              dataType={dataType}
              onSetAttributeValue={onSetAttributeValue}
              onSetUnit={onSetUnit}
              onSetMethod={onSetMethod}
              dataLayers={dataLayersWithoutDuplications}
            />
          );
        })}
      </MatchingTableStyle>
      {(dataType === DataTypesKey.soilSampling ||
        dataType === DataTypesKey.electroconductivity) && (
        <MatchingCustomAttributesTable
          dataLayers={dataLayersWithoutDuplications}
          meta={meta}
          dataType={dataType}
        />
      )}
    </>
  );
};
