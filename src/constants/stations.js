// Like ISO8601, but excluding timezone information
export const BackendDateFormat = 'YYYY-MM-DD[T]HH:mm:ss.SSS';

export const ChartColors = [
  '#29ae60',
  '#6336ff',
  '#027aff',
  '#f4d359',
  '#ff4ee8',
  '#fe3b31',
];

export const CompareColors = [
  '#FF3B30',
  '#FF8551',
  '#007AFF',
  '#C09FFD',
  '#F4D359',
  '#53FF4A',
  '#27AE60',
  '#44FFF0',
  '#FF4FE9',
  '#C63867',
  '#6337FF',
  '#B64FDF',
  '#02BAFF',
  '#80B1FF',
  '#FD9103',
  '#DDFF1A',
  '#9B7939',
  '#DBDAB9',
  '#A5B2BC',
  '#45436C',
];

// prettier-ignore
export const MapColorClasses = [
  {
    layers: ['air_temp', 'temp'],
    colors: ['#000000', '#7F25FC', '#0A24FB', '#1883FB', '#48C0FD', '#85FFFE', '#84FF31', '#FFFE38', '#FD8023', '#FC0D1C', '#FFFFFF'],
  },
  {
    layers: ['air_moisture', 'moisture', 'precipitation'],
    colors: ['#86132C', '#EE732C', '#EEE317', '#A9C502', '#39A01E', '#1FCF94', '#18C1F8', '#0658FF', '#1D02C3', '#3E0081', '#190045'],
  },
  {
    layers: ['battery'],
    colors: ['#FF3B30', '#FF3B30', '#F4D359', '#ACD31F', '#0CB959'],
  },
  {
    layers: ['luminance'],
    colors: ['#130630', '#163672', '#2E977C', '#B3DC65', '#FFEF9E'],
  },
  {
    layers: ['air_pressure'],
    colors: ['#091D6C', '#006793', '#2E977C', '#DCBE65', '#D45A24'],
  }
];

export const DefaultMapColors = MapColorClasses[0].colors;

export const LayerOrder = [
  'air_temp',
  'air_moisture',
  'temp',
  'moisture',
  'air_pressure',
  'luminance',
  'precipitation',
  'battery',
];

export const UnitsMapping = {
  '°С': {
    category: 'temp',
    unit: 'C',
  },
  V: {
    category: 'voltage',
    unit: 'm',
  },
  '%': {
    category: 'humidity',
    unit: 'm',
  },
  lx: {
    category: 'luminance',
    unit: 'lx',
  },
  Pa: {
    category: 'pressure',
    unit: 'Pa',
  },
  mm: {
    category: 'sensorPrecip',
    unit: 'mm',
  },
};

export const DomainMapping = {
  air_temp: [-25, 45], // C
  temp: [-10, 45], // C
  luminance: [0, 35000], // lux
  air_pressure: [83993, 108657], // Pa
};
