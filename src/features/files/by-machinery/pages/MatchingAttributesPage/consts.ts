import { TranslateFunction } from 'utils/use-translate';

export type AttributeType = {
  id: string;
  group: AttributeGroup;
  units?: AttributeUnits[];
};

export enum AttributeGroup {
  empty = 'empty',
  planting = 'planting',
  harvesring = 'harvesring',
  tillaging = 'tillaging',
  machinery = 'machinery',
}

export enum AttributeUnits {
  kg = 'kg',
  centner = 'centner',
  ton = 'ton',
  kg_l = 'kg_l',

  m_s = 'm_s',
  km_h = 'km_h',
  m = 'm',
  sm = 'sm',
  percents = 'percents',
}

export const matchingAttributes: AttributeType[] = [
  {
    id: 'date',
    group: AttributeGroup.empty,
  },
  {
    id: 'crop',
    group: AttributeGroup.empty,
  },
  {
    id: 'hybrid',
    group: AttributeGroup.empty,
  },

  {
    id: 'application_rate',
    group: AttributeGroup.planting,
    units: [AttributeUnits.kg_l],
  },
  {
    id: 'planned_application_rate',
    group: AttributeGroup.planting,
    units: [AttributeUnits.kg_l],
  },
  {
    id: 'wet_yield',
    group: AttributeGroup.harvesring,
    units: [AttributeUnits.kg, AttributeUnits.centner, AttributeUnits.ton],
  },
  {
    id: 'moisture',
    group: AttributeGroup.harvesring,
    units: [AttributeUnits.percents],
  },
  {
    id: 'width',
    group: AttributeGroup.harvesring,
    units: [AttributeUnits.m],
  },
  {
    id: 'depth',
    group: AttributeGroup.tillaging,
    units: [AttributeUnits.sm],
  },
  {
    id: 'speed',
    group: AttributeGroup.machinery,
    units: [AttributeUnits.m_s, AttributeUnits.km_h],
  },
  {
    id: 'elevation',
    group: AttributeGroup.machinery,
    units: [AttributeUnits.m],
  },
  {
    id: 'machinery_id',
    group: AttributeGroup.machinery,
  },
];

export const EcUnitAttribute = (attr: string, t: TranslateFunction) => {
  switch (attr) {
    case 'osl_ec_depth_1':
    case 'osl_ec_depth_2':
    case 'osl_ec_depth_3':
    case 'osl_ec_depth_4':
      return t('import.matching.units.mS1m-1');
    case 'osl_ms_depth_1':
    case 'osl_ms_depth_2':
      return t('import.matching.units.ppt');

    case 'osl_relative_water_content':
      return t('import.matching.units.percents');
    default:
      return '';
  }
};
