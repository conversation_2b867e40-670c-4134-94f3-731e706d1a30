import { DataTypesKey, TaskStatus } from './tasks';

export type ExtraFieldsType = Nullable<{
  [DataTypesKey.soilSampling]: { method: string | null };
}>;

export type ApiProcessedTask = {
  uuid: string;

  status: TaskStatus;

  name: string;
  size: number;
  data_type: DataTypesKey;

  sources: ApiDataSource[];
  created_at: string;
  err_code: Nullable<string>;
  file: string;
  updated_at: string;

  url: string;
  viewed_at: string;
};

export type ApiDataFieldLayerProperties = {
  uuid: string;
  layer_uuid: string;
  type: string;
  name: string;
  is_custom_target_name: boolean;
  is_values_imported: boolean;
  unit: Nullable<string>;
  extra_fields: ExtraFieldsType;
  sample_values?: [Nullable<string>, Nullable<string>, Nullable<string>];
  target_name: Nullable<string>;
  matching_history: {
    unit: Nullable<string>;
    target_name: Nullable<string>;
  };
};

export type SpatialMatchingMeta = {
  matched_fields: number;
  matched_soil_sampling_maps: number;
  matched_soil_sampling_points: number;
  total_fields: number;
  total_fields_with_soil_sampling_maps: number;
  total_soil_sampling_maps: number;
  total_soil_sampling_points: number;
};

export type ApiDataSourceLayer = {
  created_at: string;
  updated_at: string;
  features_count: number;
  is_spatial_matched: boolean;
  name: string;
  source_uuid: string;
  spatial_matching_params: {};
  spatial_matching_meta: SpatialMatchingMeta;
  status: 'created' | 'processing' | 'finished' | 'failed';
  uuid: string;
};

export type ApiDataSource = {
  uuid: string;
  upload_uuid: string;
  driver:
    | 'ESRI Shapefile'
    | 'GML'
    | 'LIBKML'
    | 'GeoJSON'
    | 'KML'
    | 'TopoJSON'
    | 'GPKG'
    | 'PANDAS'
    | 'ADAPT';
  path: string;
  is_processed: boolean;
  created_at: string;
  updated_at: string;
  layers?: ApiDataSourceLayer[];
};

export type ApiDataFieldLayer = {
  uuid: string;

  source_uuid: string;
  created_at: string;
  updated_at: string;
  name: string;
  features_count: number;

  source: ApiDataSource;
  properties: ApiDataFieldLayerProperties[];
  operation_timelog_id: Nullable<number>;
};

export type ApiDataFieldOperationLegendItem<
  TItemType extends string | number = number,
> = {
  min: TItemType;
  max: TItemType;
  unit?: string;
};

export type ApiDataFieldOperationLegend = Record<
  string,
  ApiDataFieldOperationLegendItem<string | number>
>;

export type ApiDataFieldSource = {
  uuid: string;
  data_source_model: string;
  data_source_id: string;
  created_at: string;
  updated_at: string;
  layer: ApiDataFieldLayer;
};

export type ApiDataFieldOperations = {
  uuid: string;
  parent_operation_uuid: string;

  data_source: ApiDataFieldSource;
  field_uuid: number;

  start_time?: string;
  end_time?: string;
  image: string;
  crop: null; // TODO
  type: ApiDataFieldOperationType;
  viewed_at: null;
  is_enabled: boolean;
  is_imported: boolean;
  area: number;

  created_at: string;
  updated_at: string;
};

export type ApiDataFieldElectroconductivity = {
  uuid: string;
  data_source: ApiDataFieldSource;
  field_user_season: {
    id: number;
  };

  viewed_at: null;
  is_imported: boolean;
  created_at: string;
  updated_at: string;
};

export enum ApiDataFieldOperationType {
  SowingAndPlanting = 'SowingAndPlanting',
  Fertilizing = 'Fertilizing',
  CropProtection = 'CropProtection',
  Tillage = 'Tillage',
  Harvesting = 'Harvesting',
  Baling = 'Baling',
}

export type FieldDataValues = {
  uuid: string;
  type: string;
  name: string;
  unit: null; // TODO
  value: string;
  sample_values: string[];
  target_value: string;
  prop: {
    created_at: string;
    is_values_imported: boolean;
    layer: ApiDataFieldLayer;
    layer_uuid: string;
    name: string;
    sample_values: string[];
    target_name: Nullable<string>;
    type: string;
    unit: Nullable<string>;
    updated_at: string;
    uuid: string;
  };
};
