import React from 'react';
import { connect } from 'react-redux';
import { Redirect } from 'react-router-dom';

import files from 'modules/files';

import getRootScoutingUrl from 'utils/get-root-scouting-url';

const MissingFileRedirector = ({ location, hasFile }) => {
  if (hasFile) {
    return null;
  }
  return <Redirect to={getRootScoutingUrl(location.pathname)} />;
};

const mapStateToProps = (state, ownProps) => ({
  hasFile: files.selectors.getIDs(state).includes(ownProps.fileId),
});

export default connect(mapStateToProps)(MissingFileRedirector);
