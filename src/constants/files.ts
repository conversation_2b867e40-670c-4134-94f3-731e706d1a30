export enum OperationTypesEnum {
  tillage = 'tillage',
  fertilization = 'fertilization',
  harvesting = 'harvesting',
  spraying = 'spraying',
  sowing = 'sowing',
  soil_analysis = 'soil_analysis',
}

export const OperationTypes = [
  OperationTypesEnum.tillage,
  OperationTypesEnum.fertilization,
  OperationTypesEnum.harvesting,
  OperationTypesEnum.spraying,
  OperationTypesEnum.sowing,
  OperationTypesEnum.soil_analysis,
];

export const Computers = [
  {
    name: 'amazone',
    hint: 'shapefile',
    type: 'model',
    format: 'shp',
  },
  {
    name: 'john_deere',
    hint: 'shapefile',
    type: 'model',
    format: 'shp',
  },
  {
    name: 'precision_planting_20_20',
    hint: 'shapefile_prj',
    type: 'model',
    format: 'shp',
    options: {
      include_optional: true,
    },
  },
  {
    name: 'trimble',
    hint: 'shapefile',
    type: 'model',
    format: 'shp',
  },
  {
    name: 'shapefile',
    hint: 'other_brands',
    type: 'format',
    format: 'shp',
  },
  {
    name: 'isoxml',
    hint: 'isobus',
    type: 'format',
    format: 'isoxml',
  },
  {
    name: 'kml',
    hint: '',
    type: 'format',
    format: 'kml',
  },
];
