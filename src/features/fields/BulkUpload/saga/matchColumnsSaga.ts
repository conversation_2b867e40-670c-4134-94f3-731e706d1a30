import { call, put, select, takeEvery } from 'redux-saga/effects';

// @ts-ignore
import fields from 'modules/fields';

import {
  BulkUploadStep,
  FieldAttribute,
  PropertyRecord,
} from 'features/fields/BulkUpload/types';

// @ts-ignore
import { apiCall } from 'utils/effects';

import {
  groupPropertiesBySeasonId,
  prepareFieldsFileUpdate,
  validateMatchedFields,
} from 'features/fields/BulkUpload/saga/utils';

import bulkUploadModule from 'features/fields/BulkUpload/redux';

export interface MatchColumnsSagaHost {
  props: {
    fileId: number;
    propertiesMap: Record<string, PropertyRecord | number[] | string>;
    onSetData: (data: Record<string, string[]>) => void;
    onShowPopup: (invalidSeasons: number[]) => void;
  };
}

export function* matchColumnsSaga(host: MatchColumnsSagaHost) {
  const { fileId, propertiesMap, onSetData, onShowPopup } = host.props;

  const data: Record<string, string[]> = yield call(
    apiCall,
    fields.actions.fetchPropertiesFromFile(fileId),
  );

  onSetData(data);

  yield takeEvery(
    fields.actions.setMatchedProperties.toString(),
    function* (action: {
      payload: { properties: Record<string, FieldAttribute>; proceed: boolean };
    }) {
      const { properties, proceed } = action.payload;

      if (!Object.keys(properties).length) {
        yield put(
          bulkUploadModule.actions.setCurrentStep(BulkUploadStep.SAVING),
        );
        return;
      }

      const groupBySeasonId = groupPropertiesBySeasonId(properties);
      const invalidSeasons = validateMatchedFields(groupBySeasonId);

      if (!proceed && invalidSeasons.length) {
        onShowPopup(invalidSeasons);
        return;
      }

      const { preparedToSend, noCrops } =
        prepareFieldsFileUpdate(groupBySeasonId);

      yield put(bulkUploadModule.actions.setCropMatchingAvailable(!noCrops));

      let dataToSend: Record<string, PropertyRecord | number[] | string> =
        preparedToSend;

      if (propertiesMap) {
        dataToSend = {
          ...preparedToSend,
          ...propertiesMap,
        };
      }

      dataToSend = {
        ...preparedToSend,
        season_ids: invalidSeasons,
      };

      yield call(apiCall, fields.actions.updateFieldsFile(fileId, dataToSend));

      if (noCrops) {
        const fileIds: number[] = yield select(
          bulkUploadModule.selectors.getUploadedFilesIds,
        );

        yield call(
          apiCall,
          bulkUploadModule.actions.fetchFieldsFromFiles(fileIds),
        );
      }

      yield put(
        bulkUploadModule.actions.setCurrentStep(
          noCrops ? BulkUploadStep.SAVING : BulkUploadStep.CROP_MATCHING,
        ),
      );
    },
  );
}
