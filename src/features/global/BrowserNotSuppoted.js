import React, { useEffect } from 'react';
import { useHistory } from 'react-router-dom';

import BodyClassName from 'components/BodyClassName';
import AuthFooter from 'features/auth/AuthFooter';
import Logo from 'components/Logo';

import { useTranslate } from 'utils/use-translate';
import logEvent from 'sagas/global/logEvent';

import logoSafari from 'assets/images/logo-safari.png';
import logoChrome from 'assets/images/logo-chrome.png';
import logoFirefox from 'assets/images/logo-firefox.png';

const BROWSERS = [
  {
    id: 'Chrome',
    link: 'https://www.google.com/chrome/',
    logo: logoChrome,
  },
  {
    id: 'Safari',
    link: 'https://www.apple.com/safari/',
    logo: logoSafari,
  },
  {
    id: 'Firefox',
    link: 'https://www.mozilla.org/en-US/firefox/',
    logo: logoFirefox,
  },
];

const BrowserNotSupported = () => {
  const { t } = useTranslate();
  const history = useHistory();

  useEffect(() => {
    logEvent('browser_not_supported', {
      userAgent: navigator.userAgent,
    });
    history.replace('/');
  }, [history]);

  return (
    <main className='page-container'>
      <div className='not-supported'>
        <BodyClassName className='onesoil-not-supported-page' />
        <div className='not-supported__header'>
          <Logo />
        </div>
        <div className='not-supported__info'>
          <span className='emoji' />
          <h1>{t('auth.browsers.title')}</h1>
          <h2>{t('auth.browsers.description')}</h2>
          <div className='not-supported__browsers'>
            {BROWSERS.map(({ id, link, logo }) => (
              <a
                key={id}
                href={link}
                className='not-supported__browser'
                target='_blank'
                rel='noopener noreferrer'
              >
                <img width='40px' src={logo} alt={`supported browser ${id}`} />
                <div className='not-supported__meta'>
                  <span className='not-supported__browser-title'>{id}</span>
                  <span className='not-supported__browser-desc'>
                    {t('auth.browsers.download')}
                  </span>
                </div>
              </a>
            ))}
          </div>
        </div>
      </div>
      <AuthFooter />
    </main>
  );
};

export default BrowserNotSupported;
