import React, { useEffect, VFC } from 'react';
import { Route, RouteComponentProps, Switch } from 'react-router-dom';
import { useDispatch } from 'react-redux';

import { FloatingNavigation } from './components/FloatingNavigation';
import { ImportProcessedHeader } from './components/ImportProcessedHeader';

import ImportFilesPage from './pages/ImportFilesPage';
import { MatchingAttributesPage } from './pages/MatchingAttributesPage';
import { MatchingOperationsPage } from './pages/MatchingOperationsPage';
import { MatchingCropsPage } from './pages/MatchingCropsPage';

import { FEATURES, ProtectedRouteHOC } from 'features/permissions';
import { actions as toastActions } from 'features/platform/ToastModule/redux/actions';

import { TOAST_KEY_TO_IMPORT } from './constants';

import { ImportFilesHeader } from './styles';

const ImportFilesModule: VFC<RouteComponentProps> = ({ match }) => {
  const dispatch = useDispatch();

  useEffect(() => {
    return () => {
      dispatch(toastActions.remove(TOAST_KEY_TO_IMPORT));
    };
  }, [dispatch]);

  return (
    <div className='main-section' style={{ overflow: 'auto' }}>
      <ImportFilesHeader>
        <ImportProcessedHeader />
      </ImportFilesHeader>
      <FloatingNavigation />

      <Switch>
        <Route
          exact
          path={`${match.url}/matching/crops/`}
          component={MatchingCropsPage}
        />
        <Route
          exact
          path={`${match.url}/matching/operations/`}
          component={MatchingOperationsPage}
        />
        <Route
          exact
          path={`${match.url}/matching/attributes/`}
          component={MatchingAttributesPage}
        />
        <Route exact path={`${match.url}/`} component={ImportFilesPage} />
      </Switch>
    </div>
  );
};

export default ProtectedRouteHOC({
  Component: ImportFilesModule,
  feature: FEATURES.IMPORT_BY_MACHINERY,
  redirect: '/fields',
});
