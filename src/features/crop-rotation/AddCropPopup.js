import React, { useEffect, useState, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { createSelector } from 'reselect';
import { call } from 'redux-saga/effects';
import keyBy from 'lodash/keyBy';
import cx from 'classnames';

import CropWarningTooltip from './CropWarningTooltip';

import entities from 'modules/entities';
import fields from 'modules/fields';
import fieldSeason from 'modules/fieldSeason';

import { getViewportHash } from 'utils/get-root-scouting-url';
import { apiCall, takeEveryProp } from 'utils/effects';
import useSaga from 'utils/use-saga';
import i18n from 'utils/i18n';
import { useTranslate } from 'utils/use-translate';

import { styles } from './CropRotationView/CropRotationView.style';
import Downshift from 'downshift';

const getUniqueCrops = createSelector(
  state => entities.selectors.getAll(state, 'fieldCrop'),
  crops => {
    const { t } = i18n;
    const uniqueCrops = Object.keys(keyBy(crops, 'crop'));
    return uniqueCrops.sort((a, b) => {
      const titleA = t(`crop.${a}`);
      const titleB = t(`crop.${b}`);
      return titleA.localeCompare(titleB);
    });
  },
);

const AddCropPopup = ({
  fieldSeasonId,
  match,
  history,
  location,
  mapSearch,
}) => {
  const { t } = useTranslate();
  const sortedCropTypes = useSelector(fields.selectors.getSortedCropTypes);
  const cropColors = useSelector(fieldSeason.selectors.getCropColors);
  const recentCrops = useSelector(getUniqueCrops);
  const [searchValue, setSearchValue] = useState('');
  const [items, setItems] = useState([]);
  const [usedItems, setUsedItems] = useState([]);
  const [recommendationsByCrop, setRecommendations] = useState({});

  const options = useMemo(() => {
    return sortedCropTypes.map(type => ({
      label: t(`crop.${type}`),
      crop: type,
    }));
  }, [sortedCropTypes, t]);

  const usedCrops = useMemo(() => {
    return recentCrops.map(crop => ({
      label: t(`crop.${crop}`),
      crop: crop,
      color: cropColors[crop],
    }));
  }, [cropColors, recentCrops, t]);

  const resetSearch = () => {
    setSearchValue('');
    setItems(options);
    setUsedItems(usedCrops);
  };

  const handleChangeSearch = e => {
    const value = e.target.value;
    setSearchValue(value);
    setItems(mapSearch({ options, value }));
    setUsedItems(mapSearch({ options: usedCrops, value }));
  };

  useEffect(() => {
    setItems(options);
    setUsedItems(usedCrops);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sortedCropTypes]);

  useSaga(
    function* (propsCh) {
      yield takeEveryProp(propsCh, 'fieldSeasonId', function* () {
        if (!fieldSeasonId) {
          return;
        }

        const { crops } = yield call(
          apiCall,
          fields.actions.fetchRecommendations(sortedCropTypes, fieldSeasonId),
        );

        setRecommendations(keyBy(crops, 'crop'));
      });
    },
    [fieldSeasonId],
  );

  const itemToString = i => (i ? i.label : '');

  const handleSelectItem = item => {
    const viewport = getViewportHash(history.location.pathname);
    history.replace({
      pathname: `/${viewport}/crop-rotation/crop-details`,
      state: {
        crop: item.crop,
      },
    });
  };

  return (
    <Downshift
      onChange={selection => {
        if (selection) {
          handleSelectItem(selection);
        }
      }}
      itemToString={itemToString}
      selectedItem={options.find(o => o.crop === searchValue) || null}
    >
      {({ getInputProps, getMenuProps, getItemProps, setHighlightedIndex }) => (
        <div
          style={{ width: 360 }}
          onMouseLeave={() => setHighlightedIndex(null)}
        >
          <div className={cx('modal-select-searchbox', 'add-crop-searchbox')}>
            <div className='form-input-extended __filled'>
              <div className='modal-select-searchbox__icon'>
                <svg width='14' height='15' className='ico-search-os'>
                  <g transform='translate(-519 -18)'>
                    <path
                      d='M524.56963,27.42398c-2.13274,0 -3.85435,-1.72162 -3.85435,-3.85435c0,-2.13274 1.72161,-3.85435 3.85435,-3.85435c2.13273,0 3.85435,1.72161 3.85435,3.85435c0,2.13273 -1.72162,3.85435 -3.85435,3.85435zM529.72041,27.43396h-0.67753l-0.24014,-0.23156c0.84048,-0.9777 1.34649,-2.247 1.34649,-3.62779c0,-3.0789 -2.49571,-5.57461 -5.57462,-5.57461c-3.0789,0 -5.57461,2.49571 -5.57461,5.57461c0,3.07891 2.49571,5.57462 5.57461,5.57462c1.38079,0 2.65009,-0.50601 3.62779,-1.34649l0.23156,0.24014v0.67753l4.28817,4.27959l1.27787,-1.27787z'
                      fill='currentColor'
                    />
                  </g>
                </svg>
              </div>
              <input
                {...getInputProps({
                  autoFocus: true,
                  type: 'text',
                  value: searchValue,
                  onChange: handleChangeSearch,
                  placeholder: t(
                    'croprotation.autofill.crop_search_placeholder',
                  ),
                  className: 'form-input size-full',
                  onClick: e => {
                    e.preventDefault();
                    e.stopPropagation();
                  },
                })}
              />
              {Boolean(searchValue) && (
                <button
                  onClick={resetSearch}
                  className='form-input-clear'
                  type='button'
                >
                  ×
                </button>
              )}
            </div>
          </div>
          <div className='modal-select__inner' {...getMenuProps()}>
            {usedItems.length > 0 && (
              <>
                <div className={styles.addCropSectionTitle}>
                  {t('croprotation.crops.sections.recent')}
                </div>
                <ul className='modal-select__list'>
                  {usedItems.map((item, index) => (
                    <li
                      key={`usedItem-${item.crop}`}
                      className='modal-select__list-item'
                      {...getItemProps({ item, index })}
                      onClick={() => handleSelectItem(item)}
                    >
                      <div className='modal-select__item'>
                        <div
                          style={{
                            width: '16px',
                            height: '16px',
                            backgroundColor: item.color,
                            borderRadius: '2px',
                            marginRight: '5px',
                            marginTop: '-2px',
                          }}
                        />
                        <span className='modal-select__value'>
                          {item.label}
                        </span>
                      </div>
                      {recommendationsByCrop[item.crop] && (
                        <CropWarningTooltip
                          recommendation={recommendationsByCrop[item.crop]}
                          inline
                        />
                      )}
                    </li>
                  ))}
                </ul>
              </>
            )}

            {items.length > 0 && (
              <>
                <div className={styles.addCropSectionTitle}>
                  {t('croprotation.crops.sections.all')}
                </div>
                <ul className='modal-select__list'>
                  {items.map((item, index) => (
                    <li
                      key={`item-${item.crop}`}
                      className='modal-select__list-item'
                      {...getItemProps({
                        item,
                        index: index + usedItems.length,
                      })}
                      onClick={() => handleSelectItem(item)}
                    >
                      <div className='modal-select__item'>
                        <span className='modal-select__value'>
                          {item.label}
                        </span>
                      </div>
                      {recommendationsByCrop[item.crop] && (
                        <CropWarningTooltip
                          recommendation={recommendationsByCrop[item.crop]}
                          inline
                        />
                      )}
                    </li>
                  ))}
                </ul>
              </>
            )}
          </div>
        </div>
      )}
    </Downshift>
  );
};

AddCropPopup.defaultProps = {
  mapSearch: ({ options, value }) =>
    value && value !== ''
      ? options.filter(({ label }) =>
          label.toLowerCase().includes(value.toLowerCase().trim()),
        )
      : options,
};

export default AddCropPopup;
