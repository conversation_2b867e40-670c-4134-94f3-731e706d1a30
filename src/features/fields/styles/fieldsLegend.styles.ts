import { css } from 'linaria';

const widgetWrapper = css`
  display: flex;
  position: relative;

  & + & {
    margin-top: 10px;
  }
`;

const widgetItem = css`
  flex-grow: 1;
  & + & {
    margin-left: 10px;
    flex-grow: 0;
    flex-shrink: 0;
  }
`;

const actions = css`
  position: relative;
  padding-left: 12px;
  margin-left: 12px;
  display: flex;
  align-items: flex-end;
  height: 24px;

  &:after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 2px;
    border-radius: 2px;
    background: rgba(255, 255, 255, 0.1);
  }

  &.__fixed {
    position: absolute;
    bottom: 0;
    right: 0;
  }
`;

const actionBtn = css`
  width: 16px;
  height: 16px;
  vertical-align: middle;
  color: #e7e7e7;
  padding: 0;
  background: none;
  border: none;
`;

export const styles = {
  widgetWrapper,
  widgetItem,
  actions,
  actionBtn,
};
