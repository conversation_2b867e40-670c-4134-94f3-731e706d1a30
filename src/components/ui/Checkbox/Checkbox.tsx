import React, { <PERSON> } from 'react';
import cx from 'classnames';

type CheckboxProps = {
  colorClassName?: string;
  designMode?: 'default' | 'new';
  disabled?: boolean;
  isSmall?: boolean;
  oneLine?: boolean;
  partly?: boolean;
} & React.DetailedHTMLProps<
  React.InputHTMLAttributes<HTMLInputElement>,
  HTMLInputElement
>;

const Checkbox: FC<CheckboxProps> = ({
  children,
  colorClassName,
  designMode = 'default',
  isSmall,
  oneLine,
  partly,
  ...otherProps
}) => (
  <label
    data-legend={colorClassName}
    data-design-mode={designMode}
    className={cx('form-checkbox', {
      __small: isSmall,
      __disabled: otherProps.disabled,
      partly: partly,
    })}
  >
    <input className='form-checkbox__chk' type='checkbox' {...otherProps} />
    <span className='form-checkbox__el' />
    <span
      className={cx('form-checkbox__value', {
        __oneline: oneLine,
      })}
    >
      {children}
    </span>
  </label>
);

export default Checkbox;
