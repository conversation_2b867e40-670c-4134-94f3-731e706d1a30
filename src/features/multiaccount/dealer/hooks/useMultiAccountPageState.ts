import { useState } from 'react';

import { Modals } from 'features/multiaccount/dealer/hooks/state/types';
import {
  ResendManager,
  useResendInvite,
} from 'features/multiaccount/dealer/hooks/state/useResendInvite';
import {
  DeleteClientsManager,
  useDeleteClients,
} from 'features/multiaccount/dealer/hooks/state/useDeleteClients';
import {
  EditClientManager,
  useEditClient,
} from 'features/multiaccount/dealer/hooks/state/useEditClient';
import {
  InviteClientManager,
  useInviteClient,
} from 'features/multiaccount/dealer/hooks/state/useInviteClient';
import {
  MoveClientsManager,
  useMoveClients,
} from 'features/multiaccount/dealer/hooks/state/useMoveClients';
import {
  DeleteSingleClientManager,
  useDeleteSingleClient,
} from 'features/multiaccount/dealer/hooks/state/useDeleteSingleClient';
import {
  FilterManager,
  useFilter,
} from 'features/multiaccount/dealer/hooks/state/useFilter';
import {
  EditConsultantManager,
  useEditConsultant,
} from 'features/multiaccount/dealer/hooks/state/useEditConsultant';
import {
  DeleteConsultantManager,
  useDeleteConsultant,
} from 'features/multiaccount/dealer/hooks/state/useDeleteConsultant';
import {
  SubscriptionClientManager,
  useSubscriptionClient,
} from 'features/multiaccount/dealer/hooks/state/useSubscriptionClient';

export type MultiAccountDealerPageStateType = {
  modal: Modals;
} & InviteClientManager &
  MoveClientsManager &
  DeleteClientsManager &
  DeleteSingleClientManager &
  EditClientManager &
  ResendManager &
  DeleteConsultantManager &
  EditConsultantManager &
  FilterManager &
  SubscriptionClientManager;

export const useMultiAccountPageState = (): MultiAccountDealerPageStateType => {
  const [modal, setVisibleModal] = useState<Modals>(null);

  return {
    modal,
    ...useResendInvite(),
    ...useDeleteClients(setVisibleModal),
    ...useEditClient(setVisibleModal),
    ...useMoveClients(setVisibleModal),
    ...useInviteClient(setVisibleModal),
    ...useDeleteSingleClient(setVisibleModal),
    ...useDeleteConsultant(setVisibleModal),
    ...useEditConsultant(setVisibleModal),
    ...useFilter(),
    ...useSubscriptionClient(setVisibleModal),
  };
};
