import React from 'react';

const SmallLogo = ({ hideText }) => (
  <svg
    className='b-small-logo'
    width={80}
    height={28}
    viewBox={hideText ? '20 0 80 20' : '0 0 80 28'}
  >
    <g transform='translate(-33849 -6028)'>
      {!hideText && (
        <use
          className='text'
          xlinkHref='#small_logo_text'
          transform='translate(33849 6039)'
          fill='#20BF55'
        />
      )}
      <use
        className='leaf'
        xlinkHref='#small_logo_leaf'
        transform='translate(33914 6028.82)'
        fill='#20BF55'
      />
    </g>
    <defs>
      <path
        id='small_logo_text'
        fillRule='evenodd'
        d='M6.911 14.562c-1.962 0-3.617-.662-4.931-2.005S0 9.558 0 7.59C0 5.62.666 3.947 1.998 2.567c1.35-1.38 2.987-2.06 4.931-2.06s3.582.68 4.914 2.06c1.35 1.38 2.016 3.036 2.016 4.986 0 1.95-.666 3.606-2.016 4.967-1.332 1.361-2.97 2.042-4.932 2.042zm4.181-7.014c0-1.251-.396-2.282-1.205-3.11-.81-.827-1.782-1.25-2.952-1.25s-2.142.423-2.952 1.25c-.81.828-1.206 1.877-1.206 3.11 0 1.232.396 2.262 1.188 3.09.81.828 1.8 1.233 2.97 1.233 1.17 0 2.16-.405 2.97-1.233.81-.828 1.206-1.858 1.188-3.09zm6.274 6.799V8.975c0-1.49.9-2.539 2.25-2.539 1.242 0 2.07.884 2.07 2.484v5.427h2.537V8.221c0-1.343-.378-2.373-1.116-3.09-.738-.718-1.656-1.086-2.717-1.086-1.386 0-2.556.699-3.078 1.674V4.321h-2.484v10.026h2.538zm17.706-5.261c0 .35-.036.735-.09 1.14H27.62c.36 1.417 1.314 2.134 2.861 2.134 1.062 0 2.322-.423 3.168-1.012l1.008 1.822c-1.242.92-2.664 1.38-4.284 1.38-1.727 0-3.041-.516-3.96-1.527-.917-1.012-1.367-2.263-1.367-3.717 0-1.527.468-2.778 1.422-3.77.954-.994 2.177-1.491 3.69-1.491 1.421 0 2.61.46 3.527 1.361.918.902 1.386 2.116 1.386 3.68zm-4.894-2.87c-1.277 0-2.231.79-2.537 2.097h4.985c-.234-1.307-1.152-2.098-2.448-2.098zm9.858 8.328c-2.07 0-3.942-.902-5.04-2.54l1.53-1.894c.864 1.232 2.16 1.895 3.528 1.895 1.097 0 1.907-.589 1.907-1.454 0-.552-.396-1.03-1.206-1.416-.18-.092-.611-.258-1.313-.534a13.755 13.755 0 0 1-1.566-.736c-1.404-.827-2.106-1.95-2.106-3.384 0-1.141.414-2.08 1.26-2.834.846-.754 1.944-1.14 3.276-1.14 1.727 0 3.185.607 4.373 1.84l-1.422 1.986c-.738-.81-1.926-1.269-2.915-1.269-1.062 0-1.71.515-1.71 1.306 0 .534.36.975 1.08 1.343l1.313.589c.738.313 1.278.552 1.62.717 1.512.755 2.268 1.877 2.268 3.385 0 1.214-.45 2.208-1.35 2.98-.9.773-2.07 1.16-3.527 1.16zm5.308-5.22c0-1.471.486-2.722 1.476-3.734.99-1.03 2.25-1.545 3.78-1.545 1.529 0 2.789.515 3.779 1.545.99 1.012 1.476 2.263 1.476 3.735 0 1.49-.486 2.74-1.476 3.753-.99 1.011-2.25 1.527-3.78 1.527-1.53 0-2.79-.516-3.78-1.527-.99-1.012-1.475-2.263-1.475-3.753zm3.317 2.025c.522.551 1.17.827 1.943.827.774 0 1.404-.276 1.926-.827.522-.57.792-1.251.792-2.042 0-.792-.27-1.472-.792-2.024a2.528 2.528 0 0 0-1.926-.828c-.773 0-1.421.276-1.943.828-.522.552-.774 1.232-.774 2.024 0 .79.252 1.471.774 2.042zm10.567-8.63c.594-.608.594-1.675 0-2.264-.594-.607-1.638-.607-2.232 0-.594.589-.594 1.656 0 2.263s1.638.607 2.232 0zm.154 1.594h-2.538v10.026h2.538V4.312zM63.33.346h-2.538V14.33h2.538V.347z'
      />
      <path
        id='small_logo_leaf'
        fillRule='evenodd'
        d='M2.401.755S0 4.245 0 9.345c0 2.804.83 4.607 1.951 5.637A4.576 4.576 0 0 0 4.99 16.21c1.435 0 2.434-.758 3.264-1.113l-.9-2.262c-1.08.46-1.937.92-2.364.92-.249 0-.886-.081-1.426-.575-.54-.494-1.163-1.496-1.163-3.835 0-3.6 1.191-5.762 1.651-6.52 1.482.547 3.212.864 4.802 1.573.938.417 1.707.973 2.251 1.65.544.675.9 1.49.9 2.76 0 1.213-.454 1.803-.9 2.225-.075.071-.117.095-.187.153-.652-1.524-1.703-3.01-3.34-4.334L6.116 8.808c3.677 2.972 3.49 6.49 3.49 9.856h2.4c0-1.448-.009-3.212-.374-5.024a5.287 5.287 0 0 0 1.125-.805c.83-.777 1.651-2.129 1.651-4.027 0-1.764-.549-3.245-1.426-4.333-.877-1.088-2.04-1.788-3.189-2.301C7.49 1.148 3.7.36 2.982 0l-.58.755z'
      />
    </defs>
  </svg>
);

export default SmallLogo;
