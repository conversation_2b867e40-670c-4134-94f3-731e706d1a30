import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useForm, useFieldArray } from 'react-hook-form';
import { Trans } from 'react-i18next';
import cx from 'classnames';

import Icon from 'components/Icon';
import Button from 'components/ui/Button/Button';
import Saga from 'components/saga/Saga';
import SubmitButton from 'components/forms/SubmitButton';
import MapNavigationLock from 'features/scouting/MapNavigationLock';
import EditableFieldListItem from './EditableFieldListItem';
import ImportFieldsButton from './ImportFieldsButton';
import EnterExitTransition from 'components/EnterExitTransition';
import ProposeCulturePopup from './ProposeCulturePopup';

import api from 'modules/api';
import fields from 'modules/fields';
import seasons from 'modules/seasons';
import toasts from 'modules/toasts';

import addFieldsSaga from 'sagas/local/add-fields';
import { parseQuery } from 'utils/query';
import { useTranslate } from 'utils/use-translate';

const AddFieldsSideBar = ({ match, history, location }) => {
  const { t } = useTranslate();
  const currentSeason = useSelector(seasons.selectors.getCurrent);
  const fieldData = useSelector(state => fields.selectors.getData(state));
  const nextIndexNumber = useSelector(fields.selectors.getNextIndexNumber);
  const fieldIDs = useSelector(state => fields.selectors.getSelectedIDs(state));
  const dispatch = useDispatch();

  const submitRequest = useSelector(state =>
    api.selectors.getRequestStatus(state, 'createFields'),
  );

  const { control, handleSubmit, register, setValue, getValues } = useForm({
    defaultValues: { selectedFields: [] },
    shouldUnregister: false,
  });

  const selectedFields = useFieldArray({
    control: control,
    keyName: 'field_id',
    name: 'selectedFields',
  });

  const [validGeometry, setValidGeometry] = useState(null);
  const [showPopup, setShowPopup] = useState(false);

  const query = parseQuery(location);

  const cancel = () => {
    dispatch(toasts.actions.removeGroup('field-editor'));
    if (query.next) {
      // TODO: Validate app domain? Are there any security considerations?
      window.location = decodeURIComponent(query.next);
      return;
    }
    history.goBack();
  };

  const onSubmit = form => {
    const { selectedFields } = form;
    const fieldIDs = selectedFields.map(field => field.id);

    dispatch(
      fields.actions.createBatch(
        selectedFields.map(field => {
          const fieldToCreate = fieldData[field.id];
          const crops = field.crops[0]?.crop
            ? [
                {
                  ...field.crops[0],
                  season_id: currentSeason.id,
                  source: 'when_add_field',
                },
              ]
            : [];
          return {
            source: fieldToCreate.source,
            geometry: fieldToCreate.geometry,
            predicted_field_id: fieldToCreate.originalId,
            title: field.title,
            seasons: [{ season_id: currentSeason.id }],
            crops,
          };
        }),
        fieldIDs,
        currentSeason.id,
      ),
    );
  };

  useEffect(() => {
    const addedItems = fieldIDs.filter(
      item => !selectedFields.fields.find(el => el.id === item),
    );

    const deletedItems = selectedFields.fields.filter(
      item => !fieldIDs.find(el => el === item.id),
    );

    if (addedItems.length) {
      selectedFields.append({
        id: addedItems[0],
        title: t('fields.default_title', {
          index: fieldIDs.length + nextIndexNumber - 1,
        }),
        titleChanged: false,
        crops: [
          {
            crop: null,
            variety: null,
          },
        ],
      });
    }

    if (deletedItems.length) {
      const item = deletedItems[0];
      selectedFields.fields.find((field, index) => {
        if (field.id === item.id) {
          selectedFields.remove(index);
        }
        return false;
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fieldIDs]);

  useEffect(() => {
    return () => {
      dispatch(fields.actions.resetSelectedIDs());
    };
  }, [dispatch]);

  return (
    <>
      <EnterExitTransition variant='popup'>
        {showPopup && (
          <ProposeCulturePopup
            onCancel={() => {
              setShowPopup(false);
            }}
          />
        )}
      </EnterExitTransition>
      <form className='soil-sidebar-create' onSubmit={handleSubmit(onSubmit)}>
        <Saga
          saga={addFieldsSaga}
          history={history}
          setValidGeometry={setValidGeometry}
        />
        {match.params.mode === 'create' && <MapNavigationLock />}
        <div className='soil-sidebar-header'>
          <Button className='app-nav-back' onClick={cancel}>
            <Icon className='ico-arrow-long-left-os' name='arrow-left' />
          </Button>
          <div className='soil-sidebar-header__inner'>
            <h1 className='soil-sidebar-header__main'>
              {t(`fields.${match.params.mode}.title`)}
            </h1>
          </div>
        </div>
        <div
          className={cx('soil-sidebar-body', '__scroll', {
            '__empty-state': selectedFields.fields.length === 0,
          })}
        >
          {selectedFields.fields.length === 0 && (
            <div className='soil-sidebar-empty'>
              <h2 className='soil-sidebar-empty__title'>
                {t(`fields.${match.params.mode}.empty`)}
              </h2>
              <p>
                <Trans>{t(`fields.${match.params.mode}.description`)}</Trans>
              </p>
              {match.params.mode === 'upload' && (
                <ImportFieldsButton>
                  <span className='btn btn-success'>
                    {t('fields.upload.select_file')}
                  </span>
                </ImportFieldsButton>
              )}
            </div>
          )}
          {selectedFields.fields.length !== 0 && (
            <ul className='soil-fields-list'>
              {selectedFields.fields.map((item, index) => (
                <EditableFieldListItem
                  key={item.field_id}
                  index={index}
                  register={register}
                  fieldId={item.id}
                  history={history}
                  nextIndex={nextIndexNumber}
                  control={control}
                  readOnly
                  setShowPopup={setShowPopup}
                  setValue={setValue}
                  getValues={getValues}
                />
              ))}
            </ul>
          )}
        </div>

        <div className='soil-sidebar-addnew'>
          <ul className='soil-sidebar-addnew__list'>
            <li className='soil-sidebar-addnew__list-item'>
              <Button className='btn btn-lg btn-primary' onClick={cancel}>
                {t('forms.cancel')}
              </Button>
            </li>
            <li className='soil-sidebar-addnew__list-item'>
              <SubmitButton
                native
                render={props => (
                  <Button
                    className='btn size-full btn-lg btn-success'
                    type='submit'
                    {...props}
                    pending={submitRequest.status === 'pending'}
                    disabled={
                      selectedFields.fields.length === 0 || !validGeometry
                    }
                    id='fb-add-fields'
                  >
                    <span className='btn__ico'>
                      <Icon className='ico-check-os' name='check' />
                    </span>
                    {t('forms.save')}
                  </Button>
                )}
              />
            </li>
          </ul>
        </div>
      </form>
    </>
  );
};

export default AddFieldsSideBar;
