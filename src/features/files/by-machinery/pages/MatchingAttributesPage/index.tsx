import React, { VFC, useEffect, useRef, useState } from 'react';

import { MatchingAttributesHeader } from './components/MatchingAttributesHeader';
import { MatchingAttributesTable } from './components/MatchingAttributesTable';
import { DataLoader } from './components/DataLoader';
import { LoadingTable } from './components/LoadingTable';

// @ts-ignore
import useQuery from 'utils/use-query';

import { DataTypesKey } from '../../types';

import { FullPageCentralSection } from '../styles';

export const MatchingAttributesPage: VFC = () => {
  const ref = useRef<HTMLDivElement>(null);
  const [isTableTooWide, setIsTableTooWide] = useState(false);

  const [query] = useQuery();
  const dataType = query['data-type'] || DataTypesKey.machinery;

  useEffect(() => {
    const interval = setInterval(() => {
      if (!ref.current) {
        return;
      }
      const rect = ref.current?.getBoundingClientRect();

      setIsTableTooWide(window.innerWidth < rect.left + rect.width);
    }, 10);

    return () => clearInterval(interval);
  }, [ref]);

  return (
    <FullPageCentralSection ref={ref}>
      <MatchingAttributesHeader
        isTableTooWide={isTableTooWide}
        dataType={dataType}
      />

      <div>
        <DataLoader
          component={MatchingAttributesTable}
          loading={LoadingTable}
        />
      </div>
    </FullPageCentralSection>
  );
};
