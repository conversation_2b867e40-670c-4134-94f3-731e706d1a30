// import React, { Fragment } from 'react';
// import { getModel, actions as formActions } from 'react-redux-form';
// import { featureCollection, feature } from '@turf/helpers';
// import { GeoJSON, Pane } from 'react-leaflet';
// import { createSelector } from 'reselect';
// import { connect } from 'react-redux';

// import MapNavigationLock from 'features/scouting/MapNavigationLock';
// import FieldUploadOverlay from 'features/fields/FieldUploadOverlay';

// import fields from 'modules/fields';

// import ZIndex from 'constants/zindex';
// import forms from 'constants/forms';
// import randomID from 'utils/random-id';

// const SelectedStyle = {
//   color: '#FFE767',
//   fillOpacity: 0.25,
//   weight: 2,
// };

// const getLayer = createSelector(
//   state => getModel(state, `${forms.addFields}.fieldIDs`),
//   ({ fields }) => fields.data,
//   (ids, data) => ({
//     key: randomID(),
//     geojson: featureCollection(
//       ids.map(id => feature(data[id].geometry, { id })),
//     ),
//   }),
// );

// const UploadFieldsLayer = ({ history, layer }) => (
//   <Fragment>
//     <MapNavigationLock />
//     <FieldUploadOverlay history={history} />
//     <Pane
//       className="fields-pane-fields"
//       style={{ zIndex: ZIndex.SelectionPane }}
//     >
//       <GeoJSON key={layer.key} data={layer.geojson} style={SelectedStyle} />
//     </Pane>
//   </Fragment>
// );

// const mapStateToProps = state => ({
//   layer: getLayer(state),
// });

// const mapDispatchToProps = {
//   onAddField: fieldId =>
//     formActions.push(`${forms.addFields}.fieldIDs`, fieldId),
//   onRemoveField: index =>
//     formActions.remove(`${forms.addFields}.fieldIDs`, index),
//   onSetLocal: fields.actions.setLocal,
// };

// export default connect(
//   mapStateToProps,
//   mapDispatchToProps,
// )(UploadFieldsLayer);
