import React from 'react';
import { useSelector } from 'react-redux';
import { Trans } from 'react-i18next';

import Icon from 'components/Icon';
import Button from 'components/ui/Button/Button';
import RenderAboveEverything from 'components/RenderAboveEverything';

import auth from 'modules/auth';

import useQuery from 'utils/use-query';
import { useTranslate } from 'utils/use-translate';

const ChangeEmailSuccessPopup = ({ onClose }) => {
  const { t } = useTranslate();

  const email = useSelector(auth.selectors.getUserEmail);

  // eslint-disable-next-line no-unused-vars
  const [query, mergeQuery] = useQuery();

  return (
    <RenderAboveEverything>
      <div className='modal modal-editseasons'>
        <div className='modal-outer'>
          <div className='modal-editseasons-content'>
            <div className='modal-editseasons-content__header'>
              {t('change_email.success.title')}
            </div>
            <div className='modal-editseasons-content__body'>
              <div className='form-group'>
                <Trans>{t('change_email.success.text', { email })}</Trans>
              </div>
            </div>
            <div className='modal-editseasons-content__actions'>
              <Button
                className='btn btn-success btn-lg'
                onClick={() => {
                  onClose();
                }}
              >
                <span className='btn__ico'>
                  <Icon className='ico-check-os' name='check' />
                </span>
                {t(`change_email.success.ok`)}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </RenderAboveEverything>
  );
};

export default ChangeEmailSuccessPopup;
