import React from 'react';

const Logo = ({ className, width, height }) => (
  <svg className={className} width={width} height={height} viewBox='0 0 104 35'>
    <g
      transform='translate(-588.000000, -183.000000)'
      fill='#27AE60'
      fillRule='nonzero'
    >
      <g transform='translate(480.000000, 163.000000)'>
        <g transform='translate(108.000000, 20.000000)'>
          <path d='M85.27194,11.9246041 C85.27194,5.41620062 88.39493,0.963240087 88.39493,0.963240087 L89.15153,0 C89.51722,0.179439247 90.49222,0.443613376 91.72618,0.776323407 C93.64576,1.29345551 96.17972,1.97756406 98.00271,2.77383409 C99.49576,3.42865815 101.00896,4.3214968 102.14646,5.70965796 C103.28903,7.09844436 104,8.98878021 104,11.2392578 C104,13.661627 102.93361,15.3862854 101.85708,16.3776195 C101.33903,16.8548481 100.81097,17.1850801 100.38951,17.4049371 C100.86674,19.7170716 100.88208,21.9682127 100.88208,23.8156235 L97.75896,23.8156235 L97.75896,23.2193429 C97.7691,19.1077757 97.78444,14.8567403 93.22417,11.2392578 L95.12347,8.74392618 C97.25118,10.4317716 98.61722,12.3283472 99.46521,14.273475 L99.55153,14.2075051 C99.60236,14.171394 99.64799,14.1377073 99.70896,14.0778623 C100.28785,13.5395126 100.88208,12.7869205 100.88208,11.2392578 C100.88208,9.6186965 100.41486,8.57881017 99.70896,7.7165065 C99.00306,6.85357757 98.00271,6.14392326 96.78396,5.61183884 C95.56521,5.07725344 94.28042,4.71713721 93.03632,4.36884962 C92.17299,4.12710907 91.33007,3.89097021 90.53785,3.60560279 C89.94375,4.57196528 88.39493,7.3308445 88.39493,11.9246041 C88.39493,14.9096948 89.20236,16.1881316 89.90306,16.818099 C90.60896,17.4479387 91.43667,17.5520613 91.7566,17.5520613 C92.16285,17.5520613 92.85354,17.2447975 93.73715,16.8529341 C94.07736,16.7027474 94.44292,16.5401835 94.83396,16.3776195 L96.00201,19.2648528 C95.74292,19.3738242 95.46875,19.51342 95.17924,19.6616926 C94.26521,20.1276924 93.17347,20.6835234 91.7566,20.6835234 C90.90354,20.6835234 89.26333,20.4330422 87.8059,19.1177286 C86.35354,17.8025425 85.27194,15.5027854 85.27194,11.9246041 Z M2.574611,32.3868542 C4.280861,34.1002836 6.433986,34.9457695 8.983208,34.9457695 C11.5375,34.9457695 13.66521,34.0773154 15.39694,32.3401521 C17.14882,30.6031164 18.01722,28.490295 18.01722,26.0018156 C18.01722,23.5133362 17.14882,21.4006424 15.39694,19.6405108 C13.66521,17.8797413 11.5375,17.0111596 9.008597,17.0111596 C6.479694,17.0111596 4.351958,17.8797413 2.594917,19.6405108 C0.8632806,21.4006424 0,23.53707 0,26.0486452 C0,28.5607309 0.8632806,30.6735523 2.574611,32.3868542 Z M12.852736,22.0280577 C13.90389,23.0840856 14.42194,24.3987612 14.42194,25.9950527 C14.44222,27.5682484 13.92924,28.882286 12.878125,29.9389519 C11.821875,30.9949798 10.537111,31.5115096 9.013667,31.5115096 C7.495306,31.5115096 6.205472,30.9949798 5.154292,29.9389519 C4.123444,28.882286 3.610542,27.5682484 3.610542,25.9950527 C3.610542,24.4223674 4.123444,23.0840856 5.179694,22.0280577 C6.230861,20.9719022 7.495306,20.4317661 9.013667,20.4317661 C10.537111,20.4317661 11.801556,20.9719022 12.852736,22.0280577 Z M22.57736,27.8168157 L22.57736,34.671682 L19.27653,34.671682 L19.27653,21.877871 L22.50625,21.877871 L22.50625,23.6622468 C23.1816,22.4180071 24.70507,21.5258192 26.50778,21.5258192 C27.88903,21.5258192 29.08243,21.995647 30.03715,22.9109307 C30.99694,23.8262144 31.48951,25.1407624 31.48951,26.8548299 L31.48951,34.671682 L28.18868,34.671682 L28.18868,27.7463798 C28.18868,25.7039943 27.11722,24.5775305 25.50236,24.5775305 C23.74528,24.5775305 22.57736,25.9153019 22.57736,27.8168157 Z M45.47465,29.4131072 C45.54576,28.8965774 45.59139,28.4037814 45.59139,27.9576874 C45.59139,25.962642 44.98715,24.4130525 43.79375,23.2629825 C42.60035,22.1127849 41.0566,21.5258192 39.20826,21.5258192 C37.24292,21.5258192 35.64847,22.159487 34.40944,23.4274605 C33.17028,24.6946685 32.56097,26.29096 32.56097,28.2393034 C32.56097,30.094115 33.14493,31.690279 34.33833,32.9813483 C35.5316,34.2722901 37.24292,34.9295641 39.4875,34.9295641 C41.59493,34.9295641 43.43833,34.342726 45.05306,33.1687946 L43.74292,30.8449207 C42.64611,31.5962369 41.0059,32.1365005 39.62465,32.1365005 C37.61368,32.1365005 36.37465,31.2205788 35.90743,29.4131072 L45.47465,29.4131072 Z M35.93278,26.9713298 C36.32889,25.30473 37.57299,24.2952766 39.23361,24.2952766 C40.91451,24.2952766 42.10778,25.30473 42.4125,26.9713298 L35.93278,26.9713298 Z M45.49493,31.6828781 C46.92194,33.7719656 49.35424,34.9221632 52.04576,34.9221632 C53.93986,34.9221632 55.46333,34.4292396 56.63125,33.4430096 C57.80424,32.45729 58.38833,31.1894441 58.38833,29.6404926 C58.38833,27.7152451 57.40306,26.2834315 55.43785,25.3209354 C54.99611,25.1096277 54.29021,24.8050435 53.33549,24.4056516 L51.62417,23.6542079 C50.68986,23.1850182 50.22264,22.6217863 50.22264,21.9407784 C50.22264,20.9314526 51.06556,20.2741786 52.44174,20.2741786 C53.7316,20.2741786 55.27535,20.8610167 56.23514,21.8940764 L58.08361,19.3588949 C56.53986,17.7856992 54.64576,17.0111596 52.39611,17.0111596 C50.66451,17.0111596 49.2375,17.5040832 48.14056,18.466707 C47.03868,19.4293308 46.50035,20.6262304 46.50035,22.0816502 C46.50035,23.912728 47.41451,25.3445416 49.2375,26.4012075 C49.70472,26.6592172 50.38514,26.9875352 51.27382,27.340225 L51.57854,27.458001 C52.31486,27.7401273 52.77194,27.9171102 52.98007,28.0205948 C54.03632,28.5133908 54.54917,29.1239628 54.54917,29.8280664 C54.54917,30.9314344 53.49799,31.6828781 52.07111,31.6828781 C50.29375,31.6828781 48.60778,30.8373923 47.48549,29.2648346 L45.49493,31.6828781 Z M60.86639,23.4977688 C59.57653,24.7888382 58.94694,26.3850021 58.94694,28.2629096 C58.94694,30.1644233 59.57653,31.7607149 60.86639,33.0516566 C62.15118,34.342726 63.79139,35 65.77701,35 C67.76757,35 69.40778,34.342726 70.69257,33.0516566 C71.97736,31.7607149 72.61215,30.1644233 72.61215,28.2629096 C72.61215,26.3850021 71.97736,24.7888382 70.69257,23.4977688 C69.40778,22.1832208 67.76757,21.5258192 65.77701,21.5258192 C63.79139,21.5258192 62.15118,22.1832208 60.86639,23.4977688 Z M65.78715,31.9015866 C64.7816,31.9015866 63.93868,31.5495348 63.25826,30.8454311 C62.58278,30.1177213 62.25271,29.2492672 62.25271,28.2398138 C62.25271,27.2304879 62.58278,26.3620339 63.25826,25.6572922 C63.93868,24.9533162 64.7816,24.6012643 65.78715,24.6012643 C66.79257,24.6012643 67.61014,24.9533162 68.29056,25.6572922 C68.96604,26.3620339 69.31639,27.2304879 69.31639,28.2398138 C69.31639,29.2492672 68.96604,30.1177213 68.29056,30.8454311 C67.61014,31.5495348 66.79257,31.9015866 65.78715,31.9015866 Z M76.99458,16.9451898 C77.76639,17.6966335 77.76639,19.0578836 76.99458,19.8330611 C76.22264,20.6074731 74.86674,20.6074731 74.09493,19.8330611 C73.32299,19.0578836 73.32299,17.6966335 74.09493,16.9451898 C74.86674,16.1707779 76.22264,16.1707779 76.99458,16.9451898 Z M73.89694,21.8672801 L77.19764,21.8672801 L77.19764,34.6610911 L73.89694,34.6610911 L73.89694,21.8672801 Z M79.0309,16.8075081 L82.3316,16.8075081 L82.3316,34.6479482 L79.0309,34.6479482 L79.0309,16.8075081 Z' />
        </g>
      </g>
    </g>
  </svg>
);

Logo.defaultProps = {
  width: 104,
  height: 35,
};

export default Logo;
