import React, { useState, useRef, useEffect } from 'react';

import Icon from 'components/Icon';
import Button from 'components/ui/Button/Button';
import { PopoverDeprecated } from 'components/ui/Popover';
import {
  SimpleTooltip,
  SimpleTooltipArrow,
} from 'components/ui/Tooltip/SimpleTooltip/SimpleTooltip';

import { getViewportHash } from 'utils/get-root-scouting-url';
import { useTranslate } from 'utils/use-translate';
import {
  FEATURE_PERMISSIONS,
  FEATURES,
  useFeaturePermission,
} from 'features/permissions';
import {
  tooltipActions,
  tooltipActionsItem,
} from './PredictedCropTooltip.style';

const CloseDelay = 50;

const PredictedCropTooltip = ({ align, field, history, v2 }) => {
  const [hovered, setHovered] = useState(false);
  const [arrow, setArrow] = useState(null);
  const timer = useRef(null);
  const { t } = useTranslate();

  useEffect(() => {
    const arrow =
      align === 'bottom-middle'
        ? SimpleTooltipArrow.TOP
        : !align
        ? SimpleTooltipArrow.LEFT
        : null;
    setArrow(arrow);
  }, [align]);

  const onMouseEnter = () => {
    clearTimeout(timer.current);
    setHovered(true);
  };
  const onMouseLeave = () => {
    clearTimeout(timer.current);
    timer.current = setTimeout(setHovered, CloseDelay, false);
  };

  const isAvailableAddCrop =
    useFeaturePermission(FEATURES.CROP_ADD) === FEATURE_PERMISSIONS.ALLOWED;
  if (!isAvailableAddCrop) {
    return null;
  }

  return (
    <PopoverDeprecated
      active={hovered}
      offset={align === 'bottom-middle' ? [0, 10] : [10, 0]}
      align={align || 'middle-right'}
      transitionVariant='legacy'
      renderTrigger={({ ref, ...otherProps }) => (
        <span
          className='soil-fields-list-meta__quest'
          onMouseEnter={onMouseEnter}
          onMouseLeave={onMouseLeave}
          {...otherProps}
        >
          <Icon innerRef={ref} name='question-circle' />
        </span>
      )}
      renderPopover={({ style, ...props }) => (
        <SimpleTooltip
          arrow={arrow}
          maxWidth={290}
          style={style}
          {...props}
          onMouseEnter={onMouseEnter}
          onMouseLeave={onMouseLeave}
          onClick={event => {
            // Don't let clicks propagate to FieldListItem
            event.stopPropagation();
          }}
        >
          <p>
            {t('fields.suggest_predicted_crop', {
              crop: t(`crop.${field.predicted_crop}`).toLowerCase(),
            })}
          </p>
          <div className={tooltipActions}>
            <div className={tooltipActionsItem}>
              <Button
                className='btn btn-sm btn-primary size-full'
                onClick={() => {
                  const viewportHash = getViewportHash(
                    history.location.pathname,
                  );
                  const id = v2 ? `o${field.id}` : field.id;
                  history.push(`/${viewportHash}/fields/${id}/edit`);
                }}
              >
                <Icon className='ico-pencil-os' name='pencil' />{' '}
                {t('fields.refine_crop.edit')}
              </Button>
            </div>
          </div>
        </SimpleTooltip>
      )}
    />
  );
};

export default PredictedCropTooltip;
