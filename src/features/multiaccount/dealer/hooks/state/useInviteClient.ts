import { Modals } from './types';

export type InviteClientManager = {
  showInviteModal: () => void;
  closeInviteModal: () => void;
};

export const useInviteClient = (
  setVisibleModal: (type: Modals) => void,
): InviteClientManager => {
  const showInviteModal = () => {
    setVisibleModal('invite-client');
  };
  const closeInviteModal = () => {
    setVisibleModal(null);
  };

  return {
    showInviteModal,
    closeInviteModal,
  };
};
