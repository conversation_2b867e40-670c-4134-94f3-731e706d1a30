import React from 'react';
import { Link, LinkProps } from 'react-router-dom';

import ModernSpinner, {
  SpinnerTheme,
} from 'components/ui/ModernSpinner/ModernSpinner';
import { CommonButtonProps } from './types';

export type LinkButtonProps = React.PropsWithoutRef<
  LinkProps<any> & CommonButtonProps // eslint-disable-line @typescript-eslint/no-explicit-any
> &
  React.RefAttributes<HTMLAnchorElement>;

const LinkButton: React.ForwardRefExoticComponent<LinkButtonProps> = React.forwardRef(
  ({ dataTestId, pending, disabled, children, ...otherProps }, ref) => {
    return (
      <Link
        ref={ref}
        data-testid={dataTestId}
        // @ts-ignore
        disabled={disabled || pending}
        {...otherProps}
      >
        {pending ? (
          <ModernSpinner theme={disabled ? SpinnerTheme.White : undefined} />
        ) : (
          children
        )}
      </Link>
    );
  },
);

export default LinkButton;
