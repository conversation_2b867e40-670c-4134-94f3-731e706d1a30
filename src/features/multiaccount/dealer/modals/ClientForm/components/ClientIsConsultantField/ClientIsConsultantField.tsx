import React from 'react';
import { Controller } from 'react-hook-form';

import Checkbox from 'components/ui/Checkbox/Checkbox';

import { useTranslate } from 'utils/use-translate';

export type ClientIsConsultantFieldProps = {
  control: TEMPORARY_ANY;
};

const ClientIsConsultantField = ({ control }: ClientIsConsultantFieldProps) => {
  const { t } = useTranslate();

  return (
    <div className='form-group'>
      <Controller
        name='is_consultant'
        control={control}
        render={({ onChange, value }) => {
          return (
            <Checkbox
              checked={value}
              onChange={event => {
                const checked = event?.target?.checked;
                onChange(checked);
              }}
            >
              {t(`multiaccount.invite-client-modal.column.vendor`)}
            </Checkbox>
          );
        }}
      />
    </div>
  );
};

export default ClientIsConsultantField;
