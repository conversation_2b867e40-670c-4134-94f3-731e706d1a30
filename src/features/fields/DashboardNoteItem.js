import React, { memo } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Link } from 'react-router-dom';
import LinesEllipsis from 'react-lines-ellipsis';

import LinkButton from 'components/LinkButton';
import BackgroundImage from 'components/ui/BackgroundImage/BackgroundImage';
import { viewNote } from 'utils/links';
import { formatDate } from 'utils/format-date';

import entities from 'modules/entities';
import notes from 'modules/notes';

const DashboardNoteItem = ({ noteId, t }) => {
  const note = useSelector(state =>
    entities.selectors.getByID(state, 'note', noteId),
  );
  const noteImages = useSelector(state =>
    entities.selectors.getIndexed(state, 'noteImage', 'note_id', noteId),
  );

  const dispatch = useDispatch();
  return (
    <div key={note.id} className='map-dashboard-notes__item'>
      <div className='soil-old-notes-list-header'>
        {formatDate(note.created_at, t('notes.date_format'))}{' '}
        <span className='soil-old-notes-list__dot'>•</span>{' '}
        {t(`notes.types.${note.type}`).toUpperCase()}
      </div>
      <div className='soil-old-notes-list-body'>
        <LinesEllipsis text={note.text} basedOn='letters' maxLine={3} />
        {noteImages.length > 0 && (
          <div className='soil-sidebar-gallery'>
            <div className='soil-sidebar-gallery__area'>
              {noteImages.slice(0, 2).map((image, index) => (
                <div key={image.id} className='soil-sidebar-gallery__item'>
                  <div className='soil-sidebar-gallery__action'>
                    {index === 1 && noteImages.length > 2 && (
                      <LinkButton
                        className='soil-sidebar-gallery-seeall'
                        onClick={() => {
                          dispatch(notes.actions.openImage(noteImages, index));
                        }}
                      >
                        <div className='soil-sidebar-gallery-seeall__inner'>
                          {t('notes.images.andmore', {
                            count: noteImages.length - 2,
                          })}
                        </div>
                      </LinkButton>
                    )}
                    <BackgroundImage
                      className='soil-sidebar-gallery__pic'
                      src={`${image.url}?width=300`}
                      onClick={event => {
                        dispatch(notes.actions.openImage(noteImages, index));
                      }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
      <Link className='map-dashboard-notes__see' to={viewNote(note)}>
        {t('fields.dashboard.notes.link')}
      </Link>
    </div>
  );
};

export default memo(DashboardNoteItem);
