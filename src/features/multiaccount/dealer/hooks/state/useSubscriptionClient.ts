import { useState } from 'react';

import type { SubscriptionClientStatusType } from 'modules/vendor/types/client-relationships';

import { Modals } from './types';

export type SubscriptionClientManager = {
  subscriptionClientStatus: Nullable<SubscriptionClientStatusType>;
  subscriptionClientId: Nullable<string>;
  showSubscriptionClientModal: (
    uuid: string,
    status: SubscriptionClientStatusType,
  ) => void;
  closeSubscriptionClientModal: () => void;
};

export const useSubscriptionClient = (
  setVisibleModal: (type: Modals) => void,
): SubscriptionClientManager => {
  const [subscriptionClientStatus, setSubscriptionClientStatus] =
    useState<Nullable<SubscriptionClientStatusType>>(null);

  const [subscriptionClientId, setSubscriptionClientId] =
    useState<Nullable<string>>(null);

  const showSubscriptionClientModal = (
    uuid: string,
    status: SubscriptionClientStatusType,
  ) => {
    setVisibleModal('subscription-client');
    setSubscriptionClientId(uuid);
    setSubscriptionClientStatus(status);
  };

  const closeSubscriptionClientModal = () => {
    setSubscriptionClientId(null);
    setSubscriptionClientStatus(null);
    setVisibleModal(null);
  };

  return {
    subscriptionClientStatus,
    subscriptionClientId,
    showSubscriptionClientModal,
    closeSubscriptionClientModal,
  };
};
