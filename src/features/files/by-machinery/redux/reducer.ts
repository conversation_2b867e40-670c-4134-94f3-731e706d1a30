import { InitialState } from './state';

import { createReducer } from 'utils/redux';

import { actions, actionsConfig } from './actions';
import { TasksByMachineryState } from '../types/store';
import { FileImportError, StoreStatus } from '../types/core';
import {
  ImportTaskType,
  TaskFailedType,
  TaskStatus,
  TaskUploadingType,
} from '../types/tasks';
import { ApiDataSourceLayer, ApiProcessedTask } from '../types/server';
import { filters } from './filters';

export const reducer = createReducer<
  typeof actionsConfig,
  TasksByMachineryState
>(InitialState, {
  [actions.requestMeta.resolved]: (state, data) => {
    const { ...newState } = state;
    // @ts-ignore
    newState.meta = data.meta.response.data;
    return newState;
  },
  [actions.requestLegend.resolved]: (state, data) => {
    const { ...newState } = state;
    const { ...newLegends } = newState.legends;
    newLegends[data.meta.requestAction.payload.operation.uuid] =
      // @ts-ignore
      data.meta.response.data;
    newState.legends = newLegends;
    return newState;
  },
  [actions.updateTasksStatus.key]: state => {
    const { ...newState } = state;
    newState.status =
      state.status === StoreStatus.init
        ? StoreStatus.updating
        : StoreStatus.reUpdating;

    return newState;
  },
  [actions.updateTasksStatus.resolved]: (state, data) => {
    const payload: ApiProcessedTask[] = data.payload;
    const { ...newState } = state;
    const [...tasks] = newState.tasks;

    for (let i = 0; i < payload.length; i++) {
      const serverTask = payload[i] as ImportTaskType;
      const taskIndex = tasks.findIndex(task => serverTask.uuid === task.uuid);
      if (taskIndex > -1) {
        tasks[taskIndex] = {
          ...serverTask,
          disabled: tasks[taskIndex]?.disabled,
        };
      } else {
        if (
          !state.preventRestoreFromServer.includes(serverTask.uuid as string)
        ) {
          tasks.push(serverTask);
        }
      }
    }

    newState.tasks = tasks;
    newState.status = StoreStatus.finished;

    return newState;
  },
  [actions.finished.resolved]: (state, data) => {
    const uuids = data.meta.requestAction.payload.operations.map(
      v => v.data_source.layer.source_uuid,
    );

    const newTasks: ImportTaskType[] = state.tasks.map(v => {
      if (v.sources.some(v => uuids.includes(v.uuid))) {
        return {
          ...v,
          status: TaskStatus.processing,
        } as ImportTaskType;
      }

      return v;
    });

    return {
      ...state,
      tasks: newTasks,
    };
  },

  [actions.markTasksAsShowed.resolved]: (state, { meta }) => {
    const { ...newState } = state;
    const [...newTasks] = newState.tasks;

    meta.requestAction.payload.tasks.forEach(({ uuid }) => {
      const taskIndex = newTasks.findIndex(task => task.uuid === uuid);
      const task = newTasks[taskIndex] as TaskUploadingType;
      if (task) {
        newTasks[taskIndex] = {
          ...task,
          viewed_at: true,
        };
      }
    });

    newState.tasks = newTasks;

    return newState;
  },
  [actions.markTasksAsShowed.key]: (state, { payload: { tasks } }) => {
    const { ...newState } = state;
    const [...newTasks] = newState.tasks;
    tasks.forEach(task => {
      if (!filters.isServerTask(task)) {
        const taskIndex = newTasks.findIndex(
          item => item.temporaryUuid === task.temporaryUuid,
        );
        const oldTask = tasks[taskIndex] as TaskFailedType;
        if (oldTask) {
          newTasks[taskIndex] = {
            ...oldTask,
            viewed_at: true,
          };
        }
        newState.tasks = newTasks;
      }
    });
    return newState;
  },
  [actions.removeTask.key]: (state, { payload: { task } }) => {
    const { ...newState } = state;

    if (task.uuid) {
      const [...tasks] = newState.tasks;
      const taskIndex = tasks.findIndex(item => task.uuid === item.uuid);

      if (taskIndex > -1) {
        tasks[taskIndex] = {
          ...task,
          disabled: true,
        };
      }

      newState.tasks = tasks;
      return newState;
    }
    newState.tasks = state.tasks.filter(
      item => item.temporaryUuid !== task.temporaryUuid,
    );

    return newState;
  },
  [actions.removeTask.resolved]: (state, data) => {
    const uuid = data.meta.requestAction.payload.task.uuid;
    const { ...newState } = state;
    newState.tasks = state.tasks.filter(item => item.uuid !== uuid);
    if (uuid) {
      newState.preventRestoreFromServer = [
        ...state.preventRestoreFromServer,
        uuid,
      ];
    }

    return newState;
  },
  [actions.createTaskWithError.key]: (state, { payload }) => {
    const { ...newState } = state;
    const [...tasks] = newState.tasks;

    const taskImport: TaskFailedType = {
      temporaryUuid: payload.temporaryUuid,
      name: payload.file.name,
      size: payload.file.size,
      status: TaskStatus.error,
      viewed_at: false,
      sources: [],
      err_code: FileImportError.too_large,
    };
    tasks.push(taskImport);

    newState.tasks = tasks;
    return newState;
  },
  [actions.createTask.resolved]: (state, data) => {
    const payload: ApiProcessedTask = data.payload;

    const temporaryUuid = data.meta.requestAction.payload.temporaryUuid;
    const { ...newState } = state;
    const [...tasks] = newState.tasks;
    const taskIndex = tasks.findIndex(
      task => task.temporaryUuid === temporaryUuid,
    );
    const task = tasks[taskIndex] as TaskUploadingType;

    if (task && task.status === TaskStatus.uploading) {
      tasks[taskIndex] = {
        ...task,
        status: TaskStatus.processing,
        uuid: payload.uuid,
      };
    }
    newState.tasks = tasks;
    return newState;
  },
  [actions.createTask.uploadProgress]: (state, { payload, meta }) => {
    const temporaryUuid = meta.requestAction.payload.temporaryUuid;
    const { ...newState } = state;
    const [...tasks] = newState.tasks;
    const taskIndex = tasks.findIndex(
      task => task.temporaryUuid === temporaryUuid,
    );
    const task = tasks[taskIndex] as TaskUploadingType;
    if (task && task.status === TaskStatus.uploading) {
      tasks[taskIndex] = {
        ...task,
        progress: payload,
      };
    }

    newState.tasks = tasks;
    return newState;
  },
  [actions.createTask.rejected]: (state, { payload, meta }) => {
    const temporaryUuid = meta.requestAction.payload.temporaryUuid;
    const { ...newState } = state;
    const [...tasks] = newState.tasks;
    const taskIndex = tasks.findIndex(
      task => task.temporaryUuid === temporaryUuid,
    );

    const task = tasks[taskIndex];

    if (task && task.status === TaskStatus.uploading) {
      let error = payload.err_code || FileImportError.download;

      if (meta.response.status === 413) {
        error = FileImportError.too_large;
      }

      if (meta.response.status > 500 && meta.response.status < 600) {
        error = FileImportError.server;
      }

      if (meta.response.status === 400) {
        if (payload?.error_details?.[0]?.code) {
          error = payload?.error_details?.[0]?.code;
        } else {
          error = FileImportError.empty;
        }
      }
      if (error) {
        tasks[taskIndex] = {
          ...task,
          err_code: error,
          status: TaskStatus.error,
          uuid: payload.uuid,
        };
      } else {
        tasks[taskIndex] = {
          ...task,
          status: payload.status,
          uuid: payload.uuid,
        };
      }
    }

    newState.tasks = tasks;
    return newState;
  },
  [actions.createTask.key]: (state, { payload, meta }) => {
    const { ...newState } = state;
    const [...tasks] = newState.tasks;

    const taskImport: TaskUploadingType = {
      // @ts-ignore
      requestId: meta.apiCall.requestId,
      temporaryUuid: payload.temporaryUuid,
      name: payload.file.name,
      size: payload.file.size,
      sources: [],
      viewed_at: false,
      status: TaskStatus.uploading,
      progress: 0,
    };
    tasks.unshift(taskImport);

    newState.tasks = tasks;
    return newState;
  },
  [actions.fetchLayerTasks.resolved]: (state, { payload, meta }) => {
    const { ...newState } = state;

    const tasks = newState.tasks.map(task => {
      const updatedSources = task.sources.map(source => {
        const updatedLayersInSource = (source.layers || []).map(layer => {
          const updatedLayer = payload.find(
            (updatedLayer: ApiDataSourceLayer) =>
              updatedLayer.uuid === layer.uuid,
          );
          return updatedLayer ? updatedLayer : layer;
        });

        return { ...source, layers: updatedLayersInSource };
      });

      return { ...task, sources: updatedSources };
    });
    newState.layerTasks = [...payload];
    newState.tasks = [...tasks];

    return newState;
  },
});
