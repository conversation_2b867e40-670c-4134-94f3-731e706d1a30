import { Action } from 'redux';
import { channel } from 'redux-saga';
import { select, takeEvery, call, fork, put } from 'redux-saga/effects';

import { actions } from 'features/fields/BulkUpload/redux/actions';
import { selectors } from 'features/fields/BulkUpload/redux/selectors';

import {
  BatchResponse,
  BulkUploadStep,
  FileBundle,
  FileBundleStatus,
  FieldsFromFilesResponse,
} from 'features/fields/BulkUpload/types';

import {
  extractFeaturesFromFilesBatchResponse,
  getFeaturesBBox,
} from 'features/fields/BulkUpload/saga/utils';

import { ServerErrorType } from 'features/fields/UploadFileStep/types';

import { uploadRawFile } from 'sagas/local/upload-files';

import { FieldsUsersFilesResponse } from 'types/api';

// @ts-ignore
import { apiCall } from 'utils/effects';

import { getMessageForSuccess } from './toasts/getMessageForSuccess';
import { getMessageForErrors } from './toasts/getMessageForErrors';

const MAX_SIMULTANEOUS_REQUESTS_NUMBER = 4;

let uploadChannel = channel();

function* uploadBundleSaga(bundle: FileBundle) {
  yield put(actions.setBundleStatus(bundle.name, FileBundleStatus.UPLOADING));

  try {
    const result: FieldsUsersFilesResponse = yield call(
      uploadRawFile,
      bundle.files,
    );

    yield put(actions.setBundleUploadingResult(bundle.name, result));
  } catch (error) {
    yield put(
      actions.setBundleUploadingError(
        bundle.name,
        // @ts-ignore
        error?.response as ServerErrorType,
      ),
    );
  } finally {
    yield put(actions.setBundleStatus(bundle.name, FileBundleStatus.UPLOADED));
    uploadChannel.put({});
  }
}

export function* bulkUploadSaga() {
  yield takeEvery(actions.importBundles.key, function* () {
    yield put(actions.setCurrentStep(BulkUploadStep.UPLOADING));

    const validBundles: FileBundle[] = yield select(
      selectors.getValidFileBundles,
    );

    yield put(actions.resetBundlesUploadStatus());

    let index = 0;
    uploadChannel = channel();

    yield takeEvery(uploadChannel, function* (action: Action<{}>) {
      // We need to select bundles on every action to get uploading results
      const validBundles: FileBundle[] = yield select(
        selectors.getValidFileBundles,
      );

      const validBundlesWithProps: FileBundle[] = yield select(
        selectors.getValidFileBundlesWithProps,
      );

      const isImportDone = yield select(selectors.isImportDone);

      if (isImportDone) {
        // We have to close the channel in order to terminate takeEvery
        // and avoid duplicate uploads if user starts uploading again
        uploadChannel.close();

        const successfullyUploadedBundles = yield select(
          selectors.getUploadedFileBundles,
        );

        if (successfullyUploadedBundles.length === 0) {
          const message = getMessageForErrors({
            titleKey: 'fields.bulk_upload.toasts.import.failed.title',
            linkKey: 'fields.bulk_upload.toasts.import.failed.btn_text',
            ttl: Infinity,
          });

          if (message) {
            yield put(message);
          }

          return;
        }

        try {
          const fileIds: number[] = yield select(selectors.getUploadedFilesIds);

          const fieldsFromFilesBatchResponse: BatchResponse<FieldsFromFilesResponse> =
            yield call(apiCall, actions.fetchFieldsFromFiles(fileIds));

          const features = extractFeaturesFromFilesBatchResponse(
            fieldsFromFilesBatchResponse,
          );

          const boundingBox = getFeaturesBBox(features);

          yield put(actions.setBbox(boundingBox));

          const message = getMessageForSuccess({
            titleKey: 'fields.bulk_upload.toasts.import.finished',
            linkKey: 'fields.bulk_upload.toasts.import.continue',
            ttl: Infinity,
          });

          if (message) {
            yield put(message);
          }

          const step =
            validBundlesWithProps.length === 1
              ? BulkUploadStep.COLUMN_MATCHING
              : BulkUploadStep.SAVING;

          yield put(actions.setCurrentStep(step));
        } catch (error) {
          const message = getMessageForErrors({
            titleKey: 'fields.bulk_upload.toasts.saving.fetch_failed.title',
            contentKey: 'fields.bulk_upload.toasts.saving.try_again',
            ttl: Infinity,
            showOnBulkUploadPage: true,
          });

          if (message) {
            yield put(message);
          }
        }

        return;
      }

      // If all requests are sent but not everything is resolved
      // just ignore actions on uploadChannel until allValidBundlesUploaded
      if (index >= validBundles.length) {
        return;
      }

      yield fork(uploadBundleSaga, validBundles[index++]!);
    });

    // Send valid bundles MAX_SIMULTANEOUS_REQUESTS_NUMBER at once
    while (
      index < validBundles.length &&
      index < MAX_SIMULTANEOUS_REQUESTS_NUMBER
    ) {
      yield uploadChannel.put({});
    }
  });
}
