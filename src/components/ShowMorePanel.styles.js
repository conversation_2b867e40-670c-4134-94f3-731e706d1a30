import { css } from 'linaria';

const showMoreTrigger = css`
  display: flex;
  align-items: center;
  transform: translate(0, 0);
  color: #ebebeb;
  background-color: transparent;
  border: none;
  padding: 5px 0 5px 10px;
  &:after {
    content: '';
    width: 5px;
    height: 5px;
    border-left: 1px solid currentColor;
    border-bottom: 1px solid currentColor;
    transform: rotate(-45deg);
    margin-left: 8px;
    margin-top: -2px;
    transition: transform 0.1s;
  }
  &:hover {
    color: #fff;
  }
`;

const showMorePanel = css`
  position: absolute;
  min-width: 100px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.25);
  background-color: #222;
  border-radius: 4px;
  font-size: 10px;
  padding: 10px 0;
  right: 0;
  bottom: 100%;
  transform: translate(0, 20px);
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.1s, visibility 0.1s, transform 0.1s;
  overflow-x: hidden;
  overflow-y: auto;
  max-height: 350px;
`;

const showMoreContainer = css`
  position: relative;
  &.__opened {
    .${showMoreTrigger} {
      &:after {
        transform: rotate(-225deg) translate(1px, -2px);
      }
    }
    .${showMorePanel} {
      opacity: 1;
      visibility: visible;
      transform: translate(0, -10px);
    }
  }
`;

const showMoreText = css``;

export const styles = {
  showMoreContainer,
  showMoreTrigger,
  showMoreText,
  showMorePanel,
};
