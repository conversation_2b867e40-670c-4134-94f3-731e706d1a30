import React from 'react';

import LinkButton, { LinkButtonProps } from './LinkButton';
import Button, { ButtonProps } from './Button';

export const ButtonDataTestId = 'button';

/**
 * SmartButton allow you use link too, not only onClick
 */

export type SmartButtonProps = LinkButtonProps | ButtonProps;

// @ts-ignore
const SmartButton: React.ForwardRefExoticComponent<SmartButtonProps> =
  React.forwardRef((props, ref) => {
    if ('to' in props && props.to) {
      // @ts-ignore
      return <LinkButton dataTestId={ButtonDataTestId} {...props} ref={ref} />;
    }
    // @ts-ignore
    return <Button dataTestId={ButtonDataTestId} {...props} ref={ref} />;
  });

export default SmartButton;
