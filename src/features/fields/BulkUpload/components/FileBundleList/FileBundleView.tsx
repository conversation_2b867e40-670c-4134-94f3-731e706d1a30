import React, { VFC } from 'react';

import Icon from 'components/Icon';

import Loader, { StyledLoaderSize } from 'components/progress/Loader';

import { useTranslate } from 'utils/use-translate';

import {
  FileBundle,
  FileBundleErrors,
  FileBundleStatus,
} from 'features/fields/BulkUpload/types';

import {
  BulkUploadItemRowStyle,
  BulkUploadItemStatusWrapper,
  BulkUploadItemDescriptionStyle,
  BulkUploadItemTitleStyle,
  BulkUploadItemErrorStyle,
  successButtonIconClass,
  failedButtonIconClass,
} from 'features/fields/BulkUpload/styles';
import { isBundleValid } from 'features/fields/BulkUpload/utils';
import { ServerErrorType } from 'features/fields/UploadFileStep/types';

interface FileBundleViewProps {
  bundle: FileBundle;
}

export const FileBundleView: VFC<FileBundleViewProps> = ({ bundle }) => {
  const { t } = useTranslate();

  function renderIcon() {
    switch (bundle.status) {
      case FileBundleStatus.PROCESSED:
        return isBundleValid(bundle) ? (
          <Icon name='check' className={successButtonIconClass} />
        ) : (
          <Icon name='alert-circle' className={failedButtonIconClass} />
        );
      case FileBundleStatus.WAITING_FOR_UPLOAD:
        return <Icon name='green-circle' className={successButtonIconClass} />;
      case FileBundleStatus.UPLOADING:
        return <Loader size={StyledLoaderSize.S} />;
      case FileBundleStatus.UPLOADED:
        return bundle.uploadingError ? (
          <Icon name='alert-circle' className={failedButtonIconClass} />
        ) : (
          <Icon name='check' className={successButtonIconClass} />
        );
      default:
        return <Icon name='check' className={successButtonIconClass} />;
    }
  }

  function composeErrorsText(errors: FileBundleErrors): string {
    return Array.from(new Set(Object.values(errors)))
      .map(error => t(`fields.bulk_upload_error.${error}`))
      .join(', ');
  }

  function composeProcessingErrorText() {
    const prefix = t('fields.bulk_upload.bundle_processing.error');
    const errors = composeErrorsText(bundle.errors);
    return (
      <BulkUploadItemErrorStyle>{`${prefix}: ${errors}`}</BulkUploadItemErrorStyle>
    );
  }

  function renderFileUploadError(error?: ServerErrorType | null) {
    if (!error) {
      return null;
    }

    if (typeof error !== 'object' || !error.error_code) {
      return t(`fields_import_files_error.internal_error.title`);
    }

    const title = t(`fields_import_files_error.${error.error_code}.title`);
    let description = [
      t(`fields_import_files_error.${error.error_code}.description`),
    ];

    if (error.error_code !== 'internal_error') {
      const errorDetails = error?.error_details || [];
      if (error?.error_details && error.error_details.length > 0) {
        description = errorDetails.map(detail => detail.message) as string[];
      }
    }

    return `${title}: ${description}`;
  }

  function renderDescription() {
    switch (bundle.status) {
      case FileBundleStatus.PROCESSED:
        return isBundleValid(bundle)
          ? t('fields.bulk_upload.bundle_processing.success')
          : composeProcessingErrorText();
      case FileBundleStatus.WAITING_FOR_UPLOAD:
        return t('fields.bulk_upload.uploading.waiting');
      case FileBundleStatus.UPLOADING:
        return t('fields.bulk_upload.uploading.importing');
      case FileBundleStatus.UPLOADED:
        return bundle.uploadingError
          ? renderFileUploadError(bundle.uploadingError)
          : t('fields.bulk_upload.uploading.ready');
      default:
        return t('fields.bulk_upload.bundle_processing.success');
    }
  }

  return (
    <BulkUploadItemRowStyle key={bundle.name}>
      <BulkUploadItemStatusWrapper>{renderIcon()}</BulkUploadItemStatusWrapper>
      <div>
        <BulkUploadItemTitleStyle>{bundle.name}</BulkUploadItemTitleStyle>
        <div>
          <BulkUploadItemDescriptionStyle>
            {renderDescription()}
          </BulkUploadItemDescriptionStyle>
        </div>
      </div>
    </BulkUploadItemRowStyle>
  );
};
