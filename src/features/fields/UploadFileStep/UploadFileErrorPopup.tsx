import React, { VFC } from 'react';

import { useTranslate } from 'utils/use-translate';
import { ServerErrorType } from 'features/fields/UploadFileStep/types';
import ErrorPopup from 'components/ErrorPopup';

type ErrorPopupProps = {
  error: ServerErrorType;
  onClose: () => void;
};

const UploadFileErrorPopup: VFC<ErrorPopupProps> = ({ error, onClose }) => {
  const { t } = useTranslate();

  const title = t(`fields_import_files_error.${error.error_code}.title`);
  let description = [
    t(`fields_import_files_error.${error.error_code}.description`),
  ];

  if (error.error_code !== 'internal_error') {
    const errorDetails = error?.error_details || [];
    if (error?.error_details && error.error_details.length > 0) {
      description = errorDetails.map(detail => detail.message) as string[];
    }
  }

  return (
    <ErrorPopup title={title} description={description} onClose={onClose} />
  );
};

export default UploadFileErrorPopup;
