import React, { useEffect, useState } from 'react';
import DragAndDropTable from './DragAndDropTable';
import { TableRow } from './TableRow/TableRow';

import { TableRowDragPreview } from 'components/ui/DragAndDropTable/TableRow/TableRowDragPreview/TableRowDragPreview';
import { LeftSideControlDrag } from 'components/ui/DragAndDropTable/TableRow/LeftSideControlDrag/LeftSideControlDrag';
import { LeftSideControlListToggle } from 'components/ui/DragAndDropTable/TableRow/LeftSideControlListToggle/LeftSideControlListToggle';

export default {
  title: 'ui/DragAndDropTable',
  component: DragAndDropTable,
  decorators: [
    (Story: React.FC) => (
      <div style={{ padding: 16 }}>
        <Story />
      </div>
    ),
  ],
};

export const Full = () => {
  const [tableData, setTableData] = useState<
    {
      id: string;
      index: number;
      text: string;
      area: number;
      children: TEMPORARY_ANY[];
    }[]
  >([
    {
      id: '1',
      index: 1,
      text: 'Write a cool JS library',
      area: 10,
      children: [],
    },
    {
      id: '2',
      index: 2,
      text: 'Make it generic enough',
      area: 20,
      children: [],
    },
    {
      id: '3',
      index: 3,
      text: 'Write README',
      area: 30,
      children: [],
    },
    {
      id: '4',
      index: 4,
      text: 'Create some examples',
      area: 40,
      children: [],
    },
    {
      id: '5',
      index: 5,
      text: 'Spam in Twitter and IRC to promote it (note that this element is taller than the others)',
      area: 50,
      children: [],
    },
    {
      id: '6',
      index: 6,
      text: '???',
      area: 60,
      children: [],
    },
    {
      id: '7',
      index: 7,
      text: 'PROFIT',
      area: 70,
      children: [],
    },
  ]);
  const [openedStatus, setOpenedStatus] = useState<{ [id: string]: boolean }>(
    {},
  );

  useEffect(() => {
    const newOpenedStatus = tableData.reduce(
      (result: { [id: string]: boolean }, item) => {
        if (item.children.length) {
          result[item.id] = openedStatus[item.id] ?? true;
        }
        return result;
      },
      {},
    );

    setOpenedStatus(newOpenedStatus);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tableData]);

  const onChildrenDrop = ({
    parentId,
    childId,
  }: {
    parentId: string | null;
    childId: string;
  }) => {
    const findItemById: TEMPORARY_ANY = (id: string) =>
      tableData
        .reduce((result: TEMPORARY_ANY[], item) => {
          result.push(item, ...item.children);
          return result;
        }, [])
        .find(item => item.id === id);

    const child = findItemById(childId, tableData);
    let movingFromParentIndex: number | null = null;

    const newTableData = tableData.reduce(
      (result: TEMPORARY_ANY[], item, index) => {
        if (item.id === childId) {
          return result;
        } else {
          if (item.children.find(child => child.id === childId)) {
            movingFromParentIndex = index;
          }
          result.push({
            ...item,
            children: [
              ...item.children.filter(child => child.id !== childId),
              ...(item.id === parentId ? [child] : []),
            ],
          });

          return result;
        }
      },
      [],
    );

    if (parentId === null && movingFromParentIndex !== null) {
      newTableData.splice(movingFromParentIndex + 1, 0, child);
    }

    setTableData(newTableData);
  };

  const onChangeOpenedStatus = (id: string, isOpened: boolean) => {
    setOpenedStatus({ ...openedStatus, [id]: !isOpened });
  };

  const columnsInfo = [
    { key: 'index', fixedInfo: { width: 75 } },
    { key: 'area' },
    { key: 'text', columnsGroupName: 'Some group' },
  ];

  return (
    <DragAndDropTable columnsInfo={columnsInfo}>
      {tableData.map(({ id, text, index, area, children }) => {
        return [
          <TableRow
            key={id}
            id={id}
            columnsInfo={columnsInfo}
            onChildrenDrop={onChildrenDrop}
            isParent={!!children?.length}
            leftSideControl={({ hovered }) =>
              children?.length ? (
                <LeftSideControlListToggle
                  isOpened={!!openedStatus[id]}
                  onClick={onChangeOpenedStatus.bind(null, id)}
                />
              ) : (
                <LeftSideControlDrag hovered={hovered} />
              )
            }
            dragPreview={<TableRowDragPreview>{text}</TableRowDragPreview>}
          >
            {() => [index, area, text]}
          </TableRow>,
          ...(children?.length && openedStatus[id]
            ? children.map((child, index, childrenArray) => {
                return (
                  <TableRow
                    key={child?.id}
                    id={child?.id}
                    columnsInfo={columnsInfo}
                    onChildrenDrop={onChildrenDrop}
                    isChild
                    parentId={id}
                    leftSideControl={({ hovered }) => (
                      <LeftSideControlDrag hovered={hovered} />
                    )}
                    dragPreview={
                      <TableRowDragPreview>{child.text}</TableRowDragPreview>
                    }
                    isLastInGroup={index === childrenArray.length - 1}
                  >
                    {() => [
                      <div style={{ paddingLeft: '2em' }}>{child.index}</div>,
                      child.area,
                      child.text,
                    ]}
                  </TableRow>
                );
              })
            : []),
        ];
      })}
    </DragAndDropTable>
  );
};

export const TableRowSimple = () => {
  const tableData = [{ index: 0, area: '100', text: 'text' }];
  const columnsInfo = [{ key: 'index' }, { key: 'area' }, { key: 'text' }];

  return (
    <DragAndDropTable columnsInfo={columnsInfo}>
      {tableData.map(item => {
        return (
          <TableRow
            id={item.index.toString()}
            columnsInfo={columnsInfo}
            dragPreview={<TableRowDragPreview>{item.text}</TableRowDragPreview>}
          >
            {() => [item.index, item.area, item.text]}
          </TableRow>
        );
      })}
    </DragAndDropTable>
  );
};

export const TableRowWithOneFixedColumn = () => {
  const tableData = [
    {
      index: 0,
      area: '100',
      text: 'text-text-text-text-text-text-text-text-text-text-text-text-text-text-text-text-text-text-text',
    },
  ];

  const columnsInfo = [
    { key: 'index', fixedInfo: { width: 75 } },
    { key: 'area' },
    { key: 'text' },
  ];

  return (
    <DragAndDropTable columnsInfo={columnsInfo}>
      {tableData.map(item => {
        return (
          <TableRow
            id={item.index.toString()}
            columnsInfo={columnsInfo}
            leftSideControl={({ hovered }) => (
              <LeftSideControlDrag hovered={hovered} />
            )}
            dragPreview={<TableRowDragPreview>{item.text}</TableRowDragPreview>}
          >
            {() => [item.index, item.area, item.text]}
          </TableRow>
        );
      })}
    </DragAndDropTable>
  );
};

export const TableRowWithTwoFixedColumns = () => {
  const tableData = [
    {
      index: 0,
      area: '100',
      text: 'text-text-text-text-text-text-text-text-text-text-text-text-text-text-text-text-text-text-text',
    },
  ];
  const columnsInfo = [
    { key: 'index', fixedInfo: { width: 75 } },
    { key: 'area', fixedInfo: { width: 75 } },
    { key: 'text' },
  ];

  return (
    <DragAndDropTable columnsInfo={columnsInfo}>
      {tableData.map(item => {
        return (
          <TableRow
            id={item.index.toString()}
            columnsInfo={columnsInfo}
            leftSideControl={({ hovered }) => (
              <LeftSideControlDrag hovered={hovered} />
            )}
            dragPreview={<TableRowDragPreview>{item.text}</TableRowDragPreview>}
          >
            {() => [item.index, item.area, item.text]}
          </TableRow>
        );
      })}
    </DragAndDropTable>
  );
};

export const TableRowWithGroup = () => {
  const tableData = [
    {
      index: 0,
      area: '100',
      text: 'text',
      group: 'group starts here',
    },
  ];

  const columnsInfo = [
    { key: 'index' },
    { key: 'area' },
    { key: 'text' },
    { key: 'group', columnsGroupName: 'Some group' },
  ];

  return (
    <DragAndDropTable columnsInfo={columnsInfo}>
      {tableData.map(item => {
        return (
          <TableRow
            id={item.index.toString()}
            columnsInfo={columnsInfo}
            leftSideControl={({ hovered }) => (
              <LeftSideControlDrag hovered={hovered} />
            )}
            dragPreview={<TableRowDragPreview>{item.text}</TableRowDragPreview>}
          >
            {() => [item.index, item.area, item.text, item.group]}
          </TableRow>
        );
      })}
    </DragAndDropTable>
  );
};

export const TableRowWithUsingHovered = () => {
  const tableData = [
    {
      index: 0,
      area: '100',
      text: 'text',
    },
  ];

  const columnsInfo = [{ key: 'index' }, { key: 'area' }, { key: 'text' }];

  return (
    <DragAndDropTable
      columnsInfo={[{ key: 'index' }, { key: 'area' }, { key: 'text' }]}
    >
      {tableData.map(item => {
        return (
          <TableRow
            id={item.index.toString()}
            columnsInfo={columnsInfo}
            leftSideControl={({ hovered }) => (
              <LeftSideControlDrag hovered={hovered} />
            )}
            dragPreview={<TableRowDragPreview>{item.text}</TableRowDragPreview>}
          >
            {({ hovered }) => [
              item.index,
              item.area,
              `${item.text} ${hovered ? '(hovered)' : ''}`,
            ]}
          </TableRow>
        );
      })}
    </DragAndDropTable>
  );
};

export const TableWithLeftSideControl = () => {
  const tableData = [
    {
      index: 0,
      area: '100',
      text: 'text',
    },
  ];

  const columnsInfo = [{ key: 'index' }, { key: 'area' }, { key: 'text' }];

  return (
    <DragAndDropTable columnsInfo={columnsInfo}>
      {tableData.map(item => {
        return (
          <TableRow
            id={item.index.toString()}
            columnsInfo={columnsInfo}
            leftSideControl={() => (
              <div
                style={{ width: '100%', height: '100%', background: 'red' }}
                // eslint-disable-next-line
                onClick={() => console.log('LeftSide control click')}
              >
                l
              </div>
            )}
            dragPreview={<TableRowDragPreview>{item.text}</TableRowDragPreview>}
          >
            {() => [item.index, item.area, item.text]}
          </TableRow>
        );
      })}
    </DragAndDropTable>
  );
};

export const TableRowHeader = () => {
  return (
    <DragAndDropTable
      columnsInfo={[{ key: 'index' }, { key: 'area' }, { key: 'text' }]}
    />
  );
};

export const TableRowHeaderWithLabels = () => {
  return (
    <DragAndDropTable
      columnsInfo={[
        { key: 'index', headerLabel: 'Index' },
        { key: 'area', headerLabel: 'Field area' },
        { key: 'text', headerLabel: 'Additional text' },
      ]}
    />
  );
};

export const TableRowHeaderGroup = () => {
  return (
    <DragAndDropTable
      columnsInfo={[
        { key: 'index' },
        { key: 'area' },
        { key: 'text' },
        { key: 'group', columnsGroupName: 'Some group' },
      ]}
    />
  );
};

export const TableRowWithDragPreview = () => {
  const tableData = [{ index: 0, area: '100', text: 'text' }];

  const columnsInfo = [{ key: 'index' }, { key: 'area' }, { key: 'text' }];

  return (
    <DragAndDropTable columnsInfo={columnsInfo}>
      {tableData.map(item => {
        return (
          <TableRow
            id={item.index.toString()}
            columnsInfo={columnsInfo}
            leftSideControl={({ hovered }) => (
              <LeftSideControlDrag hovered={hovered} />
            )}
            dragPreview={<TableRowDragPreview>{item.text}</TableRowDragPreview>}
          >
            {() => [item.index, item.area, item.text]}
          </TableRow>
        );
      })}
    </DragAndDropTable>
  );
};
