.form-select {
    max-height: var(--select-max-height);
    background-color: var(--select-background-color);
    font-size: var(--select-font-size);
    padding: var(--select-padding);
    color: var(--select-text-color);
    border-width: var(--select-border-width);
    border-color: var(--select-border-color);
    border-style: solid;

    width: 100%;
    position: relative;
    min-height: 42px;
    border-radius: 8px;
    line-height: 1.25rem;


    display: flex;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.1s;
}

.form-select-white {
    background-color: #FFFFFF;
    border: 1px solid #D5DBDF;
    font-weight: 500;
    font-size: 14px;
    line-height: 18px;
    color: #222222;
}

.form-select:hover {
    color: var(--select-text-color-hovered);
    border-color: var(--select-border-color-hovered);
    background-color: var(--select-background-color-hovered);
}

.form-select__value {
    flex: 1 1 auto;
    padding: 2px 0;
    position: relative;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 0;
    white-space: nowrap;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    text-align: start;
}
.form-select__ico {
    margin-right: 6px;
    display: flex;
    flex: 0 0 auto;
    line-height: 1.25rem;
    min-height: 1.25rem;
}
.form-select__ico svg {
    margin: auto;
    vertical-align: top;
}
.form-select--active:after {
    -webkit-transform: rotate(-180deg) translate(0, 1px);
    transform: rotate(-180deg) translate(0, 1px);
}
.form-select--disabled {
    cursor: default;

    color: var(--select-text-color-disabled);
    border-color: var(--select-border-color-disabled);
    background-color: var(--select-background-color-disabled);

    pointer-events: none;
}
.form-select-sm {
    min-height: 34px;
    font-size: 14px;
    border-radius: 6px;
    padding: 5px 10px;
}
.form-select-sm .form-select__ico {
    margin-top: -2px;
}
.form-select.__error {
    color: var(--select-text-color-error);
    border-color: var(--select-border-color-error);
    background-color: var(--select-background-color-error);
}

.form-select:after {
    flex: 0 0 auto;
    content: '';
    border-left: 3.5px solid transparent;
    border-right: 3.5px solid transparent;
    border-top: 4px solid #222;
    margin-left: 5px;
    width: 0;
    height: 0;
    color: transparent;
    transition: -webkit-transform 0.2s;
    transition: transform 0.2s;
    transition: transform 0.2s, -webkit-transform 0.2s;
}

.light-theme .form-select .form-select__arrow {
    display: none;
}

.dark-theme .form-select .form-select__arrow {
    width: 16px;
    height: 16px;
    margin-left: 12px;
}

.dark-theme .form-select:after {
    display: none;
}

.dark-theme .form-select__value {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
}

.form-select--disabled:after {
    border-top-color: rgba(165, 178, 188, 0.7);
}
.form-select.__wo-arr:after {
    display: none;
}

