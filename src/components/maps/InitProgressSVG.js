import React from 'react';

export const PROGRESS_SVG_ID = 'progress-overlay';

const InitProgressSVG = () => {
  return (
    <svg width='0' height='0' style={{ position: 'absolute' }}>
      <defs>
        <pattern
          id={PROGRESS_SVG_ID}
          x='0'
          y='0'
          width='16'
          height='10'
          patternUnits='userSpaceOnUse'
          preserveAspectRatio='none'
          patternTransform='rotate(45 50 50)'
          viewBox='0,0,16,10'
        >
          <line
            stroke='#e6e6e6'
            opacity='0.2'
            strokeWidth='8px'
            x1='4'
            x2='4'
            y1='0'
            y2='10'
          >
            <animate
              attributeName='x1'
              values='4; -12'
              dur='0.8s'
              repeatCount='indefinite'
            />
            <animate
              attributeName='x2'
              values='4; -12'
              dur='0.8s'
              repeatCount='indefinite'
            />
          </line>
          <line
            stroke='#e6e6e6'
            opacity='0.2'
            strokeWidth='8px'
            x1='20'
            x2='20'
            y1='0'
            y2='10'
          >
            <animate
              attributeName='x1'
              values='20; 4'
              dur='0.8s'
              repeatCount='indefinite'
            />
            <animate
              attributeName='x2'
              values='20; 4'
              dur='0.8s'
              repeatCount='indefinite'
            />
          </line>
        </pattern>
      </defs>
    </svg>
  );
};

export default InitProgressSVG;
