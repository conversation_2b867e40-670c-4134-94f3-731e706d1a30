import React from 'react';
import { useDispatch } from 'react-redux';

import { actions } from 'features/fields/BulkUpload/redux/actions';
import { BulkUploadStep } from 'features/fields/BulkUpload/types';

interface ImportFieldsButtonProps {
  children: React.ReactNode;
}

const ImportFieldsButton = ({ children }: ImportFieldsButtonProps) => {
  const dispatch = useDispatch();

  function onImportFields(files: File[]) {
    if (files) {
      dispatch(actions.setFiles(files));
      dispatch(actions.setReachedStep(BulkUploadStep.FILE_SELECTION));
    }
  }

  return (
    <div className='form-upload'>
      <input
        className='form-upload__input'
        type='file'
        multiple
        onChange={event => {
          if (event?.target?.files) {
            const arr = Array.from(event.target.files);
            onImportFields(arr);
          }
        }}
      />
      {children}
    </div>
  );
};

export default ImportFieldsButton;
