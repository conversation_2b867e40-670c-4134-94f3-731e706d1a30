import {
  MetaAttributes<PERSON><PERSON><PERSON><PERSON>dren,
  MetaAttributes<PERSON><PERSON>,
  TasksByMachineryMeta,
} from 'features/files/by-machinery/types';

type MetaPropertiesMap = Record<string, MetaAttributesApi[]>;

const dictionary: Record<string, string> = {
  // sowing and planting
  vr_app_rate_mass_actual: 'osl_rate_actual',
  vr_app_rate_volume_actual: 'osl_rate_actual',
  vr_seed_rate_seeds_actual: 'osl_rate_actual',
  vr_seed_rate_mass_actual: 'osl_rate_actual',

  osl_rate_set_point: 'osl_rate_actual',
  vr_app_rate_mass_set_point: 'osl_rate_actual',
  vr_app_rate_volume_set_point: 'osl_rate_actual',
  vr_seed_rate_seeds_set_point: 'osl_rate_actual',
  vr_seed_rate_mass_set_point: 'osl_rate_actual',

  vr_total_quantity_applied_mass: 'osl_rate_set_point',
  vr_total_quantity_applied_volume: 'osl_rate_set_point',

  // harvesting (dry)
  vr_yield_mass_per_area: 'osl_yield_dry',
  vr_yield_mass: 'osl_yield_dry',
  vr_yield_volume_per_area: 'osl_yield_dry',
  vr_yield_volume: 'osl_yield_dry',
  vr_avg_yield_mass_per_area: 'osl_yield_dry',
  vr_yield_total_mass: 'osl_yield_dry',
  vr_total_yield_mass: 'osl_yield_dry',
  vr_yield_total_volume: 'osl_yield_dry',
  vr_total_yield_volume: 'osl_yield_dry',

  // harvesting (wet)
  osl_yield_wet: 'osl_yield_dry',
  vr_yield_wet_mass_per_area: 'osl_yield_dry',
  vr_yield_wet_mass: 'osl_yield_dry',
  vr_avg_yield_wet_mass_per_area: 'osl_yield_dry',
  vr_yield_wet_volume: 'osl_yield_dry',
  vr_yield_wet_volume_per_area: 'osl_yield_dry',
  vr_total_yield_wet_volume: 'osl_yield_dry',

  // harvesting (forage)
  vr_yield_mass_forage: 'osl_yield_dry',
  vr_yield_mass_frg_per_area: 'osl_yield_dry',
  vr_yield_total_mass_forage: 'osl_yield_dry',
  vr_forage_mass_productivity: 'osl_yield_dry',
  vr_yield_wet_mass_forage: 'osl_yield_dry',
  vr_yield_wet_mass_frg_per_area: 'osl_yield_dry',
  vr_yield_total_wet_mass_forage: 'osl_yield_dry',

  // harvesting (moisture)
  vr_harvest_moisture: 'vr_avg_harvest_moisture',

  // tillage
  vr_tillage_depth_control: 'vr_tillage_depth_actual',
  vr_tillage_depth_actual: 'vr_tillage_depth_actual',
};

const hidden = [
  'vr_bale_total',
  'vr_vehicle_speed',
  'vr_elevation',
  'vr_heading',
  'vr_working_width_actual',

  'osl_rate_set_point',
  'osl_yield_wet',
];

export const formatMeta = (
  meta: TasksByMachineryMeta,
): TasksByMachineryMeta<MetaAttributesWithChildren> => {
  const properties: MetaAttributesApi[] = meta.groups
    .map(group => group.properties)
    .flat(1);

  const propertiesByParentGroup: MetaPropertiesMap =
    properties.reduce<MetaPropertiesMap>((acc, property) => {
      const parent = dictionary[property.name];
      if (parent) {
        if (!acc[parent]) {
          acc[parent] = [];
        }
        acc[parent]!.push(property);
      } else {
        // console.error('Not found parent attribute for', property);
      }

      return acc;
    }, {});

  return {
    ...meta,
    groups: meta.groups.map(group => {
      const groupedProperties = group.properties
        .filter(property => property.match)
        .filter(v => !hidden.includes(v.name))
        .reduce((acc, property) => {
          if (property.preview) {
            const column = property.preview.column;
            const colObj = acc[column];

            if (
              !colObj ||
              (colObj?.preview &&
                colObj.preview.priority > property.preview.priority)
            ) {
              acc[column] = {
                ...property,
                children: propertiesByParentGroup[property.name] || [],
              };
            }
          }
          return acc;
        }, {} as Record<string, MetaAttributesWithChildren>);
      return {
        ...group,
        properties: Object.values(groupedProperties),
      };
    }),
  };
};
