import { createSelector } from 'reselect';

import { selectors } from './index';
import { filters } from '../filters';
import { ImportingTaskStatus } from '../../types/tasks';
import RootState from 'types/rootState';

export const getImportStatusForNotifications = createSelector(
  (state: RootState) => selectors.getActiveTasks(state),
  activeTasks => {
    if (activeTasks.length === 0) {
      return ImportingTaskStatus.empty;
    }

    if (activeTasks.some(filters.isWaitingBackendTask)) {
      return ImportingTaskStatus.inProgress;
    }

    if (activeTasks.every(filters.isFailedTask)) {
      return ImportingTaskStatus.failed;
    }

    if (activeTasks.some(filters.isFailedTask)) {
      return ImportingTaskStatus.partlySuccess;
    }

    if (activeTasks.every(filters.isShowedTask)) {
      return ImportingTaskStatus.empty;
    }

    return ImportingTaskStatus.success;
  },
);
