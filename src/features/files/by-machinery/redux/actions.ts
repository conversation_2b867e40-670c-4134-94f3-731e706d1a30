import { createActions } from 'utils/redux';

// @ts-ignore
import { abortApiRequest, withAPICall } from 'sagas/global/api';

import { ImportTaskType, TaskStatus } from '../types/tasks';
import {
  ApiDataFieldElectroconductivity,
  ApiDataFieldOperations,
} from '../types/server';

export const FinishRequestId = 'fields-data-operations/import';
export const FinishElectroconductiviytRequestId =
  'electroconductivity-analysis-results​/import';

type SpatialMatchingType = {
  uuid: string;
  sampling_point_prop_id: string;
  field_name_prop_id?: string;
  field_user_season_id?: string;
};

export const actionsConfig = {
  finished: [
    (operations: ApiDataFieldOperations[]) => ({ operations }),
    (operations: ApiDataFieldOperations[]) => [
      withAPICall(`fields-data-operations/import`, 'POST', {
        requestId: FinishRequestId,
        body: {
          operation_ids: operations
            .filter(operation => operation.is_enabled)
            .map(operation => operation.uuid),
        },
        v: 2,
      }),
    ],
  ] as const,

  finishedElectroconductivity: [
    (electroconductivityData: ApiDataFieldElectroconductivity[]) => ({
      electroconductivityData,
    }),
    (electroconductivityData: ApiDataFieldElectroconductivity[]) => [
      withAPICall(`electroconductivity-analysis-results/import`, 'POST', {
        requestId: FinishElectroconductiviytRequestId,
        body: {
          electroconductivity_analysis_result_uuids:
            electroconductivityData.map(e => e.uuid),
        },
        v: 2,
      }),
    ],
  ] as const,

  finishSoilSamplingTasks: () => {},
  finishOperations: () => {},

  markTasksAsShowed: [
    (tasks: ImportTaskType[]) => ({ tasks }),
    (tasks: ImportTaskType[]) => [
      withAPICall(`batch`, 'POST', {
        requestId: `markTasksAsShowed`,
        v: 2,
        body: {
          requests: tasks.map(task => ({
            request_id: `markTasksAsShowed/${task.uuid}`,
            method: 'patch',
            path: `/en/v2/fields-data-uploads/${task.uuid}`,
            body: {
              viewed_at: new Date(),
            },
          })),
        },
      }),
    ],
  ] as const,
  updateTasksStatus: [
    () => ({}),
    () => [
      withAPICall(`fields-data-uploads`, 'GET', {
        requestId: `fields-data-uploads/updateStatus`,
        query: {
          'include[]': ['sources.layers'],
        },
        v: 2,
      }),
    ],
  ] as const,
  removeTask: [
    (task: ImportTaskType) => ({ task }),
    (task: ImportTaskType) => {
      if (task.disabled) {
        return [];
      }
      if (task.uuid) {
        return [
          withAPICall(`fields-data-uploads/${task.uuid}`, 'DELETE', {
            requestId: `delete/fields-data-uploads/uuid/${task.uuid}`,
            trackUploadProgress: true,
            v: 2,
          }),
        ];
      } else {
        if (task.status === TaskStatus.uploading) {
          abortApiRequest(task.requestId);
        }
      }

      return [];
    },
  ] as const,
  createTaskWithError: (temporaryUuid: string, file: File) => ({
    temporaryUuid,
    file,
  }),
  requestMeta: [
    (requestId: string, dataType: string) => ({ requestId, dataType }),
    (requestId: string, dataType: string) => [
      withAPICall(
        `fields-data-layers-properties/meta?data_type=${dataType}`,
        'GET',
        {
          requestId: requestId,
          v: 2,
        },
      ),
    ],
  ] as const,
  requestLegend: [
    (operation: ApiDataFieldOperations, requestId: string) => ({
      operation,
      requestId,
    }),
    (operation: ApiDataFieldOperations, requestId: string) => [
      withAPICall(`fields-data-operations/${operation.uuid}/legend`, 'GET', {
        requestId: requestId,
        v: 2,
      }),
    ],
  ] as const,

  createTask: [
    (temporaryUuid: string, file: File, dataType: string) => ({
      temporaryUuid,
      file,
      dataType,
    }),
    (temporaryUuid: string, file: File, dataType: string) => {
      const body = new FormData();
      body.append('file', file);
      body.append('data_type', dataType);
      return [
        withAPICall(`fields-data-uploads`, 'POST', {
          requestId: `create/fields-data-uploads/uuid/${temporaryUuid}`,
          trackUploadProgress: true,
          body,
          v: 2,
        }),
      ];
    },
  ] as const,
  finishTask: [
    (task: ImportTaskType) => ({ task }),
    (task: ImportTaskType) => [
      withAPICall(`fields-data-uploads/${task.uuid}`, 'PATCH', {
        requestId: `fields-data-uploads/${task.uuid}`,
        v: 2,
        body: {
          status: TaskStatus.done,
        },
      }),
    ],
  ] as const,
  spatialMatching: [
    (data: SpatialMatchingType) => ({ data }),
    (data: SpatialMatchingType) => {
      const { uuid, ...rest } = data;
      return [
        withAPICall(`fields-data-layers/${uuid}/spatial-matching`, 'POST', {
          requestId: `spatial-matching`,
          v: 2,
          body: {
            ...rest,
          },
        }),
      ];
    },
  ] as const,
  fetchLayerTasks: [
    () => ({}),
    () => [
      withAPICall(`fields-data-layers`, 'GET', {
        v: 2,
      }),
    ],
  ] as const,
};

export const actions = createActions('files-by-machinery', actionsConfig);
