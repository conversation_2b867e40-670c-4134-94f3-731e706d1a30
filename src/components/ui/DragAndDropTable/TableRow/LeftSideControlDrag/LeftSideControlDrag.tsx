import React from 'react';
import { DragPreviewIconContainer } from 'components/ui/DragAndDropTable/TableRow/TableRow.style';
import Icon from 'components/Icon';

export type LeftSideControlDragProps = {
  hovered: boolean;
};

export const LeftSideControlDrag = ({ hovered }: LeftSideControlDragProps) => {
  if (!hovered) {
    return null;
  }

  return (
    <div className={DragPreviewIconContainer}>
      <Icon name='drag' />
    </div>
  );
};
