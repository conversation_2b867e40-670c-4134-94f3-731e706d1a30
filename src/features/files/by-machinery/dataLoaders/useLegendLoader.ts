import { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import api from 'modules/api';

import { selectors } from 'features/files/by-machinery/redux/selectors';
import importTaskModule from 'features/files/by-machinery/redux';
import {
  ApiDataFieldOperationLegend,
  ApiDataFieldOperations,
  DataLoader,
} from 'features/files/by-machinery/types';

const cache: Record<string, boolean> = {};

export const useLegendLoader = (
  operation: ApiDataFieldOperations | undefined,
): DataLoader<Nullable<ApiDataFieldOperationLegend>> => {
  const dispatch = useDispatch();
  const requestId = `useLegendLoader${operation?.uuid}`;

  const request = useSelector(state =>
    api.selectors.getRequestStatusV2(state, requestId),
  );

  const fetch = useCallback(() => {
    if (operation) {
      if (!cache[operation.uuid]) {
        dispatch(importTaskModule.actions.requestLegend(operation, requestId));
      }
      cache[operation?.uuid] = true;
    }
  }, [dispatch, operation, requestId]);

  const data: Nullable<ApiDataFieldOperationLegend> = useSelector(state =>
    selectors.getLegend(state, operation),
  );

  useEffect(fetch, [fetch]);

  return [data, request, fetch];
};
