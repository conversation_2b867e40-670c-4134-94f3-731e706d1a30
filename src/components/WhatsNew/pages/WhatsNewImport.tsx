import React, { VFC } from 'react';
import {
  StyledPage,
  styles,
} from 'components/WhatsNew/WhatsNewContent/WhatsNewContent.style';
import Button from 'components/ui/Button/Button';

import WhatsNewImportImg from 'assets/images/whatsnew/whatsnew_import.jpg';
import WhatsNewImportImg2x from 'assets/images/whatsnew/<EMAIL>';
import { WhatsNewProps } from '../types';
import { FeaturesKeyEnum } from '../consts';

const Import: VFC<WhatsNewProps> = ({ style, onClick, t, isLast }) => (
  <StyledPage style={{ ...style }}>
    <div className={styles.semiPage}>
      <div className={styles.pageContent}>
        <div className={styles.textContent}>
          <h2 className={styles.title}>{t('whatsnew.import.title')}</h2>
          <p className={styles.text}>{t('whatsnew.import.text')}</p>
        </div>
        <Button className='btn btn-success fit-content' onClick={onClick}>
          {t(`whatsnew.${isLast ? 'end' : 'next'}.action_text`)}
        </Button>
      </div>
    </div>
    <div className={styles.semiPage}>
      <picture className={styles.pagePicture}>
        <source srcSet={`${WhatsNewImportImg} 1x, ${WhatsNewImportImg2x} 2x`} />
        <img
          src={WhatsNewImportImg}
          alt={t('whatsnew.import.title') as string}
        />
      </picture>
    </div>
  </StyledPage>
);

const WhatsNewImport = {
  key: FeaturesKeyEnum.import,
  component: Import,
  filter: {},
};

export default WhatsNewImport;
