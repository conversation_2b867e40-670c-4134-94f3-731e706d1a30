import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';

import Saga from 'components/saga/Saga';
import { CustomDropdown } from 'components/ui/CustomDropdown/CustomDropdown';
import Button from 'components/ui/Button/Button';
import Icon from 'components/Icon';
import ModernSpinner from 'components/ui/ModernSpinner/ModernSpinner';
import { FloatingActionsStyle } from 'components/ui/FloatingActions/styles';
import EnterExitTransition from 'components/EnterExitTransition';
import MissedCropPopup from './MissedCropPopup';

import api from 'modules/api';
import fields from 'modules/fields';
import seasons from 'modules/seasons';

import matchColumnsSaga from 'sagas/local/match-columns';
import logEvent from 'sagas/global/logEvent';
import getRootScoutingUrl from 'utils/get-root-scouting-url';
import { formatTitle as formatSeasonTitle } from 'utils/seasons-formatters';
import { useTranslate } from 'utils/use-translate';

const FIXED_ATTR = [
  'crop',
  'sowing_date',
  'harvest_date',
  'yield_value',
  'variety',
];

const createAttrs = (t, seasons = []) => {
  return seasons.flatMap(item => [
    ...FIXED_ATTR.map(attr => ({
      id: `${attr}_${item.id}`,
      label:
        t(`fields.upload.match_crops.${attr}.label`) +
        ` (${formatSeasonTitle(t, item)})`,
      season_id: item.id,
      type: attr,
    })),
  ]);
};

const MatchColumnsStep = ({ match, history, location }) => {
  const { t } = useTranslate();
  const saveRequest = useSelector(state =>
    api.selectors.getRequestStatus(state, 'update-fields-file'),
  );
  const allSeasons = useSelector(state => seasons.selectors.getAll(state));
  const cropsAttr = createAttrs(t, allSeasons);

  const attributes = [
    { id: 'default', label: t('fields.upload.match_crops.no_data') },
    { id: 'title', label: t('fields.upload.match_crops.field_title') },
    ...cropsAttr,
  ];
  const baseUrl = getRootScoutingUrl(history.location.pathname);
  const dispatch = useDispatch();

  const [matchFields, setMatchFields] = useState({});
  const [data, setData] = useState({});
  const [showPopup, setShowPopup] = useState(false);
  const { fileId } = match.params;

  const handleSubmit = () => {
    logEvent('fields_import_match_properties', matchFields);
    dispatch(fields.actions.setMatchedProperties(fileId, matchFields));
  };

  return (
    <>
      <Saga
        saga={matchColumnsSaga}
        fileId={fileId}
        history={history}
        onSetData={setData}
        onShowPopup={setShowPopup}
      />
      <EnterExitTransition variant='popup'>
        {showPopup && (
          <MissedCropPopup
            seasonIds={showPopup}
            allSeasons={allSeasons}
            onConfirm={() => {
              logEvent('fields_import_missed_crops', { answer: 'confirm' });
              setShowPopup(false);
              const proceed = true;
              dispatch(
                fields.actions.setMatchedProperties(
                  fileId,
                  matchFields,
                  proceed,
                ),
              );
            }}
            onCancel={() => {
              logEvent('fields_import_missed_crops', { answer: 'cancel' });
              setShowPopup(false);
            }}
          />
        )}
      </EnterExitTransition>
      <div className='main-section__body'>
        <form onSubmit={() => {}}>
          <div className='main-uploader' data-type='columns'>
            <div className='main-uploader-header'>
              <h2 className='main-uploader-header__title'>
                {t('fields.upload.match_columns.title')}
              </h2>
              <p className={'main-uploader-header__description'}>
                {t('fields.upload.match_columns.sub_title')}
              </p>
            </div>
            <div className='main-uploader-naming'>
              <div className='main-uploader-naming__header'>
                <div className='main-uploader-naming__col'>
                  {t('fields.upload.match_columns.from_file')}
                </div>
                <div className='main-uploader-naming__col'>
                  {t('fields.upload.match_columns.to_platform')}
                </div>
              </div>
              {!Object.keys(data).length && (
                <div className='main-uploader-spinner'>
                  <ModernSpinner large />
                </div>
              )}
              {Object.keys(data).map(key => (
                <div
                  key={key}
                  className='main-uploader-naming__row'
                  data-column={matchFields[`${key}`] ? 'selected' : 'passed'}
                >
                  <div className='main-uploader-naming__arr'>
                    <Icon
                      className='ico-arrow-circle-right-os'
                      name='arrow-circle-right'
                    />
                  </div>
                  <div className='main-uploader-naming__col'>
                    <h3 className='main-uploader-naming__title'>{[key]}</h3>
                    <ul className='main-uploader-naming-chars'>
                      {data[key].map(item => (
                        <li
                          key={item}
                          className='main-uploader-naming-chars__list-item'
                        >
                          {item}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div className='main-uploader-naming__col'>
                    <CustomDropdown
                      placeholder={t('files.actions.placeholder')}
                      className='form-select form-select-sm'
                      value={matchFields[key]}
                      renderValue={({ value }) => (
                        <div className='form-select__value'>
                          {value?.label
                            ? value.label
                            : t('fields.upload.match_crops.no_data')}
                        </div>
                      )}
                      options={attributes.map(option => ({
                        label: option.label,
                        id: option.id,
                      }))}
                      disabledIDs={Object.values(matchFields).map(
                        item => item.id,
                      )}
                      withSearchBox={attributes.length >= 5}
                      searchBoxProps={{
                        placeholder: t(
                          'fields.upload.match_crops.search_placeholder',
                        ),
                      }}
                      onClick={event => event.stopPropagation()}
                      onChange={action => {
                        let state = { ...matchFields };
                        if (action === 'default') {
                          delete state[key];
                        } else {
                          state = {
                            ...state,
                            [key]: attributes.find(item => item.id === action),
                          };
                        }
                        setMatchFields(state);
                      }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </form>
      </div>
      <FloatingActionsStyle>
        <Button
          className='btn btn-dark btn-lg'
          onClick={() => {
            const {
              location: { state },
            } = history;
            history.replace({
              pathname: `${baseUrl}/add/upload`,
              state,
            });
          }}
        >
          {t('forms.cancel')}
        </Button>
        <Button
          className='btn btn-success btn-lg'
          type='submit'
          pending={saveRequest.status === 'pending'}
          onClick={() => handleSubmit()}
        >
          {t('forms.next')}
        </Button>
      </FloatingActionsStyle>
    </>
  );
};

export default MatchColumnsStep;
