import React, { useState } from 'react';
import { connect } from 'react-redux';
import { Control } from 'react-redux-form';
import transformScale from '@turf/transform-scale';
import calcBbox from '@turf/bbox';
import moment from 'moment';
import cx from 'classnames';

import Icon from 'components/Icon';
import LinkButton from 'components/LinkButton';
import BackgroundImage from 'components/ui/BackgroundImage/BackgroundImage';
import FileIcon from 'assets/images/ic_file.svg';

import { OperationTypes } from 'constants/files';
import { formatTitle } from 'utils/fields-formatters';
import { formatDate } from 'utils/format-date';
import { useTranslate } from 'utils/use-translate';

import map from 'modules/map';
import files from 'modules/files';
import fields from 'modules/fields';
import { CustomDropdown } from 'components/ui/CustomDropdown/CustomDropdown';
import SharedLinkForFilePopup from '../shared/SharedLinkForFilePopup';
import { FEATURES } from 'features/permissions';

const operations = [
  {
    name: 'edit',
    feature: FEATURES.UPLOAD_DATA_EDIT,
  },
  {
    name: 'remove',
    feature: FEATURES.UPLOAD_DATA_DELETE,
  },
  {
    name: 'share',
    feature: FEATURES.UNKNOWN,
  },
];

const FileListItem = ({
  file,
  field,
  selected,
  highlighted,
  formModel,
  noZoom,
  readOnly,
  onFlyTo,
  onEdit,
  enableShare,
  onRemove,
  onClick,
}) => {
  const { t } = useTranslate();
  const [visibilitySharedPopup, setVisibilitySharedPopup] = useState(false);

  const zoomToFile = () => {
    if (noZoom || file.status !== 'resolved') {
      return;
    }
    const bbox = calcBbox(transformScale(file.geojson, 2));
    onFlyTo([bbox.slice(0, 2).reverse(), bbox.slice(2, 4).reverse()]);
  };

  const date = moment(file.operation_date || file.created_at);
  const thisYear = moment().diff(date, 'years') < 1;

  return (
    <li
      data-scroll-to={`file-${file.id}`}
      className={cx('soil-fields-list__list-item', {
        __updated: readOnly || highlighted,
      })}
    >
      {visibilitySharedPopup && (
        <SharedLinkForFilePopup
          onClose={() => setVisibilitySharedPopup(false)}
          file={file}
        />
      )}
      <LinkButton
        className={cx('soil-fields-list__item', {
          __selected: selected,
        })}
        onClick={() => {
          zoomToFile();
          onClick();
        }}
      >
        <BackgroundImage className='soil-fields-list__pic' src={FileIcon} />
        <div className='soil-fields-list__content'>
          {formModel ? (
            <Control.select
              className='soil-fields-list__header'
              component={CustomDropdown}
              model={`${formModel}.type`}
              controlProps={{
                placeholder: t('files.types.select'),
                options: OperationTypes.map(type => ({
                  label: t(`files.types.${type}`),
                  id: type,
                })),
                renderValue: ({ value }) => (
                  <div className='soil-fields-list__select-type'>
                    {t(`files.types.${value || 'select'}`)}{' '}
                    <Icon
                      className='ico-chevron-down-small-os'
                      name='arrow-down-small'
                    />
                  </div>
                ),
              }}
            />
          ) : (
            <h2 className='soil-fields-list__header'>
              {t(`files.types.${file.type}`)}
            </h2>
          )}
          {file.description && (
            <div className='soil-fields-list-meta_description'>
              {file.description}
            </div>
          )}
          <div className='soil-fields-list-meta'>
            <div className='soil-fields-list-meta__item soil-fields-list-meta__places'>
              {field ? formatTitle(t, field) : t('files.no_field')}
            </div>
            <div className='soil-fields-list-meta__item soil-fields-list-meta__date'>
              {formatDate(
                date,
                t(thisYear ? 'files.date_format' : 'files.date_format_full'),
              )}
            </div>
          </div>
        </div>
      </LinkButton>
      {!readOnly && (
        <CustomDropdown
          className='soil-fields-list-actions'
          placeholder={t('files.actions.placeholder')}
          renderValue={() => (
            <span className='soil-fields-list-actions__item' />
          )}
          options={operations
            .filter(operation => !(!onEdit && operation.name === 'edit'))
            .filter(operation => !(!enableShare && operation.name === 'share'))
            .filter(Boolean)
            .map(option => ({
              label: t(`files.actions.${option.name}`),
              feature: option.feature,
              id: option.name,
            }))}
          onClick={event => {
            event.stopPropagation();
          }}
          onChange={action => {
            switch (action) {
              case 'share': {
                setVisibilitySharedPopup(true);
                break;
              }
              case 'edit': {
                onEdit(file);
                break;
              }
              case 'remove': {
                onRemove(file);
                break;
              }
              default: {
                console.warn('Unsupported action', action);
                break;
              }
            }
          }}
        />
      )}
    </li>
  );
};

const mapStateToProps = (state, ownProps) => {
  const file = files.selectors.getByID(state, ownProps.fileId);
  return {
    field: fields.selectors.getByOriginalID(state, 'own', file.field_user_id),
    file,
  };
};

const mapDispatchToProps = {
  onFlyTo: bounds => map.actions.addIntent('fly-to-bounds', { bounds }),
};

export default connect(mapStateToProps, mapDispatchToProps)(FileListItem);
